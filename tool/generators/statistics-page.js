module.exports = function (plop) {
  plop.setGenerator('component', {
    description: '生成一个新的统计页面',
        prompts: [
            {
                type: 'input',
                name: 'directory',
                message: '请输入统计二级目录名称：'
                },
                {
                type: 'input',
                name: 'fileName',
                message: '请输入文件名：'
            },
        ],
        actions: [{
        type: 'add',
        path: '../../src/views/statistics/{{directory}}/{{fileName}}/{{fileName}}.vue',
        templateFile: 'statistics-base-page/index.vue.hbs'
        }]
    });
};
