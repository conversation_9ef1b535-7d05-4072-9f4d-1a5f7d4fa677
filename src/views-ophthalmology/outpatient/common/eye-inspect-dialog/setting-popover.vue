<template>
    <abc-popover
        v-model="showPopover"
        width="auto"
        :open-delay="200"
        placement="bottom-end"
        trigger="manual"
        theme="white"
        :visible-arrow="false"
        :popper-style="{ padding: 0 }"
    >
        <div
            slot="reference"
            class="eye-instpect-setting-reference"
            style="margin-top: 8.5px;"
            @click="showPopover = true"
        >
            <abc-button
                icon="set"
                icon-color="#aab4bf"
                size="large"
                variant="text"
            ></abc-button>
        </div>

        <div
            v-abc-click-outside="
                () => {
                    showPopover = false;
                }
            "
            class="eye-instpect-setting-popover"
        >
            <div class="setting-content">
                <h5>眼部检查项目设置</h5>
                <div>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.eyeball"
                        type="number"
                    >
                        眼球
                    </abc-checkbox>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.eyelid"
                        disabled
                        type="number"
                    >
                        眼睑
                    </abc-checkbox>
                </div>
                <div>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.tearOrgan"
                        type="number"
                    >
                        泪器
                    </abc-checkbox>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.conjunctiva"
                        disabled
                        type="number"
                    >
                        结膜
                    </abc-checkbox>
                </div>
                <div>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.sclera"
                        type="number"
                    >
                        巩膜
                    </abc-checkbox>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.cornea"
                        disabled
                        type="number"
                    >
                        角膜
                    </abc-checkbox>
                </div>
                <div>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.iris"
                        type="number"
                    >
                        虹膜
                    </abc-checkbox>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.pupil"
                        type="number"
                    >
                        瞳孔
                    </abc-checkbox>
                </div>
                <div>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.atria"
                        type="number"
                    >
                        前房
                    </abc-checkbox>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.lens"
                        type="number"
                    >
                        晶状体
                    </abc-checkbox>
                </div>
                <div>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.fundus"
                        type="number"
                    >
                        眼底
                    </abc-checkbox>
                    <abc-checkbox
                        v-model="eyeExaminationProducts.vitreum"
                        type="number"
                    >
                        玻璃体
                    </abc-checkbox>
                </div>
                <div class="setting-content-footer">
                    <abc-button size="small" @click="updateConfig">
                        确定
                    </abc-button>
                    <abc-button size="small" type="blank" @click="showPopover = false">
                        取消
                    </abc-button>
                </div>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import { ANONYMOUS_ID } from '@abc/constants';

    export default {
        name: 'EyeInspectSettingPopover',
        props: {
            scopeId: String,
        },

        data() {
            return {
                showPopover: false,

                eyeExaminationProducts: {
                    eyelid: 1, // 眼睑
                    cornea: 1, // 角膜
                    conjunctiva: 1, // 结膜

                    eyeball: 0, // 眼球
                    atria: 0, // 前房
                    iris: 0, //虹膜
                    tearOrgan: 0, // 泪器
                    pupil: 0, // 瞳孔
                    lens: 0, // 晶状体
                    sclera: 0, // 巩膜
                    vitreum: 0, // 玻璃体
                    fundus: 0, // 眼底
                },
            };
        },

        computed: {
            ...mapGetters('outpatientConfig', [
                'outpatientEmployeeConfig',
                'departmentDoctorMedicalRecord',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters(['userInfo']),
        },
        watch: {
            showPopover(val) {
                const config = this.outpatientEmployeeConfig;
                const { eyeExaminationProducts } = config.medicalRecord;
                if (val && eyeExaminationProducts) {
                    for (const key of eyeExaminationProducts) {
                        this.eyeExaminationProducts[key] = 1;
                    }
                }
                this.$nextTick(() => {
                    this._loading = false;
                });
            },
        },
        created() {
            this._loading = true;
        },
        methods: {
            ...mapActions('outpatientConfig', [
                'initOutpatientConfig',
                'updateEmployeeMRConfig',
                'updateDepartmentDoctorMRConfig',
            ]),

            updateConfig() {
                const settings = [];
                for (const key in this.eyeExaminationProducts) {
                    if (this.eyeExaminationProducts[key] === 1) {
                        settings.push(key);
                    }
                }
                const {
                    outpatientConfigScope,
                } = this.viewDistributeConfig.Outpatient;

                if (outpatientConfigScope === 'dep_emp') {
                    const scopeId = this.scopeId || `${ANONYMOUS_ID}_${this.userInfo.id}`;
                    this.updateDepartmentDoctorMRConfig({
                        scopeId,
                        key: 'ophthalmology.eyeExaminationProducts',
                        value: settings,
                    });
                } else {
                    this.updateEmployeeMRConfig({
                        key: 'ophthalmology.eyeExaminationProducts',
                        value: settings,
                    });
                }
                this.showPopover = false;
            },
        },
    };
</script>

<style lang="scss" scoped>
.eye-instpect-setting-popover {
    width: auto;
    padding: 12px 4px 12px 12px;

    .setting-content {
        &-footer {
            display: flex;
            justify-content: space-between;
            padding-right: 8px;
            margin-top: 12px;

            .abc-button-small {
                padding: 0 18px;
            }
        }

        .abc-checkbox-wrapper {
            min-width: 66px;
            margin-top: 12px;
        }
    }
}
</style>
