/*
 * <AUTHOR>
 * @DateTime 2021-03-09 15:58:25
 */
import fetch from 'utils/fetch';

export default {
    /**
     * 查看积分流水
     * <AUTHOR>
     * @date 2020-09-15
     * @param {String} patientId 患者id
     * @param {Object} params 入参数据
     * @returns {Promise}
     */
    async fetchPointsBillByPatientId(patientId, params) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/points/bills`,
            method: 'GET',
            params,
        });
        return res.data;
    },
    /**
     * 查看积分规则设置
     * <AUTHOR>
     * @date 2020-09-15
     * @returns {Promise}
     */
    async fetchPointsConfig() {
        const res = await fetch({
            url: '/api/v2/crm/patients/points/config',
            method: 'GET',
        });
        return res.data;
    },
    /**
     * 更新积分规则设置
     * <AUTHOR>
     * @date 2020-09-15
     * @returns {Promise}
     */
    async updatePointsConfig(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/points/config',
            method: 'PUT',
            data,
        });
        return res.data;
    },

    /**
     * @desc 更新积分规则设置校验接口
     * <AUTHOR> Yang
     * @date 2024-08-12 14:16:33
    */
    async preCheckPointsConfig(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/points/config/check',
            method: 'PUT',
            data,
        });
        return res.data;
    },
    /**
     * 积分人工发放积分
     * <AUTHOR>
     * @date 2020-09-15
     * @returns {Promise}
     */
    async handlePointsAddManual(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/points/add-manual',
            method: 'POST',
            data,
        });
        return res.data;
    },
    /**
     * 积分人工兑换积分
     * <AUTHOR>
     * @date 2020-09-15
     * @returns {Promise}
     */
    async handlePointsDeductionManual(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/points/deduction-manual',
            method: 'POST',
            data,
        });
        return res.data;
    },
};
