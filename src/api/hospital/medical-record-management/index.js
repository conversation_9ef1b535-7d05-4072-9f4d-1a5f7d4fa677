import fetch from 'utils/fetch';
import BaseAPI from 'api/base-api.js';

export default class MedicalRecordManagementAPI extends BaseAPI {
    // 获取住院病案QL
    static async getMedicalRecordManagementQL(params) {
        const res = await fetch({
            url: '/api/emr/archive',
            method: 'get',
            params,
        });
        return res && res.data && res.data.data && res.data.data.rows;
    }

    // 请求归档
    static async requrstArchive(patientOrderId) {
        const res = await fetch({
            url: `/api/emr/archive/${patientOrderId}`,
            method: 'post',
        });
        return res && res.data;
    }

    // 批量完成归档
    static async batchArchive(data) {
        const res = await fetch({
            url: '/api/emr/archive/pass/batch',
            method: 'put',
            data,
        });
        return res && res.data;
    }

    // 退回文档
    static async sendBackArchive(data) {
        const res = await fetch({
            url: '/api/emr/medicalDoc/archive/back',
            method: 'put',
            data,
        });
        return res && res.data;
    }
}
