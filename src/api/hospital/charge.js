import BaseAPI from 'api/base-api.js';
import fetch from 'utils/fetch.js';
import Qs from 'qs';

export default class HospitalChargeAPI extends BaseAPI {
    /**
     * @desc 业务下单
     * <AUTHOR>
     * @date 2023-01-13 15:41:23
     */
    static async pay(patientOrderId, data) {
        const res = await fetch({
            url: `/api/his-charges/settle/${patientOrderId}/pay`,
            method: 'put',
            data,
        });
        return res && res.data && res.data.data;
    }

    /**
     * @desc 业务退款下单
     * <AUTHOR>
     * @date 2023-02-21 14:37:40
     */
    static async refund(patientOrderId, data) {
        const res = await fetch({
            url: `/api/his-charges/settle/${patientOrderId}/refund`,
            method: 'put',
            data,
        });
        return res && res.data && res.data.data;
    }

    static async fetchQuickList(params) {
        const res = await fetch({
            url: '/api/his-charges/settle/quick-list',
            method: 'get',
            params,
            paramsSerializer(ps) {
                return Qs.stringify(ps, { indices: false });
            },
        });
        return res && res.data;
    }

    static async fetchDetail(patientOrderId) {
        const res = await fetch({
            url: `/api/his-charges/${patientOrderId}`,
        });
        return res.data;
    }

    /**
     * @desc 查询住院结算单详情
     * <AUTHOR>
     * @date 2023-02-22 16:48:33
     */
    static async fetchSettleDetail(patientOrderId) {
        const res = await fetch({
            url: `/api/his-charges/settle/${patientOrderId}`,
        });
        return res && res.data && res.data.data;
    }

    /**
     * @desc 重新结算
     * <AUTHOR>
     * @date 2023-02-22 17:32:07
     */
    static async renewSettle(patientOrderId) {
        const res = await fetch({
            url: `/api/his-charges/settle/${patientOrderId}/renew-settle`,
            method: 'put',
        });
        return res && res.data && res.data.data;
    }

    /**
     * @desc 补欠费计费 患者补费用项目
     * <AUTHOR>
     * @date 2023-01-31 10:22:48
     * @param {object} data
     * @param {array} data.chargeFormItems
     * @param {array} data.patientOrderIds
     */
    static async supplement(data) {
        const res = await fetch({
            url: '/api/his-charges/supplement',
            method: 'post',
            data,
        });
        return res && res.data;
    }

    static async fetchCheckFeeList(patientOrderId, params) {
        const res = await fetch({
            url: `/api/his-charges/${patientOrderId}/check-fee-list`,
            params,
        });
        return res.data;
    }

    /**
     * 获取患者计费流水详情
     * @param {string} patientOrderId 住院单ID
     * @param {Object} params
     * @param {number} params.adviceType 医嘱类型
     * @param {string} params.chargedTimeEnd 计费开始时间
     * @param {string} params.chargedTimeStart 计费结束时间
     * @param {number} params.limit 每页条数
     * @param {string} params.name 搜索: 计费项名称
     * @param {number} params.offset 页码
     * @return {Promise<{ page: Object, priceScopeType: number, settleType: number } | Object>}
     */
    static async fetchCheckFeeDetailList(patientOrderId, params) {
        const res = await fetch({
            url: `/api/his-charges/${patientOrderId}/charge-item-detail`,
            method: 'GET',
            params,
        });
        return res.data || {};
    }

    /**
     * @desc 获取费用详情明细
     * <AUTHOR>
     * @date 2023-02-03 14:47:52
     * @param {string} patientOrderId
     * @param {string} goodsId
     * @param {object} params
     * @param {number} params.hisChargeSheetType
     * @param {string} params.adviceRuleItemId
     * @param {string} params.goodsSignatureKey
     */
    static async fetchCheckFeeDetail(patientOrderId, goodsId, params) {
        const res = await fetch({
            url: `/api/his-charges/${patientOrderId}/${goodsId}/check-fee-detail`,
            params,
        });
        return res.data;
    }

    /**
     * @desc 住院押金缴纳
     * <AUTHOR>
     * @date 2023-02-09 16:30:49
     * @param {string} patientOrderId - patientOrderId
     * @param {object} data -
     * @param {number} data.amount - 金额
     */
    static async deposit(patientOrderId, data) {
        const res = await fetch({
            url: `/api/his-charges/deposit/${patientOrderId}/pay`,
            method: 'put',
            data,
        });
        return res && res.data;
    }

    /**
     * @desc 撤销计费
     * <AUTHOR>
     * @date 2023-02-13 15:11:11
     * @param {string} patientOrderId - patientOrderId
     * @param {object} data -
     * @param {number} data.chargeFormItemIds -
     */
    static async refundBill(patientOrderId, data) {
        const res = await fetch({
            url: `/api/his-charges/${patientOrderId}/refund-bill`,
            method: 'put',
            data,
        });
        return res && res.data;
    }

    /**
     * @desc 医嘱单计费明细
     * <AUTHOR>
     * @date 2023/02/23 17:39:39
     * @param {string} patientOrderId
     * @param {string} adviceId
     * @return {object} data
     */
    static async getAdviceBillDetail(patientOrderId, adviceId) {
        const res = await fetch({
            url: `/api/his-charges/${patientOrderId}/advice-bill-detail`,
            method: 'get',
            params: {
                patientOrderId,
                adviceId,
            },
        });
        return res && res.data;
    }

    /**
     * @desc 医嘱单费用统计
     * <AUTHOR>
     * @date 2023/02/23 17:39:39
     * @param {string} patientOrderId
     * @param {string} adviceId
     * @return {object} data
     */
    static async getAdviceStatistics(patientOrderId, adviceId) {
        const res = await fetch({
            url: `/api/his-charges/${patientOrderId}/advice-statistics`,
            method: 'get',
            params: {
                patientOrderId,
                adviceId,
            },
        });
        return res && res.data;
    }

    // 查询计费关联用法项目
    static async getRelatedUsageItemDetails(chargeFromItemId) {
        const res = await fetch({
            url: `/api/his-charges/${chargeFromItemId}/related-usage/item-details`,
            method: 'get',
        });
        return res && res.data;
    }

    /**
     * @desc 出院结算预校验
     * @param {string} patientOrderId
     * @return {Promise<*>}
     */
    static async preCheckBeforeDischarge(patientOrderId) {
        const res = await fetch({
            url: `/api/his-charges/settle/pre-check-task/${patientOrderId}`,
            method: 'get',
        });
        return res && res.data;
    }
}
