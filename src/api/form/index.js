import fetch from 'utils/fetch';

export default {
    /**
     * @desc 获取表单模版 https://doc.abczs.cn/organization/repository/editor?id=31&mod=184&itf=1358
     * <AUTHOR>
     * @date 2021/01/14 17:12:41
     * @params
     * @return
     */
    async fetchFormTemplate(id) {
        const res = await fetch.get(`/api/v2/form/template/${id}`);
        return res.data;
    },

    /**
     * @desc 提交表单 https://doc.abczs.cn/organization/repository/editor?id=31&mod=184&itf=1359
     * <AUTHOR>
     * @date 2021/01/14 17:13:28
     * @params
     * @return
     */
    async postForm(data) {
        const res = await fetch.post(`/api/v2/form/template/{id}/fill`, data);
        return res.data;
    },

    /**
     * @desc 获取已填表单数据 https://doc.abczs.cn/organization/repository/editor?id=31&mod=184&itf=1360
     * <AUTHOR>
     * @date 2021/01/14 17:15:43
     * @params
     * @return
     */
    async fetchFormData(id) {
        const res = await fetch.get(`/api/v2/form/data/${id}`);
        return res.data;
    },

    /**
     * @desc 获取表单模版列表
     * <AUTHOR> Yang
     * @date 2021-01-22 08:28:19
     * @params { type: 1 一般问诊单 2 慢病问诊单 }
     * @return
     */
    async fetchFormTemplates(params) {
        const res = await fetch({
            url: `/api/v2/form/templates`,
            params,
        });
        return res.data;
    },
};
