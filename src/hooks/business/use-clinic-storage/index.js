import Storage from '@/utils/localStorage-handler';

export const CustomKeys = {
    // 当前门店
    CURRENT_CLINIC: '_current_clinic_',
    // 用户设置的商品单位
    USER_GOODS_UNIT: '_user_goods_unit_',
    // 用户设置的商品类型
    USER_GOODS_TYPE: '_user_goods_type_',
    // 门店库存页面每页显示数量
    CLINIC_INVENTORY_PAGE_SIZE: '_clinic_inventory_page_size_',
};

export const TableKeys = {
    // 诊所医院库存表格
    inventoryGoodsTable: '_inventory_goods_table_',
    // 药店统计-历史数据表格
    statisticsHistoryDataTable: '_statistics_history_data_table_',
};

/**
 * @desc 用于存储诊所相关的数据、如诊所库存页面每页显示数量、记录药品每次采购选择的单位等数据
 * <AUTHOR>
 * @date 2024/7/9 下午3:09
 * @param {string} clinicId
 */
export default function useClinicStorage(clinicId = '') {
    if (!clinicId) {
        clinicId = Storage.get(CustomKeys.CURRENT_CLINIC, true)?.clinicId;
    }


    function getStorage(customKey, labelKey) {
        const obj = Storage.getObj(customKey, clinicId, true) || {};
        return obj[labelKey];
    }

    function setStorage(customKey, labelKey, value) {
        const obj = Storage.getObj(customKey, clinicId, true) || {};
        obj[labelKey] = value;
        return Storage.setObj(customKey, clinicId, obj);
    }

    return {
        getStorage,
        setStorage,
    };
}
