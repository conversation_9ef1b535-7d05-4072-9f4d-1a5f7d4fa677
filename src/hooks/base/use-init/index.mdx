import { Meta, Story, Canvas } from '@storybook/blocks';
import * as InitStories from './index.stories';

<Meta of={InitStories} />

# useInit

一个用于包装获取初始化数据方法的 Hook，确保数据只会初始化一次，多次调用只会返回第一次初始化的数据。

## 功能特性

- 只初始化一次
- 支持强制刷新
- 支持传递初始化选项
- Promise 化的结果处理

## 使用示例

<Canvas>
  <Story of={InitStories.Usage} />
</Canvas>

## API

### 参数

```typescript
{
  // 初始化函数，返回一个 Promise
  fn: (options?: any) => Promise<any>;
}
```

### 返回值

```typescript
{
  // 初始化方法
  init: (refresh?: boolean, options?: any) => Promise<any>;
  // 初始化 Promise 的引用
  initPromise: Ref<Promise<any> | null>;
}
```

### 参数说明

#### init 方法参数

- `refresh`: boolean - 是否强制刷新，如果为 true，将重新执行初始化函数
- `options`: any - 传递给初始化函数的选项参数

### 使用建议

1. 适用于需要确保只初始化一次的场景
2. 当数据需要更新时，可以通过 refresh 参数强制刷新
3. 可以通过 initPromise 获取当前初始化状态
