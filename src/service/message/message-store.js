import Vue from 'vue';

const state = Vue.observable({
    messages: [],
    unreadCount: 0,
    noticesParams: {
        offset: 0,
        limit: 20,
        total: 0,
        isLast: false,
        readStatus: null, // 0 未读, 1 已读，null 全部
    },
});
export const useMessageStore = () => {

    const setMessages = (data) => {
        state.messages = data;
    };
    const setUnreadCount = (count) => {
        state.unreadCount = count;
    };
    const setNoticesParams = (params) => {
        Object.assign(state.noticesParams, params);
    };
    const setAllMessageRead = () => {
        state.messages.forEach((item) => {
            item.detailReadCount = 1;
        });
    };



    const addNewMessage = (msg) => {
        const index = state.messages?.findIndex((item) => {
            return msg.id === item.id;
        });
        if (index === -1) {
            state.messages.unshift(msg);
        }
    };

    const updateMsgItem = (msg) => {
        state.messages.forEach((item) => {
            if (msg?.id === item?.id) {
                Object.assign(item, msg);
            }
        });
    };

    const deleteMessage = (msg) => {
        const index = state.messages.findIndex((item) => {
            return item.id === msg.id;
        });
        if (index > -1) {
            state.messages.splice(index, 1);
        }

    };

    const setItemMessageRead = (id) => {
        state.messages.forEach((item) => {
            if (item.id === id) {
                item.detailReadCount = 1;
            }
        });
    };



    return {
        state,
        setMessages,
        setUnreadCount,
        setNoticesParams,
        setAllMessageRead,
        setItemMessageRead,
        addNewMessage,
        deleteMessage,
        updateMsgItem,
    };
};
