import { useMessageStore } from '@/service/message/message-store.js';
import AbcSocket from 'views/common/single-socket.js';
import {
    MessageActionEnum, SocketMessageKey,
} from '@/service/message/constant.js';

import MessageAPI from 'api/message';

// 1. start中全局拉取一次message 列表
// 2. socket 中推送的消息插入到message列表中
// 3. 小铃铛有 message 列表

export class AbcSocketMessageManager {
    msgStore = null;
    static instance = null;

    constructor() {
        this.msgStore = useMessageStore();
        const { socket } = AbcSocket.getSocket();
        this.socket = socket;
        this._handleMessage = this.handleMessage.bind(this);
    }

    static getInstance() {
        if (!AbcSocketMessageManager.instance) {
            AbcSocketMessageManager.instance = new AbcSocketMessageManager();
        }
        return AbcSocketMessageManager.instance;
    }

    // 收到消息后检查是否有重复的消息
    handleMessage(message) {
        const data = message.messageBody;
        if (message.action === MessageActionEnum.create) {
            this.addMsg(data);
            return;
        }
        if (message.action === MessageActionEnum.update) {
            this.updateMsg(data);
            return;
        }
        if (message.action === MessageActionEnum.delete) {
            this.deleteMsg(message);

        }
    }

    async initMessage() {
        try {
            this.msgStore.setNoticesParams({
                offset: 0,
            });
            this.msgStore.setMessages([]);
            await this.getMessages();
        } catch (e) {
            console.log('初始化数据失败', e);
        }
    }

    async getMessages() {
        try {
            const { data } = await MessageAPI.fetchMessageList(this.msgStore.state.noticesParams);
            this.msgStore.setUnreadCount(data.newMessageCount);
            this.msgStore.setMessages(this.msgStore.state.messages.concat(data.messageList));
            this.msgStore.setNoticesParams({ total: data.total });
        } catch (e) {
            console.error('getMessages error', e);
        }
    }

    async readAllNotices() {
        try {
            await MessageAPI.updateAllMessageRead();
            this.msgStore.setUnreadCount(0);
            this.msgStore.setAllMessageRead();
        } catch (e) {
            console.error('readAllNotices error', e);
        }
    }

    async readOneNotice(id) {
        try {
            await MessageAPI.updateMessageStatus(id);
            this.msgStore.setUnreadCount(Math.max(this.msgStore.state.unreadCount - 1, 0));
            this.msgStore.setItemMessageRead(id);
        } catch (e) {
            console.error('readOneNotice error', e);
        }
    }

    /**
     * @desc 删除消息
     * <AUTHOR>
     * @date 2022-12-08 18:06:44
     */
    deleteMsg(msg) {
        this.msgStore.deleteMessage(msg);
    }

    /**
     * @desc 更新消息
     * <AUTHOR>
     * @date 2022-12-08 18:06:58
     * @params
     * @return
     */
    updateMsg(msg) {
        this.msgStore.updateMsgItem(msg);
    }

    /**
     * @desc 新增消息
     * <AUTHOR>
     * @date 2022-12-08 18:07:05
     * @params
     * @return
     */

    addMsg(msg) {
        this.msgStore.addNewMessage(msg);
        this.msgStore.setUnreadCount(this.msgStore.state.unreadCount + 1);
    }

    start() {
        this.initMessage({
            offset: 0,
        });
        this.socket.on(SocketMessageKey, this._handleMessage);
    }

    stop() {
        this.socket.off(SocketMessageKey, this._handleMessage);
    }
}
