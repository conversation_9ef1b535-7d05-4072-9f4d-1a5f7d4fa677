/*
 * <AUTHOR>
 * @DateTime 2020-07-27 20:28:43
 */
import BaseApi from '@/regulatory/base/BaseApi';

// 深圳-平安智慧城市
class GuangdongApi extends BaseApi {
    /**
     * 上传附件
     * <AUTHOR>
     * @date 2020-07-27
     * @returns {Promise<Object>}
     */
    async postAttachmentInfo(formData) {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/attachment',
            headers: { 'Content-Type': 'multipart/form-data' },
            method: 'post',
            data: formData,
        });
    }
    /**
     * 获取机构信息
     * <AUTHOR>
     * @date 2020-07-27
     * @returns {Promise<Object>}
     */
    async fetchInstitutionInfo() {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/institution',
            method: 'get',
        });
    }
    /**
     * 更新机构信息
     * <AUTHOR>
     * @date 2020-07-27
     * @param {Object} data 机构信息
     * @returns {Promise<Object>}
     */
    async postInstitutionInfo(data) {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/institution',
            method: 'post',
            data,
        });
    }
    /**
     * 获取职工列表
     * <AUTHOR>
     * @date 2020-07-27
     * @returns {Promise<Object>}
     */
    async getEmployeeInfo() {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/employee',
            method: 'get',
        });
    }
    /**
     * 获取本人职工信息
     * <AUTHOR>
     * @date 2020-07-27
     * @returns {Promise<Object>}
     */
    async getEmployeeInfoSelf() {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/employee/self',
            method: 'get',
        });
    }
    /**
     * 更新职工信息
     * <AUTHOR>
     * @date 2020-07-27
     * @param {Object} data 职工信息
     * @returns {Promise<Object>}
     */
    async postEmployeeInfo(data) {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/employee',
            method: 'post',
            data,
        });
    }
    /**
     * 获取科室列表
     * <AUTHOR>
     * @date 2020-07-27
     * @returns {Promise<Object>}
     */
    async getDepartmentInfo() {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/department',
            method: 'get',
        });
    }
    /**
     * 更新科室信息
     * <AUTHOR>
     * @date 2020-07-27
     * @param {Object} data 科室信息
     * @returns {Promise<Object>}
     */
    async postDepartmentInfo(data) {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/department',
            method: 'post',
            data,
        });
    }
    /**
     * 获取供应商列表
     * <AUTHOR>
     * @date 2020-07-27
     * @returns {Promise<Object>}
     */
    async getSupplierInfo() {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/supplier',
            method: 'get',
        });
    }
    /**
     * 更新供应商信息
     * <AUTHOR>
     * @date 2020-07-27
     * @param {Object} data 机构信息
     * @returns {Promise<Object>}
     */
    async postSupplierInfo(data) {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/supplier',
            method: 'post',
            data,
        });
    }
    /**
     * 人脸识别监管事件创建
     * <AUTHOR>
     * @date 2020-07-27
     * @param {Object} data 入参数据
     * @returns {Promise<Object>}
     */
    async postUserRecognizeBuild(data) {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/event/user/recognize/build',
            method: 'post',
            data,
        });
    }
    /**
     * 人脸识别监管事件结果查询
     * <AUTHOR>
     * @date 2020-07-27
     * @param {Object} data 入参数据
     * @returns {Promise<Object>}
     */
    async postUserRecognizeQuery(data) {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/event/user/recognize/query',
            method: 'post',
            data,
        });
    }
    /**
     * 调用其他接口
     * <AUTHOR>
     * @date 2020-07-27
     * @param {Object} path 路径
     * @returns {Promise<Object>}
     */
    async postOtherInterface(path) {
        return this.fetchPack({
            url: `/api/v2/supervision/shenzhen${path}`,
            method: 'post',
        });
    }
    /**
     * 获取月报、年报跳转连接参数eventid
     * <AUTHOR>
     * @date 2021-07-30
     * @returns {Promise<Object>}
     */
    async getDataReportEventID() {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/common/eventid',
            method: 'get',
        });
    }

    /**
     * 查询上报历史
     * <AUTHOR>
     * @date 2021-11-25
     * @param {Object} path 路径
     * @returns {Promise<Object>}
     */
    async getDataReportHistory() {
        return this.fetchPack({
            url: '/api/v2/supervision/shenzhen/report-history',
            method: 'get',
        });
    }
}

export default GuangdongApi;