/*
 * <AUTHOR>
 * @DateTime 2022-12-29 09:33:09
 */
import Vue from 'vue';
import { isEqual } from 'utils/lodash';
import Response from 'utils/Response';
import { regulatoryMap } from '@/regulatory/constant';

class AbcRegulatory {
    address = {
        provinceId: '', // 省id
        cityId: '', // 市id
        districtId: '', // 区id
    };
    basicInfo = null; // 当前门店的basicInfo
    isOpenRegulatory = false; // 是否开通监管
    isInitFinish = false; // 是否初始化完成，指configs接口是否拉去成功，未成功，不允许进行监管上报
    isElectron = false; // 是否客户端
    matchConfig = null; // 匹配配置
    config = null; // 基本配置
    api = null; // api配置
    constants = null; // 常量配置
    protocol = null; // 上传协议
    options = null; // 选项配置
    tools = null; // 工具配置

    // 是否支持监管
    get isSupportRegulatory() {
        return this.matchConfig ? this.matchConfig.checkIsSupportRegulatory() : false;
    }
    // 是否需要客户端才上报
    get isNeedElectron() {
        return this.config.checkIsNeedElectron();
    }
    // 是否需要设置
    get isNeedSetting() {
        return this.config.isNeedSetting;
    }
    /**
     * Vue原型链挂载函数
     * <AUTHOR>
     * @date 2021-09-30
     * @param {Vue} Vue
     */
    install() {
        window.$abcRegulatory = AbcRegulatory.instance;
        Vue.prototype.$abcRegulatory = AbcRegulatory.instance;
    }
    /**
     * @desc 初始化监管数据
     * <AUTHOR>
     * @date 2023-04-12
     */
    async initRegulatoryData() {
        if (!this.config) return;
        await this.config.init();
    }
    /**
     * 设置是否初始化完成
     * <AUTHOR>
     * @date 2021-11-29
     * @param {Boolean} isInitFinish 是否初始化完成
     */
    setIsInitFinish(isInitFinish) {
        this.isInitFinish = isInitFinish;
    }
    /**
     * 设置是否客户端
     * <AUTHOR>
     * @date 2021-11-29
     * @param {Boolean} isElectron 是否客户端
     */
    setIsElectron(isElectron) {
        this.isElectron = isElectron; // 是否客户端
    }
    /**
     * 设置当前门店的基本信息
     * <AUTHOR>
     * @date 2021-11-26
     * @param {Object} basicInfo 门店basicInfo
     */
    setBasicInfo(basicInfo) {
        this.basicInfo = basicInfo;
    }
    /**
     * 设置是否开通监管
     * <AUTHOR>
     * @date 2021-11-29
     * @param {Boolean} isOpenRegulatory 是否开通监管
     */
    setIsOpenRegulatory(isOpenRegulatory) {
        this.isOpenRegulatory = isOpenRegulatory;
    }
    /**
     * 设置当前门店的地区
     * <AUTHOR>
     * @date 2021-11-26
     * @param {Object} address
     */
    async setAddress(address) {
        this.address = address;
        const {
            districtId,
            cityId,
            provinceId,
        } = address;
        const addressList = [districtId, cityId, provinceId];
        for (const areaCode of addressList) {
            if (regulatoryMap.hasOwnProperty(areaCode)) {
                const {
                    exportModule, isUpdate,
                } = regulatoryMap[areaCode];
                const module = await exportModule();
                try {
                    const config = new module.default(address, isUpdate);
                    // config.isSupportRegulatory = true;
                    this.config = config; // 匹配到的配置
                    this.matchConfig = config;
                    this.api = this.config.api;
                    this.constants = this.config.constants;
                    this.protocol = this.config.protocol;
                    this.options = this.config.options;
                    this.tools = this.config.tools;
                } catch (e) {
                    console.log(e);
                }
                return true;
            }
        }
    }
    /**
     * 挂载监管路由
     * <AUTHOR>
     * @date 2022-12-29
     */
    addRoutes(router) {
        if (!this.config) {
            return;
        }
        const route = this.config.createRoute();
        if (this.isSupportRegulatory === false) {
            const reportRouter = route.children.find((item) => item.path === 'report');
            route.redirect = {
                name: reportRouter.name,
            };
            route.children = [ reportRouter ];
        }
        const layoutRouter = (router || []).find((item) => item.name === 'layout');
        if (!layoutRouter) {
            return;
        }
        const layoutRouterChildren = layoutRouter?.children || [];

        const settingRouter = layoutRouterChildren.find((item) => item.name === 'settings' || item.name === '@PharmacySettings' || item.name === '@common-settings');
        if (!settingRouter || !settingRouter.children) {
            return;
        }
        const isHospital = !!window.$platform.context.store.getters?.isHospital;
        if (isHospital) {
            // 如果是医院，替换监管上报内容
            const regulatoryIndex = settingRouter.children.findIndex((item) => item.name === 'regulatory');
            settingRouter.children[regulatoryIndex] = route;
        } else {
            settingRouter.children.push(route);
        }
    }
    // 初始化监管
    async initRegulatory() {
        if (!window.$platform || !this.basicInfo) return;
        // 定时检查监管自动上报
        this.handleAutoReport();
    }
    /**
     * 处理自动上报监管信息
     * <AUTHOR>
     * @date 2023-06-13
     */
    handleAutoReport() {
        if (this.autoReportTimer) {
            clearTimeout(this.autoReportTimer);
            this.autoReportTimer = null;
        }
        this.autoReportTimer = setTimeout(async () => {
            if (this.config?.isUploadReportVersion &&
                this.isOpenRegulatory &&
                (this.isNeedElectron ? this.isElectron : true)) {
                // 新版监管 && 环境运行上传
                await this.regulatoryMaintain();
            }
            this.handleAutoReport();
        }, 1000 * 60 * 30);
    }
    /**
     * 监管上报
     * @author: lr
     * @date: 2024-07-03
     */
    async regulatoryMaintain() {
        if (new Date().getHours() < 10) {
            return Response.error('没到上报时间，上报结束');
        }
        const reportTimeKey = this.config.constants.REGULATORY_AUTO_REPORT_TIME;
        const reportTime = JSON.parse(localStorage.getItem(reportTimeKey));
        if (reportTime) {
            if (Date.now() - new Date(reportTime).getTime() < 1000 * 60 * 60 * 8) {
                return Response.error('当前时间段已经上报过监管数据，上报结束');
            }
        }

        const fetchResponse = await this.api.fetchNewReportList();
        if (fetchResponse.status === false) {
            return Response.error(fetchResponse.message);
        }
        const reportPresenter = new this.config.ReportPresenter(window);
        for (const row of fetchResponse.data.data?.contents || []) {
            const reportResponse = await reportPresenter.reportDetail(row);
            if (reportResponse.status === false) {
                return Response.error(reportResponse.message);
            }
        }
        localStorage.setItem(reportTimeKey, JSON.stringify(new Date()));
        return Response.success();
    }
    /**
     * 获取监管对象实例
     * <AUTHOR>
     * @date 2021-11-30
     * @returns {AbcRegulatory}
     */
    static getInstance(...args) {
        if (!isEqual(args, AbcRegulatory.args)) {
            AbcRegulatory.args = args;
            AbcRegulatory.instance = Vue.observable(new AbcRegulatory(...args));
        }
        return AbcRegulatory.instance;
    }
}

export default AbcRegulatory.getInstance();
