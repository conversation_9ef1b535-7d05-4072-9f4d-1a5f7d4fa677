// Util
import csTools from '@abc/cornerstone-tools';
import {
    clearGlobalSelectedAnnotation,
    hasDoubleSelectedAnnotation, hasSelectedAnnotation,
    markGlobalSelectedAnnotation,
} from '@/medical-imaging-viewer/abc-viewer/extensions/cornerstone/utils/globalSelectedAnnotation';
import handleMouseMoveHover
    from '@/medical-imaging-viewer/abc-viewer/extensions/cornerstone/utils/handleMouseMoveHover';
import {freeHandCursor} from "@/medical-imaging-viewer/abc-viewer/extensions/cornerstone/abc/cursors.js";
import drawHandles from "@/medical-imaging-viewer/abc-viewer/extensions/cornerstone/utils/drawHandles.js";
import {
    calculateRectangleHandlePosition
} from "@/medical-imaging-viewer/abc-viewer/extensions/cornerstone/utils/calculateRectangleHandlePosition.js";
import { loadCursor } from '@/medical-imaging-viewer/abc-viewer/extensions/cornerstone/utils/cursor';
import {
    CLICK_SHAKE_DISTANCE,
} from '@/medical-imaging-viewer/abc-viewer/extensions/cornerstone/abc/common';

const moveAnnotation = csTools.importInternal('manipulators/moveAnnotation');
const BaseAnnotationTool = csTools.importInternal('base/BaseAnnotationTool');

const {
    getToolState,
    external,
    toolColors,
    getModule,
    store,
} = csTools;
const draw = csTools.importInternal('drawing/draw');
const getNewContext = csTools.importInternal('drawing/getNewContext');
const setShadow = csTools.importInternal('drawing/setShadow');
const drawRect = csTools.importInternal('drawing/drawRect');
const moveHandleNearImagePoint = csTools.importInternal('manipulators/moveHandleNearImagePoint');

/**
 * @public
 * @class RectangleRoiTool
 * @memberof Tools.Annotation
 * @classdesc Tool for drawing rectangular regions of interest, and measuring
 * the statistics of the enclosed pixels.
 * @extends Tools.Base.BaseAnnotationTool
 */
export default class RectangleTool extends BaseAnnotationTool {
    constructor(props = {}) {
        const defaultProps = {
            name: 'Rectangle',
            supportedInteractionTypes: ['Mouse', 'Touch'],
            configuration: {
                drawHandles: true,
                drawHandlesOnHover: false,
                hideHandlesIfMoving: false,
                renderDashed: false,
                // showMinMax: false,
                // showHounsfieldUnits: true
            },
            svgCursor: freeHandCursor,
        };

        super(props, defaultProps);
    }

    createNewMeasurement(eventData) {
        const goodEventData =
            eventData && eventData.currentPoints && eventData.currentPoints.image;

        if (!goodEventData) {
            console.error(
                `required eventData not supplied to tool ${this.name}'s createNewMeasurement`
            );

            return;
        }

        const newMeasurement = {
            visible: true,
            active: true,
            color: undefined,
            invalidated: true,
            handles: {
                start: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: false,
                },
                end: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: true,
                },
                topRight: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: false,
                },
                bottomLeft: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: false,
                },
                bottomCenter: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: false,
                    lockX: true,
                },
                topCenter: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: false,
                    lockX: true,
                },
                rightCenter: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: false,
                    lockY: true,
                },
                leftCenter: {
                    x: eventData.currentPoints.image.x,
                    y: eventData.currentPoints.image.y,
                    highlight: true,
                    active: false,
                    lockY: true,
                },
                initialRotation: eventData.viewport.rotation,
            },
        };
        // 创建新标注时，需要标记为选中，同时把已经存在的 selectedAnnotation 移除
        markGlobalSelectedAnnotation(newMeasurement);
        return newMeasurement;
    }

    pointNearTool(element, data, coords, interactionType) {
        const hasStartAndEndHandles =
            data && data.handles && data.handles.start && data.handles.end;
        const validParameters = hasStartAndEndHandles;

        if (!validParameters) {
            console.warn(
                `invalid parameters supplied to tool ${this.name}'s pointNearTool`
            );
        }

        if (!validParameters || data.visible === false) {
            return false;
        }

        const distance = interactionType === 'mouse' ? 15 : 25;
        const startCanvas = external.cornerstone.pixelToCanvas(
            element,
            data.handles.start
        );
        const endCanvas = external.cornerstone.pixelToCanvas(
            element,
            data.handles.end
        );

        const rect = {
            left: Math.min(startCanvas.x, endCanvas.x),
            top: Math.min(startCanvas.y, endCanvas.y),
            width: Math.abs(startCanvas.x - endCanvas.x),
            height: Math.abs(startCanvas.y - endCanvas.y),
        };

        const distanceToPoint = external.cornerstoneMath.rect.distanceToPoint(
            rect,
            coords
        );

        return distanceToPoint < distance;
    }


    renderToolData(evt) {
        const toolData = getToolState(evt.currentTarget, this.name);

        if (!toolData) {
            return;
        }

        const isToolLocked = store.state.isToolLocked;

        const eventData = evt.detail;
        const {element} = eventData;
        const lineDash = getModule('globalConfiguration').configuration.lineDash;
        const {
            handleRadius,
            drawHandlesOnHover,
            hideHandlesIfMoving,
            renderDashed,
        } = this.configuration;
        const context = getNewContext(eventData.canvasContext.canvas);

        draw(context, context => {
            // If we have tool data for this element - iterate over each set and draw it
            for (let i = 0; i < toolData.data.length; i++) {
                const data = toolData.data[i];

                if (data.visible === false) {
                    continue;
                }

                // Configure
                const color = toolColors.getColorIfActive(data);
                const handleOptions = {
                    color,
                    handleRadius,
                    drawHandlesIfActive: drawHandlesOnHover,
                    hideHandlesIfMoving,
                };

                setShadow(context, this.configuration);

                const rectOptions = {color};

                if (renderDashed) {
                    rectOptions.lineDash = lineDash;
                }

                // Draw
                drawRect(
                    context,
                    element,
                    data.handles.start,
                    data.handles.end,
                    rectOptions,
                    'pixel',
                    data.handles.initialRotation
                );

                if (this.configuration.drawHandles && data.isSelected && !data.moving && !data.creating) {
                    drawHandles(context, eventData, data.handles, handleOptions);
                }
            }
        });
    }


    addNewMeasurement(evt, interactionType) {

    }

    preMouseDownActivateCallback(evt) {
        if(hasDoubleSelectedAnnotation() || hasSelectedAnnotation()) {
            clearGlobalSelectedAnnotation();
            external.cornerstone.updateImage(evt.detail.element);
            loadCursor();
        }
    }

    mouseDragCallback(evt, interactionType = 'mouse') {
        // 拖动距离
        const deltaDistance = external.cornerstoneMath.point.distance(evt.detail.startPoints.canvas, evt.detail.lastPoints.canvas);
        if(deltaDistance <= CLICK_SHAKE_DISTANCE) {
            return;
        }
        if(hasDoubleSelectedAnnotation() || hasSelectedAnnotation()) {
            clearGlobalSelectedAnnotation();
            external.cornerstone.updateImage(evt.detail.element);
            loadCursor();
            return;
        }
        super.addNewMeasurement(evt, this, handleDraggingCallback);
    }

    // mouseDown 操作 handle 时，会回调该方法
    handleSelectedCallback(evt, annotation, handle, interactionType = 'mouse') {
        markGlobalSelectedAnnotation(annotation);

        const draggingCallback = (toolName, annotation, handle) => {
            // 获取当前移动的 handle 的位置
            let position = '';
            Object.keys(annotation.handles).forEach(key => {
                if (annotation.handles[key] === handle) {
                    position = key;
                }
            });
            calculateRectangleHandlePosition(position, handle, annotation.handles);
        }

        moveHandleNearImagePoint(evt, this, annotation, handle, interactionType, draggingCallback);
    }

    // mouseDown 操作整个标注数据时，会回调该方法
    toolSelectedCallback(evt, annotation, interactionType = 'mouse') {
        markGlobalSelectedAnnotation(annotation);
        moveAnnotation(evt, this, annotation, interactionType);
    }

    // mouseMove 会回调该方法，主要实现 hover 的效果
    mouseMoveCallback(evt) {
        return handleMouseMoveHover(evt, this);
    }
}

function handleDraggingCallback(toolName, annotation, handle) {
    // 获取当前移动的 handle 的位置
    let position = '';
    Object.keys(annotation.handles).forEach(key => {
        if (annotation.handles[key] === handle) {
            position = key;
        }
    });
    calculateRectangleHandlePosition(position, handle, annotation.handles);
}

