@import "./theme.scss";
@import "./mixin";

.app-header {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    line-height: 44px;

    @include clearfix;

    .header-wrapper {
        position: relative;
        display: flex;
        width: auto;
        max-width: 1420px;
        height: 44px;
        margin: 4px auto 0;
        color: var(--headerFontColor);

        .clinic-name {
            height: 44px;
        }

        .header__left-wrapper {
            display: flex;
            justify-content: flex-start;

            .logo-cloud-img-wrapper {
                display: flex;
                align-items: center;
                margin-right: 10px;

                img {
                    width: 83px;
                    height: 12px;
                }
            }

            .logo-wrapper {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                img {
                    width: 83px;
                    height: 12px;
                }
            }
        }

        .navbar-bg {
            position: absolute;
            top: 0;
            left: 0;
            height: 48px;
            background: transparent;
        }

        .header-fixed-right {
            display: flex;
            flex-shrink: 0;
            align-items: center;
            justify-content: flex-end;
            max-width: 350px;
            margin-left: auto;

            &.menu-nav-is-center {
                width: var(--leftContainerWidth);
                max-width: var(--leftContainerWidth);
            }

            .app-header--avator-btn {
                &:hover {
                    outline: 4px solid var(--headerItemHoverBC);
                }

                &:active {
                    outline: 4px solid var(--headerItemActiveBC);
                }
            }

            .app-header--round-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 26px;
                min-width: 26px;
                height: 26px;
                margin-right: 16px;
                cursor: pointer;
                border: 1px solid var(--headerRoundBorderColor);
                border-radius: 13px;

                &:hover {
                    background-color: var(--headerItemHoverBC);
                }

                &:active {
                    background-color: var(--headerItemActiveBC);
                }

                &.app-notice-wrapper {
                    margin-right: 20px;
                }
            }

            .user-dropdown {
                width: 32px;
                color: #4a4a4a;
                cursor: pointer;

                .user-dropdown__popover {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .reference {
                    display: flex;
                    align-items: center;
                    width: 24px;
                    line-height: 54px;
                }

                .avatar-img {
                    width: 32px;
                    height: 32px;
                    overflow: hidden;
                    border-radius: var(--abc-border-radius-mini);
                }

                .triangle {
                    position: absolute;
                    top: 23px;
                    right: 0;
                    width: 0;
                    height: 0;
                    border-top: 4px solid rgba(255, 255, 255, 0.8);
                    border-right: 4px solid transparent;
                    border-left: 4px solid transparent;
                }
            }
        }
    }
}

.menu-dropdown.options-wrapper {
    border: none !important;
}

.user-header-dropdown {
    width: auto;
    max-width: 260px;

    .abc-dropdown-item {
        padding: 8px 12px;
    }

    &::before {
        position: absolute;
        top: -6px;
        right: 10px;
        display: block;
        width: 0;
        height: 0;
        margin-left: -5px;
        content: " ";
        border-right: 5px solid transparent;
        border-bottom: 6px solid #dddddd;
        border-left: 5px solid transparent;
    }

    &::after {
        position: absolute;
        top: -4px;
        right: 10px;
        display: block;
        width: 0;
        height: 0;
        margin-left: -4px;
        content: " ";
        border-right: 4px solid transparent;
        border-bottom: 4px solid #ffffff;
        border-left: 4px solid transparent;
    }
}

.user-setting-popper {
    padding: var(--abc-paddingTB-s) var(--abc-paddingLR-s) !important;

    .split-cut-line {
        padding: 4px 10px;
    }

    .user-label-layout {
        display: flex;
        justify-content: space-between;
        padding: 5px var(--abc-paddingLR-ml);
        line-height: 22px;

        .title {
            font-size: 14px;
            font-weight: 400;
        }

        .content {
            font-size: 14px;
            font-weight: 400;
            color: var(--abc-color-T2);
        }
    }

    .hover-img {
        cursor: pointer;
        background: #000000;
        opacity: 0.3;
    }

    .hover-color {
        :hover {
            cursor: pointer;
            background: var(--abc-color-cp-grey4);
            border-radius: var(--abc-border-radius-mini);
        }
    }

    .img-qrcode {
        position: absolute;
        top: 74px;
        left: -130px;
        z-index: 1000;
        width: 136px;
        height: 158px;
        padding: 0 0 8px;
        overflow: hidden;
        text-align: center;
        background-color: #ffffff;
        border-radius: 5px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        img {
            width: 136px;
            height: 136px;
        }

        span {
            position: relative;
            display: block;
            margin-top: -12px;
            font-size: 12px;
            color: $T2;
            text-align: center;
        }
    }
}

.user-sign-setting-popper {
    top: 0 !important;
    left: -13px !important;
    padding: var(--abc-paddingTB-s) var(--abc-paddingLR-s) !important;

    .hover-color {
        :hover {
            cursor: pointer;
            background: var(--abc-color-cp-grey4);
            border-radius: var(--abc-border-radius-mini);
        }
    }

    .choose-color {
        background-color: var(--abc-color-B4);
        border-radius: var(--abc-border-radius-small);

        :hover {
            background-color: var(--abc-color-B4);
        }
    }

    .img-signature {
        display: flex;
        width: 76px;
        height: 22px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .hover-color + .hover-color {
        margin-top: 2px;
    }

    .user-label-layout {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px var(--abc-paddingLR-ml, 10px);

        .title {
            font-size: 14px;
            font-weight: 400;
        }

        .content {
            position: relative;
            margin-right: 28px;
            font-size: 14px;
            font-weight: 400;
            color: var(--abc-color-T2);

            .sign-icon-position {
                position: absolute;
                top: -8px;
                left: 8px;
            }
        }

        .electronic-signature {
            position: relative;
            margin-right: 10px;
            font-size: 14px;
            font-weight: 400;
            color: #7a8794;
        }

        .no-sign-name {
            font-size: 14px;
            color: var(--abc-color-T2);
        }

        .sign-setting {
            font-size: 14px;
            font-weight: 400;
            color: #005ed9;
        }

        .set-signature {
            position: relative;
            display: inline-block;

            .cis-icon-Code,
            span {
                margin-right: 5px;
                color: #247cf0;
            }
        }
    }
}

.user-sign-setting-popper-v2 {
    top: -7px !important;
    left: 10px !important;
}

.user-header-dropdown.options-wrapper {
    padding: 0;
    background: rgba(255, 255, 255, 1);
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

    &::before {
        position: absolute;
        top: -7px;
        left: 50%;
        display: block;
        margin-left: -6px;
        content: " ";

        @include triangle(12px, 7px, #fff, "up");
    }

    &::after {
        display: none;
    }

    .abc-dropdown-item {
        display: flex;
        align-items: center;
        height: 36px;
        padding: 0 16px;
        line-height: 36px;
    }

    i.iconfont {
        margin-right: 8px;
        font-size: 14px;
        color: #2e3439;
    }
}

.user-setting-popper_avatar_hover-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    border-radius: var(--abc-border-radius-small);
}

@media screen and (min-width: 1440px) {
    .app-header {
        .header-wrapper {
            width: 1440px;
        }
    }
}

@media screen and (min-width: 1281px) and (max-width: 1439px) {
    .app-header {
        .header-wrapper {
            width: auto;
            margin: 4px 14px 0;

            --headerClinicDropdownWidth: 239px;
        }
    }
}

@media screen and (min-width: 1024px) and (max-width: 1280px) {
    .app-header {
        .header-wrapper {
            width: auto;
            margin: 4px 14px 0;

            --headerClinicDropdownWidth: 166px;
        }
    }

    .app-clinic-dropdown__popover {
        --headerClinicDropdownLeftMargin: -30px;
    }
}

@media screen and (max-width: 1024px) {
    .app-header {
        .header-wrapper {
            width: auto;
            min-width: 928px;
            margin: 4px 14px 0;

            --headerClinicDropdownWidth: 166px;
        }
    }

    .app-clinic-dropdown__popover {
        --headerClinicDropdownLeftMargin: -30px;
    }
}
