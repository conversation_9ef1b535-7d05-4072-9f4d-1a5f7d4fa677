import { InspectClinicRoute<PERSON><PERSON> } from '@/router/route-key/inspect';
import InspectBlank from '@/views-hospital/inspect-diagnosis/frames/blank.vue';
import { MODULE_ID_MAP } from 'utils/constants';
import AbcAccess from '@/access/utils';
import { AppTabId } from '@/core';

const PageAsync = () => import('@/views-hospital/inspect-diagnosis/core/page.js');

const Index = () => import('@/views-hospital/inspect-diagnosis/index.vue');

const OphthalmologyInspectReport = () => import('@/views-ophthalmology/inspect/form.vue');

const RisInspectReport = () => import('@/views-hospital/inspect-diagnosis/frames/index.vue');

const InspectRoute = {
    path: 'inspect',
    component: Index,
    name: InspectClinicRouteKey.index,
    meta: {
        name: InspectClinicRouteKey.index,
        moduleId: MODULE_ID_MAP.inspect,
        needAuth: true,
        pageAsyncClass: PageAsync,
        openBrowserInfo: () => {
            const isOpen = AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION);

            return isOpen ? {
                path: '/physical-examination/inspect',
                appTabId: AppTabId.INSPECT,
            } : null;
        },
        icon: 'nav_scan_outline',
        selectedIcon: 'nav_scan_fill',
    },
    children: [
        {
            path: '',
            component: InspectBlank,
            name: InspectClinicRouteKey.blank,
            meta: {
                moduleId: MODULE_ID_MAP.inspect,
                needAuth: true,
            },
        },
        {
            path: 'ris/:id',
            component: RisInspectReport,
            name: InspectClinicRouteKey.ris,
            meta: {
                moduleId: MODULE_ID_MAP.inspect,
                needAuth: true,
            },
        },
        {
            path: 'ophthalmology/:id',
            component: OphthalmologyInspectReport,
            name: InspectClinicRouteKey.ophthalmology,
            meta: {
                moduleId: MODULE_ID_MAP.inspect,
                needAuth: true,
            },
        },
    ],
};
export default InspectRoute;
