<template>
    <div
        class="pay-public-transfer-box"
        :class="{
            'pay-public-transfer-box-order-view': isOrderView, 'pay-public-transfer-box-step': showStep || waitTotal
        }"
    >
        <template v-if="showStep || waitTotal">
            <abc-flex class="pay-public-transfer-box-title" align="center">
                <abc-space align="center">
                    <abc-text>{{ name }}</abc-text><abc-text v-if="waitTotal" size="mini" theme="gray">
                        {{ waitTotal }}个厂商不支持对公转账，请对ABC直采商城转账
                    </abc-text>
                </abc-space>
            </abc-flex>
        </template>
        <div class="pay-public-transfer-box-body">
            <template v-if="loading">
                <div class="pay-public-transfer-box-loading">
                    <div style="width: 154px; margin-top: 150px;">
                        <abc-flex align="center" justify="space-around" style="width: 100%;">
                            <abc-loading-spinner middle></abc-loading-spinner>
                        </abc-flex>
                        <abc-flex align="center" style="width: 100%;">
                            <abc-text theme="gray-light">
                                正在创建门店对公子账户
                            </abc-text>
                        </abc-flex>
                    </div>
                </div>
            </template>
            <template v-if="isErr">
                <div class="pay-public-transfer-box-loading">
                    <div style="width: 244px; margin-top: 130px;">
                        <abc-flex align="center" justify="space-around" style="width: 100%;">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="33"
                                height="33"
                                viewBox="0 0 33 33"
                                fill="none"
                            >
                                <path d="M4.66794 31.0076C3.8028 31.0076 3.0598 30.8244 2.43893 30.458C1.81807 30.0916 1.33969 29.5929 1.00382 28.9618C0.667939 28.341 0.5 27.6438 0.5 26.8702C0.5 26.1272 0.698473 25.4148 1.09542 24.7328L12.9427 4.0916C13.3295 3.39949 13.8435 2.88041 14.4847 2.53435C15.126 2.17812 15.7977 2 16.5 2C17.2023 2 17.869 2.17812 18.5 2.53435C19.1412 2.88041 19.6603 3.39949 20.0573 4.0916L31.9046 24.7328C32.098 25.0687 32.2455 25.4198 32.3473 25.7863C32.4491 26.1527 32.5 26.514 32.5 26.8702C32.5 27.6438 32.3321 28.341 31.9962 28.9618C31.6603 29.5929 31.1819 30.0916 30.5611 30.458C29.9402 30.8244 29.1972 31.0076 28.3321 31.0076H4.66794ZM16.5153 20.7939C17.3092 20.7939 17.7112 20.3766 17.7214 19.542L17.9504 11.2366C17.9606 10.8397 17.8232 10.5089 17.5382 10.2443C17.2634 9.96947 16.9173 9.83206 16.5 9.83206C16.0725 9.83206 15.7214 9.96438 15.4466 10.229C15.1819 10.4936 15.0547 10.8244 15.0649 11.2214L15.2634 19.542C15.2837 20.3766 15.701 20.7939 16.5153 20.7939ZM16.5153 25.9084C16.9733 25.9084 17.3702 25.7506 17.7061 25.4351C18.0522 25.1196 18.2252 24.7328 18.2252 24.2748C18.2252 23.8168 18.0522 23.43 17.7061 23.1145C17.3702 22.7888 16.9733 22.626 16.5153 22.626C16.0471 22.626 15.645 22.7888 15.3092 23.1145C14.9733 23.4402 14.8053 23.827 14.8053 24.2748C14.8053 24.7328 14.9733 25.1196 15.3092 25.4351C15.6552 25.7506 16.0573 25.9084 16.5153 25.9084Z" fill="#FF9933" />
                            </svg>
                        </abc-flex>
                        <abc-flex align="center" justify="space-around" style="width: 100%; margin-top: 16px;">
                            <abc-text size="large" :bold="true">
                                创建门店对公子账户失败
                            </abc-text>
                        </abc-flex>
                        <abc-flex align="center" justify="space-around" style="width: 100%; margin-top: 8px;">
                            <abc-text theme="gray-light">
                                请联系ABC1对1客服或销售协助处理
                            </abc-text>
                        </abc-flex>
                    </div>
                </div>
            </template>
            <template v-else>
                <abc-flex
                    v-if="!hiddenStep"
                    align="center"
                    class="pay-public-transfer-box-header"
                    :class="{
                        'pay-public-transfer-box-header-step': showStep || waitTotal,
                    }"
                >
                    <abc-space align="center">
                        <abc-text :bold="true">
                            <template v-if="showStep">
                                步骤一：
                            </template>参考下方转账信息，完成对公转账
                        </abc-text>
                    </abc-space>
                </abc-flex>
                <abc-flex
                    v-for="(publicTransferItem, index) in publicTransfers"
                    :key="index"
                    align="center"
                    justify="space-between"
                    class="pay-public-transfer-box-line"
                >
                    <abc-space>
                        <abc-text theme="gray" class="pay-public-transfer-box-line-label">
                            {{ publicTransferItem.label }}
                        </abc-text>
                        <abc-text>{{ publicTransferItem.value }}</abc-text>
                    </abc-space>
                    <abc-link @click="copy(publicTransferItem.value)">
                        复制
                    </abc-link>
                </abc-flex>
                <abc-flex
                    v-if="!hiddenStep"
                    align="center"
                    justify="space-between"
                    class="pay-public-transfer-box-line"
                >
                    <abc-space>
                        <abc-text theme="gray" class="pay-public-transfer-box-line-label">
                            转账金额：
                        </abc-text>
                        <abc-text :theme="yellowTheme ? 'warning-light' : 'danger-light'">
                            ¥{{ totalPrice }}
                        </abc-text>
                    </abc-space>
                </abc-flex>
                <template v-if="showStep">
                    <abc-flex
                        v-if="!hiddenStep"
                        align="center"
                        style="margin-bottom: 16px;"
                        class="pay-public-transfer-box-header"
                    >
                        <abc-space align="center">
                            <abc-text :bold="true">
                                步骤二：上传转账成功截图，并点击下方【我已完成转账】按钮
                            </abc-text>
                            <abc-link
                                @click="
                                    handleExamplePreview(
                                        publicTransModel
                                    )
                                "
                            >
                                查看截图示例
                            </abc-link>
                        </abc-space>
                    </abc-flex>
                    <abc-flex
                        v-if="!hiddenStep"
                        style="width: 100%; height: 80px; margin-bottom: 16px;"
                    >
                        <external-file
                            v-model="attachments"
                            :hidden-left="true"
                            :business-type="BusinessTypeEnum.CORPORATE_TRANSFER"
                            oss-filepath="public-transfer-box"
                            :max-upload-count="1"
                            business-desc="上传截图"
                            :accept="['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG']"
                            width="80px"
                            height="80px"
                            upload-description="转账成功截图支持jpg，jpeg，png图片格式"
                        ></external-file>
                    </abc-flex>
                </template>
                <abc-flex
                    v-if="!showStep && !waitTotal"
                    align="center"
                    justify="space-around"
                    class="pay-public-transfer-box-footer"
                >
                    <abc-text v-if="publicTips" theme="gray" size="mini">
                        {{ publicTips }}
                    </abc-text>
                    <abc-space v-else :size="4">
                        <abc-text theme="gray" size="mini">
                            在汇款户名、账号信息正确的情况下，一般
                        </abc-text><abc-text theme="warning-light" size="mini">
                            30分钟
                        </abc-text><abc-text theme="gray" size="mini">
                            后到账，{{ tipsText }}
                        </abc-text>
                    </abc-space>
                </abc-flex>
                <abc-preview
                    v-if="isShowPreview"
                    v-model="isShowPreview"
                    :lists="previewImagesList"
                    :index="0"
                    :enable-compress="false"
                ></abc-preview>
            </template>
        </div>
    </div>
</template>

<script>
    import ExternalFile from 'views/layout/external-file/index.vue';
    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';
    import AbcSocket from 'views/common/single-socket';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    export default {
        name: 'PayPublicTransferBox',
        components: {
            ExternalFile,
            AbcLoadingSpinner,
        },
        props: {
            totalPrice: {
                type: [Number, String],
                default: 0,
            },
            hiddenStep: {
                type: Boolean,
                default: false,
            },
            isOrderView: {
                type: Boolean,
                default: false,
            },
            loading: {
                type: Boolean,
                default: false,
            },
            currentAttachments: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            name: {
                type: String,
                default: '',
            },
            publicInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            isFetchErr: {
                type: Boolean,
                default: false,
            },
            tipsText: {
                type: String,
                default: '系统自动将订单改为“待发货”状态',
            },
            showStep: {
                type: Boolean,
                default: false,
            },
            waitTotal: {
                type: Number,
                default: 0,
            },
            publicTips: {
                type: String,
                default: '',
            },
            yellowTheme: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                publicTransfers: [],
                BusinessTypeEnum,
                isShowPreview: false,
                publicTransModel: require('src/assets/images/public-transfer/public-tranfer-model.png'),
                previewImagesList: [],
            };
        },
        computed: {
            isErr() {
                return this.isFetchErr;
            },
            // eslint-disable-next-line vue/no-dupe-keys
            attachments: {
                get() {
                    return this.currentAttachments;
                },
                set(val) {
                    this.$emit('update:currentAttachments', val);
                },
            },
        },
        async created() {
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleImages);
            this.handlePublicInfo(this.publicInfo);
        },
        beforeDestroy() {
            this._socket?.off('short-url.upload_attachment', this.handleImages);
        },
        methods: {
            handleExamplePreview(url) {
                this.isShowPreview = true;
                this.previewImagesList = [{ url }];
            },
            handlePublicInfo(publicInfo) {
                this.publicTransfers = [
                    {
                        label: '收款单位：',
                        value: publicInfo?.payee || publicInfo?.receiveCompany || '--',
                    },
                    {
                        label: '银行账户：',
                        value: publicInfo?.bankAccount || '--',
                    },
                    {
                        label: '开户账户：',
                        value: publicInfo?.bankName || publicInfo?.bankDeposit || '--',
                    },
                    {
                        label: '开户行号：',
                        value: publicInfo?.bankCode || publicInfo?.bankDepositNum || '--',
                    },
                ];
            },
            handleImages(data) {
                const {
                    attachments = [],
                    businessType,
                } = data;
                if (businessType === BusinessTypeEnum.CORPORATE_TRANSFER && attachments && attachments.length) {
                    this.attachments = this.attachments.concat(attachments.map((item) => {
                        return {
                            fileName: item.fileName,
                            fileSize: item.fileSize,
                            sort: item.sort,
                            url: item.url,
                        };
                    }));
                    this.attachments = this.attachments.map((item, index) => {
                        return {
                            ...item,
                            sort: index,
                        };
                    });
                }
            },
            copy(value) {
                const input = document.createElement('input');
                input.value = value;
                document.body.appendChild(input);
                input.select();
                document.execCommand('copy');
                input.remove();
                this.$Toast({
                    message: '复制成功',
                    type: 'info',
                });
            },
        },
    };
</script>

<style scoped lang="scss">
.pay-public-transfer-box {
    width: 100%;

    &-step {
        margin-top: 16px;
        background-color: #ffffff;
        border: 1px solid #e0e5ee;
        border-radius: var(--abc-border-radius-small);

        &:first-child {
            margin-top: 0 !important;
        }

        .pay-public-transfer-box-body {
            height: auto;
            padding: 16px 16px;
        }
    }

    &-title {
        height: 40px;
        padding: 0 16px;
        font-size: 14px;
        background-color: #f9fafc;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    &-order-view {
        .pay-public-transfer-box-footer {
            width: 100%;
            height: 40px;
            margin-top: 16px !important;
            margin-bottom: 8px !important;
            background-color: #f9fafc;
        }
    }

    &-footer {
        width: 100%;
        height: 40px;
        margin-top: 24px;
        margin-bottom: 24px;
        background-color: #f9fafc;
    }

    &-loading {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 100%;
    }

    &-header {
        width: 100%;
        padding: 24px 0;
        margin-bottom: 24px;
        line-height: 22px;
        border-bottom: 1px dashed var(--abc-color-P6) !important;

        &-step {
            padding: 8px 0 24px 0 !important;
        }
    }

    &-line {
        width: 100%;
        height: 22px;
        margin-top: 10px;

        &-label {
            width: 70px;
            height: 22px;
            line-height: 22px;
        }
    }
}
</style>
