<template>
    <div
        class="marketing-message-push-notice-item"
        @click="handleLookInformDetailClick"
    >
        <div class="marketing-message-push-notice-item__name">
            {{ noticeItem.name }}
        </div>
        <div
            class="marketing-message-push-notice-item__time"
            v-html="noticeItem.notifyDesc"
        >
        </div>
        <div class="marketing-message-push-notice-templates" :style="{ 'justify-content': calAlignStyle(switches) }">
            <template v-for="(item, index) in switches">
                <div
                    v-if="item.visible"
                    :key="index"
                    class="marketing-message-push-notice-templates__content"
                >
                    <abc-icon
                        v-if="noMsgPushDesc && item.key !== 'smsSwitch'"
                        icon="Attention"
                        size="16"
                        :color="$store.state.theme.style.Y2"
                    ></abc-icon>
                    <template v-else>
                        <img
                            v-if="item.enabled"
                            class="marketing-message-push-notice-item__icon"
                            width="16"
                            src="~/assets/images/marketing/icon-circle-checkbox-active.svg"
                            alt=""
                        />
                        <img
                            v-if="!item.enabled"
                            class="marketing-message-push-notice-item__icon"
                            width="16"
                            src="~/assets/images/marketing/icon-circle-checkbox.svg"
                            alt=""
                        />
                    </template>
                    {{ item.label }}
                </div>
            </template>
        </div>
        <abc-text v-if="noticeItem.name === '微诊所患者获取验证码' && smsQuotaFree > 0" theme="warning-light">
            免费次数(中国大陆)剩余{{ smsQuotaFree }}条
        </abc-text>
    </div>
</template>

<script>
    export default {
        name: 'NoticeItem',
        props: {
            noticeItem: {
                type: Object,
                default: () => ({}),
            },
            noMsgPushDesc: {
                type: Boolean,
                default: false,
            },
            smsQuotaFree: {
                type: Number,
                default: 0,
            },
        },
        computed: {
            isCardOrCoupon() {
                return [
                    'marketing.promotion-patient-coupon',
                    'marketing.promotion-patient-card',
                    'marketing.patient-points-expire',
                    'marketing.patient-points',
                ].includes(this.noticeItem.key);
            },
            switches() {
                const {
                    smsSwitch,wxSwitch,smsTemplates, wxTemplates,
                } = this.noticeItem;
                // 卡项 & 优惠券暂时做特殊处理

                const switches = [
                    {
                        label: '短信通知', enabled: smsSwitch, visible: !!smsTemplates, key: 'smsSwitch',
                    },
                    {
                        label: '微信通知', enabled: wxSwitch, visible: this.isCardOrCoupon ? false : !!wxTemplates, key: 'wxSwitch',
                    },
                ];

                return switches;
            },
        },
        methods: {
            handleLookInformDetailClick() {
                this.$emit('look-inform-detail', this.noticeItem);
            },
            calAlignStyle(switches) {
                if (switches.filter((item) => item.visible).length === 1) {
                    if (switches.find((item) => item.visible).label === '短信通知') {
                        return 'flex-start';
                    }
                    return 'flex-end';
                }
                return 'space-between';
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme";

.marketing-message-push-notice-item {
    display: flex;
    padding: 14px 16px;
    cursor: pointer;
    border-bottom: 1px solid $P6;

    &:last-child {
        border-bottom: none;
    }

    .marketing-message-push-notice-item__name {
        width: 20%;
    }

    .marketing-message-push-notice-item__time {
        width: 57%;
        color: $T2;
    }

    .marketing-message-push-notice-tips {
        color: $Y2;
    }

    .marketing-message-push-notice-templates {
        display: flex;
        flex: 1;
        align-items: center;
        max-width: 300px;

        .marketing-message-push-notice-templates__content {
            margin-right: 55px;

            &:last-child {
                margin-right: 0;
            }

            .marketing-message-push-notice-item__icon {
                margin-right: 3px;
                margin-bottom: -2px;
            }
        }
    }
}
</style>
