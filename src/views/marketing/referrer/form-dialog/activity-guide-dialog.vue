<template>
    <abc-dialog
        v-if="activityGuideDialogVisible"
        v-model="activityGuideDialogVisible"
        title="老带新活动介绍"
        class="referrer__activity-guide-dialog"
        data-cy="referrer-activity-guide-dialog"
        content-styles="width:900px; padding:24px;height:520px "
    >
        <div class="referrer__activity-guide-dialog--title">
            开启老带新活动，快速低成本获客
        </div>

        <div class="referrer__activity-guide-dialog--content">
            <div v-for="(item, index) in featureList" :key="item.title" class="feature-item">
                <div class="feature-item__left">
                    <div class="img-wrapper">
                        <img :src="item.img" alt="" class="sample-img" />
                        <img
                            v-if="index !== featureList.length - 1"
                            class="arrow"
                            src="~assets/images/marketing/blue-arrow.png"
                            alt=""
                            width="50"
                            height="20"
                        />
                    </div>
                    <div class="title">
                        {{ item.title }}
                    </div>
                    <div class="desc">
                        {{ item.desc }}
                    </div>
                    <div class="btn" @click="onLookSampleBtnClick(item)">
                        查看示例
                    </div>
                </div>
            </div>
        </div>

        <div class="referrer__activity-guide-dialog--footer">
            <div class="create">
                <abc-button data-cy="activity-guide-create" @click="handleCreate">
                    立即创建
                </abc-button>
            </div>

            <div class="not-remind" data-cy="activity-guide-not-remind" @click="updateRemindStatus">
                下次不再提示
            </div>
        </div>
        <sample-dialog
            v-if="sampleDialogVisible"
            v-model="sampleDialogVisible"
            :sample-info="currentSampleInfo"
        ></sample-dialog>
    </abc-dialog>
</template>
<script>
    import MarketingAPI from 'api/marketing';
    import { mapGetters } from 'vuex';
    import GuideStepImg1 from 'src/assets/images/marketing/referral/guide-step-1.png';
    import GuideStepImg2 from 'src/assets/images/marketing/referral/guide-step-2.png';
    import GuideStepImg3 from 'src/assets/images/marketing/referral/guide-step-3.png';
    import lookActivityResultImg from '@/assets/images/marketing/referral/guide-activity-sample/look-activity-result.png';
    import newCustomerRewardImg from '@/assets/images/marketing/referral/guide-activity-sample/new-customer-look-reward.png';
    import bindRelationImg2 from '@/assets/images/marketing/referral/guide-activity-sample/referrer-bind-relationship-2.png';
    import newCustomerPayImg from '@/assets/images/marketing/referral/guide-activity-sample/new-customer-pay.png';
    import SampleDialog from 'views/marketing/referrer/form-dialog/sample-dialog.vue';
    export default {
        components: {
            SampleDialog,
        },
        props: {
            sampleInfo: {
                type: Object,
                default: () => ({}),
            },
            value: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                sampleDialogVisible: false,
                currentSampleInfo: {},
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            activityGuideDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            featureList() {

                return [
                    {
                        title: '推荐人分享医生/项目给新客',
                        desc: '新客建档后，推荐关系自动绑定，并发放新客奖励',
                        img: GuideStepImg1,
                        sampleInfo: {
                            title: '推荐人分享医生/项目给新客',
                            id: '1',
                            list: [
                                {
                                    title: '推荐关系如何绑定',
                                    id: '1-1',
                                    imgs: [
                                        {
                                            title: '方式一：新客通过推荐人分享链接建档，自动绑定',
                                            img: this.viewDistributeConfig.Marketing.referrer.bindRelationImg1,
                                            id: '1-1-1',
                                        },
                                        {
                                            title: '方式二：线下门店建档时，手动绑定',
                                            img: bindRelationImg2,
                                            id: '1-1-2',
                                        },
                                    ],
                                },
                                {
                                    title: '新客如何查看奖励',
                                    id: '1-2',
                                    imgs: [
                                        {
                                            title: `新客登录微${this.$app.institutionTypeWording}，进入【我的-优惠券】查看已发奖励`,
                                            img: newCustomerRewardImg,
                                            id: '1-2-1',
                                        },
                                    ],
                                },
                            ],
                        },
                    },
                    {
                        title: '新客到店消费',
                        desc: '自动发放推荐人奖励',
                        img: GuideStepImg2,
                        sampleInfo: {
                            title: '新客到店',
                            id: '2',
                            list: [
                                {
                                    title: '',
                                    id: '2-1',
                                    imgs: [
                                        {
                                            title: `推荐人登录微${this.$app.institutionTypeWording}，进入【推荐有礼】查看奖励`,
                                            img: newCustomerPayImg,
                                        },
                                    ],
                                },
                            ],
                        },
                    },
                    {
                        title: '活动效果',
                        desc: '开启活动后，活动数据及奖励发放明细一目了然',
                        img: GuideStepImg3,
                        sampleInfo: {
                            title: '活动效果',
                            id: '3',
                            list: [
                                {
                                    title: '',
                                    id: '3-1',
                                    imgs: [
                                        {
                                            title: '老带新活动列表，点击数据查看活动效果',
                                            img: lookActivityResultImg,
                                        },
                                    ],
                                },
                            ],
                        },
                    },
                ];
            },
        },
        methods: {
            onLookSampleBtnClick(item) {
                this.currentSampleInfo = item.sampleInfo;
                this.sampleDialogVisible = true;
            },
            async updateRemindStatus() {
                try {
                    const { data } = await MarketingAPI.referrer.updateRemindFlog('promotion_referral');
                    if (data) {
                        this.activityGuideDialogVisible = false;
                        this.$emit('refresh');
                    }

                } catch (err) {
                    console.log(err);
                }

            },
            handleCreate() {
                this.$emit('create-activity');
                this.activityGuideDialogVisible = false;
            },
        },
    };
</script>
<style lang="scss">
@import '~styles/theme.scss';

.referrer__activity-guide-dialog {
    &--title {
        margin-top: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #333333;
        text-align: center;
    }

    &--content {
        display: flex;
        justify-content: space-between;
        margin-top: 40px;

        .feature-item {
            width: 100%;
            text-align: center;

            .img-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                margin-left: 44px;

                .sample-img {
                    display: inline-block;
                    width: 198px;
                    height: 166px;
                }

                .arrow {
                    position: absolute;
                    right: -20px;
                    display: inline-block;
                    width: 50px;
                    height: 20px;
                }
            }

            .title {
                margin-top: 16px;
                font-weight: bold;
                color: #333333;
            }

            .desc {
                margin-top: 8px;
                margin-bottom: 16px;
                font-size: 12px;
                color: $T2;
            }

            .btn {
                display: inline-block;
                font-size: 12px;
                color: $theme1;
                cursor: pointer;
            }
        }
    }

    &--footer {
        margin-top: 32px;
        text-align: center;

        .not-remind {
            display: inline-block;
            margin-top: 12px;
            font-size: 12px;
            color: $T2;
            cursor: pointer;
        }
    }
}
</style>
