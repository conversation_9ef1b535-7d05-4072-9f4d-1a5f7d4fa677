<!--
 * @Author: your name
 * @Date: 2020-03-24 14:36:38
 * @LastEditTime: 2020-04-13 15:45:40
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /AbcPc/src/views/marketing/components/setting/index.vue
 -->
<template>
    <abc-dialog
        v-if="visible"
        :value="visible"
        :title="title"
        size="large"
        @input="val => $emit('visible' , val)"
    >
        <biz-setting-form :no-limit-width="true" :label-width="112">
            <biz-setting-form-group>
                <biz-setting-form-item :label="name">
                    <biz-setting-form-item-tip :tip="disc">
                        <abc-checkbox :value="flag" type="number" @change="handleChange">
                            开启
                        </abc-checkbox>
                    </biz-setting-form-item-tip>
                </biz-setting-form-item>
            </biz-setting-form-group>
        </biz-setting-form>
        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleConfirm">
                确定
            </abc-button>
            <abc-button variant="ghost" @click="setMarketingCouponSetting(false); $emit('visible' , false)">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import useMarketingCouponSetting from 'views/marketing/hooks/couponHook';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form/index.js';

    export default {
        components: {
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
        },
        props: {
            visible: {
                type: Boolean,
                default: true,
            },
            name: String,
            disc: {
                type: String,
                default: '',
            },
            flag: {
                type: Number,
                default: 0,
            },
            title: {
                type: String,
                default: '活动设置',
            },
        },
        setup() {
            const {
                setMarketingCouponSetting,
            } = useMarketingCouponSetting();
            return {
                setMarketingCouponSetting,
            };
        },
        data() {
            return {};
        },
        methods: {
            handleChange(val) {
                this.$emit('change', val);
            },
            handleConfirm() {
                this.$emit('Ok');
            },
        },
    };
</script>
