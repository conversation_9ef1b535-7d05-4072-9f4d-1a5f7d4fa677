<template>
    <abc-dialog
        v-model="visible"
        :title="dialogTitle"
        size="xlarge"
        append-to-body
        content-styles="min-height: 212px;max-height: 688px;"
    >
        <abc-form
            ref="formData"
            :item-no-margin="true"
            label-position="left-top"
            :label-width="72"
        >
            <abc-form-item
                :custom-label-style="{
                    lineHeight: '22px', padding: '0 0', minHeight: '22px'
                }"
                label="积分规则"
            >
                <abc-radio-group v-model="formData.pointsRule">
                    <abc-flex style="width: 100%; height: 22px;" align="center">
                        <abc-radio :label="0">
                            全部商品统一积分
                        </abc-radio>
                    </abc-flex>
                    <abc-flex
                        v-if="formData.pointsRule === 0"
                        gap="8"
                        style="width: 100%; height: 32px; padding-left: 24px; margin-top: 12px;"
                        align="center"
                    >
                        <abc-text>每消费</abc-text>
                        <abc-form-item required>
                            <abc-input
                                v-model="formData.amountAccumulateRat"
                                v-abc-focus-selected
                                type="money"
                                :max-length="5"
                                :config="{
                                    max: 99999, formatLength: 2
                                }"
                                :input-custom-style="{ 'text-align': 'center' }"
                                :width="75"
                            >
                                <span slot="append">元</span>
                            </abc-input>
                        </abc-form-item>
                        <abc-text>，积</abc-text>
                        <abc-form-item required>
                            <abc-input
                                v-model="formData.pointsAccumulateRat"
                                v-abc-focus-selected
                                type="number"
                                :max-length="10"
                                :config="{ max: 99999 }"
                                :input-custom-style="{ 'text-align': 'center' }"
                                :width="75"
                            >
                                <span slot="append">分</span>
                            </abc-input>
                        </abc-form-item>
                    </abc-flex>
                    <abc-flex style="width: 100%; height: 22px; margin-top: 12px;" align="center">
                        <abc-radio :label="1">
                            按商品毛利率积分
                        </abc-radio>
                    </abc-flex>
                    <template v-if="formData.pointsRule === 1">
                        <abc-flex style="height: 16px; margin-top: 4px;">
                            <abc-text size="mini" theme="gray">
                                毛利率按照零售单实收计算 毛利率 = (实收金额 - 进价) / 实收金额 × 100%
                            </abc-text>
                        </abc-flex>
                        <abc-flex
                            v-for="(item, index) in formData.list"
                            :key="index"
                            gap="8"
                            align="center"
                            style="width: 100%; padding-left: 24px; margin-top: 12px;"
                        >
                            <abc-form-item v-if="index !== (formData.list.length - 1)" :validate-event="(value, callback)=> handleValidateProfitRate(value, (index === 0 ? formData.list[index].startGrossMargin : formData.list[index - 1].endGrossMargin), callback)" required>
                                <abc-input
                                    :key="`end-${index}`"
                                    v-model="item.endGrossMargin"
                                    v-abc-focus-selected
                                    type="number"
                                    :config="{
                                        max: 100, min: 0, supportZero: true,
                                    }"
                                    :input-custom-style="{ 'text-align': 'center' }"
                                    :width="76"
                                >
                                    <span slot="appendInner">%</span>
                                </abc-input>
                            </abc-form-item>
                            <abc-text v-if="index === (formData.list.length - 1)">
                                毛利率 ≤ {{ formData.list[index - 1].endGrossMargin || 0 }}%的商品，每单消费
                            </abc-text>
                            <abc-text v-else style="display: inline-flex; justify-content: space-between;">
                                <span style="display: inline-block; width: 67px; margin-right: 6px;">{{ '< 毛利率 ≤' }}</span><span style="display: inline-block; width: 36px;">{{ `${index === 0 ? formData.list[index].startGrossMargin : (formData.list[index - 1].endGrossMargin || 0)}%` }}</span>
                                <span style="display: inline-block; margin-left: 4px;">的商品，每单消费</span>
                            </abc-text>
                            <abc-form-item required>
                                <abc-input
                                    :key="`amount-${index}`"
                                    v-model="item.amountAccumulateRat"
                                    v-abc-focus-selected
                                    type="money"
                                    :max-length="5"
                                    :config="{
                                        max: 99999, formatLength: 2, supportZero: false
                                    }"
                                    :input-custom-style="{ 'text-align': 'center' }"
                                    :width="70"
                                >
                                    <abc-text slot="append" style="display: inline-block; width: 19px; text-align: center;" theme="gray-light">
                                        元
                                    </abc-text>
                                </abc-input>
                            </abc-form-item>
                            <abc-text>，积</abc-text>
                            <abc-form-item required>
                                <abc-input
                                    :key="`points-${index}`"
                                    v-model="item.pointsAccumulateRat"
                                    v-abc-focus-selected
                                    type="number"
                                    :max-length="10"
                                    :config="{
                                        max: 99999, supportZero: true
                                    }"
                                    :input-custom-style="{ 'text-align': 'center' }"
                                    :width="70"
                                >
                                    <abc-text slot="append" style="display: inline-block; width: 19px; text-align: center;" theme="gray-light">
                                        分
                                    </abc-text>
                                </abc-input>
                            </abc-form-item>
                            <abc-space
                                v-if="index !== (formData.list.length - 1)"
                                is-compact
                                compact-block
                                align="center"
                                style="width: auto !important;"
                            >
                                <abc-button
                                    icon="s-b-add-line-medium"
                                    variant="ghost"
                                    theme="default"
                                    @click="addRules(index)"
                                ></abc-button>
                                <abc-button
                                    v-if="(index !== (formData.list.length - 1) && index !== 0)"
                                    icon="s-b-reduce-line-medium"
                                    variant="ghost"
                                    theme="default"
                                    @click="deleteRules(index)"
                                ></abc-button>
                            </abc-space>
                        </abc-flex>
                    </template>
                </abc-radio-group>
            </abc-form-item>
        </abc-form>

        <template slot="footer">
            <abc-flex align="center" justify="space-between">
                <div></div>
                <abc-space>
                    <abc-button theme="primary" :disabled="disabled" @click="handleRuleConfirm">
                        确定
                    </abc-button>
                    <abc-button variant="ghost" theme="primary" @click="handleCancel">
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    export default {
        name: 'IntegralBasicDialog',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            memberData: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                formData: {
                    amountAccumulateRat: null,
                    pointsAccumulateRat: null,
                    pointsRule: 0,
                    ruleDetails: [],
                    id: 0,
                    list: [],
                },
                formDataCache: null,
            };
        },
        computed: {
            visible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            dialogTitle() {
                return `${this.memberData.name || '会员'}积分规则`;
            },
            disabled() {
                return isEqual(this.formData, this.formDataCache);
            },
        },
        created() {
            this.initFormData();
        },
        methods: {
            handleValidateProfitRate(value, profitRate, callback) {
                console.log('profitRate', profitRate);
                if (Number(value) >= Number(profitRate)) {
                    return callback({
                        validate: false,
                        message: `毛利率不能超过${profitRate}%`,
                    });
                }
                return callback({
                    validate: true,
                });
            },
            deleteRules(index) {
                this.formData.list.splice(index, 1);
            },
            addRules(index) {
                const model = {
                    startGrossMargin: null,
                    endGrossMargin: null,
                    pointsAccumulateRat: null,
                    amountAccumulateRat: null,
                };
                this.formData.list.splice(index + 1, 0, model);
            },
            initFormData() {
                this.formData = {
                    ...this.formData,
                    ...this.memberData,
                    list: this.memberData?.ruleDetails?.length ? this.memberData?.ruleDetails : [
                        {
                            startGrossMargin: 100,
                            endGrossMargin: 0,
                            amountAccumulateRat: null,
                            pointsAccumulateRat: null,
                        },
                        {
                            startGrossMargin: 0,
                            endGrossMargin: null,
                            amountAccumulateRat: null,
                            pointsAccumulateRat: null,
                        },
                    ],
                };
                console.log('this.formData=', this.formData);
                this.formDataCache = clone(this.formData);
            },
            handleCancel() {
                this.visible = false;
            },
            handleRuleConfirm() {
                this.$refs.formData.validate(async (valid) => {
                    if (valid) {
                        this.$emit('handleRuleConfirm', this.formData);
                        this.visible = false;
                    }
                });
            },
        },
    };
</script>
