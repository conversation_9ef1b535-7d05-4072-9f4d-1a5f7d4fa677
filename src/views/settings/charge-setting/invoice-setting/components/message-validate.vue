<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        size="small"
        class="message-validate-dialog-wrapper"
        title="提交验证码"
        style="z-index: 999999;"
        append-to-body
    >
        <template #top-extend>
            <abc-tips-card-v2>
                <abc-text style="text-align: left;">
                    {{ tipMessage }}
                </abc-text>
            </abc-tips-card-v2>
        </template>
        <abc-form ref="form">
            <abc-form-item label="手机验证码" required>
                <abc-input
                    v-model="messageCode"
                    placeholder="请输入验证码"
                    :width="312"
                    :max-length="6"
                    :disallowed="/\D/"
                >
                    <template #appendInner>
                        <abc-button
                            variant="text"
                            theme="primary"
                            :disabled="isCounting"
                            @click="onSend"
                        >
                            {{ isCounting ? `${timeLeft}s` : '发送验证码' }}
                        </abc-button>
                    </template>
                </abc-input>
            </abc-form-item>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="loading" :disabled="!messageCode" @click="handleSubmit">
                确定
            </abc-button>
            <abc-button variant="ghost" @click="handleClose">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    export default {
        name: 'MessageValidate',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            onSend: {
                type: Function,
                required: true,
            },
            onFinish: {
                type: Function,
                required: true,
            },
            isCounting: Boolean,
            timeLeft: Number,
            tipMessage: String,
        },
        data() {
            return {
                messageCode: '',
                loading: false,
            };
        },
        computed: {
            visible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        methods: {
            handleSubmit() {
                this.loading = true;
                this.$emit('finish', this.messageCode);
                this.onFinish(() => {
                    this.loading = false;
                });
            },

            handleClose() {
                this.$emit('finish', '');
                this.visible = false;
            },
        },
    };
</script>
