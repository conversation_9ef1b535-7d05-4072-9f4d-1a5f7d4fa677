<template>
    <biz-fill-remain-height>
        <template #header>
            <abc-manage-tabs
                v-if="showTabs"
                :option="tabOptions"
                class="stock-set-tabs"
                @change="handleTabsChange"
            ></abc-manage-tabs>
        </template>

        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script>
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import { mapGetters } from 'vuex';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    export default {
        name: 'WareHouseSet', // 库房设置

        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },

        computed: {
            ...mapGetters(['isChainAdmin', 'isSingleStore', 'multiPharmacyCanUse', 'multiPharmacyCanUseWithDefault']),
            ...mapGetters('viewDistribute',['viewDistributeConfig']),
            showInvoiceActionSetting() {
                return this.viewDistributeConfig?.Settings?.showInvoiceActionSetting;
            },
            showWareHouseSetBasic() {
                return this.multiPharmacyCanUse || this.multiPharmacyCanUseWithDefault;
            },
            // 是否支持库房管理设置
            isSupportWareHouse() {
                return this.viewDistributeConfig.Settings.isSupportWareHouse;
            },
            // 是否支持进销存动作设置
            isSupportInvoiceAction() {
                return this.viewDistributeConfig.Settings.isSupportInvoiceAction;
            },
            // 总部是否支持预警采购设置
            isSupportWarningProcurementForChainAdmin() {
                return this.viewDistributeConfig.Settings.isSupportWarningProcurementForChainAdmin;
            },
            showTabs() {
                const noTabsNames = [ 'wareHouseForm', 'wareHouseAdd'];
                return noTabsNames.indexOf(this.$route.name) === -1;
            },
            tabOptions() {
                return [
                    {
                        label: '库房管理',
                        value: 'wareHouseSetBasic',
                        isShow: this.isSupportWareHouse && (this.isChainAdmin ? false : this.showWareHouseSetBasic),
                    },
                    {
                        label: '进销存动作设置',
                        value: 'invoiceActionSetting',
                        isShow: this.isSupportInvoiceAction && (this.isChainAdmin ? this.showInvoiceActionSetting : false),
                    },
                    {
                        label: '预警与采购设置',
                        value: 'warningProcurement',
                        isShow: this.isSupportWarningProcurementForChainAdmin ? true : !this.isChainAdmin,
                    },
                    {
                        label: '库存标签',
                        value: 'tagsManagement',
                        isShow: this.isChainAdmin || this.isSingleStore,
                    },
                ].filter((item) => item.isShow);
            },
        },
        methods: {
            handleTabsChange(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>
