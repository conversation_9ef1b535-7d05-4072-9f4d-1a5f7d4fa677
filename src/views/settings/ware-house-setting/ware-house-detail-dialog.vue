<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        content-styles="height: 410px;"
        size="xlarge"
    >
        <div v-abc-loading="loading" class="ware-house-setting-detail-dialog__page-content">
            <abc-form
                ref="wareHouseForm"
                :label-width="110"
                label-position="top"
                item-no-margin
            >
                <abc-space direction="vertical" size="large" align="left">
                    <abc-space size="large">
                        <abc-form-item
                            label="库房名称"
                            required
                            hidden-red-dot
                        >
                            <abc-input
                                v-model.trim="postData.name"
                                :width="228"
                                :max-length="6"
                                :input-custom-style="{
                                    backgroundColor: '#ffffff'
                                }"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="库房描述">
                            <abc-input
                                v-model="postData.remark"
                                :width="228"
                                :max-length="50"
                                :input-custom-style="{
                                    backgroundColor: '#ffffff'
                                }"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="库房地点">
                            <abc-input
                                v-model="postData.pharmacyAddress"
                                :width="228"
                                :max-length="10"
                                :input-custom-style="{
                                    backgroundColor: '#ffffff'
                                }"
                            ></abc-input>
                        </abc-form-item>
                    </abc-space>
                    <abc-form-item label="库房功能">
                        <abc-space :size="16" wrap>
                            <abc-checkbox v-model="postData.enablePurchase" type="number">
                                采购入库
                            </abc-checkbox>
                            <abc-checkbox v-model="postData.enableDispense" type="number">
                                调剂发药
                            </abc-checkbox>
                            <abc-checkbox v-model="postData.enablePharmacyTrans" type="number">
                                领用
                            </abc-checkbox>
                            <abc-checkbox v-model="postData.enableTrans" type="number">
                                调拨
                            </abc-checkbox>
                            <abc-checkbox v-if="hasWard" v-model="postData.enableDepartmentOut" type="number">
                                科室消耗
                            </abc-checkbox>
                            <abc-checkbox v-model="postData.enableLossOut" type="number">
                                报损
                            </abc-checkbox>
                            <abc-checkbox v-model="postData.enableCheck" type="number">
                                盘点
                            </abc-checkbox>
                            <abc-checkbox v-if="hasProductionOut" v-model="postData.enableProductionOut" type="number">
                                生产出库
                            </abc-checkbox>
                            <abc-checkbox v-if="hasWard" v-model="postData.enableOtherOut" type="number">
                                其他出库
                            </abc-checkbox>
                        </abc-space>
                    </abc-form-item>
                    <abc-form-item label="库房成员" style="width: 100%;">
                        <abc-radio-group
                            v-model="postData.defaultMember"
                            gap="8px"
                            style="flex-direction: column; width: 100%;"
                            @change="handleMemberTypeChange"
                        >
                            <abc-radio :label="1">
                                <span> {{ postData.enableDispense ? '有库存、药房权限的成员' : '有库存权限的成员' }}</span>
                            </abc-radio>
                            <abc-radio :label="0">
                                指定成员
                            </abc-radio>
                        </abc-radio-group>
                        <abc-flex
                            v-if="!postData.defaultMember"
                            vertical
                            :gap="8"
                            style="width: 100%; padding-left: 24px; margin-top: 8px;"
                        >
                            <abc-flex justify="space-between">
                                <abc-button
                                    variant="text"
                                    size="small"
                                    @click="openSelectEmployeeDialog"
                                >
                                    添加人员
                                </abc-button>
                                <abc-button
                                    v-if="!isDeletePeople"
                                    variant="text"
                                    size="small"
                                    :disabled="!selectedEmployees.length"
                                    @click="isDeletePeople = true"
                                >
                                    删除
                                </abc-button>
                                <abc-button
                                    v-else
                                    variant="text"
                                    size="small"
                                    @click="isDeletePeople = false"
                                >
                                    退出删除
                                </abc-button>
                            </abc-flex>

                            <abc-card>
                                <div class="safe-login-card" :class="{ 'has-config-item': selectedEmployees.length }">
                                    <template v-if="selectedEmployees.length">
                                        <abc-tag-v2
                                            v-for="(employee, index) in selectedEmployees"
                                            :key="employee.id"
                                            shape="square"
                                            size="huge"
                                            theme="default"
                                            variant="outline"
                                            :closable="isDeletePeople"
                                            close-resident
                                            :min-width="80"
                                            icon="s-user-color"
                                            @close="deleteEmployee(index)"
                                        >
                                            <abc-text
                                                siz="normal"
                                                tag="div"
                                                class="ellipsis"
                                                style="min-width: 42px; max-width: 42px;"
                                                :title="employee.name"
                                            >
                                                {{ employee.name }}
                                            </abc-text>
                                        </abc-tag-v2>
                                    </template>
                                    <abc-content-empty
                                        v-else
                                        top="19px"
                                        value="暂无数据"
                                        :show-icon="false"
                                    ></abc-content-empty>
                                </div>
                            </abc-card>
                        </abc-flex>
                    </abc-form-item>
                </abc-space>
            </abc-form>
        </div>
        <div slot="footer" class="dialog-footer">
            <div style="flex: 1;">
                <abc-button
                    v-if="id"
                    type="danger"
                    @click="handleStopConfirm"
                >
                    {{ postData.status ? '停用' : '启用' }}
                </abc-button>
            </div>
            <abc-button
                :loading="btnLoading"
                :disabled="btnDisabled"
                @click="handleSubmit"
            >
                保存
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
        <abc-dialog
            v-if="selectEmployeeDialog"
            v-model="selectEmployeeDialog"
            title="选择库房人员"
            append-to-body
            content-styles="padding: 0;"
        >
            <abc-transfer-v2
                :default-checked-keys="defaultCheckedKeys"
                :result-width="280"
                :props="{
                    label: 'name',
                }"
                leaf-icon="s-user-color"
                node-key="id"
                show-check-all
                show-search
                :search-placeholder=" postData.enableDispense ? '含有药房、库存任意权限人员姓名' : '含有任意库存权限人员姓名' "
                :data="employees"
                :search-node-method="handleSearch"
                @confirm="confirmSelect"
                @cancel="selectEmployeeDialog = false"
            >
            </abc-transfer-v2>
        </abc-dialog>
    </abc-dialog>
</template>

<script>
    import RegistrationsAPI from 'api/registrations/index.js';
    import SettingAPI from 'api/settings';
    import { mapGetters } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';

    export default {
        name: 'WareHouseDetailDialog',
        props: {
            value: Boolean,
            id: {
                type: [Number, String],
                default: '',
            },
        },
        data() {
            return {
                postData: {
                    employeeIds: [],
                    employees: [],
                    enableCheck: 0,
                    enableDepartmentOut: 0,
                    enableDispense: 0,
                    enableLossOut: 0,
                    enableOtherOut: 0,
                    enablePharmacyTrans: 0,
                    enablePurchase: 0,
                    enableTrans: 0,
                    enableProductionOut: 0,
                    defaultMember: 1,
                    id: '',
                    name: '',
                    remark: '',
                    pharmacyAddress: '',
                    status: 1,
                },
                loading: false,
                selectEmployeeDialog: false,
                EmployeeLoading: false,
                employees: [],
                queryKeyword: '',
                btnLoading: false,
                cachePostData: null,
                wareHouseList: [],

                defaultCheckedKeys: [],
                isDeletePeople: false,
                transferLoading: false,
                selectedEmployees: [],
                cacheSelectedEmployees: [],
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            dialogTitle() {
                if (this.isModify) {
                    return '修改库房';
                }
                return '新增库房';
            },
            isModify() {
                return !!this.id;
            },
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            btnDisabled() {
                return isEqual(this.postData, this.cachePostData) && isEqual(this.selectedEmployees, this.cacheSelectedEmployees);
            },
            hasWard() {
                return this.viewDistributeConfig?.Settings?.openSetting?.hasWard;
            },
            hasProductionOut() {
                return this.viewDistributeConfig?.Inventory?.isSupportProductionOut;
            },
        },
        created() {
            this.getWareHouseList();
            if (this.id) {
                this.fetchWareHouseDetail();
            }
        },
        methods: {
            async getWareHouseList() {
                try {
                    const res = await SettingAPI.clinicGoodsConfig.getClinicGoodsConfig();
                    // 过滤掉虚拟药房
                    this.wareHouseList = res.data?.pharmacyList?.map((item) => item.name) || [];
                } catch (err) {
                    console.log(err);
                    this.wareHouseList = [];
                }
            },
            handleMemberTypeChange(val) {
                if (val === 1) {
                    this.selectedEmployees = [];
                }
            },
            handleSubmit() {
                this.$refs.wareHouseForm.validate(async (valid) => {
                    if (valid) {
                        this.submit();
                    }
                });
            },

            async fetchWareHouseDetail() {
                try {
                    this.loading = true;
                    const res = await SettingAPI.wareHouse.getWareHouseDetail(this.id);
                    if (res) {
                        this.postData = res;
                        this.selectedEmployees = res?.employees || [];
                        this.cachePostData = clone(this.postData);
                        this.cacheSelectedEmployees = clone(this.selectedEmployees);
                    }
                } catch (err) {
                    console.log(err);
                } finally {
                    this.loading = false;
                }
            },

            async handleStopConfirm() {
                try {
                    this.postData.employeeIds = this.selectedEmployees.map((item) => item.id);
                    const params = {
                        ...this.postData,
                        status: this.postData.status === 0 ? 1 : 0,
                    };
                    const res = await SettingAPI.wareHouse.updateWarehouse(this.id, params);
                    if (res) {
                        this.postData.status = this.postData.status === 0 ? 1 : 0;
                        this.$Toast({
                            message: `${this.postData.status === 0 ? '停用' : '启用'}成功`,
                            type: 'success',
                        });
                        this.$emit('refresh');
                        this.showDialog = false;
                        // this.$router.replace({
                        //     name: 'wareHouseSetBasic',
                        // });
                        this.$store.dispatch('getGoodsConfig');
                    }
                } catch (err) {
                    console.log(err);
                }
            },

            async submit() {
                if (!this.postData.defaultMember && !this.selectedEmployees.length) {
                    this.$Toast({
                        type: 'error',
                        message: '请选择人员',
                    });
                    return;
                }
                if (![
                    this.postData.enableCheck,
                    this.postData.enableDepartmentOut,
                    this.postData.enableDispense,
                    this.postData.enableLossOut,
                    this.postData.enableOtherOut,
                    this.postData.enablePharmacyTrans,
                    this.postData.enablePurchase,
                    this.postData.enableTrans].some((item) => item)) {
                        this.$Toast('请选择库房功能');
                        return;
                    }
                this.postData.employeeIds = this.selectedEmployees.map((item) => item.id);
                this.btnLoading = true;
                try {
                    const params = this.postData;
                    if (this.id) {
                        const res = await SettingAPI.wareHouse.updateWarehouse(params.id, params);
                        if (res) {
                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });
                            this.$emit('refresh');
                            this.showDialog = false;
                            // this.$router.replace({
                            //     name: 'wareHouseSetBasic',
                            // });
                        }
                    } else {
                        const res = await SettingAPI.wareHouse.createWarehouse(params);
                        if (res) {
                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });
                            this.$emit('refresh');
                            this.showDialog = false;
                            // this.$router.replace({
                            //     name: 'wareHouseSetBasic',
                            // });
                        }
                    }
                } catch (err) {
                    console.log(err);
                } finally {
                    this.$store.dispatch('getGoodsConfig');
                    this.btnLoading = false;
                }

            },
            async openSelectEmployeeDialog() {
                this.transferLoading = true;
                try {
                    const { data } = await RegistrationsAPI.fetchlistByCondition({
                        moduleIds: this.postData.enableDispense ?
                            this.viewDistributeConfig.Hospital.wareHouseMemberPharmacyModuleIds :
                            this.viewDistributeConfig.Hospital.wareHouseMemberModuleIds,
                    });
                    this.employees = data?.rows?.map((item) => {
                        return {
                            ...item,
                            id: item.employeeId,
                            name: item.employeeName,
                            namePy: item.employeeNamePy,
                            namePyFirst: item.employeeNamePyFirst,
                        };
                    });
                    this.defaultCheckedKeys = this.selectedEmployees.map((item) => item.id);
                    this.selectEmployeeDialog = true;
                    this.isDeletePeople = false;
                } catch (error) {
                    console.log('fetchEmployeeList error', error);
                } finally {
                    this.transferLoading = false;
                }
            },
            handleSearch(val) {
                return {
                    key: val,
                    list: this.employees.filter((item) => {
                        return item.name.includes(val) ||
                            item.namePy.includes(val) ||
                            item.namePyFirst.includes(val.toUpperCase());
                    }),
                };
            },
            confirmSelect(payload) {
                this.selectedEmployees = payload;
                this.selectEmployeeDialog = false;
                this.isDeletePeople = false;
            },
            deleteEmployee(index) {
                this.selectedEmployees.splice(index, 1);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.ware-house-setting-detail-dialog__page-content {
    .safe-login-card {
        min-height: 92px;
        max-height: 210px;
        padding: 10px 0 10px 10px;
        overflow-y: scroll;

        @include scrollBar(true);

        &.has-config-item {
            display: grid;
            grid-template-columns: repeat(auto-fill, 80px);
            grid-gap: 8px;
        }
    }
}
</style>
