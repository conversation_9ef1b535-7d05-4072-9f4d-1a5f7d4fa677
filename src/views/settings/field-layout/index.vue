<template>
    <biz-fill-remain-height>
        <template #header>
            <abc-manage-tabs :option="tabsOptions" @change="HandleChangeTab"></abc-manage-tabs>
        </template>
        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script>
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';
    import { mapGetters } from 'vuex';
    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isChainSubStore',
                'isSingleStore',
                'isPharmacy',
                'isEnableRegUpgrade',
                'isOpenMp',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            tabsOptions() {
                const arr = [
                    {
                        label: '门诊病历',
                        value: 'FieldLayoutMedicalRecord',
                        isHidden: this.isChainAdmin || this.isPharmacy,
                    }, {
                        label: this.isPharmacy ? '会员建档' : '患者建档',
                        value: 'FieldLayoutPatientCreate',
                    }, {
                        label: '预约挂号',
                        value: 'FieldLayoutRegistration',
                        isHidden: this.isChainAdmin || this.isPharmacy || !this.isEnableRegUpgrade && !this.viewDistributeConfig.Settings.fieldLayoutSetting.registration,
                    },
                    {
                        label: '微诊所就诊卡',
                        value: 'FieldLayoutWeappPatientCard',
                        isHidden: !this.isOpenMp || this.isChainSubStore || this.isPharmacy,
                    },
                ];

                return arr.filter((item) => !item.isHidden);
            },
        },
        watch: {
            '$route': function (to) {
                if (to.name === 'FieldLayoutMedicalRecord' && this.isChainAdmin) {
                    this.$router.push({
                        name: 'FieldLayoutWeappPatientCard',
                    });
                }
            },
        },
        methods: {
            HandleChangeTab(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>
