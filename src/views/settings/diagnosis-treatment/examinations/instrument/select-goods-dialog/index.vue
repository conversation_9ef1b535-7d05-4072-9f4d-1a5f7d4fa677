<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        :show-footer="false"
        :content-styles="contentStyles"
        custom-class="examinations-settings__select-goods-dialog"
    >
        <delete-icon
            class="close-wrapper"
            size="huge"
            @delete="handleClose"
        ></delete-icon>
        <div v-if="createStatus === 0" v-abc-loading="true" class="default-loading-wrapper"></div>
        <div
            v-else-if="createStatus === 1"
            class="select-goods-wrapper"
        >
            <h3>选择仪器常用检验项目</h3>
            <div class="select-goods-dialog__content-wrapper">
                <span class="tips">
                    确认添加后，系统自动创建仪器可开展的所有检验项目
                </span>
                <ul class="composition-goods-list">
                    <composition-goods-item
                        show-check-box
                        :combine-goods-list="combineGoodsList"
                    ></composition-goods-item>
                </ul>
                <div class="btns-wrapper">
                    <abc-button
                        :loading="loadingBtn"
                        :disabled="disabledBtn"
                        size="large"
                        style="width: 225px;"
                        @click="onClickAddDevice"
                    >
                        添加仪器并创建项目
                    </abc-button>
                </div>
            </div>
        </div>
        <div v-if="createStatus === 2" class="create-loading-wrapper">
            <div class="create-progress-wrapper">
                <div class="progress-jid" :style="{ width: `${progressPercentage}%` }"></div>
                <div class="progress-txt">
                    初始化进行中 {{ progressPercentage }}%
                </div>
            </div>
            <span class="tips">正在创建项目，请等待...</span>
        </div>
        <div v-if="createStatus === 3" class="create-success-wrapper">
            <div class="success-animation-wrapper">
                <span class="checkmark"></span>
            </div>
            <span class="tit">创建成功</span>
            <template v-if="isSelfSetPrice">
                <span class="con">现在只需完成价格设置和医保对码便可开单收费</span>
                <div class="btns-wrapper">
                    <abc-button
                        style="width: 120px;"
                        @click="onClickConfigNow"
                    >
                        现在设置
                    </abc-button>
                    <abc-button
                        style="width: 120px;"
                        type="blank"
                        @click="onClicConfigNex"
                    >
                        稍后设置
                    </abc-button>
                </div>
                
                <abc-flex justify="center" style="margin-top: 12px;">
                    <abc-button variant="text" @click="handle2ApplyOrder">
                        仪器对接工单申请
                    </abc-button>
                </abc-flex>
            </template>
            <template v-else>
                <span class="con">请联系总部完成价格设置和医保对码便可开单收费</span>
                <div class="btns-wrapper">
                    <abc-button
                        style="width: 120px;"
                        type="success"
                        @click="onClicConfigNex"
                    >
                        知道了
                    </abc-button>
                </div>
                
                <abc-flex justify="center" style="margin-top: 12px;">
                    <abc-button variant="text" @click="handle2ApplyOrder">
                        仪器对接工单申请
                    </abc-button>
                </abc-flex>
            </template>
        </div>
    </abc-modal>
</template>

<script>
    import SettingAPI from 'api/settings';
    import DeleteIcon from 'views/layout/delete-icon/delete-icon';
    import CompositionGoodsItem from '../detail/composition-goods-item.vue';
    import { delayPromise } from '@/lis/common/tools';

    export default {
        components: {
            DeleteIcon,
            CompositionGoodsItem,
        },
        props: {
            // 设备型号id
            deviceModelId: {
                type: String,
                required: true,
            },
            // 处理成功
            handleSuccess: {
                type: Function,
                required: true,
            },
        },
        data() {
            return {
                visible: false,
                loadingBtn: false,
                originData: null,
                linkSuccess: false,
                combineGoodsList: [], // 组合项目列表
                singleItemGoodsIds: [], // 单个项目列表
                createStatus: 0, // 创建项目状态，0 默认；1 选择项目；2 创建中；3 创建成功；4 创建失败
                progressPercentage: 0,
            };
        },
        computed: {
            // 内容样式
            contentStyles() {
                const height = this.createStatus === 1 ? '460px' : '324px';
                return `width: 548px; min-height: ${height}; padding: 0px`;
            },
            // 已选择组合项目
            selectCombineGoodsList() {
                return this.combineGoodsList.filter((item) => item.isSelected === true);
            },
            // 是否禁用按钮
            disabledBtn() {
                if (this.selectCombineGoodsList.length === 0) {
                    return true;
                }
                return false;
            },
            // 是否自己能设置价格
            isSelfSetPrice() {
                return false;
            },
            // 是否跳过选择项目
            isJumpSelectGoods() {
                return this.combineGoodsList.length === 0;
            },
            // 是否跳过创建项目
            isJumpCreateGoods() {
                return this.singleItemGoodsIds.length === 0;
            },
        },
        async mounted() {
            await this.fetchCombineGoodsList();
            if (this.isJumpSelectGoods) {
                this.onClickAddDevice();
            } else {
                this.createStatus = 1;
            }
        },
        methods: {
            /**
             * 拉取组合项目列表
             * <AUTHOR>
             * @date 2022-11-03
             */
            async fetchCombineGoodsList() {
                const fetchResponse = await SettingAPI.inspect.getDeviceModel(this.deviceModelId);
                if (fetchResponse.status === true) {
                    this.originData = fetchResponse.data;
                    this.combineGoodsList = this.createGoodsList(this.originData, true); // 组合项目列表
                    this.singleItemGoodsIds = this.createGoodsList(this.originData, false); // 单个项目列表
                }
            },
            /**
             * 创建项目列表
             * <AUTHOR>
             * @date 2022-11-03
             * @param {Object} data
             * @param {Boolean} isCombine
             * @returns {Array}
             */
            createGoodsList(data, isCombine = false) {
                let {
                    installedLocalGoodsList,
                    unInstallStandardGoodsList,
                } = data || {};
                if (!installedLocalGoodsList) {
                    installedLocalGoodsList = [];
                }
                if (!unInstallStandardGoodsList) {
                    unInstallStandardGoodsList = [];
                }
                installedLocalGoodsList.forEach((item) => { item.installed = true; });
                unInstallStandardGoodsList.forEach((item) => { item.installed = false; });
                const allGoodsList = [...unInstallStandardGoodsList, ...installedLocalGoodsList];
                if (isCombine) {
                    return allGoodsList
                        .filter((item) => item.combineType)
                        .map((item) => ({
                            name: item.name,
                            children: item.children,
                            id: item.id,
                            isSelected: true,
                            bizExtensions: item.bizExtensions,
                            installed: item.installed,
                        }));
                }
                return allGoodsList
                    .filter((item) => !item.combineType)
                    .map((item) => item.id);
            },
            /**
             * 当点击添加仪器
             * <AUTHOR>
             * @date 2022-11-03
             */
            async onClickAddDevice() {
                // 先添加设备
                this.loadingBtn = true;
                const linkResponse = await SettingAPI.inspect.linkDevice(this.deviceModelId);
                this.loadingBtn = false;
                if (linkResponse.status === false) {
                    this.isJumpCreateGoods && this.handleClose();
                    return linkResponse;
                }
                this.loadingBtn = false;
                this.linkSuccess = true;

                if (this.isJumpCreateGoods) {
                    // 直接跳过创建项目
                    this.createStatus = 3; // 直接跳转到创建成功
                    return;
                }

                // 再创建项目
                this.handleCreateGoods();
            },
            /**
             * 处理创建项目
             * <AUTHOR>
             * @date 2022-11-10
             */
            async handleCreateGoods() {
                this.loadingBtn = true;
                const postData = this.createPostData();
                const createResponse = await SettingAPI.inspect.batchCreate(this.deviceModelId,postData);
                this.loadingBtn = false;
                if (createResponse.status === false) {
                    this.handleClose();
                    return createResponse;
                }
                this.loadingBtn = false;
                this.createStatus = 2; // 创建中

                this.mockProgressPercentage();
            },
            /**
             * 创建创建项目的提交数据
             * <AUTHOR>
             * @date 2022-11-08
             * @returns {Object}
             */
            createPostData() {
                const postData = {
                    combineItemGoodsIds: this.combineGoodsList
                        .filter((it) => it.isSelected)
                        .filter((it) => it.installed && it.bizExtensions?.bizRefId || !it.installed)
                        .map((it) => ({
                            id: it.installed ? it.bizExtensions.bizRefId : it.id,
                            children: it.children.map((i) => i.id),
                        })),
                    singleItemGoodsIds: this.singleItemGoodsIds,
                };
                return postData;
            },
            /**
             * 检查项目是否创建成功
             * <AUTHOR>
             * @date 2022-11-08
             * @returns {Boolean}
             */
            checkIsCreateSuccess() {
                return window.createSuccess || this.progressPercentage === 100;
            },
            /**
             * 控制进度
             * <AUTHOR>
             * @date 2022-11-08
             */
            async mockProgressPercentage() {
                this.progressPercentage = 0;
                await this.$nextTick();
                // let isCreateSuccess = false;
                do {
                    if (this.visible === false) {
                        // 弹窗手动关闭后，不在模拟进度
                        return;
                    }
                    this.progressPercentage += 1;
                    await delayPromise(150);
                    // isCreateSuccess = this.checkIsCreateSuccess();
                } while (this.progressPercentage < 85);

                await delayPromise(3000);
                this.progressPercentage = 100;
                await delayPromise(1000);

                this.createStatus = 3;
            },
            /**
             * 当点击现在设置
             * <AUTHOR>
             * @date 2022-11-08
             */
            onClickConfigNow() {
                this.$router.replace({
                    name: 'examinations',
                    query: {
                        type: '12',
                    },
                });
            },
            /**
             * 当点击稍后设置
             * <AUTHOR>
             * @date 2022-11-08
             */
            onClicConfigNex() {
                this.handleClose();
            },
            /**
             * 处理关闭
             * <AUTHOR>
             * @date 2022-11-08
             */
            handleClose() {
                if (this.linkSuccess && this.handleSuccess) {
                    this.handleSuccess();
                }
                this.visible = false;
            },
            
            handle2ApplyOrder() {
                if (this.linkSuccess && this.handleSuccess) {
                    this.handleSuccess(true);
                }
                
                this.visible = false;
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .examinations-settings__select-goods-dialog {
        position: relative;

        .abc-dialog-body {
            border-radius: 5px;
        }

        .close-button-wrapper {
            display: none !important;
        }

        .close-wrapper {
            position: absolute;
            top: 6px;
            right: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            cursor: pointer;
            border-radius: 50%;

            &:hover {
                background-color: $P4;

                .cis-icon-cross_small {
                    color: $T3;
                }
            }

            &:active {
                background-color: $P6;

                .cis-icon-cross_small {
                    color: $T3;
                }
            }

            .cis-icon-cross_small {
                font-size: 18px;
                color: $P1;
                text-align: center;
            }
        }

        .default-loading-wrapper {
            position: relative;
            width: 100%;
            height: 324px;
        }

        .select-goods-wrapper {
            padding: 44px 48px 40px 48px;

            h3 {
                font-size: 16px;
                font-weight: bold;
                line-height: 24px;
            }

            .select-goods-dialog__content-wrapper {
                margin-top: 4px;

                .tips {
                    font-size: 14px;
                    line-height: 20px;
                    color: $T2;
                }

                .composition-goods-list {
                    min-height: 232px;
                    max-height: 480px;
                    margin-top: 16px;
                    overflow-y: auto;
                }
            }

            .btns-wrapper {
                height: 40px;
                margin-top: 40px;

                @include flex(row, center, flex-end);
            }
        }

        .create-loading-wrapper {
            @include flex(column, flex-start, center);

            margin-top: 144px;

            .create-progress-wrapper {
                position: relative;
                width: 240px;
                height: 40px;
                overflow: hidden;
                background-color: rgba(18, 215, 96, 0.5);
                border-radius: 40px;
            }

            .progress-jid {
                position: absolute;
                z-index: 1;
                width: 0%;
                height: 100%;
                background-color: #01d053;
                transition: all 0.5s linear;
            }

            .progress-txt {
                position: absolute;
                z-index: 2;
                width: 100%;
                height: 100%;
                font-size: 14px;
                line-height: 40px;
                color: #ffffff;
                text-align: center;
            }

            .tips {
                margin-top: 16px;
                font-size: 14px;
                line-height: 20px;
                color: $T2;
            }
        }

        .create-success-wrapper {
            @include flex(column, flex-start, center);

            .success-animation-wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 48px;
                height: 48px;
                margin-top: 80px;
                background-color: rgba(14, 186, 82, 0.8);
                border-radius: 50%;

                .checkmark {
                    position: relative;
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    transform: scale(1.5);

                    &::after {
                        position: absolute;
                        top: 10px;
                        left: 2px;
                        width: 10px;
                        height: 18px;
                        content: " ";
                        border-top: 2px solid white;
                        border-right: 2px solid white;
                        opacity: 0;
                        transform: scaleX(-1) rotate(135deg);
                        transform-origin: left top;
                        animation-name: checkmark;
                        animation-duration: 1s;
                        animation-timing-function: ease;
                        animation-delay: 200ms;
                        animation-fill-mode: forwards;
                    }
                }
            }

            .tit {
                margin-top: 16px;
                font-size: 16px;
                line-height: 24px;
                color: $T1;
            }

            .con {
                margin-top: 8px;
                font-size: 14px;
                line-height: 20px;
                color: $T2;
            }

            .btns-wrapper {
                @include flex(row, center, center);

                margin-top: 32px;

                .abc-button {
                    margin: 0 6px;
                }
            }
        }
    }

    @keyframes checkmark {
        0% {
            width: 0;
            height: 0;
            opacity: 1;
        }

        20% {
            width: 8px;
            height: 0;
            opacity: 1;
        }

        40% {
            width: 8px;
            height: 16px;
            opacity: 1;
        }

        100% {
            width: 8px;
            height: 16px;
            opacity: 1;
        }
    }
</style>

