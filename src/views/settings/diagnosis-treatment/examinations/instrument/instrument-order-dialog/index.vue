<template>
    <abc-dialog
        :value="dialogVisible"
        title="仪器对接工单"
        content-styles="width: 660px;height: 375px;padding: 24px"
        @close-dialog="dialogVisible = false"
    >
        <div v-abc-loading="loading" class="instrument-order-dialog">
            <template v-if="dataList.length">
                <abc-button @click="handleOpenApplyOrderDialog('')">
                    申请工单
                </abc-button>
                <div
                    v-for="item in dataList"
                    :key="item.id"
                    class="order-item"
                    @click="handleOpenApplyOrderDialog(item.id,item.status)"
                >
                    <div class="top">
                        <span>
                            申请时间：{{ parseTime(item.created, 'y-m-d') }}
                        </span>
                        <span
                            class="status"
                            :class="{
                                wait: [ _OrderStatus.START, _OrderStatus.PROCESS ].includes(item.status),
                                finish: [ _OrderStatus.SUCCESS, _OrderStatus.FAIL ].includes(item.status) ,
                            }"
                        >
                            <template v-if="[ _OrderStatus.SUCCESS, _OrderStatus.FAIL ].includes(item.status)">
                                <span>{{ parseTime(item.completed, 'y-m-d') }}</span>
                            </template>
                            <span>{{ item.statusName }}</span>
                        </span>
                    </div>
                    <div class="content">
                        <div v-for="d in item.devices" :key="d.id" class="device-item">
                            <template v-if="d.name">
                                <abc-image :src="d.iconUrl || ''" width="32"></abc-image>
                                <span>
                                    {{ d.name }}
                                </span>
                            </template>
                            <template v-else>
                                <abc-icon icon="s-device-color" size="32"></abc-icon>
                                <span>{{ d.manufacture }}-{{ d.model }}</span>
                            </template>
                        </div>
                    </div>
                </div>
            </template>

            <div v-else :style="{ height: '100%' }">
                <abc-content-empty value="暂无数据">
                    <div style="margin-top: 12px;">
                        <abc-button @click="handleOpenApplyOrderDialog('')">
                            申请工单
                        </abc-button>
                    </div>
                </abc-content-empty>
            </div>
        </div>
        <apply-order-dialog
            v-if="applyOrderVisible"
            :id="operateId"
            v-model="applyOrderVisible"
            @refresh="init"
        ></apply-order-dialog>
    </abc-dialog>
</template>

<script>
    import ApplyOrderDialog from './apply-order.vue';
    import SettingAPI from 'api/settings';
    import { parseTime } from '@/filters/index';

    const OrderStatus = {
        START: 0,
        PROCESS: 10,
        SUCCESS: 20,
        FAIL: 30,
    };

    export default {
        components: {
            ApplyOrderDialog,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                applyOrderVisible: false,
                loading: false,
                dataList: [],
                operateId: '',
            };
        },
        computed: {
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input',val);
                },
            },
        },
        mounted() {
            this._OrderStatus = OrderStatus;
            this.init();
        },
        methods: {
            async init() {
                this.loading = true;
                this.dataList = await this.getInstrumentOrderList();
                this.loading = false;
            },

            async getInstrumentOrderList() {
                const data = await SettingAPI.examination.getApplyInstrumentOrderList();
                return data?.rows?.map((item) => ({
                    ...item,
                    devices: item.devices.filter((d) => !!d),
                })) || [];
            },
            handleOpenApplyOrderDialog(id, status) {
                if (status) {
                    // 申请工单流程已结束不可编辑
                    if ([ this._OrderStatus.SUCCESS, this._OrderStatus.FAIL ].includes(status)) {
                        return;
                    }
                }
                this.operateId = id;
                this.applyOrderVisible = true;
            },
            parseTime,
        },
    };
</script>

<style lang="scss" scoped>
    .instrument-order-dialog {
        height: 100%;

        .order-item {
            margin-top: 16px;
            cursor: pointer;
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            .top {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                border-bottom: 1px solid $P6;

                .status {
                    cursor: pointer;

                    &.wait {
                        color: $theme1;
                    }

                    &.finish {
                        color: $T2;
                    }
                }
            }

            .content {
                display: flex;
                flex-wrap: wrap;
                padding: 10px 12px 0;

                .device-item {
                    display: flex;
                    align-items: center;
                    height: 32px;
                    margin-bottom: 10px;

                    >span {
                        width: 150px;
                        margin-left: 8px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }

    .dialog-footer {
        text-align: right;
    }
</style>
