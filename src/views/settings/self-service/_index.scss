.main-content.self-service-wrapper {
    padding: 0 24px 24px;

    .title {
        padding: 0;
    }

    .section-title {
        padding-bottom: 8px;
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: bold;
        line-height: 14px;
        border-bottom: 1px dashed $P6;
    }

    .show-content {
        padding-bottom: 0;
        border-bottom: 0;
    }

    .abc-form section {
        padding: 24px 0 16px;

        &:first-child {
            padding-top: 0;
        }

        .setting-item {
            display: flex;
            margin-bottom: 24px;

            &:last-child {
                margin-bottom: 0;
            }

            > label {
                width: 130px;
                line-height: 18px;
                color: $T2;
            }

            &.section-title {
                margin-bottom: 12px;

                > label {
                    color: $S1;
                }
            }

            .content {
                display: flex;
                flex: 1;
                align-items: center;
                width: 0;

                > p {
                    line-height: 32px;
                }

                > .abc-checkbox-wrapper {
                    height: 18px;
                    line-height: 18px;
                    color: #000000;
                    vertical-align: middle;

                    > span {
                        font-weight: normal;
                    }
                }

                > .print-number {
                    display: inline-flex;

                    .print-desc {
                        width: 60px;
                        margin-left: 20px;
                        line-height: 18px;
                        color: #7a8794;
                    }

                    .print-count {
                        display: inline-flex;

                        .abc-radio {
                            margin-left: 15px;
                        }
                    }
                }

                .abc-tipsy--n {
                    height: 18px;
                    line-height: 18px;
                }

                > .tips {
                    display: inline-block;
                    font-weight: normal;

                    .warn-tips {
                        padding: 0 8px;
                        color: $Y2;
                    }
                }

                &.guide-message {
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: flex-start;

                    & > span {
                        font-size: 12px;
                        line-height: 32px;
                        color: #7a8794;
                    }
                }
            }

            .rule-set {
                .radio-desc {
                    .abc-radio {
                        width: 224px;
                        margin-right: 56px;
                        overflow: visible;
                        // & :last-child {
                        //     margin-right: 0;
                        // }
                    }
                }

                .pay-methods {
                    height: 32px;
                    line-height: 32px;

                    .cis-icon-jinggao1 {
                        font-size: 14px;
                        color: $T3;

                        &:hover {
                            cursor: pointer;
                        }
                    }
                }
            }

            .opening-btn {
                color: $theme2;
                cursor: pointer;
            }

            .radio-desc {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                > span {
                    font-size: 12px;
                    color: #8d9aa8;
                }

                &:first-child {
                    margin-left: 0;
                }
            }

            .content-checkbox {
                .abc-checkbox-group {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                }

                .checkbox-desc {
                    display: inline-flex;
                    align-items: center;
                    justify-content: flex-start;
                    width: 224px;
                }

                .checkbox-desc + .checkbox-desc {
                    margin-left: 72px;
                }
            }
        }
    }

    .buttons-wrapper {
        padding-top: 8px;
    }
}
