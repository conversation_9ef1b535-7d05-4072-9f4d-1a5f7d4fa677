<template>
    <biz-setting-layout class="surgery-room">
        <biz-setting-content v-abc-loading:page="loading">
            <abc-form>
                <abc-table
                    type="excel"
                    custom
                    support-delete-tr
                    delete-confirm
                    :data-list="displayRoomList"
                    style="width: 568px;"
                >
                    <abc-table-header>
                        <abc-table-td :width="408">
                            手术间名称
                        </abc-table-td>
                        <abc-table-td :width="118">
                            状态
                        </abc-table-td>
                    </abc-table-header>

                    <abc-table-body>
                        <abc-table-tr
                            v-for="row in displayRoomList"
                            :key="row.id"
                            :data-id="row.id"
                            @delete-tr="onDeleteRow(row)"
                        >
                            <abc-table-td :width="408" custom-td>
                                <abc-form-item required>
                                    <abc-input
                                        ref="feeTypeInput"
                                        v-model.trim="row.name"
                                        :max-length="15"
                                        placeholder="请输入手术间名称"
                                        :width="408"
                                        @enter="enterEvent"
                                        @change="onChange(row)"
                                    ></abc-input>
                                </abc-form-item>
                            </abc-table-td>
                            <abc-table-td :width="118" custom-td>
                                <abc-form-item required>
                                    <abc-select
                                        v-model="row.status"
                                        :width="118"
                                        @change="onChange(row)"
                                    >
                                        <abc-option :value="1" label="启用"></abc-option>
                                        <abc-option :value="0" label="停用">
                                            <abc-space>
                                                <span>停用</span>
                                                <abc-tooltip-info :z-index="9999" placement="right">
                                                    <span>停止接受手术安排， </span>
                                                    <br />
                                                    <span>不影响已安排的手术继续进行</span>
                                                </abc-tooltip-info>
                                            </abc-space>
                                        </abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-table-td>
                        </abc-table-tr>
                    </abc-table-body>
                </abc-table>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button :disabled="isEqualed" :loading="postLoading" @click="handleSaveRoom">
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import { isEqual } from 'utils/lodash.js';
    import clone from 'utils/clone';
    import { createGUID } from '@/utils';
    import SurgeryAPI from 'api/hospital/surgery';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';

    const OpTypeEnum = Object.freeze({
        ADD: 0,
        EDIT: 1,
        DELETE: 2,
    });

    export default {
        name: 'FeeType',
        components: {
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
        },
        beforeRouteLeave(to, from, next) {
            if (!this.isEqualed) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '编辑内容未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                loading: false,
                roomList: [],
                cacheList: [],
                postLoading: false,
            };
        },
        computed: {
            displayRoomList() {
                return this.roomList
                    .filter((x) => x.opType !== OpTypeEnum.DELETE)
                    .map((x, index) => ({
                        ...x,
                        rowIndex: index + 1,
                    }));
            },
            isEqualed() {
                const roomList = this.roomList.filter((x) => x.name);
                return isEqual(roomList, this.cacheList);
            },
        },
        mounted() {
            const p = this.handleFetchRoomList();
            p.then(() => {
                this._timer = setTimeout(() => {
                    this.handleAddRoom();
                });
            });
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            async handleFetchRoomList() {
                this.loading = true;

                try {
                    this.roomList = await SurgeryAPI.fetchSurgeryRoomList();
                    this.cacheList = clone(this.roomList);
                } catch (error) {
                    console.error(error);
                }

                this.loading = false;
            },

            // 新增手术室
            async handleAddRoom() {
                await this.$nextTick();
                this.roomList.push({
                    name: '',
                    status: 1,
                    scopeId: 15,
                    createdUser: '',
                    opType: OpTypeEnum.ADD,
                    id: createGUID(),
                });
                await this.$nextTick();
                this.$refs.feeTypeInput[this.displayRoomList.length - 1].focus();
            },

            // 保存手术室
            async handleSaveRoom() {
                this.postLoading = true;
                try {
                    await SurgeryAPI.saveSurgeryRoomList({
                        rows: this.roomList.filter((it) => it.name && it.opType !== OpTypeEnum.DELETE),
                    });
                    this.$Toast({
                        message: '操作成功',
                        type: 'success',
                    });
                    await this.handleFetchRoomList();
                    await this.handleAddRoom();
                } catch (error) {
                    this.$Toast({
                        message: '操作失败',
                        type: 'error',
                    });
                    console.error(error);
                } finally {
                    this.postLoading = false;
                }
            },

            enterEvent(e) {
                this.$nextTick(() => {
                    const inputs = $('.surgery-room-table .abc-input__inner').not(':disabled').not(function () {
                        const tabIndex = $(this).attr('tabindex');
                        return parseInt(tabIndex) === -1;
                    });
                    const targetIndex = inputs.index(e.target);
                    const nextInput = inputs[targetIndex + 1];
                    nextInput && this.$nextTick(() => {
                        nextInput.selectionStart = 0;
                        nextInput.selectionEnd = nextInput.value.length;
                        nextInput.select();
                        nextInput.focus();
                    });
                });
            },

            onDeleteRow(row) {
                const { id } = row;
                const index = this.roomList.findIndex((f) => f.id === id);
                const { opType } = this.roomList[index];
                if (opType === OpTypeEnum.ADD) {
                    this.roomList.splice(index, 1);
                    return;
                }
                this.$set(this.roomList[index], 'opType', OpTypeEnum.DELETE);
            },

            onChange(row) {
                const { id } = row;
                const index = this.roomList.findIndex((f) => f.id === id);
                if (this.roomList[index].opType !== OpTypeEnum.ADD) {
                    this.$set(this.roomList[index], 'opType', OpTypeEnum.EDIT);
                }
                Object.assign(this.roomList[index], row);

                // 最后一行填写后新增一行
                if (this.displayRoomList[this.displayRoomList.length - 1].name.trim()) {
                    this.handleAddRoom();
                }
            },
        },
    };
</script>
