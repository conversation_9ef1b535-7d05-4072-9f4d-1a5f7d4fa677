import PrintAPI from 'api/print.js';
import { validateName } from 'views/inventory/goods/utils.js';
export default {
    methods: {
        validateName,
        async updateTitle() {
            try {
                await PrintAPI.updateMedicalDocumentsTitle('clinic', {
                    childKeys: [
                        'print.medicineTag.title',
                        'print.patientTag.title',
                    ],
                    value: this.postData.title,
                });

                this.$store.commit('SET_PRINT_MEDICINE_TAG_CONFIG', {
                    title: this.postData.title,
                });
                this.$store.commit('SET_PRINT_PATIENT_TAG_CONFIG', {
                    title: this.postData.title,
                });
            } catch (e) {
                console.warn('更新标签抬头失败', e);
            }
        },
        handleSetAllDocuments() {
            this.$refs.printForm.validate((val) => {
                if(val) {
                    // 设置为全部医疗文书
                    this.$confirm({
                        title: `是否确认将全部标签抬头名称统一修改为“${this.postData.title}”？`,
                        type: 'warn',
                        onConfirm: async () => {
                            await this.updateTitle();
                            this.$Toast({
                                message: '设置成功',
                                type: 'success',
                            });
                        },
                    });
                }
            });
        },
    },
};



