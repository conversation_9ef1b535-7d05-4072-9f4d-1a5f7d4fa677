@import './components/preview/preview.scss';

.print-config-wrapper {
    @import './components/preview/A5-preview/index.scss';

    .checkbox-no-margin {
        width: 20%;
        margin-right: 0;
        margin-left: 0;
    }

    .checkbox-no-margin-50 {
        width: 50%;
        margin-right: 0;
    }

    .print-configs-detail {
        display: flex;
        width: 100%;

        .abc-form-item {
            margin-bottom: 12px;
        }

        .manage-section-item {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .printConfig-feeInfo-chargeItem {
            .abc-radio + .abc-radio {
                margin-left: 24px;
            }
        }

        .config-detail {
            width: 726px;
            min-width: 500px;
            max-width: 726px;
            padding: 0 0 0 24px;

            &.print-cashier-config-detail {
                .head {
                    width: 90px;
                    min-width: 90px;
                    max-width: 90px;
                }

                .second-level {
                    padding-left: 90px;

                    .manage-section-item {
                        margin-bottom: 16px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .abc-form-item {
                            margin-bottom: 0;

                            .abc-radio {
                                height: 18px;
                                margin-bottom: 8px;
                            }
                        }
                    }
                }

                .manage-section-lowVersionElectron {
                    .manage-section-item {
                        li:first-child {
                            padding-top: 2px;
                        }
                    }
                }

                .abc-radio-label {
                    color: $T1;
                }

                .clinic-info {
                    .content {
                        margin-bottom: 16px;

                        .abc-form-item {
                            margin-bottom: 8px;
                        }
                    }
                }
            }

            .abc-form {
                .manage-section {
                    .manage-section-item:last-of-type {
                        border: 0;

                        ul:last-child {
                            .row-item:last-of-type {
                                margin-bottom: 0;
                            }
                        }
                    }

                    .content-ticket {
                        .abc-form-item {
                            margin-bottom: 8px;
                        }
                    }
                }
            }
        }

        .split-line-bottom {
            border-bottom: 1px dashed $P6;
        }

        .setting-save {
            padding-top: 24px;
            border-top: 1px solid $P6;
        }

        &.registration-wrapper {
            .config-item {
                padding-top: 0;
            }
        }

        .config-item {
            display: flex;
            padding-top: 12px;

            &.split-line {
                border-bottom: 1px dashed $P6;
            }

            &.split-top-line {
                border-top: 1px dashed $P6;
            }

            &.manage-section-config {
                > ul {
                    li:first-child {
                        padding-top: 6px;
                    }
                }
            }

            .abc-checkbox__label {
                color: $T1;
            }

            .abc-radio {
                height: 24px;
                margin: 0 24px 0 0;
            }

            .head {
                width: 144px;
                min-width: 144px;
                max-width: 144px;
                color: $T2;
            }

            .tips {
                margin-top: 4px;
                font-size: 12px;
                line-height: 14px;
                color: #8d9aa8;
            }

            .desc-info {
                align-items: center;
                color: #8d9aa8;
            }

            .cis-icon-Edit_Profile {
                margin-left: 8px;
                font-size: 14px;
                color: #8d9aa8;
                cursor: pointer;
            }

            .row-item {
                width: 100%;

                &:not(:last-child) {
                    padding-bottom: 12px;
                    border-bottom: 1px dashed $P6;
                }

                &.flex-row-item {
                    .abc-form-item-content {
                        display: flex;

                        .label {
                            margin-right: 24px;
                            line-height: 24px;
                        }
                    }
                }

                .amount-radio-group {
                    display: inline-flex;
                    align-items: center;

                    .abc-radio {
                        height: 16px;
                    }
                }
            }

            .manage-section-item-abc-checkbox {
                .abc-form-item {
                    margin-bottom: 8px;
                }
            }
        }

        .second-level {
            padding-bottom: 24px;
            padding-left: 144px;

            &.split-line {
                border-bottom: 1px dashed $P6;
            }

            .config-item {
                .head {
                    width: 100px;
                    min-width: 100px;
                    max-width: 100px;
                }
            }
        }

        .price-detail-radio {
            &.abc-form-item {
                align-items: start;

                .abc-form-item-label {
                    margin-top: 8px;
                }
            }

            margin-top: 20px;
            margin-bottom: 20px;

            .abc-radio-group {
                display: flex;
                flex-direction: column;
            }

            .abc-radio {
                margin-left: 0;
                font-size: 0;
            }
        }

        .zhejiang-reg-bg-image {
            position: relative;
            //border: 1px solid #1BD1CB;
            width: 9.5cm;
            height: 9.3cm;
            background: url('~assets/images/print/zhejiang-reg.png') center no-repeat;
            background-size: cover;
        }
    }

    .medical-bill-preview-wrapper {
        padding-left: 24px;
        margin-top: 24px;

        .preview-title {
            font-size: 18px;
            color: $T2;
            text-align: center;
        }

        .medical-bill-preview {
            position: relative;
            padding-left: 0;
            margin-top: 24px;

            .back-img {
                position: relative;
                border: 1px solid $P6;

                > img {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }
            }

            .preview-content {
                color: #2a82e4;
            }

            .print-content {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        &.medical-fee-list-preview {
            //width: 250mm;

            .preview-content-wrapper {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .preview-content {
                color: $T1;
            }

            .print-content {
                position: relative;
                padding: 12px;
            }

            .medical-bill-preview {
                //border: 1px solid $T2;
            }
        }
    }

    .pc-print-config-content-wrapper {
        position: relative;
        flex: 1;
        width: 100%;
        padding-right: 24px;

        .print-config-items {
            //padding-right: 24px;
            margin-bottom: 40px;

            &.no-margin-bottom {
                margin-bottom: 0;
            }

            &.checkbox-print-config-items {
                margin-bottom: 32px;
            }
        }

        .print-form-item {
            margin-bottom: 0;
        }

        .print-manage-page {
            height: calc(100% - 36px);
            padding: 0;
            overflow-y: auto;

            @include scrollBar;
        }

        .print-config-content {
            margin-bottom: 24px;

            .abc-form {
                width: 604px;
            }
        }

        .group-title {
            padding-bottom: 12px;
            margin-bottom: 12px;
            font-weight: bold;
            color: $T1;
            border-bottom: 1px dashed $P6;
        }

        .group-item {
            display: flex;
            align-items: flex-start;

            .fee-compose-info-detail {
                font-size: 12px;
                line-height: 16px;
                color: $T2;
            }

            &:first-child {
                margin-top: 10px;
            }

            &.patient-group-item:not(:last-child) {
                margin-bottom: 16px;
            }

            &:not(:last-child) {
                margin-bottom: 24px;
            }

            &.center {
                align-items: center;
            }

            .group-label {
                width: 94px;
                min-width: 94px;
                max-width: 94px;
                line-height: 20px;
                color: $T2;

                &.middle-group-label {
                    padding: 6px 0;
                }
            }

            .print-title-wrapper {
                .print-title {
                    display: flex;
                    align-items: center;

                    &:not(:last-of-type) {
                        margin-bottom: 12px;
                    }
                }
            }

            .group-option {
                padding: 8px 0;
                margin-left: 8px;

                .subtitle-delete-button {
                    color: $R2;
                }
            }

            .group-content {
                display: flex;
                flex-wrap: wrap;
            }

            .col-group-content {
                display: flex;
                flex-direction: column;

                .abc-radio-group {
                    display: flex;
                    flex-direction: column;

                    .abc-radio {
                        &:not(:last-child) {
                            margin-bottom: 8px;
                        }

                        & + .abc-radio {
                            margin-left: 0;
                        }
                    }
                }

                .tips-info {
                    &:first-child {
                        margin-top: 4px;
                    }
                }

                .group-content {
                    margin-top: 16px;
                }
            }

            .abc-checkbox-wrapper {
                min-width: 88px;
                margin-right: 24px;
                margin-bottom: 8px;

                &.tow-col-checkbox {
                    min-width: 200px;
                }

                .abc-checkbox__label {
                    position: relative;
                    font-weight: 400;
                    color: $T1;
                }
            }

            &.cashier-radio-group {
                .abc-radio {
                    margin-right: 16px;
                }
            }

            .abc-radio {
                min-width: 88px;
                height: 20px;
                margin-right: 20px;
                margin-left: 0;

                .abc-radio-label {
                    font-weight: 400;
                }
            }

            .tips-info {
                font-size: 12px;
                color: #aab4bf;
            }

            .add-cell-input {
                width: 74px;
                height: 32px;
                margin-bottom: 12px;
                line-height: 32px;
                text-align: center;
                cursor: pointer;
                border: 1px solid $P1;

                .cis-icon-a-plus13px {
                    color: $P1;
                }

                &:hover {
                    border-color: $T3;

                    .cis-icon-a-plus13px {
                        color: $T3;
                    }
                }
            }

            .print-edit-cell-wrapper {
                margin-right: 12px;
                margin-bottom: 12px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        .print-config-footer {
            display: flex;
            padding-top: 24px;
            padding-bottom: 24px;
            margin-top: 24px;
            border-top: 1px solid $P6;
        }
    }
}
