<template>
    <biz-setting-layout>
        <biz-setting-content>
            <abc-form ref="warnSetForm" item-no-margin>
                <biz-setting-form :label-width="84">
                    <biz-setting-form-group
                        title="库存预警设置"
                    >
                        <biz-setting-form-item
                            label-line-height-size="medium"
                            label="预警规则"
                        >
                            <div class="content stock-early-warning">
                                <span class="item-content" style="margin-bottom: 0;">
                                    周转天数 不足
                                    <abc-form-item style="padding: 0 8px;" :validate-event="validateMax">
                                        <abc-input
                                            v-model="postData.stockWarnGoodsTurnoverDays"
                                            :width="60"
                                            type="number"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                        >
                                            <template #append>
                                                天
                                            </template>
                                        </abc-input>
                                    </abc-form-item>
                                    ，系统预警提示
                                </span>
                            </div>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label-line-height-size="medium"
                            label="周转天数规则"
                        >
                            <div class="content stock-early-warning">
                                <span class="item-content">
                                    周转天数=当前库存/近
                                    <abc-form-item style="padding: 0 8px;" :validate-event="validateMax">
                                        <abc-input
                                            v-model="postData.stockDaysOfDayAvgSell"
                                            :width="60"
                                            :config="{
                                                max: 120,
                                            }"
                                            type="number"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                        >
                                            <template #append>
                                                天
                                            </template>
                                        </abc-input>
                                    </abc-form-item>
                                    日均消耗量（日均消耗量包含发药、领用出库、科室消耗）
                                </span>
                            </div>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <biz-setting-form-group
                        title="效期预警设置"
                    >
                        <biz-setting-form-item
                            label-line-height-size="medium"
                            label="预警规则"
                        >
                            <div class="content">
                                <div class="clinic-item-content item-content">
                                    库存有效期 不足
                                    <abc-form-item style="padding: 0 8px;" :validate-event="validateMax">
                                        <abc-input
                                            v-model="postData.stockWarnGoodsWillExpiredMonth"
                                            :width="60"
                                            type="number"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                        >
                                            <template #append>
                                                月
                                            </template>
                                        </abc-input>
                                    </abc-form-item>
                                    ，系统预警提示
                                </div>
                            </div>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <biz-setting-form-group
                        title="采购设置"
                    >
                        <biz-setting-form-item
                            label-line-height-size="medium"
                            label="建议采购量"
                        >
                            <div class="content">
                                <div
                                    v-for="item in postData.goodsPurchaseCycleDays"
                                    :key="item.typeId"
                                    class="item-content purchase-set"
                                >
                                    <span style="display: inline-block; width: 72px; margin-right: 16px; margin-bottom: 14px;">
                                        {{ item.name }}
                                    </span>
                                    建议采购量 =（采购周期
                                    <abc-form-item style="padding: 0 8px;" :validate-event="validateMax">
                                        <abc-input
                                            v-model="item.days"
                                            :width="60"
                                            type="number"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                        ></abc-input>
                                    </abc-form-item>
                                    天 × 近 {{ postData.stockDaysOfDayAvgSell }} 天日均消耗量）- 可售库存
                                </div>
                            </div>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        :loading="btnLoading"
                        :disabled="btnDisabled"
                        @click="submit"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import {
        mapGetters, mapActions, mapState,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';

    import { BusinessKeyMap } from '@/assets/configure/buried-point';

    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';

    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';

    export default {
        name: 'ClinicWarnSetOld',
        components: {
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
        },
        data() {
            return {
                postData: {
                    disableNoStockGoods: 0, // 0:允许，会警示 1：不允许，会警示 2 允许，不警示
                    disableExpiredGoods: 0, // 过期药品能否被开出 0:允许， 1：不允许
                    stockWarnGoodsTurnoverDays: '', // 药品周转天数小于 XX 天
                    stockWarnGoodsWillExpiredMonth: '', // 药品效期不足 xxx月
                    stockDaysOfDayAvgSell: '', // 近 xxx 天的销量
                    goodsPurchaseCycleDays: [], // 建议采购量设置
                },
                cachePostData: null,
                loading: false,
                btnLoading: false,
            };
        },
        computed: {
            ...mapState({
                goodsConfigIsInit: (state) => state.inventory.goodsConfigIsInit,
            }),
            ...mapGetters([ 'goodsConfig', 'clinicConfig' ]),
            btnDisabled() {
                return isEqual(this.postData, this.cachePostData);
            },
        },
        watch: {
            goodsConfigIsInit: {
                handler(val) {
                    // 说明数据已经初始化
                    if (val) {
                        this.postData = this.goodsConfig?.stockGoodsConfig ?? {};
                        this.cachePostData = clone(this.postData);
                    }
                },
                immediate: true,
            },
        },
        methods: {
            ...mapActions(['setGoodsConfig']),
            submit() {
                this.$refs.warnSetForm.validate(async (val) => {
                    if (val) {
                        this.$abcPlatform.service.track.report(BusinessKeyMap.CLK_INVENTORY_WARNING_SETTING_SAVE);
                        try {
                            this.btnLoading = true;
                            const submitData = {};

                            this.postData.stockWarnGoodsTurnoverDays = Number(this.postData.stockWarnGoodsTurnoverDays);
                            this.postData.stockDaysOfDayAvgSell = Number(this.postData.stockDaysOfDayAvgSell);
                            this.postData.stockWarnGoodsWillExpiredMonth = Number(
                                this.postData.stockWarnGoodsWillExpiredMonth,
                            );

                            submitData.stockGoodsConfig = clone(this.postData);
                            submitData.chainReview = null;
                            submitData.subClinicPrice = null;
                            submitData.inOutTaxList = null;
                            submitData.readOnlyConfig = null;

                            await this.setGoodsConfig(submitData);
                            this.cachePostData = clone(this.postData);// 缓存灰度的值，需要用于校验是否修改
                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });

                            this.btnLoading = false;
                        } catch (e) {
                            this.$Toast({
                                message: '保存失败',
                                type: 'error',
                            });
                            this.btnLoading = false;
                        }
                    }
                });
            },

            validateMax(val, callback) {
                if (!val) {
                    callback({
                        message: '不能为空',
                        validate: false,
                    });
                } else if (+val > 999) {
                    callback({
                        message: '最大值不能超过999',
                        validate: false,
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
        },
    };
</script>
