<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        append-to-body
        size="large"
    >
        <div style="margin-bottom: 8px;">
            <span>{{ `${textBeforeText} ${contentText} 的成员` }}</span>
            <abc-button
                variant="text"
                size="small"
                @click="openAddMembersDialog()"
            >
                添加
            </abc-button>
        </div>
        <div v-if="contentTextTips" style="margin-top: -4px; margin-bottom: 8px; font-size: 12px; color: #7a8794;">
            {{ contentTextTips }}
        </div>
        <!--    已添加成员    -->
        <abc-tag-group>
            <abc-tag-v2
                v-for="(item, index) in postMembers"
                :key="item.id"
                variant="outline"
                closable
                icon="people"
                icon-size="12px"
                size="large"
                @close="deleteItem(index)"
            >
                {{ item.name }}
            </abc-tag-v2>
        </abc-tag-group>
        <!--    添加成员弹窗    -->
        <abc-dialog
            v-if="showAddMembersDialog"
            v-model="showAddMembersDialog"
            title="选择人员"
            append-to-body
            content-styles="padding: 0 0 0 24px;height: 494px;"
        >
            <abc-transfer
                v-model="selectedMembers"
                v-abc-loading="addMembersDialogLoading"
                searchable
                :data="remoteMembers"
                @search="onSearch"
            >
                <template #selected="{ item }">
                    <abc-space>
                        <abc-icon icon="people" size="12" color="#73a8e2"></abc-icon>
                        {{ item.name }}
                    </abc-space>
                </template>
            </abc-transfer>

            <div slot="footer" class="dialog-footer">
                <abc-button @click="confirmSelect">
                    确定
                </abc-button>
                <abc-button type="blank" @click="showAddMembersDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <div slot="footer" class="dialog-footer">
            <abc-button :disabled="!isUpdated" @click="$emit('confirm',postMembers)">
                确定
            </abc-button>
            <abc-button type="blank" @click="$emit('cancel')">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import ClinicAPI from 'api/clinic.js';
    import {
        debounce, compareArray,
    } from 'utils/lodash';

    export default {
        name: 'MembersSettingDialog',
        props: {
            visible: {
                type: Boolean,
                default: false,
            },
            dialogTitle: {
                type: String,
                default: '指定成员设置',
            },
            contentText: {
                type: String,
                default: '药品物资成本',
            },
            contentTextTips: {
                type: String,
                default: '',
            },
            // 传入的初始值
            members: {
                type: Array,
                default: () => [],
            },
            textBeforeText: {
                type: String,
                default: '可查看',
            },
        },
        data() {
            return {
                // 组件内部维护的-点击确定时会暴露给弹窗外部
                postMembers: [],
                // 成员列表弹窗开关
                showAddMembersDialog: false,
                // 添加成员弹窗loading
                addMembersDialogLoading: false,
                // 选中的成员
                selectedMembers: [],
                // 远程获取的全部成员
                remoteMembers: [],
                onSearch: () => {},
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.visible;
                },
                set(v) {
                    if (!v) {
                        return this.$emit('cancel', v);
                    }
                    return this.$emit('update:visible', v);
                },
            },
            isUpdated() {
                return compareArray(this.members, this.postMembers);
            },
        },
        created() {
            this.postMembers = this.members.slice();
            this.onSearch = debounce(this.openAddMembersDialog, 600, true);
        },
        methods: {
            // 打开添加成员弹窗，每次打开都获取数据，保证最新
            async openAddMembersDialog(val) {
                try {
                    this.showAddMembersDialog = true;
                    this.addMembersDialogLoading = true;
                    this.selectedMembers = this.postMembers.slice();
                    // 基于已选的项构建id字典
                    const selectedMembersMap = this.selectedMembers.reduce((res, item) => {
                        res.set(item.id, item);
                        return res;
                    }, new Map());
                    // 获取全部成员
                    const { data } = await ClinicAPI.getClinicEmployee(false, val);
                    // 勾选列表
                    this.remoteMembers = (data?.rows ?? []).map((item) => {
                        if (selectedMembersMap.get(item.id)) {
                            item.checked = true;
                        }
                        return item;
                    });

                } catch (e) {
                    console.log(e);
                } finally {
                    this.addMembersDialogLoading = false;
                }
            },
            // 确定选择添加成员
            confirmSelect() {
                this.postMembers = this.selectedMembers.slice();
                this.showAddMembersDialog = false;
            },
            // 取消选择添加成员
            deleteItem(index) {
                const members = this.postMembers.slice();
                members.splice(index, 1);
                this.postMembers = members;
            },
        },
    };
</script>
