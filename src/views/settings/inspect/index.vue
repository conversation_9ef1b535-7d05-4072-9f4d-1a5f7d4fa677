<template>
    <biz-fill-remain-height>
        <template #header>
            <abc-manage-tabs :option="tabsOption" @change="handleTabsChange"></abc-manage-tabs>
        </template>

        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import AbcAccess from '@/access/utils';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        computed: {
            ...mapGetters(['isChainAdmin']),
            ...mapGetters('viewDistribute',[
                'featureInspectReportSync',
            ]),
            tabsOption() {
                const { inspect = {} } = getViewDistributeConfig().Settings;
                const { autoReservationSettingVisible } = inspect;

                const _arr = [
                    {
                        label: '检查设置',
                        value: 'inspectRule',
                    },
                    {
                        label: '报告模板设置',
                        value: '@inspectReportTemplate',
                        isHidden: this.isChainAdmin,
                    },
                    {
                        label: '诊断意见设置',
                        value: '@inspectDiagnosisAdvice',
                        isHidden: this.isChainAdmin,
                    },
                    {
                        label: '报告自动上传',
                        value: 'inspectFileSync',
                        isHidden: !this.featureInspectReportSync,
                    },
                    {
                        label: 'PACS服务设置', value: 'inspectPACS',
                    },
                    {
                        label: '自动预约登记设置',
                        value: 'autoReservation',
                        isHidden: !autoReservationSettingVisible &&
                            !AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
                    },
                    {
                        label: '检查单号设置', value: '@inspectNo',
                    },
                ].filter((item) => !item.isHidden);

                return _arr;
            },
        },
        methods: {
            handleTabsChange(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>
