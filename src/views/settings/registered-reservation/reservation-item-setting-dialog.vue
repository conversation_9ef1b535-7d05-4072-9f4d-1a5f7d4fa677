<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            title="设置预约项目"
            :before-close="handleClose"
            class="reservation-item-dialog"
            size="huge"
            append-to-body
            content-styles="padding:0; height:700px;"
        >
            <reservation-item-list
                :visit-service-duration="visitServiceDuration"
                :revisited-service-duration="revisitedServiceDuration"
                :registration-type="registrationType"
                :available-role-ids="availableRoleIds"
                :table-config="tableConfig"
            ></reservation-item-list>
        </abc-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import ReservationItemList from 'views/settings/registered-reservation/reservation-item-list';

    export default {
        name: 'ReservationItemSettingDialog',
        components: {
            ReservationItemList,
        },
        props: {
            value: Boolean,
            registrationType: {
                type: Number,
                required: true,
            },
            visitServiceDuration: Number,
            revisitedServiceDuration: Number,
            availableRoleIds: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                tableConfig: [
                    {
                        key: 'displayName',
                        label: '可预约项目',
                        style: {
                            flex: 1,
                        },
                    },
                    {
                        label: '分类',
                        key: 'registrationProductTypeNames',
                        style: {
                            flex: 'none',
                            width: '100px',
                        },
                    },
                    {
                        label: this.registrationType === 0 ? '可约医生' : '可约理疗师',
                        key: 'doctorNumber',
                        style: {
                            flex: 'none',
                            width: '100px',
                        },
                    },
                    {
                        label: '服务时长',
                        key: 'displayServiceDuration',
                        style: {
                            flex: 'none',
                            width: '120px',
                        },
                    },
                    {
                        label: '价格',
                        key: 'price',
                        style: {
                            flex: 'none',
                            width: '100px',
                            textAlign: 'right',
                        },
                    },
                    {
                        label: '状态',
                        key: 'disable',
                        style: {
                            flex: 'none',
                            width: '80px',
                            textAlign: 'center',
                        },
                    },
                ],
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input',val);
                },
            },
        },
        methods: {
            handleClose() {
                this.$emit('products-count');
                this.showDialog = false;
            },
        },
    };
</script>
