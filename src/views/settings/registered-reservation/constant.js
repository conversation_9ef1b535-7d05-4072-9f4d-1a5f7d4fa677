export const RESERVATION_TYPE = {
    OUTPATIENT_RESERVATION: 0, // 门诊预约类型
    TREATMENT_RESERVATION: 1, // 治疗理疗预约类型
};

export const SCHEDULES_TYPE = {
    OUTPATIENT: 0, // 门诊排班
    TREATMENT: 1, // 理疗师排班
    STAFF: -1, // 员工排班
};

// 预约模式
export const RESERVATION_MODE_TYPE = {
    FIXED_NUMBER: 0, // 固定号源
    FLEXIBLE_TIME: 1, // 灵活时间
};

// 预约人员
export const RESERVATION_EMPLOYEE = {
    NOT_MUST: 1, // 可不指定
    MUST: 0, // 必须指定
};

// 号数确定时机
export const GENERATE_ORDER_NO_TIME_TYPE = {
    REGISTRATION: 0, // 预约取号
    SIGN_IN: 10, // 签到取号
};

// 预约时间
export const RESERVATION_TIME_TYPE = {
    ACCURATE: 1, // 精确时间预约
    OTHER: 0, // 其他(分段、上、下午、晚上)
};

// 预约时间段类型 固定号源预约模式服务类型
export const SERVICE_TYPE_ENUM = {
    MORNING_AFTERNOON_EVENING: 0, // 按上午/下午/晚上
    ACCURATE_TIME: 1, // 按精确时间段预约;
    CUSTOM_TIME: 2, // 按自定义时段
};

export const GENERATE_ORDER_NO_TIME_ENUM = {
    REGISTRATION: 0, // 预约取号
    SIGN_IN: 10, // 签到取号
};

export const wechatPeriodOptions = [
    {
        value: '1 天',
        label: '1 天',
    },
    {
        value: '1 周',
        label: '1 周',
    },
    {
        value: '1 个月',
        label: '1 个月',
    },
];
export const expireOptions = [
    {
        value: '24 小时',
        label: '24 小时后',
    },
    {
        value: '48 小时',
        label: '48 小时后',
    },
    {
        value: '2 天',
        label: '第二天',
    },
    {
        value: '3 天',
        label: '第三天',
    },
];
export const CloseReserveTimeOptions = [
    {
        value: '10 分钟',
        label: '前10分钟',
    },
    {
        value: '15 分钟',
        label: '前15分钟',
    },
    {
        value: '20 分钟',
        label: '前20分钟',
    },
    {
        value: '30 分钟',
        label: '前半小时',
    },
    {
        value: '60 分钟',
        label: '前1小时',
    },
    {
        value: '120 分钟',
        label: '前2小时',
    },
    {
        value: '180 分钟',
        label: '前3小时',
    },
    {
        value: '360 分钟',
        label: '前6小时',
    },
    {
        value: '720 分钟',
        label: '前12小时',
    },
    {
        value: '1080 分钟',
        label: '前18小时',
    },
];
export const minOptions = [
    {
        value: 4,
        label: '4 分钟',
    },
    {
        value: 5,
        label: '5 分钟',
    },
    {
        value: 10,
        label: '10 分钟',
    },
    {
        value: 15,
        label: '15 分钟',
    },
    {
        value: 20,
        label: '20 分钟',
    },
    {
        value: 30,
        label: '30 分钟',
    },
    {
        value: 40,
        label: '40 分钟',
    },
    {
        value: 50,
        label: '50 分钟',
    },
    {
        value: 60,
        label: '60 分钟',
    },
];
export const timeOptions = [
    {
        value: '15 分钟',
        label: '15 分钟',
    },
    {
        value: '30 分钟',
        label: '30 分钟',
    },
    {
        value: '1 小时',
        label: '1 小时',
    },
    {
        value: '2 小时',
        label: '2 小时',
    },
    {
        value: '3 小时',
        label: '3 小时',
    },
    {
        value: '6 小时',
        label: '6 小时',
    },
    {
        value: '12 小时',
        label: '12 小时',
    },
    {
        value: '24 小时',
        label: '24 小时',
    },
    {
        value: '48 小时',
        label: '48 小时',
    },
    {
        value: '72 小时',
        label: '72 小时',
    },
    {
        value: '4 天',
        label: '4 天',
    },
    {
        value: '5 天',
        label: '5 天',
    },
    {
        value: '7 天',
        label: '7 天',
    },
    {
        value: '10 天',
        label: '10 天',
    },
    {
        value: '15 天',
        label: '15 天',
    },
    {
        value: '20 天',
        label: '20 天',
    },
    {
        value: '30 天',
        label: '30 天',
    },
    {
        value: '60 天',
        label: '60 天',
    },
];

export const wechatReserveRefundRateOptions = [
    {
        value: 100,
        label: '100%',
    },
    {
        value: 90,
        label: '90%',
    },
    {
        value: 80,
        label: '80%',
    },
    {
        value: 70,
        label: '70%',
    },
    {
        value: 60,
        label: '60%',
    },
    {
        value: 50,
        label: '50%',
    },
    {
        value: 40,
        label: '40%',
    },
    {
        value: 30,
        label: '30%',
    },
    {
        value: 20,
        label: '20%',
    },
    {
        value: 10,
        label: '10%',
    },
    {
        value: 5,
        label: '5%',
    },
    {
        value: 0,
        label: '0%',
    },
];

export const limitCountOptions = [
    {
        value: 1,
        label: '1 次',
    },
    {
        value: 2,
        label: '2 次',
    },
    {
        value: 3,
        label: '3 次',
    },
    {
        value: 4,
        label: '4 次',
    },
    {
        value: 5,
        label: '5 次',
    },
    {
        value: 6,
        label: '6 次',
    },
    {
        value: 7,
        label: '7 次',
    },
    {
        value: 8,
        label: '8 次',
    },
    {
        value: 9,
        label: '9 次',
    },
    {
        value: 10,
        label: '10 次',
    },
];

// 阶段退费规则
export const defaultSteppedRefundRates = [
    {
        minAheadTime: {
            day: 0,
            hour: 0,
            min: 15,
        },
        refundRate: 0,
    },
    {
        minAheadTime: {
            day: 0,
            hour: 0,
            min: 15,
        },
        refundRate: 0,
    },
];

// 爽约时间选项
export const wechatBreakAppointmentPeriodOptions = [
    {
        value: '7 天',
        label: '7 天',
    },
    {
        value: '15 天',
        label: '15 天',
    },
    {
        value: '1 个月',
        label: '1 个月',
    },
    {
        value: '2 个月',
        label: '2 个月',
    },
    {
        value: '3 个月',
        label: '3 个月',
    },
    {
        value: '6 个月',
        label: '6 个月',
    },
];

// 爽约触发次数选项
export const wechatBreakAppointmentLimitCountOptions = [
    {
        value: 1,
        label: '1 次',
    },
    {
        value: 2,
        label: '2 次',
    },
    {
        value: 3,
        label: '3 次',
    },
    {
        value: 4,
        label: '4 次',
    },
    {
        value: 5,
        label: '5 次',
    },
    {
        value: 6,
        label: '6 次',
    },
];

// 爽约时间选项
export const wechatBreakAppointmentLockTimeOptions = [
    {
        value: '1 周',
        label: '1 周',
    },
    {
        value: '2 周',
        label: '2 周',
    },
    {
        value: '1 个月',
        label: '1 个月',
    },
    {
        value: '2 个月',
        label: '2 个月',
    },
    {
        value: '3 个月',
        label: '3 个月',
    },
    {
        value: '6 个月',
        label: '6 个月',
    },
];

export const modeOptions = [
    {
        label: '半小时',
        value: 30,
    },
    {
        label: '1小时',
        value: 60,
    },
    {
        label: '1.5小时',
        value: 90,
    },
    {
        label: '2小时',
        value: 120,
    },
];
