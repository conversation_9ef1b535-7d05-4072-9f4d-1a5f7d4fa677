<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="小程序备案"
        content-styles="padding:24px 24px;width: 900px;max-width: 900px;"
        custom-class="create-we-clinic_filing--dialog"
    >
        <step-title :step="step" :own-step-list="ownStepList" style="padding: 0 120px;"></step-title>
        <div class="create-we-clinic_filing--dialog-box">
            <filing-box :filings-status="filingsStatus"></filing-box>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button v-if="filingsStatus === 1" @click="handleFiling">
                前往备案
            </abc-button>
            <abc-button v-else-if="[3, 5].includes(filingsStatus)" @click="goToWechat">
                前往微信公众平台
            </abc-button>
            <abc-button type="blank" @click="openFilingLead">
                查看备案教程
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import FilingBox from './filing/index';
    import StepTitle from './step-title';
    import {
        geyMcWeappIcpEnTranceInfo, goToWechat, openFilingLead, getWeappFilingStep,
    } from 'utils/handle-we-clinic-open-process';
    import { windowOpen } from '@/core/navigate-helper';
    export default {
        name: 'FilingDialog',
        components: {
            FilingBox,
            StepTitle,
        },
        props: {
            value: Boolean,
        },
        data() {
            return {
                ownStepList: [{
                                  name: '前往微信备案',
                                  step: 1,
                              },
                              {
                                  name: '微信平台初审',
                                  step: 2,
                              },
                              {
                                  name: '管局审核',
                                  step: 3,
                              },
                              {
                                  name: '完成',
                                  step: 4,
                              }],
                filingsStatus: 1024,
            };
        },
        computed: {
            step() {
                return getWeappFilingStep(this.filingsStatus);
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        async created() {
            await this.geyMcWeappIcpEnTranceInfo();
        },
        methods: {
            goToWechat,
            openFilingLead,
            goToRecordGuidelines() {
                windowOpen(`//${window.location.host}/record-guidelines`);
            },
            async geyMcWeappIcpEnTranceInfo() {
                const res = await geyMcWeappIcpEnTranceInfo(false);
                this.filingsStatus = res?.filingsStatus || 0;
            },
            async handleFiling() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '为了避免审核失败，备案前请务必认真阅读小程序备案教程',
                    confirmText: '查看备案教程',
                    cancelText: '我已知晓，直接前往',
                    onConfirm: async () => {
                        this.openFilingLead();
                    },
                    onCancel: async () => {
                        this.goToWechat();
                    },
                });
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.create-we-clinic_filing--dialog {
    max-height: 700px;

    &-box {
        min-height: 412px;
    }
}
</style>
