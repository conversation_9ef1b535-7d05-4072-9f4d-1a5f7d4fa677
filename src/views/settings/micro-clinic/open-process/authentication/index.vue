<template>
    <div class="authentication-step_box">
        <div v-if="currentStep === 1" class="authentication-step_box--text">
            因腾讯要求，小程序必须认证才能正常使用，请前往微信公众平台完成认证
        </div>
        <div v-else-if="currentStep === 2" class="authentication-step_box--text">
            <div class="authentication-step_box--text-title">
                <abc-icon
                    size="16"
                    icon="time1"
                    style="margin-right: 8px;"
                    color="#FF9933"
                ></abc-icon>小程序认证审核
            </div>
            请前往微信公众平台查看审核进度
        </div>
        <div v-else-if="currentStep === 3" class="authentication-step_box--text">
            <div class="authentication-step_box--text-title">
                <abc-icon
                    size="16"
                    icon="time1"
                    style="margin-right: 8px;"
                    color="#FF9933"
                ></abc-icon>小程序认证被打回
            </div>
            请前往微信公众平台，重新填写资料
        </div>
        <div v-else-if="currentStep === 4" class="authentication-step_box--text">
            <div class="authentication-step_box--text-title">
                <abc-icon
                    size="16"
                    icon="time1"
                    style="margin-right: 8px;"
                    color="#FF9933"
                ></abc-icon>小程序认证失败
            </div>
            请前往微信公众平台，重新提交认证
        </div>
    </div>
</template>

<script>
    export default {
        name: 'index.vue',
        props: {
            currentStep: {
                type: Number,
                default: 1,
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.authentication-step_box {
    display: flex;
    justify-content: space-around;
    width: 100%;
    height: 100%;
    padding-top: 53px;
    font-size: 14px;
    line-height: 20px;
    color: $T1;
    text-align: center;

    &--text {
        font-size: 14px;
        font-weight: normal;

        &-title {
            height: 22px;
            margin-bottom: 17px;
            font-size: 16px;
            font-weight: bold;
            line-height: 22px;
        }
    }
}
</style>
