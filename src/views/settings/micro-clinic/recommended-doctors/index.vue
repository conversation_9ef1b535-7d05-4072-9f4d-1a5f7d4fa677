<template>
    <manage-page :content-styles="contentStyles" style="padding: 0;" class="manage-page_recommended-doctors">
        <abc-flex ref="recommendDoctor" style="height: 100%;">
            <abc-form ref="recommendDoctorForm" style=" flex: 1; padding: 24px;">
                <abc-tips-card-v2 theme="primary" style="margin-bottom: 24px;">
                    设置推荐后，可在医生主页查看其他推荐医生
                </abc-tips-card-v2>

                <biz-setting-form :label-width="72">
                    <biz-setting-form-item label="标题">
                        <abc-form-item :validate-event="handleValidateName">
                            <abc-input
                                v-model.trim="detail.name"
                                :width="224"
                                :max-length="20"
                                trim
                                type="text"
                            >
                            </abc-input>
                        </abc-form-item>
                    </biz-setting-form-item>

                    <biz-setting-form-item label="设置推荐">
                        <abc-table
                            type="excel"
                            :style="tableStyle"
                            :render-config="tableHeader"
                            :data-list.sync="detail.employeeDetailSuggestEmployees"
                            support-delete-tr
                            :show-hover-tr-bg="false"
                            :custom-tr-key="customTrKey"
                            :loading="tableLoading"
                            show-order
                            @delete-tr="handleClickTr"
                        >
                            <template #footer>
                                <abc-button
                                    variant="text"
                                    style="margin: 0 auto;"
                                    @click="handleAddRule"
                                >
                                    新增推荐规则
                                </abc-button>
                            </template>

                            <template #employeeId="{ trData }">
                                <abc-form-item :validate-event="handleValidateEmployee">
                                    <abc-select
                                        v-model="trData.employeeId"
                                        :width="156"
                                        :inner-width="156"
                                        with-search
                                        :fetch-suggestions="handleSearch"
                                        size="medium"
                                    >
                                        <abc-option
                                            v-for="doctor in displayDoctors"
                                            :key="doctor.id"
                                            :label="doctor.name"
                                            :value="doctor.id"
                                            :disabled="selectedDoctorIds.includes(doctor.id) && doctor.id !== trData.employeeId"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </template>

                            <template #suggestEmployees="{ trData }">
                                <abc-form-item style="width: 100%;" :validate-event="handleValidateSuggestEmployee" :validate-params="trData">
                                    <abc-select
                                        v-model="trData.suggestEmployees"
                                        :inner-width="200"
                                        adaptive-width
                                        with-search
                                        :fetch-suggestions="handleSearch"
                                        multiple
                                        :multiple-limit="9"
                                        multi-label-mode="tag"
                                        :max-tag="3"
                                        :tag-max-width="78"
                                        size="medium"
                                    >
                                        <abc-option
                                            v-for="doctor in displayDoctors"
                                            :key="doctor.id"
                                            :label="doctor.name"
                                            :value="doctor.id"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </template>
                        </abc-table>
                    </biz-setting-form-item>
                </biz-setting-form>
            </abc-form>

            <mobile-preview style="border-left: 1px solid var(--abc-color-P7);">
                <div
                    slot-scope="{
                        height,
                    }"
                    :style="{
                        height: `${height}px`,
                        overflow: 'hidden',
                    }"
                >
                    <img src="~assets/images/micro-clinic/clinic-decoration/recommend-doctor.png" alt="" style="max-width: 100%;" />
                </div>
            </mobile-preview>
        </abc-flex>

        <abc-flex slot="footer" :gap="8" style="padding: 0 24px;">
            <abc-button
                :loading="btnLoading"
                :disabled="!isChange || btnLoading"
                @click="handleSave"
            >
                保存
            </abc-button>
            <abc-button
                variant="ghost"
                :disabled="!isChange || btnLoading"
                @click="cancelModify"
            >
                取消
            </abc-button>
        </abc-flex>
    </manage-page>
</template>

<script>
    import ManagePage from 'views/settings/components/manage-page.vue';
    import WeClinicAPI from 'api/we-clinic';
    import Clone from 'utils/clone.js';
    import { createGUID } from 'utils/index';
    import { isEqual } from 'utils/lodash';
    import MobilePreview from 'views/settings/components/mobile-preview.vue';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import ResizeObserver from 'resize-observer-polyfill';


    export default {
        name: 'RecommendedDoctors',
        components: {
            ManagePage, MobilePreview, BizSettingForm, BizSettingFormItem,
        },
        beforeRouteLeave(to, form, next) {
            if (this.isChange) {
                this.$modal({
                    content: '你的修改内容还未保存，确定离开？',
                    title: '保存提示',
                    type: 'warn',
                    showIcon: true,
                    onConfirm: () => {
                        next(true);
                    },
                    onCancel: () => {
                        next(false);
                    },
                });
            } else {
                next(true);
            }
        },

        data() {
            return {
                btnLoading: false,
                tableLoading: false,

                doctors: [],

                detail: {
                    name: '推荐医生',
                    employeeDetailSuggestEmployees: [],
                },

                cacheDetail: {
                    name: '推荐医生',
                    employeeDetailSuggestEmployees: [],
                },

                searchKey: '',

                screenSize: 'large', // large | small
            };
        },

        computed: {
            contentStyles() {
                return 'height:calc(100vh - 180px);';
            },

            isChange() {
                return !isEqual(this.detail, this.cacheDetail);
            },

            tableHeader() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: '医生',
                            key: 'employeeId',
                            style: {
                                width: '156px',
                                maxWidth: '156px',
                            },

                            // eslint-disable-next-line no-unused-vars
                            headerAppendRender: (h, config) => {
                                return (
                                    <abc-tooltip-info placement="top-start" content="设置在哪个医生详情页展示推荐" style="margin-left: 4px;">
                                    </abc-tooltip-info>
                                );
                            },
                        },
                        {
                            label: '推荐',
                            key: 'suggestEmployees',
                            style: {
                                flex: 1,
                                minWidth: '300px',
                            },
                        },
                    ],
                };
            },

            selectedDoctorIds() {
                return this.detail.employeeDetailSuggestEmployees.map((item) => item.employeeId);
            },

            displayDoctors() {
                if (!this.searchKey) return this.doctors;

                return this.doctors.filter((doctor) => {
                    const keyword = this.searchKey.toLocaleUpperCase();
                    return (
                        (doctor.name && doctor.name.indexOf(keyword) !== -1) ||
                        (doctor.namePy && doctor.namePy.toLocaleUpperCase().indexOf(keyword) !== -1) ||
                        (doctor.namePyFirst && doctor.namePyFirst.toLocaleUpperCase().indexOf(keyword) !== -1)
                    );
                });
            },

            tableStyle() {
                const base = {
                    width: '100%',
                    fontSize: '14px',
                };

                if (this.screenSize === 'small') {
                    return {
                        ...base,
                        height: '407px',
                    };
                }

                return {
                    ...base,
                    height: '489px',
                };
            },
        },

        async created() {
            try {
                const { data } = await WeClinicAPI.getDoctorSort();
                this.doctors = data?.doctors || [];

                await this.loadData();
            } catch (e) {
                console.error(e);
            }
        },

        async mounted() {
            await this.$nextTick();

            this.handleScreenSize();

            this.resizeObserver = new ResizeObserver(this.handleScreenSize);

            const dom = this.$refs.recommendDoctor?.$el;
            if (dom) {
                this.resizeObserver.observe(dom);
            }
        },

        beforeDestroy() {
            this.resizeObserver?.disconnect();
        },

        methods: {
            async handleScreenSize() {
                const height = this.$refs.recommendDoctor?.$el.offsetHeight || 660;
                this.screenSize = height <= 660 ? 'small' : 'large';
            },

            async loadData() {
                try {
                    this.tableLoading = true;
                    const { data } = await WeClinicAPI.fetchDoctorRecommendList();

                    if (!data) {
                        return;
                    }

                    data.employeeDetailSuggestEmployees = (data.employeeDetailSuggestEmployees || []).map((o) => {
                        return {
                            ...o,
                            suggestEmployees: o.suggestEmployees.map((m) => m.id),
                        };
                    });

                    this.detail = data;
                    this.cacheDetail = Clone(data);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.tableLoading = false;
                }
            },

            async saveData() {
                const {
                    name, employeeDetailSuggestEmployees,
                } = this.detail;
                await WeClinicAPI.updateDoctorRecommendList({
                    name,
                    employeeReqs: employeeDetailSuggestEmployees.map((o) => {
                        return {
                            employeeId: o.employeeId,
                            suggestEmployeeIds: o.suggestEmployees,
                        };
                    }),
                });
            },

            handleSearch(keyword) {
                this.searchKey = keyword;
            },

            customTrKey(item) {
                return item.keyId || item.employeeId;
            },

            handleAddRule() {
                this.detail.employeeDetailSuggestEmployees.push({
                    employeeId: '',
                    employeeName: '',
                    keyId: createGUID(),
                    suggestEmployees: [],
                });
            },

            handleClickTr(index) {
                this.detail.employeeDetailSuggestEmployees.splice(index, 1);

                this.detail.employeeDetailSuggestEmployees = [...this.detail.employeeDetailSuggestEmployees];
            },

            handleValidateName(value, callback) {
                if (!value) {
                    callback({
                        validate: false,
                        message: '标题不能为空',
                    });
                } else {
                    callback({ validate: true });
                }
            },

            handleValidateEmployee(value, callback) {
                if (!value) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                } else {
                    callback({ validate: true });
                }
            },

            handleValidateSuggestEmployee(value, callback, params) {
                const {
                    suggestEmployees, employeeId,
                } = params;
                if (!suggestEmployees.length) {
                    return callback({
                        validate: false,
                        message: '推荐医生不能为空',
                    });
                }

                if (suggestEmployees.includes(employeeId)) {
                    return callback({
                        validate: false,
                        message: '推荐医生不能包含左侧医生',
                    });
                }

                callback({ validate: true });
            },


            handleSave() {
                this.$refs.recommendDoctorForm.validate(async (valid) => {
                    if (!valid) {
                        return;
                    }

                    try {
                        this.btnLoading = true;
                        await this.saveData();
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.cacheDetail = Clone(this.detail);
                    } catch (e) {
                        console.error(e);
                    } finally {
                        this.btnLoading = false;
                    }
                });
            },

            cancelModify() {
                this.detail = Clone(this.cacheDetail);
            },
        },
    };
</script>

