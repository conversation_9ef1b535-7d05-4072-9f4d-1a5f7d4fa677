<template>
    <abc-flex vertical class="announcement-report">
        <abc-flex>
            <abc-flex
                flex="1"
                vertical
                style="height: calc(100vh - 186px); padding: 32px 24px 24px; overflow-y: auto;"
                :gap="32"
            >
                <slot></slot>

                <abc-form
                    ref="announcementReport"
                    label-position="left-top"
                    :label-width="110"
                    item-no-margin
                >
                    <abc-flex vertical :gap="24">
                        <abc-form-item label="就诊报告展示" help=" 开启后，患者可在微诊所上查看就诊报告">
                            <abc-checkbox
                                v-model="config.enableShow"
                                type="number"
                            >
                                开启
                            </abc-checkbox>
                        </abc-form-item>

                        <abc-flex
                            v-if="config.enableShow"
                            vertical
                            :gap="24"
                        >
                            <abc-divider margin="none" style="margin-top: 18px;"></abc-divider>

                            <abc-flex :gap="24" vertical>
                                <abc-flex vertical>
                                    <abc-text bold>
                                        付款前
                                    </abc-text>
                                </abc-flex>

                                <abc-flex vertical :gap="19">
                                    <abc-form-item label="病历信息">
                                        <abc-checkbox-group v-model="config.beforePaid.showMedicalRecord">
                                            <abc-checkbox
                                                v-for="(o, key) in medicalRecordsOptionList"
                                                :key="key"
                                                :label="o.value"
                                                type="number"
                                            >
                                                {{ o.label }}
                                            </abc-checkbox>
                                        </abc-checkbox-group>
                                    </abc-form-item>

                                    <abc-form-item label="检查检验">
                                        <abc-radio-group v-model="config.beforePaid.showExaminationItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目详情及检查检验报告
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"检查检验"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item :label="treatmentText">
                                        <abc-radio-group v-model="config.beforePaid.showTreatItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"{{ treatmentText }}"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item v-if="isNeedNursingItem" label="护理">
                                        <abc-radio-group v-model="config.beforePaid.showNursingItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"护理"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="套餐">
                                        <abc-radio-group v-model="config.beforePaid.showComposeItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                展示套餐及子项
                                            </abc-radio>
                                            <abc-radio :label="3">
                                                合并展示为"套餐"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="材料商品">
                                        <abc-radio-group v-model="config.beforePaid.showDrugMaterialItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"材料商品"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item
                                        v-if="isNeedOther"
                                        label="其他"
                                    >
                                        <abc-radio-group v-model="config.beforePaid.showOtherItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"其他"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>
                                </abc-flex>

                                <div style="padding-left: 110px;">
                                    <abc-divider variant="dashed" margin="none"></abc-divider>
                                </div>

                                <abc-flex vertical :gap="19">
                                    <abc-form-item label="中西成药处方">
                                        <abc-radio-group v-model="config.beforePaid.showWesternMedicine">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"中西成药处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="中药处方">
                                        <abc-radio-group v-model="config.beforePaid.showChinese">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio v-if="showMergeChinesePrescriptionSetting" :label="2">
                                                合并展示为"中药处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="输液处方">
                                        <abc-radio-group v-model="config.beforePaid.showInfusion">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"输液处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="外治处方">
                                        <abc-radio-group v-model="config.beforePaid.showExternal">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"外治处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>
                                </abc-flex>

                                <div style="padding-left: 110px;">
                                    <abc-divider variant="dashed" margin="none"></abc-divider>
                                </div>

                                <abc-form-item label="费用信息">
                                    <abc-checkbox-group v-model="config.beforePaid.showFeeInfo">
                                        <abc-checkbox :label="0" type="number">
                                            金额
                                        </abc-checkbox>
                                        <abc-checkbox :label="1" type="number">
                                            折扣优惠
                                        </abc-checkbox>
                                    </abc-checkbox-group>
                                </abc-form-item>
                            </abc-flex>

                            <abc-divider margin="none"></abc-divider>

                            <abc-flex :gap="24" vertical>
                                <abc-flex vertical>
                                    <abc-text bold>
                                        付款后
                                    </abc-text>
                                </abc-flex>

                                <abc-flex vertical :gap="19">
                                    <abc-form-item label="病历信息">
                                        <abc-checkbox-group v-model="config.afterPaid.showMedicalRecord">
                                            <abc-checkbox
                                                v-for="(o, key) in medicalRecordsOptionList"
                                                :key="key"
                                                :label="o.value"
                                                type="number"
                                            >
                                                {{ o.label }}
                                            </abc-checkbox>
                                        </abc-checkbox-group>
                                    </abc-form-item>

                                    <abc-form-item label="检查检验">
                                        <abc-radio-group v-model="config.afterPaid.showExaminationItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目详情及检查检验报告
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"检查检验"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item :label="treatmentText">
                                        <abc-radio-group v-model="config.afterPaid.showTreatItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"{{ treatmentText }}"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item
                                        v-if="isNeedNursingItem"
                                        label="护理"
                                    >
                                        <abc-radio-group v-model="config.afterPaid.showNursingItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"护理"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="套餐">
                                        <abc-radio-group v-model="config.afterPaid.showComposeItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                展示套餐及子项
                                            </abc-radio>
                                            <abc-radio :label="3">
                                                合并展示为"套餐"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="材料商品">
                                        <abc-radio-group v-model="config.afterPaid.showDrugMaterialItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"材料商品"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item
                                        v-if="isNeedOther"
                                        label="其他"
                                    >
                                        <abc-radio-group v-model="config.afterPaid.showOtherItem">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"其他"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>
                                </abc-flex>

                                <div style="padding-left: 110px;">
                                    <abc-divider variant="dashed" margin="none"></abc-divider>
                                </div>


                                <abc-flex vertical :gap="19">
                                    <abc-form-item label="中西成药处方">
                                        <abc-radio-group v-model="config.afterPaid.showWesternMedicine">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"中西成药处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="中药处方">
                                        <abc-radio-group v-model="config.afterPaid.showChinese">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio v-if="showMergeChinesePrescriptionSetting" :label="2">
                                                合并展示为"中药处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="输液处方">
                                        <abc-radio-group v-model="config.afterPaid.showInfusion">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"输液处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>

                                    <abc-form-item label="外治处方">
                                        <abc-radio-group v-model="config.afterPaid.showExternal">
                                            <abc-radio v-if="isDisabled" :label="0">
                                                不展示
                                            </abc-radio>
                                            <abc-radio :label="1">
                                                展示项目明细
                                            </abc-radio>
                                            <abc-radio :label="2">
                                                合并展示为"外治处方"
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>
                                </abc-flex>

                                <div style="padding-left: 110px;">
                                    <abc-divider variant="dashed" margin="none"></abc-divider>
                                </div>

                                <abc-flex vertical :gap="19">
                                    <abc-form-item label="费用信息">
                                        <abc-checkbox-group v-model="config.afterPaid.showFeeInfo">
                                            <abc-checkbox :label="0" type="number">
                                                金额
                                            </abc-checkbox>

                                            <abc-checkbox :label="1" type="number">
                                                折扣优惠
                                            </abc-checkbox>
                                        </abc-checkbox-group>
                                    </abc-form-item>
                                </abc-flex>
                            </abc-flex>
                        </abc-flex>
                    </abc-flex>
                </abc-form>
            </abc-flex>
            <mobile-preview style="height: calc(-186px + 100vh); border-left: 1px solid var(--abc-color-P7);">
                <div>
                    <div class="tab-box">
                        <report-mobile-view v-if="tabCheck === 0" :medical-record-options="medicalRecordOptions" :config="config.beforePaid"></report-mobile-view>
                        <report-mobile-view v-else :medical-record-options="medicalRecordOptions" :config="config.afterPaid"></report-mobile-view>
                    </div>
                </div>

                <abc-tabs-v2
                    slot="content"
                    v-model="tabCheck"
                    size="small"
                    type="outline"
                    :option="[
                        {
                            label: '付款前',
                            value: 0,
                        },
                        {
                            label: '付款后',
                            value: 1,
                        },
                    ]"
                >
                </abc-tabs-v2>
            </mobile-preview>
        </abc-flex>

        <abc-divider margin="none"></abc-divider>

        <abc-flex :gap="8" style="padding: 24px;">
            <abc-button
                :loading="isLoading"
                :disabled="!isChange || isLoading"
                @click="handleSave"
            >
                保存
            </abc-button>
            <abc-button
                variant="ghost"
                :disabled="!isChange || isLoading"
                @click="cancelModify"
            >
                取消
            </abc-button>
        </abc-flex>
    </abc-flex>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import { McConfigAPI } from 'views/settings/micro-clinic/core/mc-config-api.js';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import ReportMobileView from '../common/report-mobile-view';
    import MobilePreview from 'views/settings/components/mobile-preview.vue';

    export default {
        name: 'AnnouncementReport',
        components: {
            MobilePreview,
            ReportMobileView,
        },
        data() {
            const {
                medicalRecordOptions = [], medicalRecordsOtherOptions = [],
            } = getViewDistributeConfig().WeClinic;
            const defaultConfig = {
                enableShow: 1,
                beforePaid: {
                    showChinese: 1,
                    showComposeItem: 1,
                    showDrugMaterialItem: 1,
                    showExaminationItem: 1,
                    showExternal: 1,
                    showInfusion: 1,
                    showOtherItem: 1,
                    showTreatItem: 1,
                    showNursingItem: 1,
                    showWesternMedicine: 1,
                    showMedicalRecord: [0,1,2],
                    showFeeInfo: [0,1],
                },
                afterPaid: {
                    showChinese: 1,
                    showComposeItem: 1,
                    showDrugMaterialItem: 1,
                    showExaminationItem: 1,
                    showExternal: 1,
                    showInfusion: 1,
                    showOtherItem: 1,
                    showTreatItem: 1,
                    showNursingItem: 1,
                    showWesternMedicine: 1,
                    showMedicalRecord: [0,1,2],
                    showFeeInfo: [0,1],
                },
            };
            return {
                medicalRecordOptions,
                medicalRecordsOtherOptions,
                isLoading: false,
                config: defaultConfig,
                isStart: 0,//是否初始化了
                // 设置的参数
                resetConfig: defaultConfig,
                cloneConfig: defaultConfig,
                tabCheck: 0,
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'isOpenMp', 'clinicConfig', 'isAdmin']),
            ...mapGetters('weClinic', ['hideNoDisplayOption']),
            ...mapGetters('viewDistribute', ['featureFeeCompose', 'viewDistributeConfig']),
            showMergeChinesePrescriptionSetting() {
                return this.viewDistributeConfig.Settings.showMergeChinesePrescriptionSetting;
            },
            isNeedNursingItem() {
                const { microClinic } = getViewDistributeConfig().Settings;
                return microClinic.isNeedNursingItem;
            },
            treatmentText() {
                const { microClinic } = getViewDistributeConfig().Settings;
                return microClinic.treatmentText;
            },
            isNeedOther() {
                const { microClinic } = getViewDistributeConfig().Settings;
                return microClinic.isNeedOther;
            },
            isChange() {
                const config = Clone(this.config);
                const cloneConfig = Clone(this.cloneConfig);
                config.beforePaid.showMedicalRecord = this.sortCheckBoxGroup(config.beforePaid.showMedicalRecord);
                config.beforePaid.showFeeInfo = this.sortCheckBoxGroup(config.beforePaid.showFeeInfo);
                config.afterPaid.showMedicalRecord = this.sortCheckBoxGroup(config.afterPaid.showMedicalRecord);
                config.afterPaid.showFeeInfo = this.sortCheckBoxGroup(config.afterPaid.showFeeInfo);
                cloneConfig.beforePaid.showMedicalRecord = this.sortCheckBoxGroup(cloneConfig.beforePaid.showMedicalRecord);
                cloneConfig.beforePaid.showFeeInfo = this.sortCheckBoxGroup(cloneConfig.beforePaid.showFeeInfo);
                cloneConfig.afterPaid.showMedicalRecord = this.sortCheckBoxGroup(cloneConfig.afterPaid.showMedicalRecord);
                cloneConfig.afterPaid.showFeeInfo = this.sortCheckBoxGroup(cloneConfig.afterPaid.showFeeInfo);
                return !isEqual(config,cloneConfig);
            },
            // 展示不展示选项，目前针对海云儿科
            isDisabled() {
                return !this.hideNoDisplayOption;
            },
            medicalRecordsOptionList() {
                if (this.medicalRecordOptions?.length && this.medicalRecordsOtherOptions?.length) {
                    return [{
                        label: `${this.medicalRecordOptions.map((item) => {
                            return item.label;
                        }).join('、')}等`,
                        value: 0,
                    }].concat(this.medicalRecordsOtherOptions);
                }
                return [];
            },
        },
        // 监听页面设置的变化切换到对应页面
        watch: {
            config: {
                handler (val) {
                    if (!this.isStart) {
                        return false;
                    }
                    if (!isEqual(this.resetConfig.beforePaid, val.beforePaid)) {
                        this.tabCheck = 0;
                    }
                    if (!isEqual(this.resetConfig.afterPaid, val.afterPaid)) {
                        this.tabCheck = 1;
                    }
                    this.resetConfig = Clone(val);
                },
                deep: true,
            },
        },
        async created() {
            await this.$store.dispatch('weClinic/fetchHideNoDisplayOption');
            await this.fetchOutpatientReportConfig();
        },
        updated () {
            this.$emit('updateHomepageConfig', this.isChange);
        },
        methods: {
            sortCheckBoxGroup(checkList) {
                if (checkList?.length <= 1) {
                    return checkList;
                }
                return checkList.sort((a,b) => {
                    return a - b;
                });
            },
            cancelModify() {
                this.config = Clone(this.cloneConfig);
            },
            changeCheck(val) {
                this.tabCheck = val;
            },
            // 获取就诊报告配置信息
            async fetchOutpatientReportConfig() {
                try {
                    const data = await McConfigAPI.getOutpatientReport();
                    this.config = data?.outpatientReport || this.config;
                    this.cloneConfig = Clone(this.config);
                    this.resetConfig = Clone(this.config);
                    this.isStart = 1;
                } catch (e) {
                    console.error(e);
                }

            },
            // 保存内容
            async handleSave() {
                try {
                    const data = await McConfigAPI.setOutpatientReport({ outpatientReport: this.config });
                    if (data.outpatientReport) {
                        this.$Toast({
                            message: '保存成功',
                            type: 'success',
                        });
                        this.cloneConfig = Clone(this.config);
                    }
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.announcement-report {
    .tab-box {
        background-color: $S2;

        .tab-box-choice {
            display: flex;
            justify-content: space-between;
            width: 116px;
            height: 20px;
            margin-bottom: 14px;
            margin-left: 102px;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            color: $T2;
            cursor: pointer;
        }

        .announcement_payment_tab {
            color: $S1;
        }
    }
}
</style>
