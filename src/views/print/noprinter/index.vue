<template>
    <abc-dialog 
        v-if="visible" 
        v-model="visible" 
        title=" "
        class="noprinter-dialog"
        @close-dialog="close">

        <div>

            <div class="noprinter-icon">
                <i class="iconfont cis-icon-Attention"></i>
            </div>
            
            <p class="noprinter-tip">请连接小票打印机</p>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button @click="ok">知道了</abc-button>
        </div>

    </abc-dialog>
</template>

<script type="text/babel">
    export default {
        data() {
            return {
                visible: false,
            };
        },

        methods: {
            

            ok() {
                this.close();
                console.warn('知道了');
                if(this.confirm) {
                    this.confirm();
                }

                /// 关闭清除 instances
                if (typeof this.onClose === 'function') {
                    this.onClose( this );
                }
            },


            close() {
                this.visible = false;
                this.clearTask && this.clearTask();
            },

        },

    };
</script>

<style lang="scss">
    @import '~styles/theme.scss';
    .noprinter-dialog {
        text-align: center;
        .abc-dialog {
            width: 360px;
        }
        .abc-dialog-body {
            width: 360px;
            box-sizing: border-box;
        }
        .noprinter-icon {
            margin: 24px 0 16px;
            i {
                font-size: 32px;
                color: #FF9933;
            }
        }
        .noprinter-tip {
            line-height: 22px;
            font-size: 16px;
            color: #000;
            margin-bottom: 34px;
        }
        .dialog-footer {
            .abc-button {
                width: 100%;
            }
        }
    }
</style>

