.settlement-dialog-wrapper {
    .settlement-item {
        margin-bottom: 16px;

        .item-label {
            margin-bottom: 4px;
            line-height: 22px;
            color: $T2;
        }

        .item-content {
            &.amount-content {
                display: flex;
                align-items: start;
                line-height: 1;
                color: $T2;

                > span {
                    display: flex;
                    margin-right: 24px;
                    line-height: 16px;
                }

                .amount {
                    font-size: 16px;
                    font-weight: bold;
                    color: $Y2;
                }
            }

            &.associated-invoices {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
            }

            > .abc-button {
                margin-bottom: 8px;
            }

            .abc-table-wrapper {
                border: 1px solid $P6;
                border-radius: var(--abc-border-radius-small);

                .table-title {
                    border-top: none;
                }

                .table-body {
                    height: auto;
                    min-height: auto;

                    .table-tr {
                        .delete-icon {
                            opacity: 0;

                            &.show {
                                opacity: 1;
                            }
                        }

                        &:hover .delete-icon {
                            opacity: 1;
                        }

                        &:last-child {
                            border-bottom: none;
                        }
                    }
                }
            }

            .total-info-wrapper {
                width: 100%;
                padding: 2px 12px;

                .total-info {
                    color: $T2;
                    text-align: right;

                    .money {
                        font-weight: bold;
                        color: $T1;
                    }
                }
            }
        }

        .add-invoice {
            width: 150px;
            height: 84px;
            padding-top: 24px;
            margin-bottom: 8px;
            font-size: 12px;
            color: #8d9aa8;
            text-align: center;
            cursor: pointer;
            border: 1px solid $P1;

            .iconfont {
                font-size: 14px;
                color: $P1;
            }

            > p {
                margin-top: 8px;
            }
        }
    }

    .settlement-enclosure {
        padding-top: 24px;
        border-top: 1px dashed $P6;

        .table-body .table-tr {
            color: $T2;
        }
    }

    .settlement-detail-content {
        display: flex;
        flex-direction: column;
        height: 100%;

        .settlement-detail-form {
            flex: 1;
        }
    }
}

.invoice-wrapper {
    align-items: center;
    width: 224px;
    margin-top: 0 !important;
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;

    &:nth-of-type(5n) {
        margin-right: 0;
    }

    .invoice-content {
        height: 54px;
        padding: 8px;
        font-size: 12px;
        color: $T2;
    }

    .invoice-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
        padding: 0 12px;
        background: #f9fafc;
        border-top: 1px solid $P6;
        border-bottom-right-radius: var(--abc-container-border-radius);
        border-bottom-left-radius: var(--abc-container-border-radius);
    }
}

.invoice-dialog-wrapper.abc-dialog-invoice-wrapper {
    width: 360px;

    .abc-dialog-header {
        padding: 10px 40px 10px 24px !important;
    }

    .abc-dialog-body {
        min-height: 160px;
    }

    .abc-form-item-label > .label-name {
        padding-right: 8px;
    }
}

//关联单据 弹窗
.associated-order-wrapper {
    .tips-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 46px;
        background-color: #e0efff;

        > div {
            .iconfont {
                font-size: 14px;
                color: $T3;
            }

            span {
                color: $T2;

                &:last-child {
                    margin-left: 24px;
                }
            }
        }
    }

    .pagination-wrapper {
        margin-top: 0;
    }

    .select-bar {
        display: flex;
        align-items: center;
        height: 64px;
    }

    .associated-order {
        > ul {
            border: 1px solid $P6;
            border-radius: 2px;
        }

        .order-item {
            position: relative;
            height: 72px;
            padding: 16px 16px 16px 48px;
            cursor: pointer;
            border-bottom: 1px solid $P6;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: $P4;

                .hover-btn {
                    opacity: 1;
                }
            }

            .order-info {
                display: flex;
                align-items: center;
                font-size: 16px;
                color: $T2;

                > span {
                    margin-right: 16px;
                }

                > .order-no {
                    font-weight: bold;
                    color: $T1;
                }
            }

            .total-info {
                display: flex;
                align-items: center;
                font-size: 14px;
                color: $T2;

                > span {
                    margin-right: 16px;
                }
            }

            .seleced-status {
                position: absolute;
                top: 28px;
                left: 16px;
            }

            .hover-btn {
                opacity: 0;
            }
        }

        .associated-order-popper {
            .detail {
                color: $B1;

                &:focus {
                    outline: none;
                }
            }
        }

        .empty-order {
            position: relative;

            > div {
                position: absolute;
                top: 150px;
                left: 50%;
                transform: translateX(-50%);

                .icon {
                    height: 48px;
                    overflow: hidden;
                    text-align: center;

                    i {
                        font-size: 48px;
                        color: #dddddd;
                    }
                }

                .label {
                    margin-top: 8px;
                    color: rgb(170, 170, 170);
                    text-align: center;
                }
            }
        }
    }

    .abc-table-normal-wrapper {
        .abc-table-footer {
            min-height: unset;
            padding: 12px;
        }
    }
}

.order-list-wrapper {
    box-sizing: border-box;
    max-height: 320px;
    overflow-y: overlay;
    font-size: 14px;

    .list-header {
        display: flex;
        align-items: center;
        padding-bottom: 8px;
        color: $T1;
        text-align: left;
        border-bottom: 1px solid $P6;

        .name {
            flex: 1;
        }
    }

    .list-body {
        height: auto;

        .table-tr {
            display: flex;
            align-items: center;
            height: 36px;
            color: $T2;

            .name {
                flex: 1;
                color: $T1;
            }
        }
    }

    .spec,
    .count {
        width: 80px;
        min-width: 80px;
        max-width: 80px;
    }

    .manufacturer,
    .supplier {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
    }
}

// 结算明细弹窗
.settlement-detail-dialog-wrapper {
    .abc-form .abc-form-item {
        margin-right: 0;

        .abc-form-item-label {
            padding-right: 16px;
        }
    }
}

@media screen and (max-width: 1024px) {
    .invoice-wrapper {
        &:nth-of-type(5n) {
            margin-right: 8px;
        }
    }
}
