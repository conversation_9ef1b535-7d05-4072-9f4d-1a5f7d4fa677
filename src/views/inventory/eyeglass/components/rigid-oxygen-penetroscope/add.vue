<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        title="新建硬性透氧镜"
        :is-scroll="false"
        content-styles="width:1194px;padding:24px;"
        :before-close="beforeClose"
    >
        <eyeglass-core
            ref="eyeglassCoreRef"
            :type-id="typeId"
            :sub-type="subType"
        ></eyeglass-core>
        <div slot="footer" class="dialog-footer">
            <abc-button
                type="primary"
                :loading="btnLoading"
                :disabled="continueBtnLoading"
                @click="onSubmit"
            >
                完成
            </abc-button>
            <abc-button
                type="blank"
                :loading="continueBtnLoading"
                :disabled="btnLoading"
                @click="onSaveContinueSubmit"
            >
                保存并继续
            </abc-button>
            <abc-button type="blank" :disabled="btnLoading || continueBtnLoading" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import {
        GoodsTypeEnum, GoodsSubTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';

    import EyeglassCore from 'views/inventory/eyeglass/components/eyeglass-core.vue';
    import SubmitMixin from 'views/inventory/eyeglass/mixins';

    export default {
        name: 'EyeglassDialog',
        components: {
            EyeglassCore,
        },
        mixins: [SubmitMixin],
        props: {
            visible: Boolean,
            // eslint-disable-next-line vue/require-default-prop
            refresh: Function,
            // eslint-disable-next-line vue/require-default-prop
            close: Function,
            // eslint-disable-next-line vue/require-default-prop
            updateList: Function,
        },
        data() {

            return {
                showDialog: this.visible,
                typeId: GoodsTypeIdEnum.RIGID_LENS,
                subType: GoodsSubTypeEnum[GoodsTypeEnum.EYEGLASSES].RIGID_LENS,
            };
        },
        created() {

        },
        methods: {
            beforeClose() {
                console.log('beforeClose');
                if (this.btnLoading) {
                    return;
                }
                this.showDialog = false;
            // this.$confirm({
            //     type: 'warn',before
            //     title: '提示',
            //     content: '你的编辑内容尚未保存，确定离开？',
            //     onConfirm: () => {
            //         this.showDialog = false;
            //     },
            // });
            },

        },
    };
</script>
