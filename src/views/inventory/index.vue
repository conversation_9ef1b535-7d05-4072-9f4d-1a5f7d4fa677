<template>
    <abc-container v-abc-loading.coverOpaque="pageLoading" class="abc-inventory-container-v2 goods-inventory">
        <abc-flex style="flex: 1; width: 100%;" vertical>
            <abc-card
                v-if="currentPharmacy"
                :border="false"
                :shadow="false"
                style="border-radius: 0;"
            >
                <abc-flex class="goods-inventory-tab">
                    <abc-dropdown
                        v-if="(hasVirtualPharmacy || multiPharmacyCanUse)"
                        :max-width="138"
                        :min-width="138"
                        class="inventory-pharmacy-dropdown"
                        custom-class="inventory-pharmacy-options-wrapper"
                        placement="bottom-start"
                        @change="handlePharmacyType"
                    >
                        <div slot="reference" :class="{ 'is-virtual': currentPharmacy.type === PharmacyTypeEnum.VIRTUAL_PHARMACY }" class="pharmacy-type-dropdown">
                            <span class="down-text">{{ curPharmacyName }}</span>
                            <div class="down-img"></div>
                        </div>
                        <template v-if="multiPharmacyCanUse">
                            <abc-dropdown-item
                                v-if="pharmacyTypeList0.length && pharmacyTypeList2.length"
                                :value="9999"
                                class="title-option"
                                disabled
                                label="本地库房"
                                style="line-height: 20px;"
                            >
                            </abc-dropdown-item>
                            <abc-dropdown-item
                                v-for="item in pharmacyTypeList0"
                                :key="item.no"
                                :class="{ 'is-selected': currentPharmacy.no === item.no }"
                                :label="item.name"
                                :value="item.no"
                            >
                            </abc-dropdown-item>
                            <abc-dropdown-item
                                v-if="pharmacyTypeList2.length"
                                :value="9998"
                                class="title-option"
                                disabled
                                label="代煎代配"
                                style="line-height: 20px;"
                            >
                            </abc-dropdown-item>
                            <abc-dropdown-item
                                v-for="item in pharmacyTypeList2"
                                :key="item.no"
                                :class="{ 'is-selected ': currentPharmacy.no === item.no }"
                                :label="item.name"
                                :value="item.no"
                            >
                            </abc-dropdown-item>
                            <abc-dropdown-item
                                v-if="pharmacyDisabledList.length"
                                :value="9997"
                                class="title-option"
                                disabled
                                label="停用库房"
                                style="line-height: 20px;"
                            >
                            </abc-dropdown-item>
                            <abc-dropdown-item
                                v-for="item in pharmacyDisabledList"
                                :key="item.no"
                                :class="{ 'is-selected ': currentPharmacy.no === item.no }"
                                :label="item.name"
                                :value="item.no"
                            >
                            </abc-dropdown-item>
                        </template>
                        <template v-else>
                            <abc-dropdown-item
                                v-for="item in pharmacyUserList"
                                :key="item.no"
                                :class="{ 'is-selected ': currentPharmacy.no === item.no }"
                                :label="item.typeName"
                                :value="item.no"
                            >
                            </abc-dropdown-item>
                        </template>
                    </abc-dropdown>
                    <abc-tabs-v2
                        v-if="tabConf.length"
                        :key="`${!!(hasVirtualPharmacy || multiPharmacyCanUse)}`"
                        :border="false"
                        :option="tabConf"
                        :style="{ 'padding-left': (hasVirtualPharmacy || multiPharmacyCanUse) ? '16px' : '8px' }"
                        size="huge"
                        style="flex: 1;"
                        @change="handleTabsChange"
                    >
                    </abc-tabs-v2>
                </abc-flex>
            </abc-card>

            <abc-card
                v-if="!pageLoading"
                :border="false"
                :shadow="false"
                style="flex: 1; overflow-y: auto; border-top: 1px solid var(--abc-color-P8); border-radius: 0;"
            >
                <router-view
                    :key="`${$route.path}/${currentPharmacyId}`"
                    :pharmacy-no="currentPharmacy?.no"
                    :pharmacy-type="currentPharmacy?.type"
                ></router-view>
            </abc-card>
        </abc-flex>
    </abc-container>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { getRouterChildren } from 'router/filter-router';
    import { PharmacyTypeEnum } from '@abc/constants';
    import {
        _CURRENT_STOCK_ROOM_ID_, MODULE_ID_MAP,
    } from 'utils/constants.js';
    import {
        SUMMARY_PHARMACY, SUMMARY_PHARMACY_ID, SUMMARY_PHARMACY_NO,
    } from './constant.js';
    import LocalStore from 'utils/localStorage-handler';
    import AbcUiThemeMixin from 'views/common/abc-ui-theme-mixin';

    const permissionMap = Object.freeze({
        // moduleId
        [MODULE_ID_MAP.goodsIn]: 'showPurchase',
        [MODULE_ID_MAP.inventoryApplyIn]: 'showPharmacyTrans',
        [MODULE_ID_MAP.goodsTrans]: 'showTrans',
        [MODULE_ID_MAP.inventoryLossOut]: 'showLossOut',
        [MODULE_ID_MAP.goodsCheck]: 'showCheck',
        [MODULE_ID_MAP.goodsProductionOut]: 'showProductionOut',
        // routeName
        'goodsIn': 'showPurchase',
        'goodsApply': 'showPharmacyTrans',
        'goodsTrans': 'showTrans',
        'goodsOut': 'showLossOut',
        'goodsCheck': 'showCheck',
        'goodsProductionOut': 'showProductionOut',

    });
    export default {
        name: 'InventoryPage',
        mixins: [AbcUiThemeMixin],
        data() {
            return {
                PharmacyTypeEnum,
                goodsTabs: {},
                pageLoading: false,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'currentClinic',
                'isChainAdmin',
                'clinicConfig',
                'inventoryTodo',
                'isChain', // 连锁
                'isSingleStore',
                'currentPharmacy',
                'currentPharmacyId',
                'multiPharmacyCanUse',// 是否开启多库房
                'pharmacyList',// 全部库房列表
                'pharmacyUserList',// 当前用户所在的库房列表（包含虚拟库房）
                'currentPharmacyModulePermission',
            ]),
            ...mapGetters('virtualPharmacy', ['virtualPharmacyClinicList']),
            todoCount() {
                return this.inventoryTodo;
            },

            tabConf() {
                const { routes } = this.$router.options;
                let routeTab = getRouterChildren(routes, '库存管理');
                routeTab = routeTab.filter((item) => {
                    return !item.meta.hidden;
                });

                routeTab = routeTab.map((item) => {
                    let noticeNumber = '';
                    switch (item.name) {
                        case 'purchase':
                            noticeNumber = this.todoCount?.purchaseTodoCount ?? '';
                            break;
                        case 'goodsApply':
                            noticeNumber = this.todoCount?.stockReceptTodoCount ?? '';
                            break;
                        case 'goodsIn':
                            noticeNumber = this.todoCount?.stockInTodoCount ?? '';
                            break;
                        case 'goodsOut':// 诊所管家出库只剩报损了
                            noticeNumber = this.todoCount?.stockLossOutTodoCount ?? '';
                            break;
                        case 'goodsTrans':
                            noticeNumber = this.todoCount?.stockTransTodoCount ?? '';
                            break;
                        case 'goodsCheck':
                            noticeNumber = this.todoCount?.stockCheckTodoCount ?? '';
                            break;
                        case 'settlementReview':
                            noticeNumber = this.todoCount?.settlementTodoCount ?? '';
                            break;
                        default:
                            noticeNumber = '';
                            break;
                    }
                    return {
                        label: item.meta.name,
                        value: item.name,
                        moduleId: item.meta.moduleId,
                        noticeNumber,
                    };
                });
                // 判断是否是虚拟库房（代煎代配库房），只保留药品、入库、供应商模块
                if (this.isVirtualPharmacy) {
                    routeTab = routeTab.filter((tab) => {
                        return tab.moduleId === MODULE_ID_MAP.goods ||
                            tab.moduleId === MODULE_ID_MAP.goodsIn ||
                            tab.moduleId === MODULE_ID_MAP.supplier;
                    });
                    return routeTab;
                }

                return routeTab.filter((tab) => {
                    // 空白页不显示在tab栏中
                    if (tab.value === 'goodsBlank') return false;
                    // 部分模块需要根据药房是否支持来判断是否显示
                    const key = permissionMap[tab.moduleId];
                    // 汇总药房全显示
                    if (key && this.currentPharmacy?.no !== SUMMARY_PHARMACY_NO) {
                        return this.currentPharmacyModulePermission[key];
                    }
                    return true;
                });
            },
            /**
             * @desc 虚拟库房
             */
            isVirtualPharmacy() {
                return this.currentPharmacy?.type === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            hasVirtualPharmacy() {
                return this.virtualPharmacyClinicList && this.virtualPharmacyClinicList.length;
            },
            pharmacyTypeList() {
                if (this.multiPharmacyCanUse && this.pharmacyUserList.length === this.pharmacyList.length) {
                    return [SUMMARY_PHARMACY, ...this.pharmacyUserList];
                }
                return this.pharmacyUserList;
            },
            pharmacyDisabledList() {
                return this.pharmacyTypeList.filter((p) => p.status === 0);
            },
            pharmacyEnabledList() {
                return this.pharmacyTypeList.filter((p) => p.status === 1);
            },
            pharmacyTypeList0() {
                return this.pharmacyEnabledList.filter((p) => p.type === PharmacyTypeEnum.LOCAL_PHARMACY);
            },
            pharmacyTypeList2() {
                return this.pharmacyEnabledList.filter((p) => p.type === PharmacyTypeEnum.VIRTUAL_PHARMACY);
            },

            curPharmacyName() {
                return this.multiPharmacyCanUse ? this.currentPharmacy?.name : this.currentPharmacy?.typeName;
            },
        },
        async created() {
            this.pageLoading = true;
            try {
                this.fetchStockEmployeeListIfNeed();

                await Promise.all([
                    this.$store.dispatch('virtualPharmacy/fetchPharmacyTypeClinicList'),
                    this.$store.dispatch('fetchGoodsPrimaryClassificationIfNeed'),
                    this.$store.dispatch('initTraceCodeConfig'),
                    this.$store.dispatch('initGoodsConfig'),
                ]);
                // 如果拥有汇总库房
                if (this.multiPharmacyCanUse && this.pharmacyUserList.length === this.pharmacyList.length && !LocalStore.get(_CURRENT_STOCK_ROOM_ID_, true)) {
                    // 选中汇总库房
                    this.$store.dispatch('selectStockRoomId', SUMMARY_PHARMACY_ID);
                } else {
                    // 执行后必须选中一个库房
                    this.$store.dispatch('selectStockRoomId', this.currentPharmacyId);
                }
                this.$store.dispatch('fetchInventoryTodo', this.currentPharmacy?.no);
                this.navigateToFirstTab();
            } catch (e) {
                console.error(e);
            } finally {
                this.pageLoading = false;
            }
        },
        methods: {
            ...mapActions([
                'fetchStockEmployeeListIfNeed',
            ]),
            handleTabsChange(index, item) {
                this.$router.push({
                    name: item.value,
                });
            },
            handlePharmacyType(val) {
                const curPharmacy = this.pharmacyTypeList.find((item) => {
                    return item.no === val;
                });
                // 可能选中汇总库房，也可能一个库房没有
                this.$store.dispatch('selectStockRoomId', curPharmacy?.id);
                this.$store.dispatch('fetchInventoryTodo', val);
                this.navigateToFirstTab();
            },
            navigateToFirstTab() {
                const { name } = this.$route;

                const hasRoutePermission = this.tabConf.some((tab) => tab.value === name);

                if (hasRoutePermission) {
                    this.$router.replace({ name });
                    return;
                }
                // 无权限需要重定向页面
                this.$router.replace({
                    name: this.tabConf[0]?.value ?? 'goodsBlank',
                });

            },

        },
    };
</script>
