<template>
    <abc-dialog
        v-model="showDialog"
        size="large"
        title="绑定供应商商品"
        content-styles="height: 240px"
        append-to-body
        :disabled-keyboard="disabledKeyboard"
        @open="pushDialogName"
        @close="popDialogName"
    >
        <abc-form
            ref="form"
            label-position="left-top"
            :label-width="86"
            item-no-margin
        >
            <abc-flex style="margin-bottom: 16px;">
                <abc-text theme="gray" size="normal" style="width: 86px;">
                    系统商品
                </abc-text>
                <abc-flex vertical>
                    <abc-text
                        theme="black"
                        size="normal"
                        bold
                        tag="div"
                    >
                        {{ goods.displayName }}
                    </abc-text>
                    <abc-space :size="4">
                        <abc-text theme="gray" size="mini" tag="div">
                            {{ goods.displaySpec }}
                        </abc-text>
                        <abc-text theme="gray" size="mini" tag="div">
                            {{ goods.manufacturer }}
                        </abc-text>
                    </abc-space>
                    <abc-text
                        v-if="goods.medicineNmpn"
                        theme="gray"
                        size="mini"
                        tag="div"
                    >
                        批准文号：{{ goods.medicineNmpn }}
                    </abc-text>
                </abc-flex>
            </abc-flex>
            <abc-form-item label="供应商商品" required hidden-red-dot>
                <abc-flex vertical>
                    <abc-space is-compact>
                        <abc-select
                            v-model="postData.supplierId"
                            custom-class="supplierWrapper"
                            with-search
                            clearable
                            placeholder="选择供应商"
                            :width="254"
                            inner-width="254px"
                            placement="bottom-end"
                            :fetch-suggestions="fetchSuppliers"
                            @change="handleSupplierChange"
                        >
                            <abc-option
                                v-for="it in supplierOptions"
                                :key="`${it.id }`"
                                :value="it.id"
                                :label="it.name"
                            ></abc-option>
                        </abc-select>

                        <abc-autocomplete
                            v-model.trim="postData.supplierGoodsId"
                            inner-width="544px"
                            :width="254"
                            :delay-time="0"
                            :async-fetch="true"
                            :fetch-suggestions="fetchData"
                            :max-length="20"
                            focus-show
                            clearable
                            placeholder="输入供应商ERP商品编码/名称"
                            placement="bottom-end"
                            @clear="handleClear"
                            @enterEvent="handleSelect"
                        >
                            <template slot="suggestion-header">
                                <abc-space style="height: 30px; padding: 0 8px;">
                                    <div class="name" style="width: 100px;">
                                        ERP编码
                                    </div>
                                    <div class="age" style="width: 166px;">
                                        商品名称
                                    </div>
                                    <div class="age" style="width: 110px;">
                                        规格
                                    </div>
                                    <div class="age" style="width: 120px;">
                                        生产厂家
                                    </div>
                                </abc-space>
                            </template>
                            <template slot="suggestions" slot-scope="props">
                                <dt
                                    class="suggestions-item"
                                    :class="{ selected: props.index == props.currentIndex }"
                                    @click="handleSelect(props.suggestion)"
                                >
                                    <div style="flex: 0 0 auto; width: 100px; margin-right: 8px;">
                                        {{ props.suggestion.supplierGoodsId }}
                                    </div>
                                    <div style="width: 166px; margin-right: 8px;">
                                        {{ props.suggestion.name }}
                                    </div>
                                    <div style="width: 110px; margin-right: 8px;">
                                        {{ props.suggestion.displaySpec }}
                                    </div>
                                    <div style="width: 120px;">
                                        {{ props.suggestion.manufacturerFull }}
                                    </div>
                                </dt>
                            </template>
                        </abc-autocomplete>
                    </abc-space>
                    <div
                        v-if="supplierGoods"
                        style="display: flex;
                                flex-direction: column;
                                gap: var(--abc-space-s, 4px);
                                align-items: flex-start;
                                padding: var(--abc-paddingTB-m, 8px) var(--abc-paddingLR-m, 8px);
                                margin-top: 10px;
                                background: var(--abc-color-AbcDivGrey, #f9fafc);
                                border: 1px dashed var(--abc-color-P7, #e0e5ee);
                                border-radius: var(--abc-border-radius-small, 6px);
"
                    >
                        <abc-text theme="black" size="mini" tag="div">
                            {{ supplierGoods.name || '' }}
                        </abc-text>
                        <abc-space :size="4">
                            <abc-text theme="gray" size="mini" tag="div">
                                {{ supplierGoods.displaySpec || '' }}
                            </abc-text>
                            <abc-text theme="gray" size="mini" tag="div">
                                {{ supplierGoods.manufacturerFull || '' }}
                            </abc-text>
                        </abc-space>
                        <abc-text
                            v-if="supplierGoods.medicineNmpn"
                            theme="gray"
                            size="mini"
                            tag="div"
                        >
                            批准文号：{{ supplierGoods.medicineNmpn || '' }}
                        </abc-text>
                    </div>
                </abc-flex>
            </abc-form-item>
        </abc-form>

        <template #footer>
            <abc-flex flex="1" justify="flex-end">
                <abc-space>
                    <abc-button :loading="btnLoading" :disabled="disabledBtn" @click="handleBind">
                        绑定
                    </abc-button>
                    <abc-button variant="ghost" @click="showDialog = false">
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';

    export default {
        name: 'BindGoodsDialog',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            goods: {
                type: Object,
                default: () => ({}),
            },
            boundSupplierGoods: {
                type: Object,
                default: null,
            },
            opType: {
                type: Number,
                default: 0,
            },
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('绑定供应商商品');

            return {
                disabledKeyboard, pushDialogName, popDialogName,
            };
        },
        data() {
            return {
                showDialog: this.value,
                btnLoading: false,
                supplierOptions: [],
                supplier: {},
                postData: {
                    supplierId: '',
                    supplierGoodsId: '',
                },
                supplierGoods: this.boundSupplierGoods,
            };
        },
        computed: {
            ...mapGetters([
                'supplierList',
            ]),
            disabledBtn() {
                if (this.boundSupplierGoods?.supplierGoodsId === this.postData.supplierGoodsId && this.boundSupplierGoods?.supplierId === this.postData.supplierId) {
                    return true;
                }
                return false;
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
            boundSupplierGoods: {
                handler(val) {
                    if (val) {
                        this.postData.supplierId = val.supplierId;
                        this.postData.supplierGoodsId = val.supplierGoodsId;
                        if (val.supplierId && val.supplierGoodsId) {
                            this.fetchSupplierGoods();
                        }
                    }
                },
                deep: true,
                immediate: true,
            },
        },
        created() {
            this.fetchSuppliers();

            // 优化：自动选中供应商
            if (!this.postData.supplierGoodsId && this.supplierOptions.length === 1) {
                this.postData.supplierId = this.supplierOptions[0].id;
            }
        },
        methods: {
            async fetchSuppliers(key = '') {
                this.supplierOptions = this.supplierList.filter((item) => {
                    return item.abcSupplierId && item.name.includes(key.trim());
                });
            },
            async fetchData(keyword = '', callback) {
                if (!this.postData.supplierId || !keyword) {
                    return callback([]);
                }
                const res = await GoodsAPIV3.searchSupplierGoods({
                    keyword,
                    limit: 50,
                    offset: 0,
                    supplierId: this.postData.supplierId,
                });
                return callback(res?.data?.rows || []);
            },
            async fetchSupplierGoods() {
                const res = await GoodsAPIV3.searchSupplierGoods({
                    keyword: this.postData.supplierGoodsId,
                    limit: 1,
                    offset: 0,
                    supplierId: this.postData.supplierId,
                });
                this.supplierGoods = res?.data?.rows[0];
            },
            handleSupplierChange(id) {
                this.supplier = this.supplierOptions.find((e) => e.id === id) || {};
            },
            handleBind() {
                console.log('handleBind');
                this.$refs.form.validate((val) => {
                    if (val) {
                        this.bindGoods();
                        console.log('postData', this.postData);
                    }
                });
            },
            async bindGoods() {
                this.btnLoading = true;
                try {
                    await GoodsAPIV3.bindSupplierGoods({
                        opType: this.opType,
                        goodsId: this.goods.id,
                        supplierId: this.postData.supplierId,
                        supplierGoodsId: this.postData.supplierGoodsId,
                        oldSupplierGoodsId: this.boundSupplierGoods?.supplierGoodsId,
                    });
                    this.$Toast.success('绑定成功');
                    this.$emit('bind-success');
                    this.showDialog = false;
                } catch (e) {
                    console.error(e);
                    // 防止重复提示
                    if (!e.alerted) {
                        this.$Toast({
                            message: e.message,
                            type: 'error',
                        });
                    }
                } finally {
                    this.btnLoading = false;
                }
            },
            handleSelect(item) {
                console.log('handleSelect', item);
                this.supplierGoods = item;
                this.postData.supplierGoodsId = item.supplierGoodsId;
            },
            handleClear() {
                console.log('handleClear');
                this.postData.supplierGoodsId = '';

            },

        },
    };
</script>
