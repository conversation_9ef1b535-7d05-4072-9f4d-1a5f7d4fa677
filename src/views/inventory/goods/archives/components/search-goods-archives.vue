<template>
    <div class="search-goods-archives-wrapper">
        <div
            :class="[
                'fast-search-wrapper',
                isShowMask ? 'real-search' : '',
                keyword ? 'auto' : '',
            ]"
        >
            <abc-space class="search-text" :size="4">
                <span>扫码建档或</span>
                <abc-icon icon="Arrow_Rgiht_" size="14"></abc-icon>
            </abc-space>
            <div class="input-wrapper" @click.stop="handleClick">
                <abc-input
                    ref="abc-nmpn-barcode"
                    v-model.trim="keyword"
                    v-abc-focus-selected
                    :clearable="!!keyword.trim()"
                    :max-length="50"
                    :placeholder="isShowMask ? '例：健胃消食片' : ''"
                    data-cy="inventory-archives-scan-code-input"
                    @input="handleInput"
                    @focus="handleInputFocus"
                    @blur="handleInputBlur"
                    @up="handleKeyUp"
                    @down="handleKeyDown"
                    @enter="handleEnter"
                >
                    <img
                        slot="prepend"
                        class="lightning-icon"
                        :width="16"
                        :height="16"
                        src="@/assets/images/archives/icon_flash.png"
                        alt=""
                        draggable="false"
                    />

                    <span v-if="!keyword" slot="appendInner" class="text">{{
                        isShowMask ? '' : '一键建档'
                    }}</span>

                    <abc-button
                        v-else
                        slot="appendInner"
                        theme="success"
                        :width="88"
                        style="margin-right: 4px;"
                        :loading="searchBtnLoading"
                        @click="searchMedicinesByCode()"
                    >
                        搜索
                    </abc-button>
                </abc-input>
            </div>

            <template v-if="isShowMask">
                <div v-show="!keyword" class="empty">
                    <!--<p>扫描条形码或搜索名称 、厂家、准字、医保码找商品</p>-->
                    <!--<div class="example">-->
                    <!--    <div class="tag">-->
                    <!--        示例-->
                    <!--    </div>-->
                    <!--    <span>健胃消食片 江中</span>-->
                    <!--</div>-->
                    <p>搜索名称、准字、条形码、追溯码找药品</p>
                    <p>20万药品库，400万耗材库，一键导入建档</p>
                </div>
                <div v-show="keyword" class="goods-data" data-cy="inventory-archives-search-result">
                    <div
                        v-if="currentTab"
                        class="tab-content"
                        :class="{
                            'is-first-tab': currentTab === 'spec'
                        }"
                        @mouseenter.stop="isHover = true"
                        @mouseleave.stop="isHover = false"
                    >
                        <div
                            v-for="tag in currentTags"
                            :key="tag.key"
                            class="tag"
                            :class="{
                                'is-selected': isTagSelected(tag.key)
                            }"
                            @click="handleClickTag(tag)"
                        >
                            {{ tag.key }}
                        </div>
                    </div>
                    <abc-table
                        v-abc-loading.coverOpaque="searchBtnLoading"
                        style="height: 456px;"
                        custom
                        show-hover-tr-bg
                    >
                        <abc-table-header>
                            <div class="tabs">
                                <abc-flex
                                    v-for="item in tabList"
                                    :key="item.value"
                                    v-abc-click-outside="closePopper"
                                    class="tab"
                                    align="center"
                                    :class="{
                                        'is-selected': currentTab === item.value,
                                        'is-disabled': false
                                    }"
                                    @click="handleClickTab(item.value)"
                                    @mouseenter="handleHoverTab(item.value)"
                                    @mouseleave="handleLeaveTab"
                                >
                                    <span :class="{ 'label': item.hasValue }">{{ item.label }}</span>
                                    <abc-delete-icon
                                        v-if="hoverTab === item.value"
                                        size="small"
                                        theme="dark"
                                        @delete="handleDelete(item.value)"
                                    ></abc-delete-icon>
                                    <abc-icon
                                        v-else
                                        size="16"
                                        :icon="currentTab === item.value ? 'dropdown_triangle_up' : 'dropdown_triangle'"
                                        :color="currentTab === item.value ? '#7A8794' : '#E0E2EB'"
                                    ></abc-icon>
                                </abc-flex>
                            </div>
                        </abc-table-header>
                        <abc-table-body>
                            <abc-table-tr
                                v-for="(item, index) in goodsList"
                                :key="item.id"
                                :class="{ 'is-selected': selectedIndex === index }"
                                @click.stop="handleClickTr(item)"
                            >
                                <abc-table-td>
                                    <span v-abc-title.ellipsis="item.medicineCadn || item.name"></span>
                                </abc-table-td>
                                <abc-table-td :width="140">
                                    <span v-abc-title.ellipsis="item.dispSpec || ''"></span>
                                </abc-table-td>
                                <abc-table-td :width="180">
                                    <div v-abc-title="item.manufacturerFull || ''" style="overflow: hidden; word-break: keep-all; white-space: nowrap;"></div>
                                </abc-table-td>
                                <abc-table-td :width="150">
                                    <!--临时兼容-->
                                    <span v-abc-title.ellipsis="item.medicineNmpn || item.medicineNpmn || ''"></span>
                                </abc-table-td>
                            </abc-table-tr>

                            <abc-content-empty v-if="!goodsList.length" top="100px" value="暂无数据"></abc-content-empty>
                        </abc-table-body>
                    </abc-table>
                    <abc-pagination
                        v-if="goodsList.length"
                        style="margin: 16px;"
                        :show-total-page="false"
                        :pagination-params="pageParams"
                        :count="pageParams.count"
                        @current-change="handlePageChange"
                    >
                    </abc-pagination>
                </div>


                <!--<div-->
                <!--    v-if="showRecommendList"-->
                <!--    v-abc-loading.coverOpaque="inputSearchLoading"-->
                <!--    class="suggestions"-->
                <!--&gt;-->
                <!--    <div-->
                <!--        v-for="item in recommendList"-->
                <!--        :key="item.id"-->
                <!--        class="suggestion"-->
                <!--        @click.stop="handleClickSuggestion(item)"-->
                <!--    >-->
                <!--        <abc-icon-->
                <!--            icon="search"-->
                <!--            color="#aab4bf"-->
                <!--            size="12"-->
                <!--        ></abc-icon>-->
                <!--        <span class="text">{{ item.medicineCadn || item.name }}</span>-->
                <!--        <img-->
                <!--            src="@/assets/images/archives/icon_enter.png"-->
                <!--            class="enter"-->
                <!--            alt=""-->
                <!--            draggable="false"-->
                <!--        />-->
                <!--    </div>-->
                <!--    <abc-content-empty v-if="!recommendList.length" top="100px" value="暂无数据"></abc-content-empty>-->
                <!--</div>-->
            </template>
        </div>
    </div>
</template>
<script>
    import GoodsAPI from 'api/goods';
    import { isBarcode } from '@/utils';
    import {
        debounce, isEqual,
    } from 'utils/lodash';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';

    let lastTime = null;
    let nextTime = null;
    export default {
        name: 'SearchGoodsArchives',
        props: {
            isShowMask: Boolean,
            pieceNum: Number,
            pieceUnit: String,
            packageUnit: String,
            goodsType: Number,
            barCode: String,
        },
        setup() {
            const {
                startBarcodeDetect,
                handleInputFocus,
                handleInputBlur,
            } = useBarcodeScanner({
                preventDefault: false,
            });

            return {
                startBarcodeDetect,
                handleInputFocus,
                handleInputBlur,
            };
        },
        data() {
            return {
                keyword: '',// 扫描条形码或搜索名称 、厂家、准字、医保码、注册证号找商品
                // 明确查询通用名
                medicineCadn: '',
                inputSearchLoading: true,
                searchBtnLoading: false,
                showRecommendList: false,
                recommendList: [],
                goodsList: [],
                currentTab: '',
                isHover: false,
                // 明确查询规格
                dispSpec: '',
                // 当前搜索药品规格列表
                dispSpecAggs: [],
                // 明确查厂家
                manufacturerFull: '',
                // 当前搜索药品厂家列表
                manufacturerAggs: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
                hoverTab: '',
                selectedIndex: -1,
            };
        },
        computed: {
            currentTags() {
                if (!this.currentTab) return [];
                return this.currentTab === 'spec' ? this.dispSpecAggs : this.manufacturerAggs;
            },
            tabList() {
                return [
                    {
                        label: this.dispSpec || '规格',
                        value: 'spec',
                        hasValue: !!this.dispSpec,
                    },
                    {
                        label: this.manufacturerFull || '厂家',
                        value: 'manufacturer',
                        hasValue: !!this.manufacturerFull,
                    },
                ];
            },

        },
        watch: {
            isShowMask(val) {
                if (!val) {
                    this.keyword = '';
                    this.medicineCadn = '';
                    this.dispSpec = '';
                    this.manufacturerFull = '';
                    this.currentTab = '';
                    this.showRecommendList = false;
                    this.recommendList = [];
                    this.dispSpecAggs = [];
                    this.manufacturerAggs = [];
                    this.goodsList = [];
                } else {
                    this.$nextTick(() => {
                        this.$refs['abc-nmpn-barcode']?.focus();
                    });
                }
            },
            keyword(val) {
                if (!val) {
                    this.medicineCadn = '';
                    this.currentTab = '';
                    this.showRecommendList = false;
                    this.recommendList = [];
                }
            },
        },
        created() {
            this.handleInput = debounce(() => {
                this.currentTab = '';
                this.pageParams.pageIndex = 0;
                this.searchMedicinesByCode();
            }, 500, true);

            if (this.barCode) {
                this.searchMedicinesByCode(this.barCode, isBarcode(this.barCode));
            }
        },
        async mounted() {
            await this.$nextTick();
            this.startBarcodeDetect((e, barcode) => {
                if (e.target === document.body) {
                    this.searchMedicinesByCode(barcode, true);
                }
            });
        },
        methods: {
            isTagSelected(tagKey) {
                return this.currentTab === 'spec' ? this.dispSpec === tagKey : this.manufacturerFull === tagKey;
            },
            handleClick() {
                this.$emit('showMask', true);
            },
            handleClickTab(tab) {
                if (this.currentTab === tab) {
                    this.currentTab = '';
                } else {
                    this.currentTab = tab;
                }
            },
            handleHoverTab(tab) {
                if (tab === 'spec' && this.dispSpec) {
                    this.hoverTab = tab;
                    return;
                }
                if (tab !== '' && this.manufacturerFull) {
                    this.hoverTab = tab;
                }
            },
            handleLeaveTab() {
                this.hoverTab = '';
            },
            handleDelete(tab) {
                if (tab === 'spec') {
                    this.dispSpec = '';
                } else {
                    this.manufacturerFull = '';
                }
                this.currentTab = '';
                this.hoverTab = '';
                this.pageParams.pageIndex = 0;
                this.searchMedicinesByCode();
            },
            handleClickTag(tag) {
                if (this.currentTab === 'spec') {
                    this.dispSpec = tag.key === this.dispSpec ? '' : tag.key;
                } else {
                    this.manufacturerFull = tag.key === this.manufacturerFull ? '' : tag.key;
                }
                this.currentTab = '';
                this.pageParams.pageIndex = 0;
                this.searchMedicinesByCode();
            },
            closePopper() {
                if (this.isHover) return;
                this.currentTab = '';
            },
            handleClickTr(item) {
                console.log('handleClickTr', item);
                if (!item) return;
                // 处理为0的非基药数据
                if (item.baseMedicineType === 0)item.baseMedicineType = undefined;
                // 处理经营范围数据
                item.businessScopeList = item.businessScopes || [];

                // 选中
                this.$emit('selectGoods', item);
                // 关闭弹窗
                this.$emit('showMask', false);
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.focusHandle();
                }, 500);
            },
            handleKeyDown(e) {
                if (e.isComposing) {
                    e.preventDefault();
                    e.stopPropagation();
                    return;
                }
                this.selectedIndex++;
                if (this.selectedIndex >= this.goodsList.length) {
                    this.selectedIndex = 0;
                }
            },
            handleKeyUp(e) {
                if (e.isComposing) {
                    e.preventDefault();
                    e.stopPropagation();
                    return;
                }
                this.selectedIndex--;
                if (this.selectedIndex < 0) {
                    this.selectedIndex = this.goodsList.length - 1;
                }
            },
            handleEnter() {
                if (this.selectedIndex !== -1) {
                    this.handleClickTr(this.goodsList[this.selectedIndex]);
                }
            },
            // handleInput: debounce(async function() {
            //     console.log(this.keyword);
            //     this.currentTab = '';
            //     if (!this.keyword) {
            //         this.showRecommendList = false;
            //         this.recommendList = [];
            //         return;
            //     }
            //     try {
            //         this.showRecommendList = true;
            //         this.inputSearchLoading = true;
            //         const res = await GoodsAPI.getRecommendGoods({
            //             keywords: this.keyword,
            //             type: this.goodsType,
            //             // needAggs: true,
            //             offset: 0,
            //             limit: 100,
            //         });
            //         this.recommendList = res?.data?.list || [];
            //     } catch (e) {
            //         console.error(e);
            //     } finally {
            //         this.inputSearchLoading = false;
            //     }
            //
            // }, 500, true),
            handleClickSuggestion(item) {
                this.medicineCadn = item.medicineCadn || item.name;
                this.keyword = this.medicineCadn;

                this.searchMedicinesByCode(this.medicineCadn);
            },
            handlePageChange(page) {
                this.pageParams.pageIndex = page - 1;
                this.selectedIndex = -1;
                this.searchMedicinesByCode();
            },
            createFetchParams(keyword) {
                const {
                    pageIndex, pageSize,
                } = this.pageParams;
                const fetchParams = {
                    dispSpec: this.dispSpec,
                    manufacturerFull: this.manufacturerFull,
                    type: this.goodsType,
                    needAggs: 1, // 是否需要聚合1,
                    offset: pageIndex * pageSize,
                    limit: pageSize,
                };
                if (this.medicineCadn) {
                    fetchParams.medicineCadn = this.medicineCadn;
                } else {
                    fetchParams.keywords = keyword;
                }

                return fetchParams;
            },
            async searchMedicinesByCode(keyword = this.keyword, fromBarCode = false) {
                this.showRecommendList = false;
                this.recommendList = [];
                this.medicineCadn = '';

                if (!keyword) {
                    this.goodsList = [];
                    this.dispSpec = '';
                    this.manufacturerFull = '';
                    this.dispSpecAggs = [];
                    this.manufacturerAggs = [];
                    return;
                }
                try {
                    this.searchBtnLoading = true;
                    const beforeFetchParams = this.createFetchParams(keyword);
                    const res = await GoodsAPI.getRecommendGoods(beforeFetchParams);
                    const afterFetchParams = this.createFetchParams(keyword);
                    if (res.data && isEqual(beforeFetchParams, afterFetchParams)) {
                        const {
                            list = [], dispSpecAggs, manufacturerAggs, total,
                        } = res.data;
                        // 是否来自扫码枪监听回调-或者输入框下扫码枪输入
                        if (fromBarCode || (isBarcode(keyword) && this.isScanCodeInput && list.length === 1)) {
                            if (list?.[0]) {
                                this.handleClickTr(list?.[0]);
                            } else {
                                // keyword的值是barcode
                                if (this.isShowMask) this.keyword = keyword;
                                this.$Toast({
                                    type: 'error',
                                    message: '无匹配结果',
                                });
                                // if (isBarcode(keyword)) {
                                //
                                // } else {
                                //     this.$Toast({
                                //         type: 'error',
                                //         message: '请扫描有效条码',
                                //     });
                                // }

                            }
                        } else {
                            this.goodsList = list || [];
                            this.pageParams.count = total || this.goodsList.length;
                            if (afterFetchParams.needAggs) {
                                this.dispSpecAggs = dispSpecAggs?.filter((e) => e.key) || [];
                                this.manufacturerAggs = manufacturerAggs?.filter((e) => e.key) || [];
                            }
                            this.selectedIndex = 0;
                            if (this.goodsList.length) {
                                this.selectedIndex = -1;
                            }
                        }
                    } else {
                        console.log('searchMedicinesByCode', res);
                    }

                } catch (e) {
                    console.log(e);
                } finally {
                    this.searchBtnLoading = false;
                }
            },
            focusHandle() {
                if (!this.pieceNum || !this.pieceUnit || !this.packageUnit) {
                    $('.base-info-wrapper .spec-content .goods-dosage input').eq(0).focus();
                } else {
                    $('.price-info-wrapper .goods-package-price input').eq(0).focus();
                }
            },
            /**
             * @desc 判断是否是扫码枪输入（要有回车键入）
             * <AUTHOR>
             * @date 2024-02-22 11:30:30
             * @param e 事件对象
             */
            inspectInputHandler(e) {
                const keycode = e.keyCode || e.which || e.charCode;
                nextTime = new Date();
                if (keycode === 13) {
                    this.isScanCodeInput = !!(lastTime && (nextTime - lastTime < 30));
                    lastTime = null;
                    e.preventDefault();
                } else {
                    lastTime = nextTime;
                    this.isScanCodeInput = false;
                }
            },
        },
    };
</script>


