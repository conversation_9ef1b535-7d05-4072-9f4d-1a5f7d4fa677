// 药品档案控制器
export const GoodsArchivesControllerKey = Symbol('GoodsArchivesControllerKey');

// 手动控制部分字段的禁用-(医院管家使用，采购管理-档案中心与库存管理-药品档案)
export const disabledBaseInfoKey = Symbol('disabledBaseInfoKey');

// 控制柜号字段是否禁用
export const hiddenPositionKey = Symbol('hiddenPositionKey');

// 控制是否能够快速建档
export const usePieceUnitFlagKey = Symbol('usePieceUnitFlagKey');

// 控制显示历史税率
export const isShowHistoryTaxratKey = Symbol('isShowHistoryTaxratKey');
// 控制显示修改日志
export const isShowUpdateLogKey = Symbol('isShowUpdateLogKey');

// 药品调价信息
export const adjustPriceInfoKey = Symbol('adjustPriceInfoKey');

// 是否允许调价
export const canAdjustPriceKey = Symbol('canAdjustPriceKey');

// gsp首营审批信息
export const approveInfoKey = Symbol('approveInfoKey');

// 是否允许修改档案基本数据
export const canUpdateGoodsInfoKey = Symbol('canUpdateGoodsInfoKey');

// 档案修改变化对象数据
export const modifyGoodsItemKey = Symbol('modifyGoodsItemKey');
