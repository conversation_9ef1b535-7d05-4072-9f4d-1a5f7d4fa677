import GoodsArchivesManagerService from 'views/inventory/goods/archives/service';
export default class GoodsArchivesManagerController {
    constructor(isDetail = false) {
        this.GoodsArchivesService = new GoodsArchivesManagerService();
        this.isGoodsModelInited = false;
        // 初始化选项数据promise
        this.initOptionsPromise = null;
        // 是否是详情页-针对详情有一些操作限制
        this.isDetail = isDetail;
        this.isDestroyed = false;
    }
    init(options = {}) {
        if (this.isGoodsModelInited) return;

        this.GoodsArchivesService.init(options);
        this.isGoodsModelInited = true;
    }
    // 初始化药品数据允许有默认行为
    initGoods(data, preventDefault = false) {
        this.GoodsArchivesService.initGoods(data, () => {
            if (!preventDefault) return;
            this.GoodsArchivesService.changeDefaultInOutTax();
            this.GoodsArchivesService.changeSpecType();
            this.GoodsArchivesService.updateDefaultFeeTypeId();
            this.GoodsArchivesService.updateDefaultFeeCategory();
        });
    }

    async initOptions() {
        if (this.initOptionsPromise) return this.initOptionsPromise;

        this.initOptionsPromise = new Promise((resolve, reject) => {
            this.GoodsArchivesService.initOptions().then(resolve, reject);
        });

        return this.initOptionsPromise;
    }

    setReactiveCallback(reactiveCallback) {
        this.GoodsArchivesService.setReactiveCallback(reactiveCallback);
    }

    getGoodsInfo() {
        if (!this.isGoodsModelInited) {
            console.error('未初始化成功，无法获取数据');
            return {};
        }
        return this.GoodsArchivesService.getGoods();
    }
    getInstructionsCategoryMap() {
        return this.GoodsArchivesService.pharmacologicsMap;
    }

    // 修改数据模型
    setGoodsModel(data, needMatchCode = !this.isDetail) {
        return this.GoodsArchivesService.setGoodsModel(data, needMatchCode);
    }

    getFeeTypesList() {
        return this.GoodsArchivesService.feeTypesList;
    }

    getFeeCategoryList() {
        return this.GoodsArchivesService.feeCategoryList;
    }
    getProfitClassificationList() {
        return this.GoodsArchivesService.profitClassificationList;
    }

    fetchAutoShebaoCode(type = 'default') {
        this.GoodsArchivesService.matchSocialSecurityCode(type);
    }
    updateInitShebaoCode() {
        this.GoodsArchivesService.updateInitShebaoCode();
    }
    fetchAutoCenterCode() {
        this.GoodsArchivesService.matchDrugSupervisionCode();
    }

    transShebaoPrice(data) {
        return this.GoodsArchivesService.transShebaoPrice(data);
    }

    getPostData() {
        return this.GoodsArchivesService.createPostData();
    }

    handleSelectGoods(goods) {
        if (this.isDetail) return;

        this.setGoodsModel(goods);
    }

    blurInput(key) {
        // 需要触发自动对码的字段
        if ([
            'medicineCadn',
            'name',
            'manufacturerFull',
            'medicineNmpn',
            'specType',
            'medicineDosageNum',
            'medicineDosageUnit',
            'componentContentUnit',
            'componentContentNum',
            'pieceNum',
            'pieceUnit',
            'packageUnit',
            'certificateNo',
        ].includes(key)) {
            // 此处加个宏任务让触发逻辑在update之后
            const timer = window.setTimeout(() => {
                this.GoodsArchivesService.matchSocialSecurityCode();
                clearTimeout(timer);
            }, 500);
        }

        if ([
            'medicineCadn',
            'name',
            'manufacturerFull',
            'medicineNmpn',
            'specType',
        ].includes(key)) {
            // 此处加个宏任务让触发逻辑在update之后
            const timer = window.setTimeout(() => {
                this.GoodsArchivesService.matchDrugSupervisionCode();
                clearTimeout(timer);
            }, 500);
        }
    }

    updateGoodsInfo(key, val) {
        // !先更新字段值
        this.GoodsArchivesService.setGoods(key, val);
        // !再对不同字段做特殊处理
        try {
            if (key === 'typeId') {
                this.GoodsArchivesService.changeGoodsTypeId(val);
            }
            // 费用类型变化
            if (key === 'feeTypeId') {
                this.GoodsArchivesService.changeFeeTypeId(val);
            }
            if (key === 'antibiotic') {
                this.GoodsArchivesService.changeFeeCategoryIdByAntibiotic(val);
            }
            // 默认税率变化
            if (key === 'defaultInOutTax') {
                this.GoodsArchivesService.changeDefaultInOutTax(val);
            }
            if (key === 'pieceNum' || key === 'dismounting' || key === 'packagePrice') {
                this.GoodsArchivesService.calPiecePrice(val);
            }
            // 颗粒机变化
            if (key === 'smartDispenseMachineNo') {
                // 开启智能发药
                this.GoodsArchivesService.setGoods('smartDispense', val ? 1 : 0);
            }
            // 定价模式变化
            if (key === 'currentPriceType') {
                this.GoodsArchivesService.changePriceType(val);
            }
            // 药品切换剂量类型
            if (key === 'specType') {
                this.GoodsArchivesService.changeSpecType(val);
            }
        } catch (e) {
            console.error('更新数据异常',e);
        }

    }

    destroy() {
        this.isDestroyed = true;
        this.GoodsArchivesService.destroy();
    }
}
