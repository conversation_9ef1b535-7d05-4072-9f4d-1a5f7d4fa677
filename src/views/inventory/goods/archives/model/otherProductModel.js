import BaseModel from './baseModel';
import {
    GoodsTypeEnum,
    GoodsSubTypeEnum,
    GoodsTypeIdEnum,
} from '@abc/constants';

export default class OtherProductModel extends BaseModel {
    constructor(obj) {
        super(obj);
        this.assignGoods({
            type: GoodsTypeEnum.GOODS,
            typeId: GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT,
            subType: GoodsSubTypeEnum[GoodsTypeEnum.GOODS].Other,
            // 商品名
            name: obj.name,
            materialSpec: obj.materialSpec, // 规格
            medicineNmpn: obj.medicineNmpn,
            // !批准文号有效期
            medicineNmpnEndExpiryDate: obj.medicineNmpnEndExpiryDate,
            medicineNmpnStartExpiryDate: obj.medicineNmpnStartExpiryDate,
            isSell: 0, // 是否对外销售
            shebao: null,// 没有社保信息
        });
    }
}
