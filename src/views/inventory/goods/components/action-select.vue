<template>
    <abc-select
        v-model="currentValue"
        :width="160"
        select-type="filter"
        @change="changeHandle"
    >
        <abc-option
            v-for="it in _actions"
            :key="it"
            :value="it"
            :label="it"
        ></abc-option>
    </abc-select>
</template>

<script>
    import { mapGetters } from 'vuex';

    export default {
        name: 'ActionSelect',
        props: ['value'],
        data() {
            return {
                currentValue: '全部动作',
            };
        },
        computed: {
            ...mapGetters(['isChain']),
        },

        watch: {
            value: {
                handler(val) {
                    this.currentValue = val || '全部动作';
                },
            },
        },

        created() {
            this._actions = this.isChain ?
                ['全部动作', '发药', '退药', '入库', '出库', '调拨', '盘点'] :
                ['全部动作', '发药', '退药', '入库', '出库', '盘点'];
        },
        methods: {
            changeHandle(val) {
                this.$emit('input', val);
            },
        },
    };
</script>
