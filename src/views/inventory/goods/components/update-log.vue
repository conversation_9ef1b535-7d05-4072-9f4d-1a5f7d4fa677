<template>
    <div class="update-log-wrapper">
        <template v-if="currentLog && currentLog.length >= 1">
            <abc-button
                v-if="!showAll"
                style="position: relative; left: -4px; font-size: 12px;"
                type="text"
                @click="showAll = true"
            >
                查看修改记录
            </abc-button>
            <abc-button
                v-else
                type="text"
                style="position: relative; left: -4px; font-size: 12px;"
                @click="showAll = false"
            >
                收起修改记录
            </abc-button>
            <template v-if="showAll">
                <div class="log-list-wrapper">
                    <ul class="event-list">
                        <li v-for="(log, index) in currentLog" :key="index">
                            <div class="log-title">
                                <div class="ellipsis" :title="formatCacheTime(log.time)">
                                    {{ formatCacheTime(log.time) }}
                                </div>
                                <div v-if="checkIsRenderOperator(log)" class="ellipsis" :title="log.operator || ''">
                                    {{ log.operator }}
                                </div>
                                <div class="ellipsis" :title="log.organName || ''">
                                    {{ log.organName || '' }}
                                </div>
                            </div>
                            <template v-if="log.detail && log.detail.length">
                                <div v-for="(detail, index) in log.detail" :key="index" class="log-detail">
                                    <div style="flex-shrink: 0;">
                                        {{ detail.field }}
                                    </div>
                                    <div>{{ formatDetail(detail.before, detail.after) }}</div>
                                </div>
                            </template>
                            <template v-else>
                                <div class="log-detail">
                                    {{ log.action }}{{ goodsTypeName }}
                                </div>
                            </template>
                            <template v-if="isChainAdmin && log.affectSubClinicList?.length">
                                <div class="log-detail">
                                    <div>影响门店</div>
                                    <abc-flex style="flex: 1; overflow: hidden;">
                                        <span style="flex: 0 1 auto; max-width: 100%;" class="ellipsis">
                                            {{ formatAffectClinic(log.affectSubClinicList) }}
                                        </span>

                                        <template v-if="log.affectSubClinicList.length > 2">
                                            <span style="flex-shrink: 0; margin-right: 4px;">
                                                等{{ log.affectSubClinicList.length }}家门店
                                            </span>

                                            <abc-popover
                                                trigger="hover"
                                                theme="yellow"
                                                size="small"
                                                style="flex-shrink: 0;"
                                                popper-class="update-log-wrapper_sub-clinic-popover"
                                            >
                                                <abc-link slot="reference" size="small">
                                                    <abc-text size="mini" theme="primary">
                                                        详情
                                                    </abc-text>
                                                </abc-link>

                                                <abc-flex
                                                    style="width: 266px;"
                                                    wrap="wrap"
                                                    class="affect-sub-clinic-list"
                                                    :gap="6"
                                                >
                                                    <abc-text
                                                        v-for="(clinic,idx) in log.affectSubClinicList"
                                                        :key="idx"
                                                        :title="clinic"
                                                        size="mini"
                                                        style="width: calc(calc(100% - 6px) / 2);"
                                                        class="ellipsis"
                                                    >
                                                        {{ clinic }}
                                                    </abc-text>
                                                </abc-flex>
                                            </abc-popover>
                                        </template>
                                    </abc-flex>
                                </div>
                            </template>
                        </li>
                    </ul>
                </div>
            </template>
        </template>
        <template v-else>
            <div class="empty-content">
                暂无修改记录
            </div>
        </template>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';

    export default {
        name: 'UpdateLog',
        props: {
            updateLog: Array,
            goodsType: [String, Number],
            isShow: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                showAll: this.isShow,
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin','isChainSubStore']),

            currentLog() {
                // if (!this.updateLog) return [];
                // if (this.updateLog.length < 3) return this.updateLog;
                // if (!this.showAll) {
                //     return this.updateLog.slice( 0, 3 );
                // } else {
                //     return this.updateLog;
                // }
                return this.updateLog;
            },
            goodsTypeName() {
                let str = '';
                if (this.goodsType === 1) {
                    str = '药品';
                }
                if (this.goodsType === 2) {
                    str = '物资';
                }
                if (this.goodsType === 7) {
                    str = '商品';
                }
                return str;
            },
        },
        methods: {
            formatDetail(before, after) {
                const currBefore = before || '无';
                const currAfter = after || '无';
                return `从 ${currBefore} 修改为 ${currAfter}`;
            },
            formatCacheTime(date) {
                // 时间格式转换
                const d = new Date(date);
                const today = new Date();
                today.setHours(0);
                today.setMinutes(0);
                today.setMinutes(0);
                const diff = (today * 1 - d * 1) / 1000;
                const hour = +d.getHours() > 9 ? d.getHours() : `0${d.getHours()}`;
                const min = +d.getMinutes() > 9 ? d.getMinutes() : `0${d.getMinutes()}`;
                if (d * 1 - today * 1 >= 0) {
                    return `今天 ${hour}:${min}`;
                } if (diff > 0 && diff < 86400) {
                    return `昨天 ${hour}:${min}`;
                } if (diff > 86400 && diff <= 86400 * 2) {
                    return `前天 ${hour}:${min}`;
                }
                const month = +d.getMonth() + 1 > 9 ? +d.getMonth() + 1 : `0${d.getMonth() + 1}`;
                const day = +d.getDate() > 9 ? d.getDate() : `0${d.getDate()}`;
                return `${d.getFullYear()}-${month}-${day} ${hour}:${min}`;
            },

            formatAffectClinic(affectSubClinicList) {
                return affectSubClinicList.slice(0,2).join('、');
            },

            checkIsRenderOperator(log) {
                return !(this.isChainSubStore && log.organName === '总部');
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import '~styles/theme.scss';
    @import 'styles/abc-common.scss';

    .update-log-wrapper {
        padding-top: 16px;
        margin: 0 16px 0 6px;
        border-top: 1px solid $P6;

        .empty-content {
            font-size: 12px;
            color: #96a4b3;
        }

        .title {
            margin-bottom: 0;
            font-size: 16px;
            font-weight: 500;
            color: $T1;
        }

        .log-list-wrapper {
            .event-list {
                li {
                    padding: 16px 0;
                    border-bottom: 1px solid $P6;

                    &:last-child {
                        border-bottom: none;
                    }
                }

                .log-title {
                    display: flex;

                    div {
                        width: auto;
                        margin-right: 6px;
                        font-size: 12px;
                        line-height: 16px;
                        color: $T3;

                        &:first-child {
                            width: 98px;
                        }

                        &:nth-child(2) {
                            width: 48px;
                        }
                    }
                }

                .log-detail {
                    display: flex;
                    margin-top: 4px;
                    font-size: 12px;
                    line-height: 17px;
                    color: $T2;

                    > div {
                        &:first-child {
                            width: 92px;
                            margin-right: 12px;
                        }

                        width: auto;
                    }
                }
            }

            .more-log {
                padding-top: 16px;
                font-size: 12px;
                color: $T2;
                border-top: 1px solid $P6;

                i {
                    font-size: 12px;
                }
            }
        }
    }

    .update-log-wrapper_sub-clinic-popover {
        padding: 10px 0 10px 10px;
        overflow-y: scroll;

        @include scrollBar;
    }
</style>
