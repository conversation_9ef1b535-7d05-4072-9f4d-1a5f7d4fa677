<template>
    <abc-dialog
        v-if="showBatchAddGoodsDialog"
        v-model="showBatchAddGoodsDialog"
        :title="isEyeglasses ? '批量添加眼镜' : '批量添加药品'"
        append-to-body
        :auto-focus="false"
        content-styles="width: 402px;padding:24px 24px;"
    >
        <abc-form
            ref="filterForm"
            label-position="left"
            :label-width="90"
        >
            <abc-form-item
                v-if="isOpenVirtualPharmacy"
                label="库房类型"
            >
                <abc-select
                    v-model="pharmacyType"
                    :width="244"
                    custom-class="line-text-select"
                    @change="filterSuppliers"
                >
                    <abc-option
                        style="padding: 8px 4px;"
                        label="本地库房"
                        :value="PharmacyTypeEnum.LOCAL_PHARMACY"
                    >
                    </abc-option>
                    <abc-option
                        style="padding: 8px 4px;"
                        label="代煎代配"
                        :value="PharmacyTypeEnum.VIRTUAL_PHARMACY"
                    >
                    </abc-option>
                </abc-select>
            </abc-form-item>
            <abc-form-item
                required
                label="商品类型"
            >
                <abc-cascader
                    v-model="selectedTypes"
                    placeholder="请选择调价范围"
                    multiple
                    confirm-text="确认"
                    cancel-text="取消"
                    :width="244"
                    :mutually-exclusive="false"
                    :options="filterGoodsAllTypes"
                    :props="{
                        label: 'name',
                        value: 'id',
                        children: 'children'
                    }"
                    :value-props="{
                        _label: 'name',
                        _value: 'id'
                    }"
                    @change="handleChangeType"
                ></abc-cascader>
            </abc-form-item>
            <abc-form-item
                v-if="showCurrentPrice"
                label="当前售价"
            >
                <abc-checkbox-group v-model="currentPrice">
                    <abc-checkbox :label="1">
                        高于限价
                    </abc-checkbox>
                    <abc-checkbox :label="0">
                        低于限价
                    </abc-checkbox>
                </abc-checkbox-group>
            </abc-form-item>
            <abc-form-item
                required
                label="最近供应商"
            >
                <abc-select
                    v-model="supplierCheck"
                    :width="244"
                    custom-class="line-text-select"
                    with-search
                    :fetch-suggestions="filterSuppliers"
                    placeholder="请选择商品的最近供应商"
                    clearable
                >
                    <abc-option
                        v-for="(item,index) in batchGoodsSupplierList"
                        :key="index"
                        style="padding: 8px 4px;"
                        :label="item.name"
                        :value="item.id"
                    >
                    </abc-option>
                </abc-select>
            </abc-form-item>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button type="primary" :loading="buttonLoading" @click="submitBatchAddGoods">
                确定
            </abc-button>
            <abc-button type="blank" @click="showBatchAddGoodsDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import GoodsV3API from 'api/goods/index-v3.js';
    import SupplierApi from 'api/goods/supplier';
    import { CHECK_IN_SUPPLIER_ID } from 'views/inventory/constant.js';
    import { cascaderDataAdapter } from '@/utils';
    export default {
        name: 'AddMedicineDialog',
        props: {
            value: Boolean,
            isEyeglasses: Boolean,
            goodsAllTypes: { // 系统所有的药品物资商品分类以及二级分类
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 定价类型
            priceType: Number,
        },
        data() {
            return {
                PharmacyTypeEnum,
                CHECK_IN_SUPPLIER_ID,
                supplierCheck: null,
                productCheck: null,
                pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY,// 库房类型-只有开通了代煎代配的门店才展示
                selectedTypes: [], // 选中的药品分类
                supplierList: [], // 供应商列表
                buttonLoading: false,
                currentPrice: [], // 当前售价
            };
        },
        computed: {
            ...mapGetters([
                'isOpenVirtualPharmacy',// 是否开启代煎代配（虚拟库房）
                'priceAdjustmentTabType', //当前调价模式
            ]),
            showBatchAddGoodsDialog: { //展示设置规则的弹窗
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            showCurrentPrice() {
                // 只有医保限价才展示
                return this.priceAdjustmentTabType === 3;
            },
            // 批量供应商列表
            batchGoodsSupplierList() {
                // 固定的需要展示的选项
                const batchGoodsSuppliers = [
                    {
                        name: '全部供应商',
                        id: -1,
                    },
                    {
                        name: '初始化入库',
                        id: this.CHECK_IN_SUPPLIER_ID,
                    },
                ];
                return batchGoodsSuppliers.concat(this.supplierList.filter((s) => s.pharmacyType === this.pharmacyType));
            },
            filterGoodsAllTypes() {
                if (this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    return this.goodsAllTypes.filter((item) => {
                        const {
                            goodsType, goodsSubType,
                        } = item;
                        // 代煎代配仅展示中药饮片和中药颗粒
                        if (goodsType === GoodsTypeEnum.MEDICINE && (goodsSubType === GoodsSubTypeEnum[goodsType].ChineseMedicine)) {
                            return true;
                        }
                        return false;
                    });
                }
                return this.goodsAllTypes;
            },
        },
        created() {
            this.filterSuppliers();
        },
        methods: {
            // 类型筛选
            handleChangeType() {
                const {
                    typeIdList, customTypeIdList,
                } = cascaderDataAdapter(this.filterGoodsAllTypes, this.selectedTypes);
                this._typeIdList = typeIdList;
                this._customTypeIdList = customTypeIdList;
            },
            async filterSuppliers(keyword = '') {
                try {
                    keyword = keyword.trim();
                    const params = {
                        keyword,
                        clinicId: this.clinicId,
                        status: '',
                        pharmacyType: this.pharmacyType,
                    };
                    const { data } = await SupplierApi.searchSupplier(params);
                    this.supplierList = (data && data.rows) || [];
                } catch (err) {
                    this.supplierList = [];
                }
            },
            async submitBatchAddGoods() {
                this.$refs.filterForm.validate(async (valid) => {
                    if (valid) {
                        const params = {
                            opType: this.priceAdjustmentTabType,
                            supplierId: this.supplierCheck,
                            sheBaoOverPrice: this.currentPrice,
                            pharmacyType: this.pharmacyType,
                            priceType: this.priceType,
                        };
                        // 有分类
                        if (this._typeIdList?.length) {
                            params.typeId = this._typeIdList;
                        }
                        //  有二级分类
                        if (this._customTypeIdList?.length) {
                            params.customTypeId = this._customTypeIdList;
                        }

                        const name = this.batchGoodsSupplierList?.find((item) => {return item.id === this.supplierCheck;})?.name || '';
                        if (this.supplierCheck === -1) {
                            params.supplierId = '';
                        }

                        try {
                            this.buttonLoading = true;
                            const { data } = await GoodsV3API.getGoodsList(params);
                            if (data.totalCount > 0) {
                                // 关闭弹窗
                                this.$emit('setGoodsList',data.list);
                                this.showBatchAddGoodsDialog = false;
                            } else {
                                let text = '';
                                if (this.currentPrice?.length === 2) {
                                    text = '高于限价或者低于限价的';
                                } else if (this.currentPrice?.includes(1)) {
                                    text = '高于限价的';
                                } else if (this.currentPrice?.includes(0)) {
                                    text = '低于限价的';
                                }
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: `未找到供应商为${name}的${text}${this.isEyeglasses ? '眼镜' : '药品'}`,
                                });
                            }
                        } catch (e) {
                            console.log(e);
                        } finally {
                            this.buttonLoading = false;
                        }
                    }
                });
            },
        },
    };
</script>
