<template>
    <div class="tr system-charge-item cashier-table-outpatient-info">
        <!--项目名称-->
        <div class="td label no-right-border">
            <div class="td-cell">
                <abc-icon icon="money" size="12" :color="themeStyle.T3"></abc-icon>
                <span style="margin-right: 10px;">医生诊断</span>
            </div>
        </div>

        <div class="td content">
            <div
                class="outpatient-info"
                :class="{
                    'is-disabled': disabled,
                    'is-error': requiredObj.flag,
                }"
                style=" max-width: calc(100% - 116px); background-color: transparent;"
                @click="handleClick"
            >
                <div v-html="displayHtml"></div>
                <abc-icon v-if="!disabled" icon="dropdown_triangle"></abc-icon>
                <div v-if="requiredObj.flag" class="error-tips">
                    {{ requiredObj.tips }}
                </div>
            </div>
            <div v-if="needPrescription" class="file-view-wrap">
                <abc-file-view
                    v-for="item in attachments"
                    :key="item.url"
                    :file="item"
                    width="32px"
                    height="32px"
                    @click.native="previewIt(item)"
                ></abc-file-view>
            </div>

            <abc-form-item v-if="requiredObj.flag" style="display: none;" required>
                <abc-input v-model="errValue"></abc-input>
            </abc-form-item>
        </div>
    </div>
</template>
<script type="text/ecmascript-6">
    import themeStyle from '@/styles/theme.module.scss';
    import { getWarnDiagnosisHtml } from 'views/outpatient/common/medical-record/utils.js';
    import { loosePlainText } from 'utils/xss-filter.js';
    import { medicalImagingViewerDialogService } from '@/medical-imaging-viewer/store/medical-imaging-viewer-dialog';

    export default {
        name: 'CashierTableOutpatientInfo',
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            required: {
                type: Boolean,
                default: false,
            },
            postData: {
                type: Object,
                required: true,
            },
            needPrescription: Boolean,
        },
        data() {
            return {
                themeStyle,
                errValue: '',
            };
        },
        computed: {

            diagnosisHtml() {
                const {
                    extendDiagnosisInfos,
                } = this.postData;
                if (!extendDiagnosisInfos) return '';

                const _arr = [];

                extendDiagnosisInfos
                    .filter((it) => it.value && it.value.length)
                    .forEach((item) => {
                        item.value.forEach((it) => {
                            it.name && _arr.push(it);
                        });
                    });

                const _html = getWarnDiagnosisHtml(_arr, this.$abcSocialSecurity.isSupportMatchDiseaseCode);
                return loosePlainText(_html, true, {
                    a: ['class', 'data-tipsy'],
                    i: ['class', 'contenteditable'],
                });
            },

            displayHtml() {
                const {
                    departmentName,
                    doctorName,
                } = this.postData;

                if (this.requiredObj.flag) {
                    return '请填写医生、诊断';
                }

                const res = this.diagnosisHtml;
                let str = '';
                if (doctorName && res) {
                    str += `${doctorName}：${res}`;
                } else if (doctorName) {
                    str += `${doctorName}`;
                } else if (res) {
                    str += `${res}`;
                }
                if (departmentName) {
                    str = `${departmentName}-${str}`;
                }
                return str;
            },

            requiredObj() {
                if (!this.required) return { flag: false };
                const {
                    doctorId,
                } = this.postData;

                if (!doctorId || !this.diagnosisHtml) {
                    return {
                        tips: '请填写医生、诊断',
                        flag: true,
                    };
                }
                return {
                    flag: false,
                };
            },

            attachments() {
                const { prescriptionUrls } = this.postData;
                return (prescriptionUrls || []).map((x) => ({
                    url: x,
                }));
            },
        },
        methods: {
            handleClick() {
                if (this.disabled) return;
                this.$emit('open-dialog');
            },
            previewIt(item) {
                medicalImagingViewerDialogService.previewImageAttachment({
                    attachments: this.attachments,
                    attachment: item,
                });
            },
        },
    };
</script>
<style lang="scss">
    @import '~styles/theme.scss';
    @import '~styles/mixin.scss';

    .cashier-table-outpatient-info {
        border-top: none !important;

        .content {
            .shortage-tips i {
                font-style: normal;
                color: #ff9933;
            }
        }

        .file-view-wrap {
            display: inline-flex;
            gap: 4px;
            margin-left: 1px;

            .abc-file-viewer .view-box.image-box {
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                .abc-image-wrapper {
                    border-radius: var(--abc-border-radius-small);
                }
            }
        }

        & + .charge-form {
            .tr {
                border-top: 1px solid $P1 !important;
            }

            .glasses-table {
                border-top: 1px solid $P1 !important;
            }
        }
    }
</style>
