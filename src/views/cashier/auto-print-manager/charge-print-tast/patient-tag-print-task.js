import PrintTask from 'views/pharmacy/pharmacy-auto/pharmacy-print-task/print-task.js';
import PrintAPI from 'api/print.js';
import { getAbcPrintOptions } from '@/printer/print-handler.js';
import AbcPrinter from '@/printer/index.js';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';
import Logger from 'utils/logger';

export class PatientTagPrintTask extends PrintTask {
    async prepareData(params) {
        const { patientOrderId } = params;
        const { data } = await PrintAPI.patientTagPrint(patientOrderId);

        Logger.report({
            scene: 'auto_print_charge',
            data: {
                scene: 'patient_tag_data',
                uuid: this.uuid,
                info: '请求患者标签数据',
                data: {
                    printData: data,
                },
            },
        });

        return data;
    }

    printHandler(data) {
        if (!data) {
            Logger.report({
                scene: 'auto_print_charge',
                data: {
                    scene: 'patient_tag_data_is_empty',
                    uuid: this.uuid,
                    info: '患者标签数据为空',
                    data: {
                        printData: data,
                    },
                },
            });
            return;
        }
        console.log('自动打印-患者标签');
        const printOptions = getAbcPrintOptions('患者标签', data);
        AbcPrinter.abcPrint({
            ...printOptions,
            isAutoPrint: true,
        }, this.uuid);
    }

    getTaskName() {
        return 'PatientTagPrintTask';
    }

    static isEnable(config) {
        return config.includes('patient-tag');
    }

    static isPrintAble(printable) {
        return printable && printable.patientTag;
    }

    static getLabel() {
        return getViewDistributeConfig().Print.printOptions.PATIENT_TAG.label;
    }
}
