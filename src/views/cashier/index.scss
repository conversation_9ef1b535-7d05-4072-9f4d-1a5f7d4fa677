@import "src/styles/theme.scss";
@import "components/medicine-item-autocomplete/medicine-item-autocomplete";
@import "./write-by-photo/write-by-photo";
@import "../air-pharmacy/index";
@import "./outpatient-post-data-form/_outpatient-post-data-form";

.cashier-quick-list-wrapper {
    height: 100%;

    .quick-footer-wrapper {
        height: 282px !important;
    }
}

.cashier-container {
    .lock-order {
        max-width: 320px;
        margin-left: 8px;
        font-size: 12px;
        color: $Y2;
    }

    .charge-seal {
        position: absolute;
        top: 5px;
        left: 160px;
        z-index: 1;
        width: 64px;
        transform: scale(1);
    }

    .main-content.cashier-main-content,
    .cashier-center-top-head {
        .header-content {
            padding-right: 20px !important;
        }

        .reference-price-info {
            display: flex;
            align-items: center;
            padding: 2px 6px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);

            &:hover {
                background-color: var(--abc-color-cp-grey4);
            }
        }
    }

    .order-is-charged {
        .abc-charge-table {
            background-color: #f9fafc;

            .table-header {
                background-color: #f9fafc;
            }
        }

        .cashier-outpatient-info {
            background-color: #f9fafc;
        }
    }

    .pay-exception-tips-card {
        margin-bottom: 16px;
    }

    .stamp-enter-active {
        opacity: 0.6;
        transition: all 0.3s ease-in;
    }

    .stamp-leave-active {
        opacity: 1;
    }

    .stamp-enter {
        transform: scale(1.25);
    }

    .charge-summary {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 12px;
        margin-top: 24px;
        font-size: 16px;

        label {
            width: 50px;
        }

        div {
            min-width: 110px;
            text-align: right;
        }
    }

    .charge-form-wrapper,
    .direct-charge-form-wrapper {
        .search-header {
            display: flex;
            align-items: center;
            margin: 16px 0 8px;

            .abc-button-ghost {
                min-width: 98px;
                padding: 0 10px;
                margin-left: 8px;

                i {
                    font-size: 14px;
                    color: $P1;
                }
            }
        }

        .outpatient-bottom-info {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 16px;

            .diagnosed-date {
                display: flex;
                align-items: center;
                margin-left: 12px;
                line-height: 20px;
                color: $T2;
            }
        }
    }

    .seller-selector-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .abc-charge-table {
        .table-body {
            .td {
                &.register {
                    min-width: 140px;
                    max-width: 300px;
                    padding-right: 20px;
                    font-size: 14px;
                    color: $T1;
                }

                &.unitCount {
                    .inputSelect {
                        input {
                            text-align: center;
                            border-left: 1px solid rgba(0, 0, 0, 0);
                        }

                        .cis-icon-dropdown_triangle {
                            display: none;
                        }
                    }

                    .append-input {
                        border-radius: 0;
                    }
                }

                .repeat-item {
                    width: 30px;
                    min-width: 30px;
                    max-width: 30px;
                    font-size: 12px;
                    color: $Y2;
                    text-align: center;
                }
            }
        }
    }

    .charge-select-member-dialog {
        .abc-input-wrapper {
            width: 100%;
        }

        li {
            display: flex;
            align-items: center;
            height: 40px;
            cursor: pointer;
            border-bottom: 1px solid #e6eaee;
        }
    }

    .other-autocomplete {
        position: relative;

        .prepend-input i {
            color: $P1;
        }

        .abc-input__inner {
            height: 40px;
        }
    }
}

.refund-dialog-content,
.select-print-dialog-content {
    .abc-table-list {
        margin-top: 0;
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        .cm-table-content {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            border-top: 1px solid $P3;

            .cm-cell-wrapper {
                display: flex;
                align-items: center;
                width: 33.33%;
                height: 40px;
                padding: 0 16px;

                > div {
                    display: inline-block;
                }
            }
        }

        .cm-usage-info {
            display: flex;
            align-items: center;
            height: 40px;
            padding: 0 16px;
            border-top: 1px solid $P3;
        }
    }
}

.abc-charge-forms {
    width: 100%;

    > div:first-child {
        margin-top: 0;
    }

    .prepend-input i.cis-icon-plus {
        font-size: 12px;
        color: #b9bccb;
    }

    .cashier-table-switch {
        display: flex;
        align-items: center;
        cursor: pointer;

        > span {
            margin-left: 6px;
            color: $T1;
        }

        .iconfont-wrapper {
            width: 16px;
            height: 16px;
            border: 1px solid $P1;
            border-radius: 50%;

            .cis-icon-duigou {
                font-size: 14px;
                color: $theme2;
                visibility: hidden;
            }
        }

        &.is-checked {
            .iconfont-wrapper {
                border-color: $theme2;

                .cis-icon-duigou {
                    visibility: visible;
                }
            }
        }

        &.is-disabled {
            cursor: not-allowed;
        }
    }

    .cut-line {
        width: 1px;
        height: 20px;
        margin: 0 16px;
        background-color: $P6;
    }

    .order-remark {
        display: flex;
        align-items: center;
        min-height: 40px;
        padding: 0;

        > div {
            display: flex;
            flex: 1;
            align-items: center;
            margin-right: 12px;
            line-height: 20px;
            text-align: left;

            .abc-form-item {
                flex: 1;
            }

            &:last-child {
                margin-right: 0;
            }

            .medical-record-suggestions-wrapper {
                left: 0;
                width: 626px;

                &.search-results {
                    width: auto;
                }
            }
        }

        label {
            min-width: 68px;
            color: $T2;
            text-align: left;
        }

        .abc-input-wrapper {
            flex: 1;
            width: 100%;
        }

        .abc-form-item-content {
            width: 100%;
        }

        .order-remark-wrapper {
            margin-right: -12px;
        }

        input,
        .abc-input__inner {
            display: flex;
            align-items: center;
            width: 100%;
            height: 40px;
            padding: 10px;
            border: 1px solid transparent;
            border-left-color: $P2;
            border-radius: 0 0 var(--abc-border-radius-small) 0;
            box-shadow: none !important;

            &:hover,
            &:focus,
            &:active {
                box-shadow: none !important;
            }
        }

        .abc-input__inner:not([disabled]):not(.is-disabled):focus {
            box-shadow: none !important;
        }
    }
}

.abc-charge-table.no-border {
    border-bottom: 0;
    border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;
}

.rule-box {
    display: flex;
    margin-top: 8px;

    .tag {
        span {
            padding: 2px;
            margin-right: 8px;
            font-size: 12px;
            line-height: 14px;
            color: #ff3029;
            border: 1px solid #ff3029;
            border-radius: 2px;
        }
    }

    .cut-content {
        flex: 1;

        .line {
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #7a8794;
        }
    }
}
