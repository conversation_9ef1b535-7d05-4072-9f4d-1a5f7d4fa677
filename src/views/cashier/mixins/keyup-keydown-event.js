import {
    off, on,
} from 'utils/dom';
import { checkHasAbcDialog } from '@/utils';
import useDisabledScanBarcode from '@/hooks/business/use-disabled-scan-barcode';
export default {
    data() {
        return {
            currentActiveIndex: 0,
        };
    },

    setup() {
        const {
            isDisabledScanBarcode,
        } = useDisabledScanBarcode();
        return {
            isDisabledScanBarcode,
        };
    },

    mounted() {
        on(document, 'keydown', this.keydownHandle);
    },
    beforeDestroy() {
        off(document, 'keydown', this.keydownHandle);
    },
    methods: {
        keydownHandle(event) {
            const hasDialog = checkHasAbcDialog();
            if (hasDialog) return;

            if (this.disabledKeyboard) return;
            if (this.disabledScanBarcode) return;
            if (this.isDisabledScanBarcode) return;
            const KEY_UP = 38;
            const KEY_DOWN = 40;
            const KEY_ENTER = 13;
            if (event.keyCode === KEY_DOWN || event.keyCode === KEY_ENTER) {
                if (document.activeElement === document.body) {
                    let len = 0;
                    this.postData.chargeForms.forEach((form) => {
                        len += form.chargeFormItems.length;
                    });
                    if (len > this.currentActiveIndex) {
                        this.currentActiveIndex += 1;
                    } else {
                        this.currentActiveIndex = len;
                        this._timer = setTimeout(() => {
                            $('.search-header input').focus();
                        }, 1);
                    }
                    return false;
                }
                event.cancelBubble = true;
                event.returnValue = false;
                if (event.preventDefault) event.preventDefault();
                if (event.stopPropagation) event.stopPropagation();

            } else if (event.keyCode === KEY_UP) {
                if (document.activeElement === document.body) {
                    if (this.currentActiveIndex >= 1) {
                        this.currentActiveIndex -= 1;
                    }
                    return false;
                }
                event.cancelBubble = true;
                event.returnValue = false;
                if (event.preventDefault) event.preventDefault();
                if (event.stopPropagation) event.stopPropagation();

            }
        },
    },
};
