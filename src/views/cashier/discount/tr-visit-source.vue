<template>
    <div
        class="visit-source-tr discount-tr"
        :class="{
            'is-disabled': disabledOperation,
            'is-simple': isSimple,
        }"
        @click="handleClick"
    >
        <abc-tag-v2
            variant="outline"
            theme="danger"
            shape="round"
            size="mini"
            style="margin-right: 10px;"
        >
            推荐
        </abc-tag-v2>

        <div class="discount-info">
            <div class="discount-info-item">
                <div v-if="visitSourceFromName" class="visit-source-info">
                    <span>{{ visitSourceFromName }}</span>
                </div>
                <abc-button
                    v-else-if="!disabledOperation"
                    variant="text"
                    size="small"
                    @click.stop="handleClick"
                >
                    选择本次推荐
                </abc-button>
            </div>
        </div>

        <select-visit-source-dialog
            v-if="showVisitSourceDialog"
            v-model="showVisitSourceDialog"
            :visit-source-info="visitSourceInfo"
            :patient-source-type="patientSourceType"
            @confirm="handleConfirm"
        >
        </select-visit-source-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import SelectVisitSourceDialog from './select-visit-source-dialog.vue';
    import { RecommendService } from '@/service/recommend';

    export default {
        name: 'TrVisitSource',
        components: {
            SelectVisitSourceDialog,
        },
        provide() {
            return {
                main: this,
            };
        },
        props: {
            disabledOperation: {
                type: Boolean,
                default: false,
            },
            isSimple: {
                type: Boolean,
                default: false,
            },
            postData: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                showVisitSourceDialog: false,
                patientSourceType: [],
            };
        },
        computed: {
            visitSourceInfo() {
                const {
                    visitSourceId,
                    visitSourceFrom,
                    visitSourceFromName,
                } = this.postData || {};

                return {
                    visitSourceId: visitSourceId || null,
                    visitSourceName: null,
                    visitSourceFrom: visitSourceFrom || null,
                    visitSourceFromName: visitSourceFromName || null,
                };
            },
            visitSourceFromName() {
                // 确认拉取到了就诊来源类型
                if (this.patientSourceType) {
                    const visitSourceList = RecommendService.getInstance().initCascaderValue({
                        ...this.visitSourceInfo,
                    });
                    return visitSourceList?.length ? visitSourceList.map((item) => item.label).join('-') : '';
                }
                return '';
            },
        },
        async created() {
            await this.getListSource();
        },

        methods: {
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                this.patientSourceType = RecommendService.getInstance().cascaderOptions;
            },
            async fetchAllDoctor() {
                await RecommendService.getInstance().structureOriginOptions();
                this.patientSourceType = RecommendService.getInstance().cascaderOptions;
            },
            handleClick() {
                if (this.disabledOperation) return;
                this.showVisitSourceDialog = true;
            },
            handleConfirm(data) {
                const {
                    visitSourceId,
                    visitSourceFrom,
                    visitSourceFromName,
                } = data || {};

                this.$emit('confirm', {
                    visitSourceId,
                    visitSourceFrom,
                    visitSourceFromName,
                });
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';

    .visit-source-tr.discount-tr {
        .discount-info-item {
            flex-wrap: wrap;
        }

        .visit-source-info {
            margin-right: 8px;
        }
    }
</style>



