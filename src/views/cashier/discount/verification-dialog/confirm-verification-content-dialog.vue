<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        title="确认核销内容"
        size="xlarge"
        :auto-focus="false"
        class="we-clinic-verification-content-dialog-wrapper"
    >
        <abc-layout preset="dialog-table">
            <abc-layout-header>
                <abc-descriptions :column="3" content-padding="0">
                    <template #title>
                        <abc-flex style=" width: 100%; margin: 0 8px;" justify="space-between">
                            <abc-text theme="gray" :bold="false" style="font-weight: normal;">
                                下单人：{{ verificationItemView?.patientInfo?.name }} {{ verificationItemView?.patientInfo?.mobile }}
                            </abc-text>
                            <abc-text theme="gray" style="font-weight: normal;">
                                购买时间：{{ orderDetail.payTime ? parseTime(orderDetail.payTime, 'y-m-d h:i:s', true) : '' }}
                            </abc-text>
                            <abc-text theme="gray" style="font-weight: normal;">
                                订单号：{{ verificationItemView?.orderId }}
                            </abc-text>
                        </abc-flex>
                    </template>
                    <abc-descriptions-item :span="3">
                        <abc-list
                            :data-list="listData"
                            readonly
                            size="large"
                        >
                            <template
                                #prepend="{ item }"
                            >
                                <abc-image
                                    :src="item.mallGoodsItemSnapShot?.images?.[0]?.url || ''"
                                    :width="54"
                                    :height="54"
                                >
                                </abc-image>
                            </template>
                            <template
                                #default="{
                                    item
                                }"
                            >
                                <abc-flex
                                    flex="1"
                                    vertical
                                    style="height: 54px;"
                                >
                                    <abc-text>
                                        {{ item.mallGoodsItemSnapShot.name }}
                                        <abc-text theme="gray">
                                            * {{ item.count }}
                                        </abc-text>
                                    </abc-text>
                                    <abc-text style="margin-top: 10px;" theme="warning-light">
                                        <abc-money
                                            is-show-space
                                            :symbol-icon-size="12"
                                            :value="orderDetail.paymentTotalPrice"
                                        ></abc-money>
                                    </abc-text>
                                </abc-flex>
                            </template>
                        </abc-list>
                    </abc-descriptions-item>
                </abc-descriptions>
            </abc-layout-header>
            <abc-layout-content>
                <abc-table :data-list="listData" :render-config="renderConfig">
                    <template #count="{ trData }">
                        <abc-flex
                            style="height: 100%;"
                            align="center"
                            justify="center"
                        >
                            <abc-tooltip :disabled="disabledToolTip" placement="top" content="核销数量不可大于收费单开具数量或商城订单剩余数量">
                                <abc-form>
                                    <abc-form-item
                                        trigger="change"
                                    >
                                        <abc-input-number
                                            ref="verifyNumber"
                                            v-model="trData.verifyCount"
                                            fixed-button
                                            button-placement="left"
                                            :config="{
                                                max: maxVerifyCount(trData),
                                                min: 1,
                                            }"
                                            :width="40"
                                            @click="handleFocus"
                                            @change="handleChange($event, trData)"
                                        >
                                        </abc-input-number>
                                    </abc-form-item>
                                </abc-form>
                            </abc-tooltip>
                        </abc-flex>
                    </template>
                </abc-table>
            </abc-layout-content>
        </abc-layout>
        <abc-flex slot="footer" justify="flex-end">
            <abc-button @click="confirmVerification">
                确定核销
            </abc-button>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    import ScanDeviceImg from '@/assets/images/weclinic/scan-device.png';
    import WeShopAPI from 'api/we-shop';
    import { parseTime } from '@abc/utils-date';
    export default {
        name: 'ConfirmationVerificationListDialog',
        props: {
            value: Boolean,
            verificationItemViews: {
                type: Array,
                default: () => [],
            },
            // 是否是收费单核销
            isChargeVerify: {
                type: Boolean,
                default: false,
            },
            currentVerifyCount: {
                type: Number,
                default: 0,
            },
        },
        data() {
            return {
                ScanDeviceImg,
                disabledToolTip: true,
                reqData: {},
                listData: [],
                orderDetail: {},
            };
        },
        computed: {
            chargeVerifyCount() {
                return this.currentVerifyCount;
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'mallGoodsItemSnapShot.name',
                            label: '商品名称',
                            style: {
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                            dataFormatter: (_, item) => item?.mallGoodsItemSnapShot?.name || '',
                        },
                        {
                            key: 'goodsInfo.displayName',
                            label: '对应His药品/项目',
                            style: {
                                width: '130px',
                                minWidth: '130px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                            visible: !this.isChargeVerify,
                            dataFormatter: (_, item) => item?.mallGoodsItemSnapShot?.goodsInfo?.displayName || '',
                        },
                        {
                            key: 'chargeVerifyCount',
                            label: '收费单可核销数量',
                            style: {
                                width: '130px',
                                minWidth: '130px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                            visible: this.isChargeVerify,
                            dataFormatter: () => this.chargeVerifyCount || '0',
                        },
                        {
                            key: 'canVerification',
                            label: '剩余数量/商城订单购买数量',
                            style: {
                                width: '190px',
                                minWidth: '190px',
                                textAlign: 'right',
                            },
                            dataFormatter: (_, item) => {
                                return `${item.canVerificationCount}/${item.count}`;
                            },
                        },
                        {
                            key: 'count',
                            label: '核销次数',
                            style: {
                                width: '122px',
                                minWidth: '122px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'center',
                            },
                        },
                    ].filter((item) => item.visible !== false),
                };
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val, false);
                },
            },
            verificationItemView() {
                return this.verificationItemViews[0] || [{}];
            },
        },
        watch: {
            verificationItemViews: {
                handler(val) {
                    this.listData = val;
                    this.listData.map((item) => {
                        item.verifyCount = 1;
                        return item;
                    });
                },
                immediate: true,
            },
        },
        async created() {
            try {
                const { data } = await WeShopAPI.fetchMallOrder(this.verificationItemView.orderId);
                this.orderDetail = data;
            } catch (err) {
                console.error(err);
            }
        },
        methods: {
            parseTime,
            async confirmVerification() {
                this.reqData.verificationItems = this.verificationItemViews || [];
                this.reqData.sceneType = 0;
                const verificationItemView = this.verificationItemViews[0] || [{}];
                if (this.isChargeVerify) {
                    this.$emit('charge-verification', verificationItemView);
                    this.$emit('input', false,true);
                    return;
                }

                const { count } = verificationItemView;
                verificationItemView.count = verificationItemView.verifyCount;
                try {
                    const { data } = await WeShopAPI.verificationToolVerify(this.reqData);
                    let verificationCode = [];
                    data?.verificationLogViews.forEach((item) => {
                        if (item?.usedVerificationCodes) {
                            verificationCode = verificationCode.concat(item?.usedVerificationCodes);
                        }
                    });
                    this.$emit('refresh');
                    this.message = this.$message({
                        type: 'success',
                        noDialogAnimation: true,
                        title: '核销成功',
                        showConfirm: false,
                        cancelText: '关闭',
                        size: 'small',
                        content: [`核销码：${verificationCode.join(',')}`, '订单已核销，可在微商城订单中查看本次核销记录'],
                        onCancel: () => {
                            this.$emit('input', false, true);
                        },
                    });
                    this.$emit('input', false, true);
                } catch (e) {
                    verificationItemView.count = count;
                }
            },
            maxVerifyCount(trData) {
                if (this.isChargeVerify) {
                    return Math.min(this.chargeVerifyCount, trData.canVerificationCount);
                }
                return trData.canVerificationCount;
            },
            handleFocus() {
                this.$nextTick(() => {
                    const input = this.$refs.verifyNumber.$el.querySelector('input');
                    input.setSelectionRange(0, input.value.length);
                });
            },
            handleChange(val, trData) {
                this.disabledToolTip = val !== this.maxVerifyCount(trData);
            },
        },
    };
</script>
<style lang="scss">
.we-clinic-verification-content-dialog-wrapper {
    .abc-list-item {
        background-color: #f9fafc;
    }
}
</style>
