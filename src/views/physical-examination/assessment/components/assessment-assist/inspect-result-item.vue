<template>
    <div class="inspect-result-item-wrapper">
        <header>
            <div class="inspect-result-item-title-left">
                <h3 class="ellipsis">
                    {{ item.goodsName }}
                </h3>
                <span>{{ formatDate(item.lastArchived, 'MM-DD HH:mm') }}</span>
            </div>
            <div class="inspect-result-item-title-right">
                <span>检查人：{{ item.results?.checkerName }}</span>
            </div>
        </header>
        <main v-if="item.results">
            <inspect-detail :results="item.results"></inspect-detail>
        </main>
    </div>
</template>

<script>
    import { formatDate } from '@abc/utils-date';

    export default {
        name: 'InspectResultItem',
        components: {
            InspectDetail: () => import('views/physical-examination/assessment/components/assessment-assist/inspect-detail.vue'),
        },
        props: {
            item: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                checkerName: '', //检查人
                results: [],
            };
        },
        methods: {
            formatDate,
        },
    };
</script>

<style lang="scss" scoped>
@import '~styles/theme.scss';

.inspect-result-item-wrapper {
    border-bottom: 1px solid $P6;

    &:last-child {
        border-bottom: none;
    }

    header {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        padding: 0 6px 0 16px;
        background: $abcBgDisabled;
        border-bottom: 1px solid $P6;

        .inspect-result-item-title-left {
            display: flex;
            align-items: center;
            max-width: 70%;

            h3 {
                margin-right: 8px;
                font-weight: bold;
            }

            span {
                min-width: fit-content;
                color: $T2;
            }
        }

        .inspect-result-item-title-right {
            min-width: fit-content;
            font-size: 13px;
            color: $T2;
        }
    }

    main {
        font-size: 12px;
    }

    footer {
        padding: 10px 0;
        font-size: 13px;
    }
}
</style>
