<template>
    <abc-dialog
        v-if="showModal"
        v-model="showModal"
        title="人员档案资料"
        class="public-health-doc-info-dialog-wrapper"
        append-to-body
        content-styles="width: 600px; max-height: 400px;"
    >
        <div v-abc-loading="isLoading" style="position: relative; height: 100%;">
            <abc-content-empty v-if="!isLoading && !healthDocInfo.id">
            </abc-content-empty>

            <template v-else-if="healthDocInfo.id">
                <public-health-patient-card :health-doc-info="healthDocInfo"></public-health-patient-card>
            </template>
        </div>
        <template #footer>
            <abc-flex justify="flex-end">
                <abc-button @click="showModal = false">
                    确定
                </abc-button>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import PublicHealthAPI from 'api/physical-examination/pe-public-health';
    import PublicHealthPatientCard from 'views/layout/patient/patient-section/public-health-patient-card.vue';

    export default {
        name: 'HealthDocInfoDialog',

        components: {
            PublicHealthPatientCard,
        },
        model: {
            prop: 'visible',
            event: 'change',
        },
        props: {
            visible: {
                type: Boolean,
                default: false,
            },
            // 健康档案id
            healthDocId: {
                type: String,
                default: '',
                required: true,
            },
        },

        data() {
            return {
                isLoading: false,
                healthDocInfo: {
                    id: '',
                    name: '',
                    birthday: '',
                    community: '',
                    bloodType: '',
                    groupName: '',
                    contactName: '',
                    contactMobile: '',
                    nation: '',
                    company: '',
                    profession: '',
                    education: '',
                    payMode: '',
                    allergicHistory: '',
                    exposureHistory: '',
                    mobile: '',
                    idCard: '',
                    lastPeTime: '',
                    latestUpdateTime: '',
                    address: '',
                    disease: '',
                    diseaseList: [],
                    doctorName: '',
                },
            };
        },

        computed: {
            showModal: {
                get() {
                    return this.visible;
                },
                set(val) {
                    this.$emit('change', val);
                },
            },
        },

        created() {
            this.fetchDetail();
        },

        mounted() {

        },

        methods: {
            async fetchDetail() {
                this.isLoading = true;
                try {
                    const res = await PublicHealthAPI.fetchHealthDocDetailInfo(this.healthDocId);
                    this.healthDocInfo = res;
                } catch (e) {
                    const message = e?.message ?? '获取人员档案信息失败';
                    this.$Toast({
                        message,
                        type: 'error',
                    });
                } finally {
                    this.isLoading = false;
                }
            },
        },
    };
</script>
