import { formatDate } from '@abc/utils-date';
import {
    GroupOrderStatusEnum, OrderStatusLabelEnum,
} from 'views/physical-examination/constants';

export const PE_GROUP_ORDER_STATUS_OPTIONS = Object.freeze([
    {
        label: '未开始', value: GroupOrderStatusEnum.NOT_START,
    },
    {
        label: '体检中', value: GroupOrderStatusEnum.PE_ING,
    },
    {
        label: '已完成', value: GroupOrderStatusEnum.FINISH,
    },
]);

export const GroupOrderStatusLabelEnum = Object.freeze({
    [GroupOrderStatusEnum.NOT_START]: '未开始',
    [GroupOrderStatusEnum.PE_ING]: '体检中',
    [GroupOrderStatusEnum.TO_GENERAL_COMMENT]: '待总评',
    [GroupOrderStatusEnum.TO_RELEASED]: '待发布',
    [GroupOrderStatusEnum.FINISH]: '已完成',
});

export const createRenderConfig = (fn) => {
    return Object.freeze(
        {
            hasInnerBorder: false,
            list: [
                {
                    label: '订单号',
                    key: 'orderNo',
                    style: {
                        textAlign: 'left',
                        width: '120px',
                        maxWidth: '120px',
                    },
                    // eslint-disable-next-line no-unused-vars
                    customRender(h, row) {
                        return (
                            <div style="color: #005ed9;" class="table-cell">{ row.orderNo }</div>
                        );
                    },
                },
                {
                    label: '机构名称',
                    key: 'organName',
                    style: {
                        textAlign: 'left',
                        flex: 1,
                    },
                },
                {
                    label: '订单名称',
                    key: 'orderName',
                    style: {
                        textAlign: 'left',
                        flex: 1,
                    },
                },
                {
                    label: '创建时间',
                    key: 'created',
                    style: {
                        textAlign: 'left',
                        width: '160px',
                        maxWidth: '160px',
                    },
                    dataFormatter: (val) => {
                        return formatDate(val, 'YYYY-MM-DD HH:mm');
                    },
                },
                {
                    label: '开始时间',
                    key: 'beginDate',
                    colType: 'date',
                    style: {
                        textAlign: 'left',
                        width: '120px',
                        maxWidth: '120px',
                    },
                },
                {
                    label: '结束时间',
                    key: 'endDate',
                    colType: 'date',
                    style: {
                        textAlign: 'left',
                        width: '120px',
                        maxWidth: '120px',
                    },
                },
                {
                    label: '剩余天数',
                    key: 'leftDays',
                    style: {
                        textAlign: 'right',
                        width: '80px',
                        maxWidth: '80px',
                    },
                },
                {
                    label: '已检人数',
                    key: 'examinedNum',
                    style: {
                        textAlign: 'right',
                        width: '88px',
                        maxWidth: '88px',
                    },
                },
                {
                    label: '已执行金额',
                    key: 'executedAmount',
                    style: {
                        textAlign: 'right',
                        width: '102px',
                        maxWidth: '102px',
                    },
                    colType: 'money',
                },
                {
                    label: '销售人',
                    key: 'salesEmployeeName',
                    style: {
                        textAlign: 'left',
                        width: '88px',
                        maxWidth: '88px',
                    },
                },
                {
                    label: '机构联系人',
                    key: 'peOrganContactName',
                    style: {
                        textAlign: 'left',
                        width: '88px',
                        maxWidth: '88px',
                    },
                },
                {
                    label: '结算状态',
                    key: 'payStatusDisplayName',
                    style: {
                        textAlign: 'center',
                        width: '88px',
                        maxWidth: '88px',
                    },
                },
                {
                    label: '操作',
                    key: 'operate',
                    style: {
                        textAlign: 'center',
                        width: '80px',
                        maxWidth: '80px',
                    },
                    // eslint-disable-next-line no-unused-vars
                    customRender(h, row) {
                        return (
                            <div class="table-cell">
                                <abc-button type="text" onClick={(event) => {
                                    event.stopPropagation();
                                    fn(row);
                                }}>
                                    复制
                                </abc-button>
                            </div>
                        );
                    },
                },
            ],
        },
    );
};

export const GROUP_TAB_MENU = Object.freeze({
    order: 0,
    personnel: 1,
});

export const GENDER_ENUM = Object.freeze({
    no: 0,
    male: 1,
    female: 2,
});

export const MARITAL_ENUM = Object.freeze({
    no: 0,
    unmarried: 1,
    married: 2,
});

export const MaritalGetWayLabel = Object.freeze({
    [MARITAL_ENUM.unmarried]: '未婚',
    [MARITAL_ENUM.married]: '已婚',
});

export const GenderGetWayLabel = Object.freeze({
    [GENDER_ENUM.male]: '男',
    [GENDER_ENUM.female]: '女',
});

export const GENDER_OPTIONS = Object.freeze([
    {
        label: '不限制',
        value: GENDER_ENUM.no,
    },
    {
        label: '男',
        value: GENDER_ENUM.male,
    },
    {
        label: '女',
        value: GENDER_ENUM.female,
    },
]);

export const MARITAL_OPTIONS = Object.freeze([
    {
        label: '不限制',
        value: MARITAL_ENUM.no,
    },
    {
        label: '未婚',
        value: MARITAL_ENUM.unmarried,
    },
    {
        label: '已婚',
        value: MARITAL_ENUM.married,
    },
]);

export const MARITAL_OPTIONS_V2 = Object.freeze([
    {
        label: '未婚',
        value: MARITAL_ENUM.unmarried,
    },
    {
        label: '已婚',
        value: MARITAL_ENUM.married,
    },
]);

export const GENDER_OPTIONS_V2 = Object.freeze([
    {
        label: '男',
        value: GENDER_ENUM.male,
    },
    {
        label: '女',
        value: GENDER_ENUM.female,
    },
]);


export const getPersonnelInfoRenderConfig = (handleClick) => {
    return {
        hasInnerBorder: false,
        list: [
            {
                label: ' ',
                isCheckbox: true,
                style: {
                    width: '36px',
                    maxWidth: '36px',
                    'textAlign': 'left',
                },
            },
            {
                key: 'patientName',
                label: '客户姓名',
                style: {
                    width: '160px',
                    flex: 'none',
                    maxWidth: '160px',
                },
                customRender: (h,row) => {
                    const patient = row.patient ?? {};
                    return (
                        <div class="table-cell" style="color:#005ED9">
                            <abc-text style={{
                                width: '57px',
                            }} class="ellipsis" title={patient.name ?? ''}>{patient.name ?? ''}</abc-text>
                            <abc-text style="margin-left: 4px">{patient.sex ?? ''}</abc-text>
                            <abc-text style="margin-left: 4px">{patient.age?.year ? `${patient.age.year}岁` : ''}</abc-text>
                        </div>
                    );
                },
            },{
                key: 'marital',
                label: '婚姻状况',
                style: {
                    width: '100px',
                    textAlign: 'center',
                    maxWidth: '100px',
                },
                customRender: (h,row) => {
                    const { patient } = row;
                    const str = MaritalGetWayLabel[patient?.marital] || '';
                    return (
                        <div class="table-cell">{str}</div>
                    );
                },
            },{
                key: 'mobile',
                label: '联系电话',
                style: {
                    width: '140px',
                    maxWidth: '140px',
                },
                customRender: (h,row) => {
                    const { patient } = row;
                    return (
                        <div class="table-cell">{patient?.mobile}</div>
                    );
                },
            },{
                key: 'name',
                label: '体检套餐',
                style: {
                    width: '300px',
                    flex: 1,
                },
            },{
                key: 'orderPrice',
                label: '订单金额',
                style: {
                    width: '140px',
                    textAlign: 'right',
                    maxWidth: '140px',
                    paddingRight: '12px',
                },
                colType: 'money',
            },{
                key: 'businessTime',
                label: '预约时间',
                style: {
                    width: '140px',
                    maxWidth: '140px',
                },
                customRender: (h,row) => {
                    const { businessTime } = row;
                    return (
                        <div class="table-cell">{formatDate(businessTime, 'YYYY-MM-DD')}</div>
                    );
                },
            }, {
                key: 'status',
                label: '体检状态',
                style: {
                    width: '140px',
                    textAlign: 'center',
                    maxWidth: '140px',
                },
                customRender: (h, row) => {
                    const { status } = row;
                    const str = OrderStatusLabelEnum[status];
                    return (
                        <div class="table-cell">{str}</div>
                    );
                },
            },{
                key: 'department',
                label: '部门',
                style: {
                    width: '120px',
                    maxWidth: '120px',
                },
                customRender: (h, row) => {
                    const { patient } = row;
                    return (
                        <div class="table-cell" title={patient.patientDepartment?.name}>
                            <span class='ellipsis'>
                                { patient.patientDepartment?.name }
                            </span>
                        </div>
                    );
                },
            },{
                key: 'operate',
                label: '操作',
                style: {
                    width: '120px',
                    maxWidth: '120px',
                },
                customRender: (h, row) => {
                    return (
                        <div class="table-cell">
                            <abc-button variant="text" type="primary" onClick={(e) => handleClick(e, row)}>
                                查看订单
                            </abc-button>
                        </div>
                    );
                },
            },
        ],
    };
};

export const ImportErrorEnum = Object.freeze({
    name: 1,
    gender: 2,
    marital: 3,
    pregnant: 4,
    idCard: 5,
    identityName: 6,
    contact: 7,
    planName: 8,
    department: 9,
    idCardRepeat: 11,
    idCardRepeatInDataBase: 12,
});

export const ImportErrorLabel = Object.freeze({
    1: '姓名缺失',
    2: '性别缺失',
    3: '婚姻状态缺失',
    4: '怀孕状态缺失',
    5: '证件号码缺失或错误',
    6: '身份信息不匹配',
    7: '手机号码错误',
    8: '未匹配到方案',
    9: '部门匹配错误',
    11: '证件号码重复',
    12: '相同证件号码的人员已导入',
});


