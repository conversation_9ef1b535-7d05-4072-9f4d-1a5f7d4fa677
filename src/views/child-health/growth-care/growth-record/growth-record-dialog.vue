<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :auto-focus="false"
        content-styles="width: 422px"
        class="dialog-growth-card"
        custom-class="growth-dialog-wrapper"
        :title="dialogTitle"
        append-to-body
    >
        <abc-form ref="growthForm">
            <div class="record-item-wrapper">
                <div class="record-item">
                    <label>检查时间</label>
                    <abc-form-item class="record-date-picker">
                        <div class="date-text">
                            {{ bodyGrowthRecord.recordTime }}
                            <i v-if="!isToday" class="iconfont cis-icon-cross_small" @click="changeToToday"></i>
                        </div>
                        <abc-date-picker
                            v-model="bodyGrowthRecord.recordTime"
                            :width="160"
                            :picker-options="pickerOptions"
                            value-format="YYYY-MM-DD"
                            @change="changePatientAge"
                        >
                            <abc-button type="text">
                                修改
                            </abc-button>
                        </abc-date-picker>
                    </abc-form-item>
                </div>
            </div>
            <div class="record-item-wrapper">
                <div class="record-item">
                    <label>年龄</label>
                    <patient-age :age.sync="bodyGrowthRecord.age" :day-append-width="94"></patient-age>
                </div>
            </div>

            <div class="record-item-wrapper">
                <div class="record-item">
                    <label>身高</label>
                    <abc-form-item>
                        <abc-input
                            v-model="bodyGrowthRecord.height"
                            v-abc-focus-selected
                            type="number"
                            :input-custom-style="{ 'text-align': 'center' }"
                            class="append-input-wrapper"
                            :config="{ formatLength: 2, max: 1000 }"
                            :width="68"
                        >
                            <span slot="append">cm</span>
                        </abc-input>
                    </abc-form-item>
                </div>
                <div class="record-item">
                    <label>体重</label>
                    <abc-form-item>
                        <abc-input
                            v-model="bodyGrowthRecord.weight"
                            v-abc-focus-selected
                            type="number"
                            :config="{ formatLength: 2, max: 500 }"
                            class="append-input-wrapper"
                            :input-custom-style="{ 'text-align': 'center' }"
                            :width="68"
                        >
                            <span slot="append">kg</span>
                        </abc-input>
                    </abc-form-item>
                </div>
            </div>

            <div class="record-item-wrapper">
                <div class="record-item">
                    <label>头围</label>
                    <abc-form-item>
                        <abc-input
                            v-model="bodyGrowthRecord.headSize"
                            v-abc-focus-selected
                            :config="{ formatLength: 2, max: 1000 }"
                            class="append-input-wrapper"
                            :input-custom-style="{ 'text-align': 'center' }"
                            :width="68"
                            type="number"
                        >
                            <span slot="append">cm</span>
                        </abc-input>
                    </abc-form-item>
                </div>
            </div>

            <div class="record-split-line"></div>

            <div class="record-item-wrapper">
                <div class="record-item">
                    <label>上部量</label>
                    <abc-form-item>
                        <abc-input
                            v-model="bodyGrowthRecord.upperBodyLength"
                            v-abc-focus-selected
                            :config="{ formatLength: 2, max: 1000 }"
                            class="append-input-wrapper"
                            :input-custom-style="{ 'text-align': 'center' }"
                            :width="68"
                            type="number"
                            @input="inputUpperBodyHandler"
                        >
                            <span slot="append">cm</span>
                        </abc-input>
                    </abc-form-item>
                </div>
                <div class="record-item">
                    <label>下部量</label>
                    <abc-form-item :validate-event="validateLength">
                        <abc-input
                            v-model="bodyGrowthRecord.lowerBodyLength"
                            v-abc-focus-selected
                            :config="{ formatLength: 2, max: 1000 }"
                            class="append-input-wrapper"
                            :input-custom-style="{ 'text-align': 'center' }"
                            :width="68"
                            type="number"
                        >
                            <span slot="append">cm</span>
                        </abc-input>
                    </abc-form-item>
                </div>
            </div>
            <div class="record-split-line"></div>
            <div class="record-item-wrapper">
                <div class="record-item">
                    <label>前囟</label>
                    <abc-form-item>
                        <abc-select v-model="bodyGrowthRecord.isBregmaClose" :width="100">
                            <abc-option
                                v-for="(option, index) in _frontFontalleOptions"
                                :key="index"
                                :value="option.value"
                                :label="option.label"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <template v-if="bodyGrowthRecord.isBregmaClose === 0">
                        <abc-form-item style="margin-left: 8px;" required>
                            <abc-input
                                v-model="bodyGrowthRecord.bregmaLength"
                                v-abc-focus-selected
                                class="append-input-wrapper"
                                :input-custom-style="{ 'text-align': 'center' }"
                                :config="{ formatLength: 2, max: 1000 }"
                                :width="58"
                                type="number"
                            >
                                <span slot="append">cm</span>
                            </abc-input>
                        </abc-form-item>
                        <div style="width: 16px; text-align: center;">
                            *
                        </div>
                        <abc-form-item required>
                            <abc-input
                                v-model="bodyGrowthRecord.bregmaWidth"
                                v-abc-focus-selected
                                class="append-input-wrapper"
                                :input-custom-style="{ 'text-align': 'center' }"
                                :config="{ formatLength: 2, max: 1000 }"
                                :width="58"
                                type="number"
                            >
                                <span slot="append">cm</span>
                            </abc-input>
                        </abc-form-item>
                    </template>
                </div>
            </div>
            <div class="record-item-wrapper" style="margin-bottom: 0;">
                <div class="record-item">
                    <label>牙齿</label>
                    <abc-form-item>
                        <abc-input
                            v-model="bodyGrowthRecord.teeth"
                            v-abc-focus-selected
                            :width="68"
                            type="number"
                            :input-custom-style="{ 'text-align': 'center' }"
                            :config="{ max: 100, supportZero: true }"
                            class="append-input-wrapper"
                        >
                            <span slot="append">颗</span>
                        </abc-input>
                    </abc-form-item>
                </div>
            </div>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="buttonLoading" @click="confirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="cancel">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { FRONT_FONTANELLE_OPTIONS } from '../config';
    import PatientAge from '../../components/patient-age/patient-age';
    import ChildHeathAPI from 'api/child-health/child-care';
    import { parseTime } from 'utils/index';
    import { birthday2age } from 'utils/index';

    import clone from 'utils/clone';

    export default {
        name: 'GrowthRecordDialog',
        components: {
            PatientAge,
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            postData: {
                type: Object,
                required: true,
            },
            id: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                buttonLoading: false,
                loading: false,
                bodyGrowthRecord: {
                    age: (this.postData.patient && clone(this.postData.patient.age)) || {
                        year: '',
                        month: '',
                        day: '',
                    },
                    height: '',
                    weight: '',
                    sitHeight: '',
                    headSize: '',
                    upperBodyLength: '',
                    lowerBodyLength: '',
                    teeth: '',
                    isBregmaClose: null,
                    bregmaLength: '',
                    bregmaWidth: '',
                    recordTime: parseTime(new Date(), 'y-m-d', true), // 记录时间
                },
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            dialogTitle() {
                return this.id ? '编辑生长记录' : '新增生长记录';
            },
            pickerOptions() {
                return {
                    disabledDate: (date) => {
                        return date > new Date();
                    },
                };
            },
            /**
             * @desc 记录时间是否是当天
             * <AUTHOR>
             * @date 2020-10-27 15:58:58
             */
            isToday() {
                return new Date() - new Date(this.bodyGrowthRecord.recordTime) <= 24 * 60 * 60 * 1000;
            },
        },
        created() {
            this._frontFontalleOptions = FRONT_FONTANELLE_OPTIONS;
            if (this.id) {
                this.fetchDetail();
            }
        },
        methods: {
            async fetchDetail() {
                this.loading = true;
                try {
                    const { data } = await ChildHeathAPI.fetchGrowthRecord(this.id);
                    const {
                        age,
                        height,
                        weight,
                        sitHeight,
                        headSize,
                        upperBodyLength,
                        lowerBodyLength,
                        teeth,
                        isBregmaClose,
                        bregmaLength,
                        bregmaWidth,
                        recordTime,
                    } = data;
                    this.bodyGrowthRecord = Object.assign(this.bodyGrowthRecord, {
                        age,
                        height,
                        weight,
                        sitHeight,
                        headSize,
                        upperBodyLength,
                        lowerBodyLength,
                        teeth,
                        isBregmaClose,
                        bregmaLength,
                        bregmaWidth,
                        recordTime,
                    });
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            confirm() {
                Object.assign(this.bodyGrowthRecord.age);
                this.$refs.growthForm.validate((val) => {
                    if (val) {
                        if (this.id) {
                            this.updateRecord();
                        } else {
                            this.createRecord();
                        }
                    }
                });
            },
            cancel() {
                this.showDialog = false;
            },

            transPostData() {
                const postData = Object.assign({}, this.postData);
                postData.bodyGrowthRecord = this.bodyGrowthRecord;
                return postData;
            },
            async createRecord() {
                try {
                    this.buttonLoading = true;
                    const postData = this.transPostData();
                    await ChildHeathAPI.createGrowthRecord(postData);
                    this.$emit('update');
                    this.buttonLoading = false;
                    this.showDialog = false;
                } catch (e) {
                    this.buttonLoading = false;
                }
            },
            async updateRecord() {
                try {
                    this.buttonLoading = true;
                    const postData = this.transPostData();
                    await ChildHeathAPI.updateGrowthRecord(this.id, postData);
                    this.$emit('update');
                    this.buttonLoading = false;
                    this.showDialog = false;
                } catch (e) {
                    this.buttonLoading = false;
                }
            },
            inputUpperBodyHandler() {
                if (this.bodyGrowthRecord.height && this.bodyGrowthRecord.upperBodyLength) {
                    const res = this.bodyGrowthRecord.height - this.bodyGrowthRecord.upperBodyLength;
                    this.bodyGrowthRecord.lowerBodyLength = res <= 0 ? 0 : res;
                }
            },
            validateLength(val, callback) {
                if (
                    (this.bodyGrowthRecord.upperBodyLength === '' || this.bodyGrowthRecord.upperBodyLength === null) &&
                    (this.bodyGrowthRecord.lowerBodyLength === '' || this.bodyGrowthRecord.lowerBodyLength === null)
                ) {
                    return callback({
                        validate: true,
                    });
                }
                const tempHeight = +this.bodyGrowthRecord.lowerBodyLength + +this.bodyGrowthRecord.upperBodyLength;

                if (tempHeight * 100 !== +this.bodyGrowthRecord.height * 100) {
                    callback({
                        validate: false,
                        message: '数据异常，身高=上部量+下部量',
                    });
                }
                callback({
                    validate: true,
                });
            },

            /**
             * @desc 跳回当天时间
             * <AUTHOR>
             * @date 2020-10-27 15:50:15
             */
            changeToToday() {
                this.bodyGrowthRecord.recordTime = parseTime(new Date(), 'y-m-d', true);
                this.changePatientAge();
            },
            /**
             * @desc 选择了记录时间之后再推算患者当时记录的年龄
             * <AUTHOR>
             * @date 2020-10-27 16:17:13
             */
            changePatientAge() {
                if (this.postData.patient && this.postData.patient.birthday) {
                    const currentAge = birthday2age(this.postData.patient.birthday, this.bodyGrowthRecord.recordTime);
                    Object.assign(this.bodyGrowthRecord.age, currentAge);
                }
            },
        },
    };
</script>
