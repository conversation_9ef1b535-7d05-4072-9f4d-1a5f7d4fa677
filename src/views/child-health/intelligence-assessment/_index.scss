@import 'src/styles/theme.scss';

.intelligence-assessment-wrapper {
    .header {
        height: 48px;
    }

    .abc-card-body {
        padding: 0 20px 20px 20px;
    }

    /* 图片单选问卷 */
    .question-select-dialog {
        .question-card {
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            float: left;
            width: 260px;
            height: 68px;
            padding: 12px;
            margin: 0 8px 8px 0;
            cursor: pointer;
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            &:nth-of-type(3n) {
                margin-right: 0;
            }

            .question-operator {
                display: none;
            }

            &:hover .question-operator {
                position: absolute;
                top: 0;
                right: 0;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                justify-content: center;
                width: 44px;
                height: 100%;
                padding: 12px 0;
                border-left: 1px solid $P6;

                span {
                    line-height: 16px;
                    color: $theme1;
                    text-align: center;
                }

                .disabled {
                    color: $T3;
                }

                button {
                    width: initial !important;
                    margin-left: initial !important;
                    font-size: 12px;
                }
            }
        }

        .question-name {
            height: 20px;
            font-size: 14px;
            line-height: 20px;
        }

        .question-description {
            height: 16px;
            padding-right: 32px;
            font-size: 12px;
            line-height: 16px;
            color: $T2;
        }
    }

    /* 表格问卷 */
    .question-dialog {
        table,
        td {
            border: 1px solid $P6;
        }

        table {
            width: 100%;

            tr:hover {
                background: $P4;
            }

            td {
                position: relative;
                height: 40px;
                padding: 0 12px;

                .abc-checkbox-wrapper {
                    width: 33.3333%;
                    height: 40px;
                    margin: 0;
                    color: $T1;
                }

                .abc-radio-group {
                    display: flex;
                }

                .abc-radio {
                    min-width: 140px;
                    height: 40px;
                    margin: 0;
                }

                .tips {
                    position: absolute;
                    top: 50%;
                    right: 12px;
                    margin-top: -8px;

                    i {
                        color: $P3;
                    }
                }
            }
        }
    }

    /* 操作表格 */
    .table-operator {
        display: flex;
        align-items: center;
        justify-content: start;
        height: 100%;
        font-size: 0;

        & > button {
            margin-left: 0;
            font-size: 14px;
            color: $theme2;
            cursor: pointer;

            &:first-child {
                margin-right: 4px;
            }

            &:last-child {
                color: $R2;
            }
        }

        .abc-button.is-disabled {
            color: $T2;
        }
    }

    .abc-button-pagination-wrapper {
        margin-top: 16px;
    }

    /* 问卷结果 */
    .question-result {
        td {
            vertical-align: top;

            &:first-child {
                white-space: nowrap;
            }

            span {
                display: block;

                &:nth-of-type(2) {
                    font-size: 12px;
                    color: $T2;
                }
            }
        }
    }
}
