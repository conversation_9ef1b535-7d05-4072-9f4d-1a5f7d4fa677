<template>
    <abc-dialog
        v-if="visible"
        ref="dialog"
        v-model="visible"
        title=" "
        header-style="padding: 0 24px"
        append-to-body
        :content-styles="contentStyle"
        :auto-focus="false"
        custom-class="chronic-care-form-dialog"
    >
        <abc-tabs-v2
            slot="title"
            v-model="activeTab"
            :option="tabOptions"
            disabled-tips-class="abc-tipsy--e"
            size="huge"
            :border="false"
            @change="changeTab"
        ></abc-tabs-v2>

        <div v-abc-loading="contentLoading" class="dialog-content clearfix">
            <p-c-form
                v-if="activeTab === 0"
                ref="form"
                v-model="postFormData"
                :disabled="currentDisabled"
                :readonly="currentReadonly"
                :external="{
                    MedicationRecord: {
                        handleStandardMedicineSearch,
                    },
                }"
                :uploader-options="{
                    accept: '',
                    onPreview: onPreview,
                    onError: onError,
                    maxLength: 100,
                    maxFileSize: 20 * 1e6, // MB
                    httpRequest: uploadFiles,
                }"
            ></p-c-form>
            <evaluation-report
                v-else-if="activeTab === 1"
                :record-id="currentRecordId"
                :chronic-care-type="currentChronicCareType"
                :evaluation-record-id="currentEvaluationRecordId"
                :report-data="reportData"
                :open-revisit-function="openRevisitFunction"
                @change-treatment-record="changeTreatmentRecordHandler"
            ></evaluation-report>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button v-if="canPrintForm" type="blank" @click="handlePrint">
                打印
            </abc-button>
            <wechat-push
                v-if="pushFunction && !disabled"
                :patient="nePpatient"
                :chain-id="chainId"
                @update-patient="nePpatient = $event"
            >
                <template
                    #default="{
                        isPush, wxBindStatus,wxStatus
                    }"
                >
                    <push-button
                        :is-push="isPush"
                        :wx-bind-status="wxBindStatus"
                        :wx-status="wxStatus"
                        :loading.sync="pushLoading"
                        :disabled="disabled"
                        :patient="nePpatient"
                        @push="pushConfirm"
                    ></push-button>
                </template>
            </wechat-push>
            <abc-button
                v-if="deleteFunction && activeTab === 0 && currentEvaluationRecordId"
                type="danger"
                :loading="deleteLoading"
                @click.stop="deleteConfirm"
            >
                删除
            </abc-button>
            <div class="layout-right-buttons">
                <template v-if="activeTab === 0">
                    <abc-button v-if="submitFunction || updateFunction" :loading="btnLoading" @click.stop="confirm">
                        {{ currentEvaluationRecordId ? '保存' : '完成' }}
                    </abc-button>
                </template>
                <abc-button type="blank" @click.stop="visible = false">
                    关闭
                </abc-button>
            </div>
        </div>

        <abc-preview
            v-if="isShowPreview"
            v-model="isShowPreview"
            :lists="previewImagesList"
            :index="previewImageIndex"
        ></abc-preview>
    </abc-dialog>
</template>

<script>
    import FormAPI from 'src/api/form/index';
    import GoodsAPI from 'src/api/goods/index';
    import ChronicCareAPI from 'src/api/chronic-care';
    import TreatmentAPI from 'api/treatment';

    import { printChronicCareHandler } from '@/printer/print-handler';
    import Clone from 'utils/clone';

    import EvaluationReport from './report';
    import WechatPush from '@/views/crm/wechat-push/index.vue';
    import PushButton from '@/views/crm/wechat-push/push-button.vue';
    import { windowOpen } from '@/core/navigate-helper';

    export default {
        // 生成 高血压、糖尿病 评估表弹窗
        name: 'GenerateEvaluationFormDialog',
        components: {
            PCForm: () => import('abc-form-engine/lib/pc'),
            EvaluationReport,
            WechatPush,
            PushButton,
        },
        props: {
            // 慢病档案Id
            recordId: String,
            // 表单模版id
            formTemplateId: {
                type: String,
            },
            // 评估表填写记录id
            evaluationRecordId: {
                type: String,
            },
            chronicCareType: Number,
            dialogWidth: {
                type: [String, Number],
                default: 700,
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            patient: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            submitFunction: {
                type: Function,
            },
            updateFunction: {
                type: Function,
            },
            deleteFunction: {
                type: Function,
            },
            /**
             * @desc 改变了治疗方案的触发函数
             * <AUTHOR> Yang
             * @date 2021-04-09 14:09:28
             */
            changeTreatmentRecordFunction: {
                type: Function,
            },
            canPrintForm: {
                type: Boolean,
                default: true,
            },
            pushFunction: {
                type: Function,
            },
            openRevisitFunction: {
                type: Function,
            },
            createdByName: {
                type: String,
                default: '',
            },
            createdDate: {
                type: String,
                default: '',
            },
            /**
             * @desc 1 问诊单  2 档案 3 评估表
             */
            printFormType: {
                type: Number,
                default: 0,
            },
            chainId: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                nePpatient: this.patient,
                visible: false,
                contentLoading: false,
                btnLoading: false,
                deleteLoading: false,
                pushLoading: false,
                postFormData: {},
                formDataCache: {},
                printFormData: {},
                isShowPreview: false,
                previewImagesList: [],
                previewImageIndex: 0,

                activeTab: 0,
                currentEvaluationRecordId: this.evaluationRecordId, // 当前评估表记录Id

                // 评估报告数据
                reportData: {
                    reportName: '',
                    abnormalItems: [],
                    existedRevisitDates: [],
                    coreData: {},
                    patientInfo: {},
                    revisitTask: null,
                    riskLevel: null,
                    treatmentRecord: null,
                },
                currentRecordId: this.recordId,
                currentChronicCareType: this.chronicCareType,
            };
        },
        computed: {
            contentStyle() {
                let _str = `width: ${
                    typeof this.dialogWidth === 'string' ? this.dialogWidth : `${this.dialogWidth}px`
                };`;
                _str += 'padding: 0 24px 24px;';
                _str += 'minHeight: 600px;maxHeight: 600px;';
                return _str;
            },

            currentReadonly() {
                return this.readonly;
            },

            currentDisabled() {
                return this.disabled;
            },

            tabOptions() {
                let str = '';
                const { key } = this.postFormData;
                if (key && key.indexOf('evaluation') > -1) {
                    str = '评估';
                } else if (key && key.indexOf('revisit') > -1) {
                    str = '随访';
                }
                return [
                    {
                        label: `${str}表`,
                        value: 0,
                    },
                    {
                        label: `${str}报告`,
                        value: 1,
                        disabled: !this.currentEvaluationRecordId,
                        disabledTips: `请先填写${str}表`,
                    },
                ];
            },
        },
        created() {
            // 有记录拉记录，没有则拉表单结构
            if (this.evaluationRecordId) {
                this.fetchEvaluationRecord();
            } else {
                this.fetchFormStruct();
            }
        },
        methods: {
            printChronicCareHandler,
            handlePrint() {
                console.log('this.reportData', this.reportData);
                if (this.activeTab === 0) {
                    printChronicCareHandler(this.printFormData);
                } else {
                    const printData = {
                        ...this.printFormData,
                        treatmentRecord: this.reportData.treatmentRecord,
                        abnormalItems: this.reportData.abnormalItems,
                        showTreatmentRecord: true,
                    };
                    printChronicCareHandler(printData);
                }
            },

            /**
             * @desc 查询评估表数据 + 评估报告
             * <AUTHOR> Yang
             * @date 2021-04-08 09:42:48
             */
            async fetchEvaluationRecord() {
                const { data } = await ChronicCareAPI.fetchChronicEvaluationRecord(this.currentEvaluationRecordId);
                const {
                    archivesId,
                    archivesType,
                    evaluationForm,
                    reportName,
                    abnormalItems,
                    coreData,
                    patientInfo,
                    revisitTask,
                    riskLevel,
                    treatmentRecord,
                    existedRevisitDates,
                } = data;

                if (!this.currentRecordId) {
                    this.currentRecordId = archivesId;
                }
                if (!this.currentChronicCareType) {
                    this.currentChronicCareType = archivesType;
                }
                evaluationForm.fields &&
                    evaluationForm.fields.forEach((item) => {
                        if (item.type === 'PatientInfo') {
                            item.value = patientInfo;
                        }
                    });

                this.postFormData = Clone(evaluationForm);
                this.formDataCache = Clone(evaluationForm);

                Object.assign(this.reportData, {
                    reportName,
                    abnormalItems,
                    coreData,
                    patientInfo,
                    revisitTask,
                    riskLevel,
                    treatmentRecord,
                    existedRevisitDates,
                });

                this.printFormData = Clone(evaluationForm);
                Object.assign(this.printFormData, {
                    readonly: true,
                    createdByName: this.createdByName,
                    createdDate: this.createdDate,
                    printFormType: this.printFormType, // 1 问诊单 2 档案 3 评估表
                });
                this.$nextTick(() => {
                    const { updateDialogHeight } = this.$refs.dialog;
                    if (typeof updateDialogHeight === 'function') {
                        updateDialogHeight();
                    }
                });
            },

            changeTab(index) {
                this.activeTab = index;
            },

            async fetchFormStruct() {
                try {
                    this.contentLoading = true;
                    const { data } = await FormAPI.fetchFormTemplate(this.formTemplateId);
                    data.fields.forEach((item) => {
                        if (item.type === 'PatientInfo') {
                            item.value = this.patient;
                        }
                    });
                    this.postFormData = data;

                    this.printFormData = data;
                    Object.assign(this.printFormData, {
                        readonly: !this.formTemplateId,
                        createdByName: this.createdByName,
                        createdDate: this.createdDate,
                        printFormType: this.printFormType, // 1 问诊单 2 档案 3 评估表
                    });

                    this.$nextTick(() => {
                        this.$refs.dialog.updateDialogHeight();
                    });
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            confirm() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        this.btnLoading = true;
                        if (this.currentEvaluationRecordId) {
                            this.updateFunction &&
                                this.updateFunction(this.postFormData, (data) => {
                                    if (data) {
                                        this.changeTab(1);
                                        this.fetchEvaluationRecord();
                                    }
                                    this.btnLoading = false;
                                });
                        } else {
                            this.submitFunction &&
                                this.submitFunction(this.postFormData, (data) => {
                                    if (data) {
                                        this.changeTab(1);
                                        this.currentEvaluationRecordId = data.id;
                                        this.fetchEvaluationRecord();
                                    }
                                    this.btnLoading = false;
                                });
                        }
                    }
                });
            },

            deleteConfirm() {
                if (this.deleteFunction) {
                    this.deleteLoading = true;
                    this.deleteFunction(this.postFormData, (data) => {
                        if (data) {
                            this.visible = false;
                        }
                        this.deleteLoading = false;
                    });
                }
            },
            pushConfirm() {
                if (this.pushFunction) {
                    this.pushLoading = true;
                    this.pushFunction(this.postFormData, (data) => {
                        if (data) {
                            this.visible = false;
                        }
                        this.pushLoading = false;
                    });
                }
            },

            /**
             * @desc 在评估报告中修改了治疗方案后的触发事件
             * <AUTHOR> Yang
             * @date 2021-04-09 14:06:42
             * @params data 改变后的treatmentRecord
             */
            changeTreatmentRecordHandler(data) {
                this.reportData.treatmentRecord = data;
                if (this.changeTreatmentRecordFunction && typeof this.changeTreatmentRecordFunction === 'function') {
                    this.changeTreatmentRecordFunction(data);
                }
            },

            async uploadFiles(file) {
                // eslint-disable-next-line no-async-promise-executor
                return new Promise(async (resolve, reject) => {
                    if (!file) return;
                    try {
                        const imgRes = await this.$abcPlatform.service.oss.clinicUsageUpload(
                            { filePath: 'chronic-care' },
                            file,
                        );
                        resolve({
                            url: imgRes.url,
                            fileName: file.name,
                            fileSize: file.size,
                            imageWidth: 0,
                            imageHeight: 0,
                        });
                    } catch (err) {
                        console.error(err);
                        // eslint-disable-next-line prefer-promise-reject-errors
                        reject('上传图片失败');
                    }
                });
            },
            onError(error) {
                console.error(error);
                const { message } = error;
                this.$Toast({
                    message,
                    type: 'error',
                });
            },
            onPreview(file, fileList) {
                console.warn(file, fileList);

                if (file.fileType === 'image') {
                    this.previewImagesList = fileList.filter((item) => {
                        return item.fileType === 'image';
                    });
                    this.previewImageIndex = this.previewImagesList.findIndex((item) => {
                        return item.url === file.url;
                    });
                    this.isShowPreview = true;
                } else if (file.fileType === 'pdf') {
                    this.previewFile(file.url);
                } else {
                    windowOpen(file.url);
                }
            },

            async previewFile(url) {
                const data = await TreatmentAPI.downloadPdf(url);
                const blob = new Blob([data], { type: 'application/pdf;charset=UTF-8' });

                if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                    windowOpen(url);
                    return;
                }

                const wUrl = URL.createObjectURL(blob);
                windowOpen(wUrl);
                URL.revokeObjectURL(blob);
            },

            handleStandardMedicineSearch(keyword, next) {
                GoodsAPI.searchProducts({
                    client: 'medicine-cadn',
                    // eslint-disable-next-line camelcase
                    key_word: keyword,
                }).then(({ data }) => {
                    const hits = data && data.hits;
                    if (Array.isArray(hits)) {
                        next(
                            hits.map((it) => {
                                return {
                                    name: it.medicine_cadn,
                                };
                            }),
                        );
                    }
                });
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';
    @import 'src/styles/mixin';

    .chronic-care-form-dialog {
        .layout-right-buttons {
            display: flex;
            margin-left: auto;
        }
    }
</style>
