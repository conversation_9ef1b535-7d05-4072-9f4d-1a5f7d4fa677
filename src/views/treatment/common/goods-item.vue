<template>
    <div v-if="visible" class="nurse-table-wrapper">
        <div class="table">
            <ul class="table-header">
                <li class="td first-td first-title">
                    <abc-text bold>
                        诊疗项目
                    </abc-text>
                    <charge-tag style="margin-left: 8px;" :status="currentFormsChargeStatus" show-part-charge></charge-tag>
                </li>
                <li class="td second-td">
                    <abc-text bold theme="gray">
                        类型
                    </abc-text>
                </li>
                <li class="td third-td">
                    <abc-text bold theme="gray">
                        单价
                    </abc-text>
                </li>
                <li class="td four-td txt-right">
                    <abc-text bold theme="gray">
                        金额
                    </abc-text>
                </li>
                <li class="td five-td txt-right">
                    <abc-text bold theme="gray">
                        实收
                    </abc-text>
                </li>
                <li class="td six-td txt-right">
                    <abc-text bold theme="gray">
                        执行进度
                    </abc-text>
                </li>
                <li class="td fixed-btn"></li>
                <!--占位-->
            </ul>
            <ul class="table-body">
                <li v-for="items in currentForms" :key="items.id">
                    <template v-if="currentForms.length">
                        <template v-for="(item, tIndex) in items.chargeFormItems">
                            <div :key="items.id + tIndex" class="tr" :style="{ height: item.remark ? '48px' : '40px' }">
                                <!--名称-->
                                <div
                                    v-abc-goods-hover-popper="{
                                        goods: item,
                                    }"
                                    class="td first-td"
                                >
                                    <tooth-selector
                                        v-if="item.toothNos && item.toothNos.length"
                                        v-model="item.toothNos"
                                        disabled
                                        :copy-tooth-nos-info="copyToothNosInfo"
                                    ></tooth-selector>
                                    <span class="ellipsis goods-name">
                                        {{ item.name }}
                                        <span v-if="item.isGift" class="is-gift">(赠)</span>
                                        <compose-tag v-if="item.productType === 11"></compose-tag>
                                    </span>
                                    <!-- 套餐头部不显示-->
                                    <template v-if="!isComposeExe(item)">
                                        <charge-tag
                                            v-if="isShowItemTag(item, items)"
                                            :status="item.status"
                                            is-item
                                        ></charge-tag>
                                    </template>
                                </div>
                                <!--类型-->
                                <div class="td second-td">
                                    {{ goodsType(item) }}
                                </div>
                                <!--单价-->
                                <div class="td third-td">
                                    {{ item.unitPrice | formatMoney(false) }}
                                </div>
                                <!--金额-->
                                <div class="td four-td txt-right">
                                    {{ item.totalPrice | formatMoney }}
                                </div>
                                <!--实收金额-->
                                <div class="td five-td txt-right">
                                    <template v-if="isShowDiscountedTotalPrice">
                                        {{ (item.discountedTotalPrice || 0) | formatMoney }}
                                    </template>
                                    <template v-else>
                                        --
                                    </template>
                                </div>
                                <!--执行次数-->
                                <div class="td six-td txt-right">
                                    {{ `${executiveCountStr(item).amount}${executiveCountStr(item).unit}` }}
                                </div>
                                <!--执行-->
                                <div class="td fixed-btn" style="font-size: 0;">
                                    <template v-if="!isClosed && item.productType !== 11">
                                        <abc-tooltip :disabled="!isDisabledExecuteButton">
                                            <span slot="content">{{ disabledExecuteButtonTips() }}</span>
                                            <div>
                                                <abc-check-access>
                                                    <abc-button
                                                        v-if="needExecutive(item)"
                                                        variant="ghost"
                                                        size="small"
                                                        :disabled="isDisabledExecuteButton"
                                                        @click="openDialog(item)"
                                                    >
                                                        执行
                                                    </abc-button>
                                                </abc-check-access>
                                            </div>
                                        </abc-tooltip>
                                    </template>
                                </div>
                                <!--备注-->
                                <div v-if="item.remark" class="table-remark">
                                    <div class="ellipsis" :title="item.remark">
                                        {{ item.remark }}
                                    </div>
                                    <div class="td fixed-btn"></div>
                                </div>
                            </div>

                            <template v-if="isComposeExe(item)">
                                <div
                                    v-for="(compose, index) in item.composeChildren"
                                    :key="item.id + index"
                                    class="compose-tr tr"
                                >
                                    <div class="td first-td" :title="compose.name">
                                        <span class="ellipsis goods-name">
                                            <span style=" color: #626d77;">{{ index + 1 }}</span>
                                            {{ compose.name }}
                                        </span>
                                        <charge-tag
                                            v-if="isShowItemTag(compose, items)"
                                            :status="compose.status"
                                            is-item
                                        ></charge-tag>
                                    </div>
                                    <div class="td second-td">
                                        {{ goodsType(compose) }}
                                    </div>
                                    <div class="td third-td">
                                        {{ compose.unitPrice | formatMoney }}
                                    </div>
                                    <div class="td four-td txt-right">
                                        {{ compose.totalPrice | formatMoney }}
                                    </div>
                                    <div class="td five-td txt-right">
                                        <template v-if="isShowDiscountedTotalPrice">
                                            {{ (compose.discountedTotalPrice || 0) | formatMoney }}
                                        </template>
                                        <template v-else>
                                            --
                                        </template>
                                    </div>
                                    <div class="td six-td">
                                        {{ `${executiveCountStr(compose).amount}${executiveCountStr(compose).unit}` }}
                                    </div>

                                    <div class="td fixed-btn" style="font-size: 0;">
                                        <abc-tooltip :disabled="!isDisabledExecuteButton">
                                            <span slot="content">{{ disabledExecuteButtonTips() }}</span>
                                            <div>
                                                <abc-check-access>
                                                    <abc-button
                                                        v-if="needExecutive(compose)"
                                                        variant="ghost"
                                                        size="small"
                                                        :disabled="isDisabledExecuteButton"
                                                        @click="openDialog(compose)"
                                                    >
                                                        执行
                                                    </abc-button>
                                                </abc-check-access>
                                            </div>
                                        </abc-tooltip>
                                    </div>
                                </div>
                            </template>
                        </template>
                    </template>
                </li>
            </ul>
            <record-dialog
                v-if="dialogVisible"
                v-model="dialogVisible"
                :executed-id="executedId"
                @close="closeDialog"
            ></record-dialog>
        </div>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import RecordDialog from './record-dialog';
    import { numToChinese } from 'utils/index.js';
    import ComposeTag from '../../outpatient/common/compose-tag';
    import ChargeTag from 'views/treatment/common/charge-tag/index';
    import ChargeStatus from './charge-status';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import ToothSelector from '@/views-dentistry/outpatient/common/medical-record/tooth-selector.vue';
    import { getChargeToothNosInfo } from 'views/outpatient/common/medical-record/utils.js';

    export default {
        name: 'GoodsItems',
        components: {
            ChargeTag,
            RecordDialog,
            ComposeTag,
            ToothSelector,
        },
        mixins: [ChargeStatus],
        props: {
            productForms: {
                type: Array,
                required: true,
            },
            isClosed: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                executedId: '',
                dialogVisible: false,
            };
        },
        computed: {
            ...mapGetters('treatment', ['treatmentSystemUnit']),
            visible() {
                return this.productForms.length;
            },
            currentForms() {
                return this.productForms || [];
            },
            formProvider() {
                return this.currentForms;
            },
            copyToothNosInfo() {
                return getChargeToothNosInfo(this.currentForms);
            },
        },
        methods: {
            numToChinese,
            // 向上传递事件  打开执行弹窗
            openDialog(item) {
                this.$emit('openDialog', item);
            },
            openRecordDialog(item) {
                this.dialogVisible = true;
                this.executedId = item.id;
                // this.$emit('openRecordDialog', item);
            },
            closeDialog() {
                this.dialogVisible = false;
                this.$emit('refresh');
            },

            goodsType(item) {
                const type = item.productType;
                const subType = item.productSubType;
                if (type === GoodsTypeEnum.EXAMINATION) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect) {
                        return '检验';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test) {
                        return '检查';
                    }
                }
                if (type === GoodsTypeEnum.TREATMENT) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment) {
                        return '治疗';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy) {
                        return '理疗';
                    }
                }
                if (type === GoodsTypeEnum.MATERIAL) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials) {
                        return '医疗器械';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].LogisticalMaterials) {
                        return '后勤材料';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].FixedAssets) {
                        return '固定资产';
                    }
                }
                if (type === GoodsTypeEnum.GOODS) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].ManufacturedGoods) {
                        return '自制成品';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthCareMedicine) {
                        return '保健药品';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthFoods) {
                        return '保健食品';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].Other) {
                        return '其他商品';
                    }
                }
                if (type === GoodsTypeEnum.editMEDICINE) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine) {
                        return '西药';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine) {
                        return '中药';
                    } if (subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM) {
                        return '中成药';
                    }
                }
                if (type === GoodsTypeEnum.COMPOSE) {
                    return '套餐';
                }

                if (type === GoodsTypeEnum.OTHER) {
                    return '其他';
                }

                if (type === GoodsTypeEnum.NURSE) {
                    return '护理';
                }

                if (type === GoodsTypeEnum.EYEGLASSES) {
                    return '眼镜';
                }

                if (type === GoodsTypeEnum.SURGERY && subType === GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY) {
                    return '手术';
                }

                return '--';
            },

            calTotalPrice(item) {
                const count = (item && item.unitCount) || 0;
                const price = (item && item.unitPrice) || 0;
                return Number(count) * Number(price);
            },

            needExecutive(item) {
                // 套餐的执行项能否执行取productInfo里面的needExecutive
                // 非套餐执行项能否执行取item上的needExecutive
                const {
                    composeType, needExecutive: needExecutiveItem, productInfo,
                } = item;
                let needExecutive;
                if (composeType !== 2) {
                    needExecutive = needExecutiveItem;
                } else {
                    needExecutive = productInfo && item.productInfo.needExecutive;
                }
                return (
                    item &&
                    (item.productType === GoodsTypeEnum.TREATMENT || item.productType === GoodsTypeEnum.NURSE) &&
                    item.executedUnitCount < item.unitCount &&
                    item.status <= 1 &&
                    needExecutive
                );
            },
            showExecutiveCount(item) {
                // 套餐的执行项能否执行取productInfo里面的needExecutive
                // 非套餐执行项能否执行取item上的needExecutive
                const {
                    composeType, needExecutive: needExecutiveItem, productInfo,
                } = item;
                let needExecutive;
                if (composeType !== 2) {
                    needExecutive = needExecutiveItem;
                } else {
                    needExecutive = productInfo && item.productInfo.needExecutive;
                }
                return (
                    item &&
                    (item.productType === GoodsTypeEnum.TREATMENT || item.productType === GoodsTypeEnum.NURSE) &&
                    needExecutive
                );
            },
            /**
             * @desc 是否为默认单位
             * <AUTHOR>
             * @date 2022/07/11 17:45:34
             * @return boolean
             */
            isDefalutUnit(unit) {
                const units = this.treatmentSystemUnit.map((x) => x.name);
                return units.includes(unit);
            },

            executiveCountStr(item) {
                const isExecutive = this.showExecutiveCount(item);
                if (isExecutive) {
                    return {
                        amount: `${parseInt(item.executedUnitCount)}/${parseInt(item.unitCount)}`,
                        unit: this.isDefalutUnit (item.unit) ? `${item.unit || '次'}` : `*${item.unit}`,
                    };
                }
                if (item.productType === 11) {
                    return {
                        amount: '',
                        unit: '',
                    };
                }
                return {
                    amount: `${parseInt(item.unitCount)}`,
                    unit: this.isDefalutUnit (item.unit) ? `${item.unit || '次'}` : `*${item.unit}`,
                };
            },

            /**
             * @desc 医嘱可以加子项目有composeChildren，但是不按套餐处理
             * <AUTHOR>
             * @date 2023/06/06 18:27:54
             * @param {Object}
             * @return {Boolean}
             */
            isComposeExe(formItem) {
                const {
                    productType,
                    composeChildren,
                } = formItem;
                return productType === GoodsTypeEnum.COMPOSE && composeChildren?.length > 0;
            },
        },
    };
</script>
