<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        :title="title"
        :show-close="false"
        custom-class="treatment-preview-fee-dialog"
        :shadow="showQrCodeDialog"
        tabindex="-1"
        append-to-body
        size="medium"
    >
        <abc-flex
            vertical
            :gap="16"
            style="width: 100%;"
        >
            <abc-tips-card-v2 v-if="!!patient.name && autoSendOrderInfoSwitch" theme="primary">
                <wechat-push
                    :patient="newPatient"
                    :chain-id="chainId"
                    @update-patient="newPatient = $event"
                >
                    <template
                        #default="{
                            isPush, wxBindStatus,wxStatus
                        }"
                    >
                        <push-text
                            source="treatment"
                            :is-push="isPush"
                            :wx-bind-status="wxBindStatus"
                            :wx-status="wxStatus"
                            :patient="newPatient"
                            @update-info="handleUpdateInfo"
                            @changeDialogVisible="visible => { showQrCodeDialog = visible }"
                        ></push-text>
                    </template>
                </wechat-push>
            </abc-tips-card-v2>
            <abc-tips-card-v2 v-if="isOpenMp && patient.name === ''">
                输入患者信息后即可在微{{ $app.institutionTypeWording }}内完成本次支付
            </abc-tips-card-v2>
            <abc-descriptions
                :column="1"
                :bordered="false"
                :label-width="32"
                content-padding="0"
            >
                <abc-descriptions-item content-class-name="preview-item" label="患者">
                    {{ !!patient.name ? patient.name : '匿名患者' }}
                </abc-descriptions-item>
                <abc-descriptions-item content-class-name="preview-item" label="诊断">
                    <div class="diagnosis-html" v-html="diagnosisHtml"></div>
                </abc-descriptions-item>
                <abc-descriptions-item v-if="showTotalPrice" content-class-name="preview-item" label="费用">
                    <div class="fee-wrapper">
                        <template>
                            <abc-space v-if="hiddenOutpatientTotalPrice">
                                <abc-text tag="div" class="text-star">
                                    ******
                                </abc-text>
                                <abc-icon icon="s-b-eye-slash-line" color="#AAB4BF" @click="changeShowPrice"></abc-icon>
                            </abc-space>
                            <abc-space v-else>
                                <abc-money :value="totalPrice"></abc-money>
                                <abc-icon icon="s-eye-line" color="#AAB4BF" @click="changeShowPrice"></abc-icon>
                            </abc-space>
                        </template>
                    </div>
                </abc-descriptions-item>
            </abc-descriptions>
        </abc-flex>
        <div slot="footer" class="dialog-footer">
            <abc-button :loading="saveLoading" @click="ok">
                确定
            </abc-button>
            <abc-button :disabled="saveLoading" type="blank" @click="no">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import {
        on, off,
    } from 'utils/dom';
    import localStorage from 'utils/localStorage-handler';
    import Store from 'src/store';
    import { loosePlainText } from 'utils/xss-filter';
    import { getWarnDiagnosisHtml } from 'src/views/outpatient/common/medical-record/utils.js';

    import { WxBindStatusEnum } from '@abc/constants';
    import WechatPush from '@/views/crm/wechat-push/index.vue';
    import PushText from '@/views/crm/wechat-push/push-text.vue';
    import { mapGetters } from 'vuex';

    export default {
        name: 'ConfirmTreatmentDialog',
        components: {
            WechatPush,
            PushText,
        },
        props: {
            patient: Object,
            title: String,
            chargeForms: Array,
            diagnosis: String,
            autoSendOrderInfoSwitch: [Boolean, Number],
            submit: Function,
            updatePatientInfo: Function,
            cancel: Function,
            params: Object,
            showTotalPrice: [Boolean,Number],
            totalPrice: [Number,String],
            chainId: String,
        },
        data() {
            return {
                newPatient: this.patient,
                visible: false,
                closed: false,
                saveLoading: false,
                hiddenOutpatientTotalPrice: false,

                showQrCodeDialog: false,

                bottomExpand: false,
            };
        },

        computed: {
            ...mapGetters(['isOpenMp']),
            patientSubscribeBind() {
                return this.patient.wxStatus === WxBindStatusEnum.SUBSCRIBE_AND_BIND;
            },
            patientBindNoSubscribe() {
                return this.patient.wxStatus === WxBindStatusEnum.BIND_AND_NO_SUBSCRIBE;
            },
            diagnosisHtml() {
                const {
                    extendDiagnosisInfos,
                } = this.params;
                if (extendDiagnosisInfos && extendDiagnosisInfos.filter((it) => it.value.length).length) {
                    const _arr = [];
                    extendDiagnosisInfos.forEach((item) => {
                        item.value.forEach((it) => {
                            it.name && _arr.push(it);
                        });
                    });
                    const _html = getWarnDiagnosisHtml(_arr, this.$abcSocialSecurity.isSupportMatchDiseaseCode);
                    return loosePlainText(_html, true, {
                        a: ['class', 'data-tipsy'],
                        i: ['class', 'contenteditable'],
                    });
                }

                return '';
            },
        },

        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            const { currentClinic } = Store.getters;
            const { userInfo } = Store.getters;
            this._key = `${currentClinic.clinicId}_${userInfo.id}`;
            this.hiddenOutpatientTotalPrice = localStorage.getObj('hidden_outpatient_total_price', this._key, true);

        },
        mounted() {
            on(document, 'keydown', this.keydownHandle);
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownHandle);
        },
        methods: {
            /**
             * @desc 点击完成接诊弹窗中，是否显示总价格
             * <AUTHOR>
             * @date 2020/01/10 14:37:21
             */
            changeShowPrice() {
                this.hiddenOutpatientTotalPrice = !this.hiddenOutpatientTotalPrice;
                localStorage.setObj('hidden_outpatient_total_price', this._key, this.hiddenOutpatientTotalPrice);
            },

            handleUpdateInfo(e) {
                this.newPatient = e;
                this.updatePatientInfo && this.updatePatientInfo(e);
            },

            keydownHandle(e) {
                const KEY_ENTER = 13;
                const KEY_ESC = 27;
                if (e.keyCode === KEY_ENTER) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.ok();
                } else if (e.keyCode === KEY_ESC) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.no();
                }
            },

            ok() {
                if (typeof this.submit === 'function') {
                    try {
                        this.saveLoading = true;
                        this.submit(this.params);
                        this.saveLoading = false;
                    } catch (e) {
                        this.saveLoading = false;
                    }
                }
                this.close();
            },

            async no() {
                if (typeof this.cancel === 'function') {
                    this.cancel();
                }
                this.close();
            },

            close() {
                this.closed = true;
                if (typeof this.onClose === 'function') {
                    this.onClose(this);
                }
            },

            destroyElement() {
                off(document, 'keydown', this.keydownHandle);
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';

.treatment-preview-fee-dialog {
    text-align: left;

    .preview-item {
        display: flex;
        font-size: 14px;

        > div {
            display: flex;
            flex: 1;
            flex-direction: column;
            text-align: left;

            > p {
                display: flex;
                align-items: center;
                font-size: 18px;
                color: $Y2;
            }

            .tips {
                margin-top: 4px;
                font-size: 12px;
                line-height: 28px;
                color: $T2;
            }

            button {
                font-size: 12px;
            }
        }

        .fee-wrapper {
            .abc-space-item {
                color: var(--abc-color-Y2);

                .iconfont {
                    line-height: var(--abc-line-height-normal);
                    cursor: pointer;

                    &:hover {
                        color: var(--abc-color-theme2) !important;
                    }
                }

                .text-star {
                    height: 5px;
                    line-height: 10px;
                }
            }
        }

        .diagnosis-html {
            display: block;

            .shortage-tips {
                position: relative;
                color: $Y2;

                i {
                    font-style: normal;
                    color: $Y2;
                }

                .abc-tipsy--n {
                    i {
                        font-style: normal;
                        color: $Y2;
                    }
                }

                &.abc-tipsy--n::before {
                    border-top-color: #000000;
                }

                &.abc-tipsy--n::after {
                    width: 216px;
                    padding: 10px 12px;
                    white-space: normal;
                    background-color: #000000;
                }
            }
        }
    }

    .print-meanwhile-set {
        margin-right: auto;
        font-size: 14px;

        .abc-checkbox-wrapper {
            margin-right: 0;
        }

        .iconfont {
            color: $T2;
            cursor: pointer;
        }

        .cis-icon-dropdown_triangle {
            margin-left: -4px;
            font-size: 14px;
        }

        > .label-text {
            margin-left: 4px;
            cursor: pointer;
        }

        .label-tips {
            font-size: 12px;
            color: #ff9933;
        }
    }

    .confirm-treatment-dialog-bottom-extend {
        position: absolute;
        bottom: -64px;
        left: 0;
        width: 100%;
        padding: 12px 24px;
        background-color: $P4;
        border-top: 1px solid $P6;
        border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

        .tip-text {
            margin-bottom: 6px;
            font-size: 12px;
            color: $T2;
            text-align: left;
        }

        .abc-checkbox-wrapper {
            margin-right: 16px;

            &:last-child {
                margin-right: 0;
            }

            .abc-checkbox__label {
                color: $T1;
            }
        }
    }

    .push-text {
        .abc-button {
            margin-left: 34px;
        }
    }
}
</style>
