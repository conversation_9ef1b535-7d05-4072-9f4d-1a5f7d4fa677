<template>
    <div class="nurse-table-wrapper">
        <div
            v-for="(pr,index) in chinesePRs"
            :key="pr.id"
            class="table cm-table-wrapper"
            :class="{ 'no-border-bottom': source === 3 }"
        >
            <ul class="table-header">
                <li v-if="chineseLength > 1" class="goods-info">
                    <span>{{ `中药处方${name(chinesePRs.length, index)}` }}</span>
                    <charge-tag style="margin-left: 8px;" :status="currentFormsChargeStatus" show-part-charge></charge-tag>
                </li>
                <li v-else class="goods-info">
                    <span>
                        <template v-if="source === 3">中药</template>
                        <template v-else>
                            <template v-if="pr.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY">空中药房</template>
                            <template v-else>中药处方</template>
                        </template>
                    </span>
                    <charge-tag style="margin-left: 8px;" :status="currentFormsChargeStatus" show-part-charge></charge-tag>
                </li>
            </ul>
            <div class="table-body">
                <div class="td chinese-medicine-wrapper">
                    <div class="chinese-medicine-item-wrapper">
                        <div
                            v-for="(it, index) in filterFormItems(pr.chargeFormItems)"
                            :key="it.id"
                            class="chinese-medicine-item"
                            :class="{ 'first-item': index % 3 === 0 }"
                        >
                            <div
                                v-abc-goods-hover-popper="{
                                    goods: it,
                                    showStock: false,
                                }"
                                class="ellipsis"
                            >
                                {{ it.name || it.medicineCadn }}
                            </div>
                            <template v-if="it.usageInfo">
                                <span
                                    v-if="it.usageInfo.specialRequirement"
                                    class="specification_small ellipsis"
                                    style="min-width: 48px;"
                                >（{{ it.usageInfo.specialRequirement }}）</span>
                            </template>

                            <div class="count">
                                {{ it.unitCount + (it.unit || 'g') }}
                                <span v-if="source === 3"> × {{ `${it.doseCount }剂` }} </span>
                            </div>
                        </div>
                    </div>
                    <div v-if="source !== 3" class="chinese-medicine-tips ellipsis" :title="requirement(pr)">
                        {{ requirement(pr) }} {{ pr.pharmacyName || '' }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {
        formatPrice2, numToChinese,
    } from 'utils/index.js';
    import { SourceFormTypeEnum } from '@/service/charge/constants.js';
    import ChargeTag from 'views/treatment/common/charge-tag/index';
    import ChargeStatus from './charge-status';
    import { GoodsTypeEnum } from '@abc/constants';

    export default {
        name: 'ChineseMedicineList',
        components: { ChargeTag },
        mixins: [ChargeStatus],
        props: {
            chinesePRs: Array,
            source: {
                type: [String, Number],
            },
        },
        data() {
            return {
                SourceFormTypeEnum,
            };
        },
        computed: {
            chineseLength() {
                return this.chinesePRs && this.chinesePRs.length;
            },
            formProvider() {
                return this.chinesePRs;
            },
        },
        methods: {
            formatPrice: formatPrice2,
            /**
             * @desc 判断发药状态
             * <AUTHOR>
             * @date 2018/10/13 上午10:42
             * @return Boolean
             */
            dispensedStatusJudger(pr) {
                let result = false;
                for (let i = 0; i < pr.prescriptionFormItems.length; i++) {
                    if (pr.prescriptionFormItems[i].dispensingId) {
                        result = true;
                        return ;
                    }
                }
                return result;
            },
            name(len, i) {
                if (len > 1) {
                    return numToChinese(i + 1);
                }
                return '';
            },
            requirement(item) {
                if (item.usageInfo) {
                    const {
                        doseCount,
                        usage,
                        usageLevel,
                        usageDays,
                        dailyDosage,
                        freq,
                        requirement,
                    } = item.usageInfo;
                    const _arr = [`${doseCount}剂`, usage, dailyDosage, freq, usageLevel, usageDays, requirement];
                    return _arr.filter((it) => it).join('，');
                }
                return '';
            },
            filterFormItems(list) {
                if (!Array.isArray(list)) {
                    return [];
                }
                return list.filter((it) => !(
                    it.productType === GoodsTypeEnum.EXPRESS_DELIVERY ||
                    it.productType === GoodsTypeEnum.DECOCTION ||
                    it.productType === GoodsTypeEnum.INGREDIENT
                ));
            },
        },
    };
</script>
