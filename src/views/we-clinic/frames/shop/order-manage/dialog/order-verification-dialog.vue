<template>
    <!--    <div>发货/核销/自提发货</div>-->
    <abc-dialog
        v-if="value"
        title="核销"
        :value="value"
        size="xlarge"
        @input="(val) => $emit('input', val)"
    >
        <abc-layout>
            <abc-section>
                <abc-title>1.确认订单信息</abc-title>
            </abc-section>
            <abc-section>
                <abc-space :size="24">
                    <abc-text theme="gray-light">
                        订单号：{{ orderDetail.orderId }}
                    </abc-text>
                    <abc-text theme="gray-light">
                        创建时间：{{ orderDetail.created ? parseTime(orderDetail.created, 'y-m-d h:i:s', true) : '-' }}
                    </abc-text>
                </abc-space>
            </abc-section>
            <abc-section>
                <goods-table :items="orderDetail.items">
                    <div slot="footer" style="width: 100%; padding: 8px;">
                        <abc-flex gap="8">
                            <abc-text style="flex-shrink: 0;" theme="gray-light">
                                订单备注：
                            </abc-text>
                            <abc-text>{{ orderDetail.remark || '-' }}</abc-text>
                        </abc-flex>
                    </div>
                </goods-table>
            </abc-section>
            <abc-section>
                <abc-flex justify="flex-end">
                    <abc-space>
                        <abc-text theme="gray-light">
                            共{{ orderDetail.items?.length }}件，订单金额:
                        </abc-text>
                        <abc-money
                            :is-format-money="false"
                            :value="moneyDigit(orderDetail.goodsTotalPrice + orderDetail.deliveryPrice)"
                        ></abc-money>
                    </abc-space>
                </abc-flex>
                <abc-flex justify="flex-end">
                    <abc-text theme="gray-light">
                        （含配送费）
                    </abc-text>
                </abc-flex>
            </abc-section>
            <abc-section>
                <abc-title>2.确认核销信息</abc-title>
            </abc-section>
            <abc-section>
                <abc-card padding-size="medium">
                    <abc-flex :gap="16" align="center" wrap="wrap">
                        <div>
                            <abc-space :size="16">
                                <abc-text theme="gray-light">
                                    核销门店
                                </abc-text>
                                <abc-text style="display: block; width: 180px;" class="ellipsis">
                                    {{ mallOrganSimpleInfo.name }}
                                </abc-text>
                            </abc-space>
                        </div>
                        <div>
                            <abc-space :size="16">
                                <abc-text style="margin-right: 12px;" theme="gray-light">
                                    核销码
                                </abc-text>
                                <abc-input
                                    v-model="inputBarcode"
                                    type="number"
                                    :width="220"
                                    placeholder="请输入核销码"
                                ></abc-input>
                            </abc-space>
                        </div>
                        <!--                        <div>-->
                        <!--                            <abc-space :size="16">-->
                        <!--                                <abc-text theme="gray-light">-->
                        <!--                                    剩余次数/总次数-->
                        <!--                                </abc-text>-->
                        <!--                                <abc-text style="display: block; width: 134px;" class="ellipsis">-->
                        <!--                                    -/- -->
                        <!--                                </abc-text>-->
                        <!--                            </abc-space>-->
                        <!--                        </div>-->
                        <!--                        <div>-->
                        <!--                            <abc-space :size="16">-->
                        <!--                                <abc-text theme="gray-light">-->
                        <!--                                    核销次数-->
                        <!--                                </abc-text>-->
                        <!--                                <abc-input-number-->
                        <!--                                    fixed-button-->
                        <!--                                    :config="{-->
                        <!--                                        max: 3,-->
                        <!--                                    }"-->
                        <!--                                    :disabled="disabled"-->
                        <!--                                    size="tiny"-->
                        <!--                                    button-placement="left"-->
                        <!--                                >-->
                        <!--                                </abc-input-number>-->
                        <!--                            </abc-space>-->
                        <!--                            次-->
                        <!--                        </div>-->
                    </abc-flex>
                </abc-card>
            </abc-section>
        </abc-layout>
        <div slot="footer" style="text-align: right;">
            <abc-space>
                <abc-button @click="getVerificationVerifyGoods(true)">
                    核销
                </abc-button>
                <abc-button variant="ghost" @click="$emit('input',false)">
                    关闭
                </abc-button>
            </abc-space>
        </div>
        <confirm-verification-content-dialog
            v-if="showConfirmVerificationContentDialog"
            v-model="showConfirmVerificationContentDialog"
            :disabled="disabledInputNumber"
            :verification-item-views="verificationItemViews"
        >
        </confirm-verification-content-dialog>
    </abc-dialog>
</template>
<script>
    import WeShopAPI from 'api/we-shop';
    import {
        moneyDigit, parseTime,
    } from 'utils';
    import GoodsTable from 'views/we-clinic/frames/shop/order-manage/goodsTable.vue';
    import BarcodeScanner from 'utils/scanner-barcode-detector';
    const ConfirmVerificationContentDialog = () => import('views/cashier/discount/verification-dialog/confirm-verification-content-dialog.vue');

    export default {
        name: 'OrderSendDialog',
        components: {
            GoodsTable,
            ConfirmVerificationContentDialog,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            orderId: {
                type: String,
                default: -1,
            },
        },
        data() {
            return {
                isLoading: false,
                isBtnLoading: false,
                orderDetail: {},
                disabled: true,
                inputBarcode: '',
                verificationItemViews: '',
                showConfirmVerificationContentDialog: false,
                disabledInputNumber: true,
            };
        },
        computed: {
            renderConfig() {
                return {
                    list: [
                        {
                            label: '商品名称',
                            key: 'name',
                        },
                        {
                            label: '单价（元）',
                            key: 'spec',
                        },
                        {
                            label: '实付单价（元)',
                            key: 'num',
                        },
                        {
                            label: '数量',
                            key: 'price',
                        },
                        {
                            label: '实付金额（元）',
                            key: 'total',
                        },
                    ],
                };
            },
            // 门店信息
            mallOrganSimpleInfo() {
                return this.orderDetail?.mallOrganSimpleInfo || {};
            },
            // 物流信息
            mallOrderLogisticsView() {
                return this.orderDetail.mallOrderLogisticsView || {};
            },
        },
        created() {
            this.getOrderDetail();
        },
        mounted() {
            const barcodeScanner = new BarcodeScanner((barcode) => {
                this.inputBarcode = barcode;
                if (!this.showCodeInput) {
                    this.getVerificationVerifyGoods(false);
                }
            }, () => {
                return true;
            });

            barcodeScanner.startDetect();
            this.$on('hook:beforeDestroy', () => {
                barcodeScanner.destoryDetect();
            });
        },
        methods: {
            moneyDigit,
            parseTime,
            async getOrderDetail() {
                try {
                    this.isLoading = true;
                    const res = await WeShopAPI.fetchMallOrder(this.orderId);
                    this.orderDetail = res.data;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.isLoading = false;
                }
            },
            async getVerificationVerifyGoods(disabledInputNumber = true) {
                this.disabledInputNumber = disabledInputNumber;
                try {
                    const { data } = await WeShopAPI.getVerificationToolVerifyGoods(this.inputBarcode);
                    if (data && data?.verificationItemViews && data?.verificationItemViews.length) {
                        this.verificationItemViews = data?.verificationItemViews;
                        this.showConfirmVerificationContentDialog = true;
                        this.inputBarcode = '';
                        return;
                    }
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '未找到核销订单，请更换二维码',
                    });
                } catch (e) {
                    console.error('获取核销码失败',e);
                }
            },
        },
    };
</script>
