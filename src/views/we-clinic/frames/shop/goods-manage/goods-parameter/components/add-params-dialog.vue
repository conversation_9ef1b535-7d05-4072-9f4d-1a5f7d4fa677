<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            :title="title"
            :auto-focus="false"
            size="large"
        >
            <div v-abc-loading="loading">
                <abc-form
                    ref="form"
                    label-position="left-top"
                    :label-width="96"
                    item-block
                >
                    <abc-form-item label="参数项名称" required hidden-red-dot>
                        <abc-input
                            v-model="categoryName"
                            :width="246"
                            :max-length="10"
                            placeholder="请输入名称"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item label="所属类目">
                        <abc-flex vertical :gap="4" style="width: 100%; margin-top: 2px;">
                            <abc-flex justify="space-between">
                                <abc-button
                                    variant="text"
                                    size="small"
                                    @click="selectCategory"
                                >
                                    选择类目
                                </abc-button>
                                <abc-button
                                    v-if="!isDeleteCategory"
                                    variant="text"
                                    size="small"
                                    :disabled="!selectCategoryList.length"
                                    @click="isDeleteCategory = true"
                                >
                                    删除
                                </abc-button>
                                <abc-button
                                    v-else
                                    variant="text"
                                    size="small"
                                    @click="isDeleteCategory = false"
                                >
                                    退出删除
                                </abc-button>
                            </abc-flex>

                            <abc-card>
                                <div
                                    style="
                                        grid-template-columns: repeat(auto-fill, 108px);
                                        grid-gap: 8px;
                                        min-height: 92px;
                                        max-height: 252px;
                                        padding: 10px 0 10px 10px;
                                        overflow-y: scroll;
"
                                    :style="{
                                        display: selectCategoryList.length ? 'grid' : undefined,
                                    }"
                                >
                                    <template v-if="selectCategoryList.length">
                                        <abc-tag-v2
                                            v-for="item in selectCategoryList"
                                            :key="item.id"
                                            shape="square"
                                            size="huge"
                                            theme="default"
                                            variant="outline"
                                            :closable="isDeleteCategory"
                                            close-resident
                                            icon="s-commodity-color"
                                            @close="handleDeleteCategory(item)"
                                        >
                                            <abc-text
                                                siz="normal"
                                                tag="div"
                                                class="ellipsis"
                                                :title="item.name"
                                                style="min-width: 68px; max-width: 68px;"
                                            >
                                                {{ item.name }}
                                            </abc-text>
                                        </abc-tag-v2>
                                    </template>
                                    <abc-content-empty
                                        v-else
                                        top="19px"
                                        value="暂无数据"
                                        :show-icon="false"
                                    ></abc-content-empty>
                                </div>
                            </abc-card>
                        </abc-flex>
                    </abc-form-item>
                    <abc-form-item label="参数值" style="margin-bottom: 0;">
                        <abc-flex vertical :gap="8" style="width: 100%;">
                            <abc-space>
                                <abc-input
                                    v-model="postCategoryInputValue"
                                    :width="174"
                                    :max-length="100"
                                    placeholder="请输入要添加的参数值"
                                ></abc-input>
                                <abc-button
                                    variant="ghost"
                                    @click="addParamsValue"
                                >
                                    添加
                                </abc-button>
                            </abc-space>
                            <abc-table
                                v-show="postCategoryValues.length"
                                style="width: 100%;"
                                support-delete-tr
                                draggable
                                type="excel"
                                :data-list.sync="postCategoryValues"
                                hidden-table-header
                                :custom-tr-key="it => it.sort"
                                :render-config="{
                                    hasInnerBorder: true,
                                    list: [{
                                        key: 'value',
                                        label: '参数值',
                                        style: {
                                            flex: 1
                                        }
                                    }]
                                }"
                                @delete-tr="delItem"
                            >
                                <template #value="{ trData: row }">
                                    <abc-input
                                        v-model="row.value"
                                        style="width: 100%;"
                                        :max-length="100"
                                    ></abc-input>
                                </template>
                            </abc-table>
                        </abc-flex>
                    </abc-form-item>
                </abc-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button
                    type="primary"
                    :loading="btnLoading"
                    :disabled="btnDisabled"
                    @click="handleConfirm"
                >
                    确定
                </abc-button>
                <abc-button type="blank" @click="handleCancel">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <select-category-dialog
            v-if="showSelectCategoryDialog"
            v-model="showSelectCategoryDialog"
            :selected-category-value="selectCategoryList"
            @selected-category-list="handleSelectCategory"
        ></select-category-dialog>
    </div>
</template>
<script>
    import SelectCategoryDialog from './select-category-dialog.vue';
    import Clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    import WeShopAPI from 'api/we-shop';


    export default {
        name: 'AddParamsDialog',
        components: {
            SelectCategoryDialog,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            id: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                loading: false,
                btnLoading: false,
                categoryName: '',
                postCategoryInputValue: '',
                postCategoryValues: [],
                selectCategoryList: [],

                cacheCategoryName: '',
                cachePostCategoryInputValue: '',
                cachePostCategoryValues: [],
                cacheSelectCategoryList: [],

                showSelectCategoryDialog: false,
                isDeleteCategory: false,
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            title() {
                return !this.id ? '新增参数项' : '修改参数项';
            },
            btnDisabled() {
                if (!this.id) return false;
                return (this.cacheCategoryName === this.categoryName &&
                    this.cachePostCategoryInputValue === this.postCategoryInputValue &&
                    isEqual(this.cacheSelectCategoryList, this.selectCategoryList) &&
                    isEqual(this.cachePostCategoryValues, this.postCategoryValues));
            },
        },
        created() {
            if (this.id) {
                this.fetchDetail();
            }
        },
        methods: {
            async fetchDetail() {
                try {
                    this.loading = true;
                    const { data } = await WeShopAPI.fetchMallGoodsAttributesDetail(this.id);

                    this.cacheCategoryName = this.categoryName = data?.name || '';
                    this.cachePostCategoryInputValue = this.postCategoryInputValue = '';
                    (data?.attributeValues || []).forEach((item, index) => {
                        this.postCategoryValues.push({
                            value: item,
                            sort: index + 1,
                        });
                    });
                    this.selectCategoryList = (data?.categoryList || []).map((item) => {
                        const {
                            id, name, parentId, parentName,
                        } = item;
                        return {
                            id,
                            name,
                            parentId,
                            parentName,
                            checked: true,
                        };
                    });

                    this.cachePostCategoryValues = Clone(this.postCategoryValues);
                    this.cacheSelectCategoryList = Clone(this.selectCategoryList);
                } catch (e) {
                    console.log('fetchDetail error', e);
                } finally {
                    this.loading = false;
                }
            },
            selectCategory() {
                this.showSelectCategoryDialog = true;
            },
            handleSelectCategory(val) {
                this.selectCategoryList = val;
            },
            handleDeleteCategory(item) {
                const currentIndex = this.selectCategoryList.findIndex((it) => it.id === item.id && item.name === it.name);
                if (currentIndex !== -1) {
                    this.selectCategoryList.splice(currentIndex, 1);
                }
            },
            addParamsValue() {
                if (!this.postCategoryInputValue) {
                    this.$Toast({
                        message: '请输入要添加的参数值',
                        type: 'warn',
                    });

                    return;
                }
                this.postCategoryValues.push({
                    value: this.postCategoryInputValue,
                    sort: this.postCategoryValues.length + 1,
                });

                this.postCategoryInputValue = '';
            },
            delItem(index) {
                this.postCategoryValues.splice(index, 1);
            },
            handleConfirm() {
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        if (!this.selectCategoryList.length) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: '请选择所属类目',
                            });
                            return;
                        }

                        const attributeValues = this.postCategoryValues.map((item) => item.value).filter((value) => value !== '');
                        const categoryList = this.selectCategoryList.map((item) => {
                            const {
                                id, name, parentId, parentName,
                            } = item;
                            return {
                                id,
                                name,
                                parentId,
                                parentName,
                            };
                        });

                        const postData = {
                            name: this.categoryName,
                            attributeValues,
                            categoryList,
                        };

                        if (this.id) {
                            await WeShopAPI.updateMallGoodsAttributes(this.id, postData);
                        } else {
                            await WeShopAPI.createMallGoodsAttributes(postData);
                        }

                        this.$Toast({
                            message: `${!this.id ? '新增' : '修改'}参数项成功`,
                            type: 'success',
                        });
                        this.$emit('refresh');

                        this.showDialog = false;
                    }
                });
            },
            handleCancel() {
                this.showDialog = false;
            },
        },
    };
</script>

