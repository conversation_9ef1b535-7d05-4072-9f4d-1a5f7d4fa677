<template>
    <div class="outpatient__consulting-online__message-list__item-finish">
        <span class="title">会话已结束</span>
        <span class="text" v-if="desc">{{ desc }}</span>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import { showDateLong } from '../utils'
export default {
    name: "ItemFinish",
    props: {
        finishTime: String
    },
    computed: {
        ...mapState("im", [
            "consultationInfo"
        ]),
        desc() {
            const { startTime } = this.consultationInfo || {}
            if (startTime && this.finishTime) {
                return `咨询时间 ${showDateLong(startTime, this.finishTime)}`
            }
            return ''
        }
    },
}
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";
.outpatient__consulting-online__message-list__item-finish {
    width: 234px;
    height: 50px;
    border-radius: 34px;
    background-color: $P6;
    user-select: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    margin: 0px auto;
    .title {
        color: $T1;
        font-size: 14px;
        line-height: 20px;
    }
    .text {
        color: $T2;
        margin-top: 2px;
    }
}
</style>