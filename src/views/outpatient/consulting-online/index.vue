<template>
    <div class="outpatient__consulting-online">
        <view-header
            :outpatient-id="outpatientId"
            @question-detail="onClickQuestionDetail"
            @preview-attachments="onClickPreviewImages"
        ></view-header>

        <message-list
            v-if="!!conversationId"
            ref="message-list"
            :user-info="userInfo || {}"
            :outpatient-id="outpatientId"
            :conversation-id="conversationId"
            :conversation-info="conversationInfo"
            :consultation-info="consultationInfo"
            @read-message="readAllMessage"
            @preview-image="onClickPreviewImages"
            @question-detail="onClickQuestionDetail"
            @add2MedicalRecordHandle="(data) => $emit('add2MedicalRecordHandle', data)"
            @send-text-again="(body) => onSendText(body)"
            @send-image-again="(body) => onSendImage(body)"
            @edit-again="handleEditAgain"
            @revoke="handleRevoke"
        ></message-list>
        <div v-else v-abc-loading.middle.gray.noCover="loading" style="flex: 1;"></div>

        <!-- <ViewControl @send-text="onSendText" /> -->
        <input-control
            ref="inputControl"
            :outpatient-id="outpatientId"
            @change-dom="checkScrollDown"
            @transition-end="onTransitionEnd"
            @send-text="onSendText"
            @send-image="onSendImage"
        ></input-control>

        <!-- 问诊单查看 -->
        <dialog-question
            v-if="visibleQuestionSheet"
            v-model="visibleQuestionSheet"
            :question-sheet-id="questionSheetId"
            :patient-info="consultationInfo.patient"
            @preview-attachments="onClickPreviewImages"
            @add2MedicalRecordHandle="(data) => $emit('add2MedicalRecordHandle', data)"
        ></dialog-question>
    </div>
</template>

<script>
    import MessageList from './message-list/index.vue';
    import InputControl from './input-control/index.vue';
    import ViewHeader from './view-header.vue';
    import DialogQuestion from './dialog-question.vue';
    import {
        mapMutations, mapGetters, mapActions, mapState,
    } from 'vuex';

    import MixinSocket from './mixin-scoket.js';
    import MixinUpload from 'views/crm/common/mixin-upload';
    import { medicalImagingViewerDialogService } from '@/medical-imaging-viewer/store/medical-imaging-viewer-dialog.js';

    export default {
        name: 'ConsultingOnline',
        components: {
            MessageList,
            InputControl,
            ViewHeader,
            DialogQuestion,
        },
        mixins: [
            MixinSocket,
            MixinUpload,
        ],
        props: {
            outpatientId: String,
        },
        data() {
            return {
                inited: false,
                loading: false,

                timer: null,
                questionSheetId: '',
                visibleQuestionSheet: false,

                isScrollDown: false,
            };
        },
        created() {
            this.muResetChargeSheetList();

            this.muUpdateNow(Date.now());
            this.timer = setInterval(() => {
                this.muUpdateNow(Date.now());
            }, 1000 * 30);
        },
        async mounted() {
            if (this.outpatientId) {
                this.loading = true;
                await this.acFetchConsultationInfo(this.outpatientId);
                // 获取会话信息
                if (this.conversationId) {
                    this.acFetchConversationInfo(this.conversationId);
                }
                this.loading = false;
            }
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'currentClinic',
            ]),
            ...mapState('im', [
                'consultationInfo',
                'conversationInfo',
            ]),
            ...mapGetters('im', [
                'conversationId', 'conversationClinicId',
            ]),
            // 用户id
            userId() {
                const { id } = this.userInfo || {};
                return id || '';
            },
        },
        methods: {
            ...mapMutations('im', [
                'muUpdateNow',
                'muResetChargeSheetList',
                'muUpdateConsultationInfo',
            ]),
            ...mapActions('im', [
                'acFetchConsultationInfo',
                'acFetchConversationInfo',
                'acUpdateAllChargeSheet',
            ]),
            /**
             * 调用消息列表内的方法
             * <AUTHOR>
             * @date 2020-05-22
             * @param {String} funcName 方法名称
             * @param {any} args 参数
             */
            handleMessageFunc(funcName, ...args) {
                const messageListRef = this.$refs['message-list'];
                return messageListRef && messageListRef[funcName] && messageListRef[funcName](...args);
            },
            /**
             * 点击发送文字消息
             * sendStatus => 0-发送中，1-发送失败，2-发送成功
             * <AUTHOR>
             * @date 2020-05-26
             * @param {Object} body 需要发送的消息体
             */
            onSendText(body) {
                const {
                    sendItem, saveItem, 
                } = this.getTextMessage(body);
                const res = this.createSendTask(sendItem);
                res && this.handleMessageFunc('insertMessageItem', saveItem);
            },
            /**
             * 当发送图片消息时
             * <AUTHOR>
             * @date 2020-04-26
             * @param {Object} body
             */
            async onSendImage(body) {
                const messageBody = this.getImageMessage(body);
                if (messageBody) {
                    const {
                        sendItem, saveItem, 
                    } = messageBody;
                    this.handleMessageFunc('insertMessageItem', saveItem);
                    //这里图片路径用当前发起会话的时候这个医生坐诊的门店id
                    const clinicId = this.conversationClinicId || this.currentClinic.clinicId;
                    const [err, url] = await this.handleImImageUpload(body.file, clinicId);
                    if (!err) {
                        sendItem.body = {
                            imageUrl: url,
                            width: body.width,
                            height: body.height,
                        };
                        saveItem.body.url = url;
                        const res = this.createSendTask(sendItem);
                        if (res) return;
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: err,
                        });
                    }
                    this.handleMessageFunc('handleSendTimeout', {});
                }
            },
            /**
             * 查看问诊单详情
             * <AUTHOR>
             * @date 2020-05-26
             * @param {String} questionSheetId 问诊单id
             */
            onClickQuestionDetail(questionSheetId) {
                this.questionSheetId = questionSheetId;
                this.visibleQuestionSheet = true;
            },
            /**
             * 当预览图片时
             * <AUTHOR>
             * @date 2020-05-24
             * @param {Array} list 预览图片列表
             * @param {Number} index 初始化打开图片索引
             */
            onClickPreviewImages(list, index) {
                medicalImagingViewerDialogService.previewImageAttachment({
                    attachments: list,
                    attachment: list[index],
                });
            },
            /**
             * 判断当前页面是否滑到底部
             * <AUTHOR>
             * @date 2020-05-28
             */
            checkScrollDown() {
                this.isScrollDown = this.handleMessageFunc('checkIsScrollDown');
            },
            /**
             * 当过渡动画完成时
             * 当是底部时，此时也滑动到底部
             * <AUTHOR>
             * @date 2020-05-28
             */
            onTransitionEnd() {
                this.isScrollDown && this.handleMessageFunc('onClickToDown');
            },

            handleEditAgain(payload) {
                this.$refs?.inputControl?.updateText(payload);
            },

            handleRevoke(id) {
                this.revokeMessage(id);
            },

            handleOtherRevoke(id) {
                const messageListRef = this.$refs['message-list'];
                messageListRef && messageListRef.handleOtherRevoke && messageListRef.handleOtherRevoke(id);
            },
        },
        beforeDestroy() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.outpatient__consulting-online {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 100%;
    height: 100%;
    font-size: 14px;
    font-weight: 400;
    color: $T2;
}
</style>
