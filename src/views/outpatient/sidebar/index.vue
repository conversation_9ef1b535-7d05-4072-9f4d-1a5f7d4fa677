<!--门诊右侧边栏 智能诊断/患者历史-->

<template>
    <component :is="curWrap">
        <div data-cy="outpatient-sidebar" class="outpatient-sidebar sidebar-container">
            <div style="width: 100%;">
                <abc-tabs-v2
                    v-model="curTab"
                    :option="tabOptions"
                    disable-indicator
                    :custom-gap="2"
                    :size="isBrief ? 'huge' : 'large'"
                    adaptation
                    :center="tabOptions.length === 1"
                    :class="{
                        'normal-font-size': tabOptions.length === 1,
                        'small-size': !isBrief,
                    }"
                    style="padding-left: 4px;"
                    @change="changeTab"
                >
                </abc-tabs-v2>
            </div>

            <div class="sidebar-content-wrapper">
                <outpatient-online-inquiry
                    v-if="curTab === 0"
                    :key="`inquiry${ consultationId}`"
                    :outpatient-id="$route.params.id"
                    :unread-message-nums="1"
                    @add2MedicalRecordHandle="(data) => $emit('add2MedicalRecordHandle', data)"
                ></outpatient-online-inquiry>

                <patient-outpatient-history
                    v-show="curTab === 1"
                    :patient-info="patientInfo"
                    :from-module="fromModule"
                    has-copy-mr
                    has-copy
                    :outpatient-status="outpatientStatus"
                    :is-brief="isBrief"
                    :prepend-slot-is-visible="prependSlotIsVisible"
                    @copy="(data, type) => $emit('copy', data, type)"
                    @changeTotalCount="handleTotalCountChange"
                >
                    <transition
                        slot="prepend"
                        name="ai-config-transition"
                    >
                        <div
                            v-show="aiConfigVisible"
                            class="ai-config-container"
                        >
                            <abc-collapse
                                v-model="configCollapseValue"
                                accordion
                                :border="false"
                            >
                                <abc-collapse-item
                                    value="1"
                                >
                                    <abc-flex
                                        slot="header"
                                        align="center"
                                        gap="6"
                                        style="width: 100%; padding: 0 6px 0 16px;"
                                    >
                                        <abc-text size="small" :bold="!isHistoryEntryCollapsed">
                                            辅助诊疗
                                        </abc-text>

                                        <abc-flex
                                            style=" width: 20px; height: 18px; font-size: 11px; color: var(--abc-color-B2); background: var(--abc-color-B4); border-radius: 3px;"
                                            align="center"
                                            justify="center"
                                        >
                                            AI
                                        </abc-flex>

                                        <abc-flex align="center" gap="8" style="margin-left: auto;">
                                            <abc-text size="mini" theme="gray">
                                                本次就诊
                                            </abc-text>

                                            <abc-icon v-if="!isHistoryEntryCollapsed" icon="dropdown_line_up" color="var(--abc-color-T3)"></abc-icon>
                                            <abc-icon v-else icon="dropdown_line" color="var(--abc-color-T3)"></abc-icon>
                                        </abc-flex>
                                    </abc-flex>

                                    <ai-config
                                        ref="AiConfigRef"
                                        :medical-record-type="medicalRecordType"
                                        :enable-deepseek="isDeepseekEnable"
                                        :medical-record="medicalRecord"
                                        :switch-setting="switchSetting"
                                        :patient-info="patientInfo"
                                        :is-draft="isDraft"
                                        :title-visible="false"
                                        @start-deepseek="handleStartDeepseek"
                                    ></ai-config>
                                </abc-collapse-item>
                            </abc-collapse>
                        </div>
                    </transition>
                </patient-outpatient-history>

                <medical-report-list
                    v-show="curTab === 5"
                    :patient-id="patientId"
                    @changeTotalCount="handleMedicalReportCountChange"
                >
                </medical-report-list>

                <medical-imaging-list
                    v-show="curTab === 2"
                    :patient-info="patientInfo"
                    @changeTotalCount="handleMedicalImagingListTotalCountChange"
                ></medical-imaging-list>

                <ai-diagnosis
                    v-if="curTab === 4"
                    :disabled="disabled"
                    :loading="loading"
                    :medical-record-type="medicalRecordType"
                    :enable-deepseek="isDeepseekEnable"
                    :medical-record="medicalRecord"
                    :switch-setting="switchSetting"
                    :patient-info="patientInfo"
                    :is-draft="isDraft"
                    @change="changeByKey"
                    @changeChineseDiagnosis="changeChineseDiagnosis"
                    @addCPrescription="addPrescription"
                >
                </ai-diagnosis>
            </div>
        </div>
    </component>
</template>

<script type="text/ecmascript-6">
    import {
        mapGetters, mapActions, mapState,
    } from 'vuex';
    import { debounce } from 'utils/lodash';

    import AiDiagnosis from './ai-diagnosis.vue';
    import MedicalImagingList from './medical-imaging-list';
    import OutpatientOnlineInquiry from '../consulting-online/index.vue';
    import PatientOutpatientHistory from 'views/layout/patient-outpatient-history.vue';
    import MedicalReportList from './medical-report-list.vue';
    import AbcContainerRight from 'src/views/layout/abc-container/container-right.vue';

    import { createMedicalRecord } from '../constants.js';
    import { formatDentistry2Text } from 'views/outpatient/common/medical-record/utils.js';
    import { OutpatientStatusEnum } from 'views/outpatient/constants';
    import {
        computed, onUnmounted, watch, ref, onBeforeMount,
    } from 'vue';
    import { getApp } from '@/core/app';
    import Logger from 'utils/logger';
    import {
        getUseDeepseekEnableStore, getUseDeepseekStore, getUseDeepseekDataStore,
    } from '@/module-federation-dynamic/deepseek';
    import { storeToRefs } from 'store/pinia';
    import { useAIProperty } from 'views/outpatient/sidebar/use-ai-property';

    export default {
        name: 'OutpatientSidebar',

        components: {
            MedicalImagingList,
            AiDiagnosis,
            OutpatientOnlineInquiry,
            PatientOutpatientHistory,
            MedicalReportList,
            AiConfig: () => import('./ai-config.vue'),
        },

        props: {
            medicalRecord: Object,
            patientInfo: Object,
            fromModule: {
                type: String,
                default: 'outpatient',
            },
            disabled: Boolean,
            isConsultation: Boolean,

            consultationId: [String, Number],
            // 是否显示简要信息
            isBrief: {
                type: Boolean,
                default: true,
            },
            // 最外层包裹是否启用container-right
            isContainerRight: {
                type: Boolean,
                default: true,
            },
            outpatientStatus: {
                type: Number,
                default: OutpatientStatusEnum.UN_DIAGNOSIS,
            },
            diagnosedDate: String,
            switchSetting: {
                type: Object,
                default: () => ({}),
            },
            isDraft: {
                type: Number,
                default: 0,
            },
            examReportFinishedIds: {
                type: Array,
                default: () => [],
            },
        },

        setup(props) {
            const isDeepseekEnable = ref(true);

            // 用于清理的变量
            let unWatchDeepseekEnable = null;
            let unWatchMedicalRecord = null;
            let unWatchPatientInfo = null;
            let unWatchExtendInfo = null;
            let unWatchSwitchSetting = null;

            const property = useAIProperty();
            const {
                changeId,
                updateOutpatientConfig,
                changeAutoStartDeepseek,
                getOutpatientConfig,
                updateDefaultConfig,
                updateHistoryEntryCollapsed,
            } = property;

            const {
                isUpdated, isHistoryEntryCollapsed,
            } = storeToRefs(property);

            // 在组件卸载时清理
            onUnmounted(() => {
                if (unWatchDeepseekEnable) unWatchDeepseekEnable();
                if (unWatchMedicalRecord) unWatchMedicalRecord();
                if (unWatchPatientInfo) unWatchPatientInfo();
                if (unWatchExtendInfo) unWatchExtendInfo();
                if (unWatchSwitchSetting) unWatchSwitchSetting();
            });

            // 加载 Deepseek 模块的函数
            const loadDeepseekModules = async () => {
                try {
                    // 获取模块
                    const { useDeepseekEnableStore } = await getUseDeepseekEnableStore();
                    const { useDeepseekStore } = await getUseDeepseekStore();
                    const { useDeepseekDataStore } = await getUseDeepseekDataStore();

                    if (!useDeepseekStore || !useDeepseekEnableStore || !useDeepseekDataStore) {
                        throw new Error('无法加载 Deepseek 模块');
                    }

                    // 初始化 store
                    const deepseekStore = useDeepseekStore();
                    const deepseekDataStore = useDeepseekDataStore();
                    const deepseekEnableStore = useDeepseekEnableStore();

                    const {
                        initReadTipsStatus,
                    } = deepseekStore;

                    const {
                        setIsDeepseekEnable,
                    } = deepseekEnableStore;
                    const {
                        isDeepseekEnable: _isDeepseekEnable,
                    } = storeToRefs(deepseekEnableStore);
                    isDeepseekEnable.value = _isDeepseekEnable;

                    const {
                        setMedicalRecord,
                        setMedicalRecordSwitch,
                        setPatientInfo,
                        setExtendInfo,
                    } = deepseekDataStore;

                    // 设置数据
                    const extendInfo = computed(() => {
                        return {
                            diagnosedDate: props.diagnosedDate || new Date(),
                            examReportFinishedIds: props.examReportFinishedIds || [],
                        };
                    });

                    // 监听属性变化
                    unWatchMedicalRecord = watch(() => props.medicalRecord, (newVal) => {
                        if (setMedicalRecord) {
                            setMedicalRecord(newVal);
                        }
                    }, {
                        immediate: true, deep: true,
                    });

                    unWatchPatientInfo = watch(() => props.patientInfo, (newVal) => {
                        if (setPatientInfo) {
                            setPatientInfo(newVal);
                        }
                    }, {
                        immediate: true, deep: true,
                    });

                    unWatchExtendInfo = watch(() => extendInfo.value, (newVal) => {
                        if (setExtendInfo) {
                            setExtendInfo(newVal);
                        }
                    }, {
                        immediate: true, deep: true,
                    });

                    unWatchSwitchSetting = watch(() => props.switchSetting, (newVal) => {
                        if (setMedicalRecordSwitch) {
                            setMedicalRecordSwitch(newVal);
                        }
                    }, {
                        immediate: true, deep: true,
                    });

                    // 设置 store 监听
                    const { store } = getApp();
                    unWatchDeepseekEnable = store.watch(
                        () => store.state.property.chainBasic?.deepseek?.enable,
                        (enable) => {
                            if (setIsDeepseekEnable) {
                                setIsDeepseekEnable(!!enable);
                            }
                        },
                        { immediate: true },
                    );

                    // 初始化提示状态
                    if (initReadTipsStatus) {
                        initReadTipsStatus();
                    }
                } catch (error) {
                    console.error('Failed to load Deepseek modules:', error);
                    Logger.report({
                        scene: 'deepseek_report_scene',
                        data: {
                            info: 'Failed to load Deepseek modules',
                            message: error.message || 'unknown',
                            error,
                        },
                    });
                }
            };

            // 在组件创建时加载模块
            onBeforeMount(() => {
                loadDeepseekModules();
            });

            return {
                isDeepseekEnable,
                changeId,
                updateOutpatientConfig,
                changeAutoStartDeepseek,
                getOutpatientConfig,
                isUpdated,
                updateDefaultConfig,
                updateHistoryEntryCollapsed,
                isHistoryEntryCollapsed,
            };
        },

        data() {
            return {
                visibleOnline: true,
                loading: false,
                curTab: 1,
                historyTotalCount: 0,
                processTotalCount: 0,
                // 影像报告数量
                medicalImagingTotalCount: 0,
                // 报告数量
                medicalReportCount: 0,

                smallScreen: false,
            };
        },

        computed: {
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapState('socialPc', [
                'dictDiseaseType',
            ]),
            ...mapGetters(['isIntranetUser']),

            configCollapseValue: {
                get() {
                    if (this.isHistoryEntryCollapsed) {
                        return [];
                    }

                    return ['1'];
                },
                set(val) {
                    if (val && val.length) {
                        this.updateHistoryEntryCollapsed(0);
                        return;
                    }

                    this.updateHistoryEntryCollapsed(1);
                },
            },

            routeParamOrQueryId() {
                return this.$route.params.id || this.$route.query.outpatientId;
            },

            outpatientSheetId() {
                return this.medicalRecord?.outpatientSheetId || '';
            },

            aiConfigVisible() {
                return !!this.medicalRecord?.chiefComplaint;
            },

            curWrap() {
                if (this.isContainerRight) {
                    return AbcContainerRight;
                }
                return {
                    render(h) {
                        return h('div', {
                            style: {
                                height: '100%',
                            },
                        }, this.$slots.default);
                    },
                };
            },

            sex() {
                return this.patientInfo && this.patientInfo.sex;
            },
            age() {
                return this.patientInfo && this.patientInfo.age;
            },
            patientId() {
                return this.patientInfo && this.patientInfo.id || '';
            },

            tabOptions() {
                const tabOptions = [];
                const {
                    sidebarShowHistory,
                    sidebarShowReport,
                } = this.viewDistributeConfig.Outpatient;

                const isConsultation = this.isConsultation && this.consultationId;

                if (isConsultation) {
                    tabOptions.push({
                        label: '会话',
                        value: 0,
                        iconOption: {
                            icon: 'notice',
                            color: '#00BB47',
                            size: '14px',
                        },
                        customClass: 'custom-tabs-item',
                    });
                }
                if (sidebarShowHistory) {
                    tabOptions.push({
                        label: '历史',
                        value: 1,
                        statisticsNumber: this.historyTotalCount,
                        maxStatisticsNumber: 99,
                        customClass: 'custom-tabs-item',
                    });
                }

                if (sidebarShowReport) {
                    tabOptions.push({
                        label: '报告',
                        value: 5,
                        statisticsNumber: this.medicalReportCount,
                        maxStatisticsNumber: 99,
                        customClass: 'custom-tabs-item',
                    });
                }

                tabOptions.push({
                    label: '影像',
                    value: 2,
                    statisticsNumber: this.medicalImagingTotalCount,
                    maxStatisticsNumber: 99,
                    customClass: 'custom-tabs-item',
                });

                if (!isConsultation && !this.isIntranetUser) {
                    tabOptions.push({
                        label: this.smallScreen ? 'AI' : 'AI诊疗',
                        value: 4,
                        customClass: 'custom-tabs-item-ai',
                        iconOption: {
                            icon: 's-abcai-color',
                        },
                    });
                }

                return tabOptions;
            },

            medicalRecordType() {
                const { medicalRecord } = this.outpatientEmployeeConfig;
                return medicalRecord.type || 0;
            },

            mr: {
                get() {
                    return this.medicalRecord || createMedicalRecord();
                },
            },
            chiefComplaint: {
                get () {
                    return this.mr.chiefComplaint;
                },
                set (val) {
                    this.mr.chiefComplaint = val;
                },
            },

            presentHistory: {
                get () {
                    return this.mr.presentHistory;
                },
                set (val) {
                    this.mr.presentHistory = val;
                },
            },

            physicalExamination: {
                get () {
                    return this.mr.physicalExamination;
                },
                set (val) {
                    this.mr.physicalExamination = val;
                },
            },
            chineseExamination: {
                get () {
                    return this.mr.chineseExamination;
                },
                set (val) {
                    this.mr.chineseExamination = val;
                },
            },
            diagnosis: {
                get () {
                    return this.mr.diagnosis;
                },
                set (val) {
                    this.mr.diagnosis = val;
                },
            },
            extendDiagnosisInfos: {
                get () {
                    return this.mr.extendDiagnosisInfos || [];
                },
                set (val) {
                    this.mr.extendDiagnosisInfos = val;
                },
            },
            syndrome: {
                get () {
                    return this.mr.syndrome;
                },
                set (val) {
                    this.mr.syndrome = val;
                },
            },
            therapy: {
                get () {
                    return this.mr.therapy;
                },
                set (val) {
                    this.mr.therapy = val;
                },
            },

            chinesePrescription: {
                get () {
                    return this.mr.chinesePrescription;
                },
                set (val) {
                    this.mr.chinesePrescription = val;
                },
            },

            prependSlotIsVisible() {
                return this.aiConfigVisible && !this.isHistoryEntryCollapsed;
            },
        },
        watch: {
            'medicalRecord.chiefComplaint': {
                handler() {
                    this.loading = true;
                    this._fetch();
                },
            },
            'medicalRecord.presentHistory': {
                handler() {
                    this._fetch();
                },
            },
            'medicalRecord.syndrome': {
                handler() {
                    this._fetch();
                },
            },
            'medicalRecord.physicalExamination': {
                handler() {
                    this._fetch();
                },
            },
            'medicalRecord.chineseExamination': {
                handler() {
                    this._fetch();
                },
            },
            'medicalRecord.extendDiagnosisInfos': {
                handler() {
                    this._fetch();
                },
                deep: true,
            },
            medicalRecordType: {
                handler() {
                    this._fetch();
                },
            },
            'medicalRecord.therapy': {
                handler() {
                    this._fetch();
                },
            },

            sex() {
                this._fetch();
            },
            age: {
                handler() {
                    this._fetch();
                },
                deep: true,
            },

            isConsultation() {
                this.initCurTab();
            },

            routeParamOrQueryId: {
                handler(val) {
                    this.changeId(val);
                },
                immediate: true,
            },
        },

        created() {
            this.initCurTab();
            this._fetch = debounce(this.fetch, 200, true);
            this.fetch();

            this.$abcEventBus.$on('outpatient-detail-loaded', () => {
                if (this.curTab === 4) {
                    return;
                }

                this.getOutpatientConfig(this.outpatientSheetId, this.isDraft);
            }, this);
        },

        mounted() {
            this.handleScreenSize();
            window.addEventListener('resize', this.handleScreenSize);
        },

        beforeDestroy() {
            window.removeEventListener('resize', this.handleScreenSize);
        },

        methods: {
            ...mapActions('aiDiagnosis', [
                'fetchDiseases',
                'fetchSickness',
                'fetchSicknessPrescription',
            ]),

            async handleStartDeepseek() {
                await this.updateOutpatientConfig(this.outpatientSheetId, this.isDraft);
                if (this.isUpdated) {
                    await this.updateDefaultConfig();

                    try {
                        this.$abcPlatform.service.report.reportEventSLS('ai_diagnosis_modify_config', 'AI诊疗修改个性化配置');
                    } catch (e) {
                        console.error(e);
                    }
                }

                this.changeAutoStartDeepseek(true);
                this.curTab = 4;
            },

            handleScreenSize() {
                const { innerWidth } = window;

                if (innerWidth < 1560) {
                    this.smallScreen = true;
                } else {
                    this.smallScreen = false;
                }
            },

            initCurTab() {
                const { sidebarShowHistory } = this.viewDistributeConfig.Outpatient;
                if (this.isConsultation && this.consultationId) {
                    this.curTab = 0;
                } else if (sidebarShowHistory) {
                    this.curTab = 1;
                }
            },

            changeTab(val) {
                this.curTab = val;
                if (val === 4) {
                    Logger.report({ scene: 'ai_diagnosis_tab' });
                }
            },

            handleTotalCountChange(totalCount) {
                this.historyTotalCount = totalCount;
            },

            handleMedicalReportCountChange(totalCount) {
                this.medicalReportCount = totalCount;
            },

            handleProcessTotalCountChange(totalCount) {
                this.processTotalCount = totalCount;
            },

            handleMedicalImagingListTotalCountChange(totalCount) {
                this.medicalImagingTotalCount = totalCount;
            },

            addPrescription(pr) {
                this.chinesePrescription += (this.chinesePrescription ? '，' : '') + pr.symptomName;
                this.$emit('addCPrescription', pr);
            },

            /**
             * @desc 由于网诊没有sidebar，需要主动请求
             * <AUTHOR>
             * @date 2020/05/08 18:29:57
             */
            async fetch() {
                try {
                    this.loading = true;
                    if (this.medicalRecordType === 1) {
                        // 中医查询  病证
                        const diagnosis = formatDentistry2Text(this.extendDiagnosisInfos) || '';
                        await this.fetchSicknessPrescription({
                            chiefComplaint: this.chiefComplaint,
                            presentHistory: this.presentHistory,
                            physicalExamination: this.physicalExamination,
                            chineseExamination: this.chineseExamination,
                            diagnosis,
                            syndrome: this.syndrome,
                            therapy: this.therapy,
                            sex: this.sex,
                            age: this.age,
                            dictDiseaseType: this.dictDiseaseType,
                        });
                    } else {
                        // 西医查询疾病诊断
                        await this.fetchDiseases({
                            chiefComplaint: this.chiefComplaint,
                            physicalExamination: this.physicalExamination,
                            chineseExamination: this.chineseExamination,
                            sex: this.sex,
                            age: this.age,
                        });
                    }
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                    console.error(e);
                }
            },

            changeByKey(key, symptomName) {
                let val = this[key] ? this[key].split(/,|，/) : [];
                if (val.indexOf(symptomName) > -1) {
                    val = val.filter((item) => { return item && item !== symptomName; });
                } else {
                    val.push(symptomName);
                }
                this[key] = val.join('，');
            },

            /**
             * @desc 中医选择辨证需要把 诊断也带入, 取消辨证不清空诊断
             * <AUTHOR>
             * @date 2020/05/14 10:43:48
             */
            changeChineseDiagnosis(diagnosis) {
                const diagnosisObj = this.extendDiagnosisInfos[0];
                if (!diagnosisObj) return;

                if (!diagnosisObj.value.find((item) => item.name === diagnosis)) {
                    diagnosisObj.value.push({
                        name: diagnosis,
                        code: null,
                    });
                }
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import "src/styles/theme.scss";
    @import "src/styles/mixin.scss";

    .outpatient-sidebar {
        .wechat-consultation {
            color: var(--abc-color-G2);
        }

        .custom-tabs-item {
            min-width: 60px;
        }

        .ai-config-container {
            max-height: 324px;
            opacity: 1;
            will-change: max-height, opacity;

            .abc-collapse-item__title {
                padding: 0 !important;
            }

            .abc-collapse-item__content {
                padding: 0;
            }
        }

        .ai-config-transition-enter-active {
            transition: max-height 0.2s cubic-bezier(0.34, 0.69, 0.1, 1), opacity 0.1s ease-in-out 0.2s;
        }

        .ai-config-transition-leave-active {
            transition: all 0.1s ease-in-out;
        }

        .ai-config-transition-enter,
        .ai-config-transition-leave-to {
            max-height: 0;
            opacity: 0;
        }
    }
</style>
