<template>
    <abc-dialog
        v-if="modalVisible"
        v-model="modalVisible"
        title="修改名称"
        content-styles="width: 360px;"
        append-to-body
    >
        <div class="modify-image-name-dialog-wrapper">
            <div class="modify-image-wrapper">
                <template v-if="isPDF(url)">
                    <abc-image
                        :src="require('src/assets/images/pdf-icon.png')"
                        :alt="name"
                        :width="82"
                        :height="82"
                        fit="cover"
                        style="border-radius: var(--abc-border-radius-small);"
                    ></abc-image>
                </template>

                <template v-else>
                    <abc-image
                        :src="url"
                        :alt="name"
                        :width="82"
                        :height="82"
                        fit="cover"
                        oss-style-name="image/resize,m_fill,w_82,h_82"
                        style="border-radius: var(--abc-border-radius-small);"
                    ></abc-image>
                </template>
            </div>


            <abc-input
                v-model="fileName"
                v-abc-focus-selected
                type="text"
                class="modify-name-input"
                placeholder="请输入名字"
            ></abc-input>
        </div>

        <div slot="footer" style="text-align: right;">
            <abc-button :loading="btnLoading" :disabled="confirmDisabled" @click="handleConfirm">
                确定
            </abc-button>

            <abc-button type="blank" @click="modalVisible = false;">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import CrmAPI from 'api/crm';
    import { isPDF } from '@/utils';

    export default {
        name: 'ModifyImageName',

        model: {
            prop: 'visible',
            event: 'change',
        },

        props: {
            name: {
                type: [String, null],
                required: true,
            },

            id: {
                type: String,
                required: true,
            },

            patientId: {
                type: String,
                required: true,
            },

            url: {
                type: String,
                required: true,
            },

            visible: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                fileName: '',
                extension: '',

                btnLoading: false,
            };
        },

        computed: {
            modalVisible: {
                get() {
                    return this.visible;
                },
                set(val) {
                    this.$emit('change', val);
                },
            },

            confirmDisabled() {
                return !this.fileName;
            },

            finalName() {
                if (!this.extension) return this.fileName;

                return `${this.fileName}.${this.extension}`;
            },
        },

        watch: {
            name: {
                handler(val) {
                    if (!val) return;

                    const reg = /^([^.]+)\.(\w+)$/;
                    const matches = val.match(reg);
                    if (matches) {
                        this.fileName = matches[1];
                        this.extension = matches[2];
                        return;
                    }

                    this.fileName = val;
                    this.extension = '';
                },
                immediate: true,
            },
        },

        methods: {
            async handleConfirm() {
                try {
                    this.btnLoading = true;

                    await CrmAPI.modifyAttachmentName(this.patientId, this.id, {
                        displayName: this.finalName,
                    });

                    this.btnLoading = false;
                    this.$emit('img-name-modify-success', this.id, this.finalName);

                    this.modalVisible = false;
                } catch (e) {
                    this.btnLoading = false;
                }
            },

            isPDF,
        },
    };
</script>

<style lang="scss" scoped>
.modify-image-name-dialog-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;

    .modify-image-wrapper {
        width: 82px;
        height: 82px;
        border-radius: var(--abc-border-radius-small);
    }

    .modify-name-input {
        width: 100%;
        margin-top: 12px;
    }
}
</style>
