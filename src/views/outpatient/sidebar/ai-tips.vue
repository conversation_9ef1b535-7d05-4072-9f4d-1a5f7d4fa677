<template>
    <abc-modal
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        :show-footer="false"
        :show-close="false"
        disabled-keyboard
    >
        <abc-flex vertical gap="24" align="center">
            <h4 class="build-in-h4" style=" margin-bottom: 0; text-align: center;">
                AI辅助诊疗使用须知
            </h4>
            <div style="width: 412px; text-align: justify;">
                <p style="margin-top: 0; color: #000000;">
                    “ABC{{ $app.institutionTypeWording }}管家AI辅助诊疗系统”（以下简称本系统）提供的信息，只供参考使用。
                </p>
                <p style="margin-top: 8px; color: #000000;">
                    本系统是在权威医学文献的基础上，结合最新临床证据及专家经验，使用人工智能技术研发的临床决策支持系统，本系统可以在医生诊前、诊中，治疗等环节提供决策支持。本系统会根据患者主诉、现病史、既往病史、查体信息、辅助检验等数据情况以循证方式计算推导出合理的诊疗决策结论，辅助医生完成诊疗服务。
                </p>
                <p style="margin-top: 8px; color: #000000;">
                    本系统充分的尊重医生在诊疗过程中的决定权，本系统提供的所有信息仅供医生在诊疗过程中参考，所有的决定权属于医生。
                </p>
                <p style="margin-top: 8px; color: #000000;">
                    在法律允许的范围内，本系统在此声明，不承担医生或任何人士就使用本系统所提供的信息所引致的任何直接、间接、附带、从属、特殊、惩罚性或惩戒性的损害赔偿。
                </p>
            </div>
            <div class="ai-diagnosis-tips-footer">
                <abc-button
                    theme="success"
                    size="large"
                    width="212"
                    @click="handleClose"
                >
                    我知道了
                </abc-button>
            </div>
        </abc-flex>
    </abc-modal>
</template>

<script>
    export default {
        name: 'AiTips',

        props: {
            showDialog: {
                type: Boolean,
                default: false,
            },
        },

        methods: {
            handleClose() {
                this.$emit('close');
            },
        },
    };
</script>
