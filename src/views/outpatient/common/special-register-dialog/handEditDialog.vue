<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="修改自付比例"
        size="small"
        append-to-body
        :auto-focus="false"
    >
        <abc-form ref="editForm" :label-width="76" label-position="left">
            <abc-form-item label="医保项目">
                <abc-text>{{ row.socialName }}</abc-text>
            </abc-form-item>
            <abc-form-item label="医保编码">
                <abc-text>{{ row.shebaoCode }}</abc-text>
            </abc-form-item>
            <abc-form-item-group grid :grid-column-count="4">
                <abc-form-item
                    required
                    label="自付比例"
                    :validate-event="validateSelfpayProp"
                >
                    <abc-input
                        v-model="row.selfpayProp"
                        :config="{
                            max: 100, supportZero: true,
                        }"
                        :width="120"
                        type="number"
                    >
                        <span slot="appendInner">%</span>
                    </abc-input>
                </abc-form-item>
            </abc-form-item-group>
        </abc-form>
        
        <div slot="footer" class="dialog-footer">
            <abc-button @click="updateSelfpayProp">
                确定
            </abc-button>
            <abc-button variant="ghost" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import Clone from 'utils/clone';

    export default {
        props: {
            value: Boolean,
            currentRow: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                row: null,
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },
        created() {
            this.row = Clone(this.currentRow);
            this.row.selfpayProp = parseFloat((this.row.selfpayProp * 100 || 0).toFixed(2));
        },
        methods: {
            validateSelfpayProp(value, callback) {
                if (value === '') {
                    return callback({
                        validate: false,
                        message: '请输入自付比例',
                    });
                }
                callback({ validate: true });
            },
            updateSelfpayProp() {
                this.$refs.editForm.validate((valid) => {
                    if (!valid) return;
                    this.row.selfpayProp = parseFloat((this.row.selfpayProp / 100).toFixed(2)); 
                    this.$emit('updateSelfpayProp', this.row);
                    this.showDialog = false;
                });
                
            },
        },
    };
</script>
