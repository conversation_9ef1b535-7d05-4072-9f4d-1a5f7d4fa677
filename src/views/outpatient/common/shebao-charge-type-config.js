import { getViewDistributeConfig } from '@/views-distribute/utils.js';

export const ShebaoChargeTypeEnum = Object.freeze({
    SEFL_PAY: 1, // 自费门诊
    GENERAL_CLINIC: 2, // 普通门诊
    CHRONIC_SPECIAL_DISEASE: 3, // 医保慢特病门诊
    GENERAL_FUNDPAY: 5, // 门诊统筹
    CHRONIC_DISEASE: 6, // 门诊慢病
    SERIOUS_DISEASE: 7, // 门诊大病
    SPECIAL_MEDICINE: 8, // 特药门诊
});

export const ShebaoChargeTypeLabel = Object.freeze({
    [ShebaoChargeTypeEnum.SEFL_PAY]: '自费门诊',
    [ShebaoChargeTypeEnum.GENERAL_CLINIC]: '医保普通门诊',
    [ShebaoChargeTypeEnum.CHRONIC_SPECIAL_DISEASE]: '医保慢特病门诊',
    [ShebaoChargeTypeEnum.GENERAL_FUNDPAY]: '门诊统筹',
    [ShebaoChargeTypeEnum.CHRONIC_DISEASE]: '门诊慢病',
    [ShebaoChargeTypeEnum.SERIOUS_DISEASE]: '门诊大病',
    [ShebaoChargeTypeEnum.SPECIAL_MEDICINE]: '特药门诊',
});

export const ShebaoChargeTypeShortLabel = Object.freeze({
    [ShebaoChargeTypeEnum.SEFL_PAY]: '自费',
    [ShebaoChargeTypeEnum.GENERAL_CLINIC]: '普通',
    [ShebaoChargeTypeEnum.CHRONIC_SPECIAL_DISEASE]: '慢特',
    [ShebaoChargeTypeEnum.GENERAL_FUNDPAY]: '统筹',
    [ShebaoChargeTypeEnum.CHRONIC_DISEASE]: '慢病',
    [ShebaoChargeTypeEnum.SERIOUS_DISEASE]: '大病',
    [ShebaoChargeTypeEnum.SPECIAL_MEDICINE]: '特药',
});

export const getShebaoChargeTypeConfig = () => {
    const viewDistributeConfig = getViewDistributeConfig();
    const { canCustomizeShebaoChargeType } = viewDistributeConfig.Outpatient;

    // 辽宁盘锦&&医院 定制
    if (
        window.$abcSocialSecurity.config.isLiaoningPanjin &&
        canCustomizeShebaoChargeType
    ) {
        return [
            {
                value: ShebaoChargeTypeEnum.SEFL_PAY,
                label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.SEFL_PAY],
            },
            {
                value: ShebaoChargeTypeEnum.GENERAL_CLINIC,
                label: '普通门诊',
            },
            {
                value: ShebaoChargeTypeEnum.GENERAL_FUNDPAY,
                label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.GENERAL_FUNDPAY],
            },
            {
                value: ShebaoChargeTypeEnum.CHRONIC_DISEASE,
                label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.CHRONIC_DISEASE],
            },
            {
                value: ShebaoChargeTypeEnum.SERIOUS_DISEASE,
                label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.SERIOUS_DISEASE],
            },
            {
                value: ShebaoChargeTypeEnum.SPECIAL_MEDICINE,
                label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.SPECIAL_MEDICINE],
            },
        ];
    }

    // 系统默认
    return [
        {
            value: ShebaoChargeTypeEnum.SEFL_PAY,
            label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.SEFL_PAY],
        },
        {
            value: ShebaoChargeTypeEnum.GENERAL_CLINIC,
            label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.GENERAL_CLINIC],
        },
        {
            value: ShebaoChargeTypeEnum.CHRONIC_SPECIAL_DISEASE,
            label: ShebaoChargeTypeLabel[ShebaoChargeTypeEnum.CHRONIC_SPECIAL_DISEASE],
        },
    ];
};

