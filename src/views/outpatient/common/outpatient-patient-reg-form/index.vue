<template>
    <div class="outpatient-patient-form">
        <patient-section
            v-if="showPatient"
            :key="postData.patient.id"
            ref="patient-section"
            v-model="postData.patient"
            :source="source"
            :patient-guardian="patientGuardian"
            :is-required="true"
            :is-scan-code="isScanCode"
            :support-one-click-billing="!disabled"
            :disabled="disabledForm"
            :loading="loading"
            :show-patient-guardian="showPatientGuardian"
            :default-tags="defaultTags"
            :show-age-day="showAgeDay"
            :default-patient="selectedPatient"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            :size="size"
            data-cy="outpatient-form-patient-section"
            @change-patient-sex="$emit('change-patient-info')"
            @change-patient-age="$emit('change-patient-info')"
            @enterEvent="enterEvent"
            @change-patient="changePatient"
            @add-patient="changePatient"
            @update-patient="changePatientInfo"
        ></patient-section>

        <abc-flex
            v-if="isDepartmentVisible"
            :key="postData.departmentId + postData.doctorId"
            :gap="6"
        >
            <abc-space is-compact compact-block border-style="dashed">
                <abc-form-item v-if="isCharged" required>
                    <abc-popover
                        placement="bottom-start"
                        :open-delay="600"
                        :offset="10"
                        trigger="hover"
                        theme="yellow"
                    >
                        <abc-input
                            slot="reference"
                            :width="120"
                            disabled
                            :size="size"
                            :value="doctorValueSelected"
                            data-cy="outpatient-form-doctor-input"
                        ></abc-input>
                        <div>
                            医生编码：{{ doctorNationCode }}
                        </div>
                    </abc-popover>
                </abc-form-item>
                <!--选择医生-->
                <abc-form-item
                    v-else
                    required
                >
                    <abc-popover
                        placement="bottom-start"
                        :open-delay="600"
                        :offset="10"
                        :disabled="showPopover || !postData.doctorId"
                        trigger="hover"
                        theme="yellow"
                        style="display: inline-block;"
                    >
                        <abc-input
                            v-if="disabledDepartmentSelect"
                            slot="reference"
                            v-model="doctorValueSelected"
                            disabled
                            :size="size"
                            :width="120"
                        ></abc-input>
                        <abc-select
                            v-else
                            slot="reference"
                            v-model="doctorKey"
                            :width="120"
                            :inner-width="186"
                            :disabled="disabledDepartmentSelect"
                            :placeholder="isAgent ? '选择医生' : ''"
                            :with-search="myDepartments.length + otherDepartments.length > 8"
                            :fetch-suggestions="updateQueryStr"
                            :size="size"
                            data-cy="outpatient-form-doctor-select"
                            @enter="enterEvent"
                            @change="handleDoctorChange"
                            @open="handlePopoverClose"
                            @close="handlePopoverOpen"
                        >
                            <abc-option
                                v-for="d in filterMyDepartments"
                                :key="d.doctorKey"
                                :value="d.doctorKey"
                                :label="d.doctorStr"
                            ></abc-option>

                            <template v-if="!isConsultation">
                                <abc-option
                                    v-if="filterMyDepartments.length && filterOtherDepartments.length"
                                    disabled
                                    :value="null"
                                    style="color: #8d9aa8;"
                                >
                                    其他医生
                                </abc-option>
                            </template>

                            <abc-option
                                v-for="(d,index) in filterOtherDepartments"
                                :key="d.doctorKey + index"
                                :value="d.doctorKey"
                                :label="d.doctorStr"
                            ></abc-option>
                        </abc-select>
                        <div>
                            医生编码：{{ doctorNationCode }}
                        </div>
                    </abc-popover>
                </abc-form-item>

                <abc-form-item v-if="!isConsultation">
                    <abc-select
                        v-model="postData.revisitStatus"
                        class="revisit-wrapper"
                        :width="62"
                        :size="size"
                        :disabled="disabledDepartmentSelect || !canChangeRevisited"
                        data-cy="outpatient-form-revisitStatus-select"
                        @enter="enterEvent"
                        @change="handleVisitChange"
                    >
                        <abc-option label="初诊" :value="1"></abc-option>
                        <abc-option label="复诊" :value="2"></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item v-if="!isConsultation && isShowRegistrationCategory" required>
                    <abc-input
                        v-if="isCharged"
                        :width="60"
                        disabled
                        :size="size"
                        :value="registrationCategoryDisplay"
                        data-cy="outpatient-form-registrationCategory-input"
                    ></abc-input>
                    <abc-select
                        v-else
                        v-model="postData.registrationCategory"
                        :show-value="registrationCategoryShowValue"
                        :size="size"
                        :width="60"
                        :inner-width="90"
                        data-cy="outpatient-form-registrationCategory-select"
                        :disabled="disabled || isCharged"
                        @change="changeCategory"
                    >
                        <abc-option
                            v-for="item in registrationCategoryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </abc-option>
                    </abc-select>
                </abc-form-item>

                <!--填写挂号费-->
                <abc-form-item>
                    <abc-popover
                        theme="yellow"
                        placement="top"
                        trigger="hover"
                        :open-delay="300"
                        :disabled="payFeePopoverDisabled"
                        popper-class="custom-registration-fee-popover"
                    >
                        <abc-input
                            slot="reference"
                            v-model="postData.registrationFee"
                            v-abc-focus-selected
                            :width="size === 'medium' ? 80 : 70"
                            type="money"
                            :size="size"
                            :input-custom-style="{
                                textAlign: 'left',
                                paddingLeft: size === 'medium' ? '32px' : '24px',
                            }"
                            :disabled="disabled || isCharged || !!isConsultation || isThreeDaysRepeat"
                            :readonly="disabledChangePrice"
                            :config="{
                                formatLength: 2, max: 10000000, supportZero: true
                            }"
                            placeholder="诊费"
                            class="doctor-wrapper"
                            data-cy="outpatient-form-registrationFee-input"
                            @enter="enterEvent"
                            @blur="blurHandler"
                            @change="handleChangeFee"
                        >
                            <label
                                slot="prepend"
                                class="prepend"
                            >
                                <abc-currency-symbol-icon size="14"></abc-currency-symbol-icon>
                            </label>
                        </abc-input>

                        <div>
                            <abc-text
                                tag="div"
                                theme="black"
                                size="normal"
                                bold
                            >
                                {{ payFeePopoverInfo.departmentName }}{{ `${payFeePopoverInfo.doctorName ? ` - ${payFeePopoverInfo.doctorName}` : ''}` }}
                            </abc-text>
                            <abc-text tag="div" theme="gray" size="mini">
                                {{ payFeePopoverInfo.modeTypeDesc }}
                            </abc-text>
                            <div class="price-wrapper">
                                <div v-for="item in payFeePopoverInfo.priceDetail" :key="item.label" class="price-item">
                                    <abc-flex justify="space-between">
                                        <abc-text>{{ item.label }}</abc-text>
                                        <abc-money :value="item.value"></abc-money>
                                    </abc-flex>
                                </div>
                            </div>
                            <abc-divider variant="dashed" margin="small"></abc-divider>
                            <abc-text tag="div" theme="gray" size="mini">
                                {{ `可前往【管理-诊疗项目-${$t('registrationFeeName')}】修改设置` }}
                            </abc-text>
                        </div>
                    </abc-popover>
                </abc-form-item>

                <!--费别-->
                <abc-form-item v-if="!isConsultation && postData.shebaoChargeType">
                    <abc-select
                        v-model="postData.shebaoChargeType"
                        :show-value="shebaoChargeTypeDesc"
                        :width="60"
                        :inner-width="size === 'medium' ? 132 : 124"
                        :size="size"
                        :disabled="disabled || disableChargeType"
                        :placement="showPatient ? 'bottom-end' : undefined"
                        data-cy="outpatient-form-shebaoChargeType-select"
                        @enter="enterEvent"
                        @change="handleShebaoChargeTypeChange"
                    >
                        <abc-option
                            v-for="item in shebaoChargeTypeOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
            </abc-space>
            <!--咨询师-->
            <abc-space v-if="showCounselor" is-compact compact-block>
                <abc-form-item>
                    <abc-select
                        v-model="postData.consultantId"
                        clearable
                        :width="80"
                        :inner-width="186"
                        :size="size"
                        :no-icon="disabledDepartmentSelect"
                        :disabled="disabledDepartmentSelect"
                        :placeholder="consultantHint"
                        data-cy="outpatient-form-consultant-select"
                        @enter="enterEvent"
                    >
                        <abc-option
                            v-for="consultant in consultantList"
                            :key="consultant.employeeId"
                            :value="consultant.employeeId"
                            :label="consultant.employeeName"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
            </abc-space>
        </abc-flex>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        mapGetters,
    } from 'vuex';
    import {
        validateMobile, validateAge,
    } from 'utils/validate';
    import localStorage from 'utils/localStorage-handler';
    import SettingAPI from 'api/settings';
    import { pick1 } from 'utils/lodash';
    import { RevisitStatus } from 'src/assets/configure/constants';
    import OutpatientAPI from 'api/outpatient';
    import PatientSection from 'src/views/layout/patient/patient-section';
    import { DepartmentTypeStatus } from 'src/views/outpatient/constants';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import {
        RegistrationCategory, RegistrationCategoryText,
    } from '@/views-hospital/registered-fee/constant';
    import Clone from 'utils/clone';
    import {
        getShebaoChargeTypeConfig,
        ShebaoChargeTypeShortLabel,
    } from 'src/views/outpatient/common/shebao-charge-type-config.js';
    import { REGISTERED_FEE_MODE_TYPE } from 'views/settings/diagnosis-treatment/registered-fee/components/constants';
    import useShowRegistrationFeePopoverInfo from '@/views-dentistry/registration/useShowRegistrationFeePopoverInfo';
    import { ReferralFlagEnum } from '@/common/constants/registration';

    export default {
        name: 'OutpatientPatientForm',

        components: {
            PatientSection,
            AbcCurrencySymbolIcon,
        },
        props: {
            title: String,
            value: Boolean,
            postData: Object,
            disabled: Boolean,
            disabledPatientDetail: Boolean,
            registrationFeeStatus: Number,
            needChangeTags: Boolean,
            isAgent: Boolean,
            status: Number, // 已诊状态
            chargeStatus: Number, // 收费状态
            // 患者信息详情
            loadingInfo: Boolean,
            info: Object,
            cardType: String,
            isConsultation: Boolean,
            showAgeDay: {
                type: Boolean,
                default: false,
            },
            source: {
                type: String,
                default: 'outpatient',
            },
            checkSource: { // 是否校验来源和路由 默认不校验，门诊/儿保tab存在频繁切换可能，需要校验
                type: Boolean,
                default: false,
            },
            isScanCode: {
                type: Boolean,
                default: false,
            },
            loading: {
                type: Boolean,
                default: false,
            },
            isRequired: {
                type: Boolean,
                default: true,
            },
            isDepartmentVisible: {
                type: Boolean,
                default: true,
            },
            showPatientGuardian: {
                type: Boolean,
                default: false,
            },
            defaultTags: {
                type: Array,
                default: () => [],
            },
            patientGuardian: {
                type: Object,
                default: () => ({}),
            },
            showPatient: {
                type: Boolean,
                default: true,
            },
            showCounselor: {
                type: Boolean,
                default: false,
            },
            doctorEnableCategories: {
                type: Array,
                default: () => {
                    return [RegistrationCategory.ORDINARY];
                },
            },
            doctorRegistrationFees: {
                type: Array,
                default: () => [],
            },
            size: String,
        },
        data() {
            return {
                filterQueryStr: '',
                myDepartments: [], // 该登录用户自己的 科室-医生-挂号费 列表
                otherDepartments: [], // 该登录用户可代录的 科室-医生-挂号费 列表
                showPopover: false,
                selectDoctorEnableCategories: Clone(this.doctorEnableCategories),
                selectDoctorRegistrationFees: Clone(this.doctorRegistrationFees),
                lastDiagnosedTime: null,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'currentClinic',
                'departmentRegFeeList',
                'currentAssistantDoctors',
                'outpatient',
                'childHealth',
                'chargeConfig',
                'clinicBasic',
                'isCanSeePatientMobileInOutpatient',
                'chainBasic',
            ]),
            ...mapGetters('consult', ['consultantList']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustOutpatientPrice',
            ]),
            isThreeDaysRepeat() {
                return this.postData.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS;
            },
            disableChargeType() {
                const { disabledOperationSettlementType } = this.chainBasic?.outpatient || {};
                return !!disabledOperationSettlementType;
            },
            isCanSeePatientMobile() {
                return this.isCanSeePatientMobileInOutpatient;
            },
            selectedPatient() {
                let selectedPatient = null;
                if (this.source === 'childHealth') {
                    selectedPatient = this.childHealth.selectedPatient;
                } else {
                    selectedPatient = this.outpatient.selectedPatient;
                }
                return selectedPatient;
            },
            patientSectionKey() {
                return this.postData?.patient?.id + this.selectedPatient?.id;
            },

            disabledForm() {
                return this.disabled || this.disabledPatientDetail;

            },
            disabledDepartmentSelect() {
                return this.disabled || (this.status > 0 && this.chargeStatus > 0) || this.isThreeDaysRepeat;
            },
            // 医生是否能修改初复诊
            canChangeRevisited() {
                return this.clinicBasic.outpatient.settings.canChangeRevisited;
            },
            doctorNationCode() {
                let docItem = this.filterOtherDepartments.find ((item) => {
                    return item.doctorKey === this.doctorKey;
                });
                if (!docItem) {
                    docItem = this.filterMyDepartments.find ((item) => {
                        return item.doctorKey === this.doctorKey;
                    }) || [];
                }
                return docItem.nationalDoctorCode || '-';
            },
            disabledChangePrice() {
                if (!this.employeeCanAdjustOutpatientPrice) return true;
                const { doctorRegisteredBargainSwitch } = this.chargeConfig;
                return !!(this.registrationFeeStatus && this.registrationFeeStatus !== 0) || (!this.userInfo?.isAdmin && doctorRegisteredBargainSwitch !== 1);
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            isCharged() {
                return this.status > 0 && this.chargeStatus > 0;
            },

            filterMyDepartments() {
                return this.myDepartments.filter((item) => {
                    let {
                        doctorName,
                        doctorNamePy,
                        doctorNamePyFirst,
                    } = item;
                    doctorName = doctorName || '';
                    doctorNamePy = doctorNamePy || '';
                    doctorNamePyFirst = doctorNamePyFirst || '';
                    return (
                        doctorName.indexOf(this.filterQueryStr) > -1 ||
                        doctorNamePy.indexOf(this.filterQueryStr.toLowerCase()) > -1 ||
                        doctorNamePyFirst.toLowerCase().indexOf(this.filterQueryStr.toLowerCase()) > -1
                    );
                });
            },
            filterOtherDepartments() {
                return this.otherDepartments.filter((item) => {
                    let {
                        doctorName,
                        doctorNamePy,
                        doctorNamePyFirst,
                    } = item;
                    doctorName = doctorName || '';
                    doctorNamePy = doctorNamePy || '';
                    doctorNamePyFirst = doctorNamePyFirst || '';
                    return (
                        doctorName.indexOf(this.filterQueryStr) > -1 ||
                        doctorNamePy.indexOf(this.filterQueryStr.toLowerCase()) > -1 ||
                        doctorNamePyFirst.toLowerCase().indexOf(this.filterQueryStr.toLowerCase()) > -1
                    );
                });
            },

            // 门诊-已诊-非编辑状态-医生展示
            doctorValueSelected() {
                let str = '';
                if (this.postData.doctorName) {
                    str += this.postData.doctorName;
                }
                if (this.postData.departmentName && this.postData.departmentName !== '默认') {
                    str += ` - ${this.postData.departmentName}`;
                }
                return str;
            },

            registrationCategoryDisplay() {
                const {
                    registrationCategory,
                } = this.postData;

                return (RegistrationCategoryText[registrationCategory] || '').slice(0, 2);
            },

            doctorKey: {
                get() {
                    return `${this.postData.departmentId}|${this.postData.doctorId}`;
                },
                async set(val) {
                    let _obj = this.myDepartments.find((item) => {
                        return item.doctorKey === val;
                    });

                    if (!_obj) {
                        _obj = this.otherDepartments.find((item) => {
                            return item.doctorKey === val;
                        });
                    }

                    if (_obj) {
                        this.postData.departmentId = _obj.departmentId;
                        this.postData.departmentName = _obj.departmentName;
                        this.postData.doctorId = _obj.doctorId;
                        this.postData.doctorName = _obj.doctorName;
                        this.$emit('change-department-doctor-type', _obj);

                        // 非网诊切换才做这些操作
                        if (!this.isConsultation) {
                            // 挂号费没有收费 && 是待诊状态 && 没有指定医生 才更新挂号费
                            if (this.registrationFeeStatus === 0) {
                                await this.changeVisitFee();
                            } else if (this.isShowRegistrationCategory) {
                                // 获取医生的挂号费和号种信息
                                const { data } = await SettingAPI.registeredFee.loadDoctorRegistrationFeeByCategories({
                                    ...pick1(this.postData, ['doctorId', 'departmentId']),
                                });

                                this.selectDoctorEnableCategories = data.enableCategories;
                                this.selectDoctorRegistrationFees = data.registrationFees;

                                // 更改医生后的号种不包含选中的号种，默认选择普通门诊
                                if (!this.selectDoctorEnableCategories.includes(this.postData.registrationCategory)) {
                                    this.postData.registrationCategory = RegistrationCategory.ORDINARY;
                                }
                            }
                        }
                    }

                    this.postData.outpatientSource = +(this.postData.doctorId !== this.userInfo?.id);
                },
            },
            isRequired2: {
                get() {
                    return this.isRequired;
                },
                set(val) {
                    console.log(val);
                    this.$emit('update:is-required',val);
                },
            },
            shebaoChargeTypeDesc() {
                const { shebaoChargeType = 1 } = this.postData;
                return ShebaoChargeTypeShortLabel[shebaoChargeType];
            },
            shebaoChargeTypeOptions() {
                return getShebaoChargeTypeConfig();
            },
            consultantHint() {
                if (this.disabledDepartmentSelect && !this.postData.consultantId) {
                    return '未指定';
                }
                return '咨询师';
            },
            isShowRegistrationCategory() {
                if (this.isConsultation) return false;
                return this.viewDistributeConfig.Settings.schedule.isShowRegistrationCategory;
            },
            registrationCategoryOptions() {
                const arr = [{
                    label: '普通门诊',
                    value: RegistrationCategory.ORDINARY,
                }];

                if (this.selectDoctorEnableCategories.includes(RegistrationCategory.SPECIALIST)) {
                    arr.push({
                        label: '专家门诊',
                        value: RegistrationCategory.SPECIALIST,
                    });
                }

                if (this.postData.isReserved === 0 && this.selectDoctorEnableCategories.includes(RegistrationCategory.CONVENIENCE)) {
                    arr.push({
                        label: '便民门诊',
                        value: RegistrationCategory.CONVENIENCE,
                    });
                }

                return arr;
            },
            registrationCategoryShowValue() {
                const { registrationCategory } = this.postData;
                return this.registrationCategoryOptions.find((item) => item.value === registrationCategory)?.label?.slice(0, 2) || '';
            },
            isSupportMultipleMode() {
                return this.viewDistributeConfig.Settings.project.registrationFee.isSupportMultipleMode;
            },
            payFeePopoverDisabled() {
                if (!this.isSupportMultipleMode) return true;

                return this.registrationFeeStatus !== 0;
            },
            payFeePopoverInfo() {
                let doctorInfo = this.myDepartments.find((item) => {
                    return item.doctorKey === this.doctorKey;
                });

                if (!doctorInfo) {
                    doctorInfo = this.otherDepartments.find((item) => {
                        return item.doctorKey === this.doctorKey;
                    });
                }

                const doctorFee = this.selectDoctorRegistrationFees.find((item) => item.registrationCategory === this.postData.registrationCategory) || {};
                const {
                    modeTypeDesc,
                    priceDetail,
                    regUnitPrice,
                    revisitedRegUnitPrice,
                } = this.getRegistrationFeeInfo(doctorFee);

                return {
                    departmentName: doctorInfo?.departmentName || '其他',
                    doctorName: doctorInfo?.doctorName || '',
                    modeTypeDesc,
                    priceDetail,
                    regUnitPrice,
                    revisitedRegUnitPrice,
                };
            },
        },
        watch: {
            departmentRegFeeList: {
                handler(val) {
                    this.trans2MyDepartments(val);
                },
                deep: true,
            },
            currentAssistantDoctors: {
                handler(val) {
                    this.trans2OtherDepartments(val);
                },
                deep: true,
            },
            status: {
                handler() {
                    this.trans2MyDepartments(this.departmentRegFeeList);
                    this.trans2OtherDepartments(this.currentAssistantDoctors);
                },
            },
            postData: {
                handler(newVal, oldVal) {
                    if (
                        (!oldVal && newVal) ||
                        (oldVal && newVal &&
                            (
                                oldVal.doctorId !== newVal.doctorId ||
                                oldVal.doctorName !== newVal.doctorName ||
                                oldVal.departmentId !== newVal.departmentId
                            )
                        )
                    ) {
                        this.trans2MyDepartments(this.departmentRegFeeList);
                        this.trans2OtherDepartments(this.currentAssistantDoctors);
                    }
                },
                deep: true,
                immediate: true,
            },
            doctorEnableCategories: {
                handler(val) {
                    this.selectDoctorEnableCategories = Clone(val) || [RegistrationCategory.ORDINARY];
                },
                immediate: true,
                deep: true,
            },
            doctorRegistrationFees: {
                handler(val) {
                    this.selectDoctorRegistrationFees = Clone(val) || [];
                },
                immediate: true,
                deep: true,
            },
        },
        setup() {
            const {
                getRegistrationFeeInfo,
            } = useShowRegistrationFeePopoverInfo();

            return {
                getRegistrationFeeInfo,
            };
        },
        async created() {
            const key = `${this.currentClinic.clinicId}_${this.userInfo.id}_last_selected_depart`;
            const lastObj = localStorage.get(key, true);
            if (lastObj) {
                this._lastSelectedDepatId = lastObj.departmentId;
                this._lastSelectedDoctorId = lastObj.doctorId;
            }
            this.trans2MyDepartments(this.departmentRegFeeList);
            this.trans2OtherDepartments(this.currentAssistantDoctors);
            if (this.source === 'childHealth') {
                this.$watch('childHealth.selectedPatient', (val) => {
                    this.updatePatientWxInfo(val);
                }, { deep: true });
            } else {
                this.$watch('outpatient.selectedPatient', (val) => {
                    this.updatePatientWxInfo(val);
                }, { deep: true });
            }
        },
        mounted() {

        },
        methods: {
            validateMobile,
            validateAge,
            handleClosePopover() {
                this.$refs?.['patient-section'].handleClosePopover();
            },
            handleShebaoChargeTypeChange(value) {
                // 存储当前用户选择的费用类型
                const storageObj = {
                    userId: this.userInfo.id,
                    shebaoChargeType: value,
                };
                localStorage.set('shebao_charge_type_last_selected', storageObj, true);
                this.$emit('change-shebao-charge-type');
            },

            async handleVisitChange() {
                if (this.registrationFeeStatus && this.registrationFeeStatus !== 0) return;

                const doctorFee = this.selectDoctorRegistrationFees.find((item) => item.registrationCategory === this.postData.registrationCategory);
                if (doctorFee?.isDiffForRevisited === REGISTERED_FEE_MODE_TYPE.SHORT_DIFFERENT) {
                    return;
                }

                this.changeVisitFee(false);
            },

            changeCategory(value, index, oldVal) {
                if (value === oldVal) return;
                if (this.registrationFeeStatus && this.registrationFeeStatus !== 0) return;
                this.changeVisitFee(false, false);
            },

            handleDoctorChange() {
                this.$emit('change-doctor');
                this.showPopover = false;
            },

            async changeVisitFee(needSetVisitStatus = true, needFetch = true) {
                const oldFee = this.postData.registrationFee;

                // 获取医生的挂号费和号种信息
                if (needFetch) {
                    const { data } = await SettingAPI.registeredFee.loadDoctorRegistrationFeeByCategories({
                        ...pick1(this.postData, ['doctorId', 'departmentId']),
                    });

                    this.selectDoctorEnableCategories = data.enableCategories;
                    this.selectDoctorRegistrationFees = data.registrationFees;
                }

                // 更改医生后的号种不包含选中的号种，默认选择普通门诊
                if (!this.selectDoctorEnableCategories.includes(this.postData.registrationCategory)) {
                    this.postData.registrationCategory = RegistrationCategory.ORDINARY;
                }

                const doctorFee = this.selectDoctorRegistrationFees.find((item) => item.registrationCategory === this.postData.registrationCategory) || {};

                if (needSetVisitStatus) {
                    if (!this.postData.patient.id) {
                        this.postData.revisitStatus = RevisitStatus.FIRST;
                        this.lastDiagnosedTime = null;
                    } else {
                        const { data: d } = await OutpatientAPI.getPatientVisitStatus({
                            doctorId: this.postData.doctorId,
                            patientId: this.postData.patient.id,
                        });

                        this.postData.revisitStatus = d.revisitStatus;
                        this.postData._revisitStatus = d.revisitStatus;
                        this.lastDiagnosedTime = d.lastDiagnosedTime;
                    }
                }

                const {
                    referralFlag,
                    revisitStatus,
                } = this.postData;

                const {
                    regUnitPrice,
                    revisitedRegUnitPrice,
                    referralRegUnitPrice,
                    referralRevisitedRegUnitPrice,
                    isDiffForRevisited,
                    revisitedFeeCustomUseRule,
                } = doctorFee || {};

                // 是转诊且开启了多号种(目前就医院有多号种)
                const isReferralAndShowRegistrationCategory = referralFlag === 1 && this.isShowRegistrationCategory;

                // 根据医生的模式取费用(只有医院有转诊费)
                // 初诊/首诊
                const firstVisitedPrice = isReferralAndShowRegistrationCategory ? referralRegUnitPrice : regUnitPrice || 0;
                // 复诊/再诊
                const reVisitedPrice = isReferralAndShowRegistrationCategory ? referralRevisitedRegUnitPrice : revisitedRegUnitPrice || 0;
                if (isDiffForRevisited === REGISTERED_FEE_MODE_TYPE.SHORT_DIFFERENT) {
                    if (!this.lastDiagnosedTime) {
                        this.postData.registrationFee = firstVisitedPrice;
                    } else {
                        const { effectiveDays } = revisitedFeeCustomUseRule || {};
                        const reserveTime = this.postData?.reserveDate ? new Date(this.postData.reserveDate) : new Date();
                        const reserveDate = new Date(reserveTime.getFullYear(), reserveTime.getMonth(), reserveTime.getDate());
                        const lastTime = new Date(this.lastDiagnosedTime);
                        const lastDate = new Date(lastTime.getFullYear(), lastTime.getMonth(), lastTime.getDate());
                        const differenceDays = Math.round(Math.abs((reserveDate - lastDate) / (24 * 60 * 60 * 1000)));
                        this.postData.registrationFee = differenceDays + 1 > effectiveDays ? firstVisitedPrice : reVisitedPrice;
                    }
                } else {
                    this.postData.registrationFee = (revisitStatus === RevisitStatus.FIRST) ? firstVisitedPrice : reVisitedPrice || 0;
                }
                this.postData._registrationFee = this.postData.registrationFee;
                // 挂号费变更
                if (oldFee !== '' && oldFee !== this.postData.registrationFee) {
                    this.handleChangeFee();
                }
            },

            updatePatientWxInfo(patientInfo) {
                if (patientInfo) {
                    const {
                        isAttention,
                        wxBindStatus,
                    } = patientInfo;
                    this.postData.patient.isAttention = isAttention;
                    this.postData.patient.wxBindStatus = wxBindStatus;
                }
            },

            trans2MyDepartments(list) {
                if (this.isConsultation) {
                    this.myDepartments = list.filter((item) => item.doctorId === this.postData.doctorId);
                } else {
                    this.myDepartments = list.filter((item) => item.doctorId === this.userInfo?.id);
                }
                const {
                    departmentId,
                    departmentName,
                    doctorId,
                    doctorName,
                    registrationFee,
                } = this.postData;

                if (departmentId && doctorId && this.status === 1) {
                    const doctor = this.myDepartments.find((d) => {
                        return d.departmentId === departmentId && d.doctorId === doctorId;
                    });
                    if (!doctor) {
                        this.myDepartments.unshift({
                            costUnitPrice: registrationFee,
                            departmentId,
                            departmentName,
                            doctorId,
                            doctorName,
                            unitPrice: registrationFee,
                        });
                    }
                }
                this.myDepartments = this.formatDepartment(this.myDepartments);
            },
            trans2OtherDepartments(list) {
                if (this.isConsultation) {
                    this.otherDepartments = this.formatDepartment(list.filter((item) => item.doctorId === this.postData.doctorId));
                } else {
                    this.otherDepartments = this.formatDepartment(list.filter((item) => item.doctorId !== this.userInfo?.id));
                }
                const hasSelectedIndex = this.otherDepartments.findIndex((item) => {
                    return item.departmentId === this._lastSelectedDepatId && item.doctorId === this._lastSelectedDoctorId;
                });
                // 把上次选择的提前
                if (hasSelectedIndex > -1) {
                    this.otherDepartments = this.otherDepartments
                        .splice(hasSelectedIndex, 1)
                        .concat(this.otherDepartments);
                }
                // 根据type筛选科室
                if (this.source === 'childHealth') {
                    this.otherDepartments = this.otherDepartments.filter((item) => { return item.departmentType === DepartmentTypeStatus.CHILD_HEALTH_TYPE; });
                } else {
                    this.otherDepartments = this.otherDepartments.filter((item) => { return item.departmentType === DepartmentTypeStatus.OUT_PATIENT_TYPE; });
                }
            },

            /**
             * @desc 处理科室列表
             * <AUTHOR>
             * @date 2019/04/18 17:13:47
             */
            formatDepartment(list) {
                const listClone = Clone(list);
                return listClone.map((item) => {
                    item.doctorKey = `${item.departmentId}|${item.doctorId}`;
                    // 非待诊，下拉列表匹配项取改名前的名称-针对改名
                    if (this.status > 0 &&
                        item.doctorId === this.postData?.doctorId &&
                        this.postData?.doctorName
                    ) {
                        item.doctorName = this.postData?.doctorName;
                    }
                    if (item.isDefault) {
                        item.doctorStr = item.doctorName;
                    } else {
                        item.doctorStr = item.departmentName ?
                            `${item.doctorName} - ${item.departmentName}` :
                            item.doctorName;
                    }
                    return item;
                });
            },

            updateQueryStr(queryStr) {
                this.filterQueryStr = queryStr;
            },

            /**
             * @desc 挂号费失焦后 为空 =》 0
             * <AUTHOR>
             * @date 2019/04/04 20:19:27
             */
            blurHandler() {
                if (this.postData.registrationFee === '') {
                    this.postData.registrationFee = 0;
                }
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                const inputs = $(this.$el).find('.abc-input__inner').not(':disabled,.is-disabled');
                let targetIndex = -1;
                if (e.target.className === 'select-inner-input') {
                    targetIndex = inputs.length - 2;
                } else {
                    targetIndex = inputs.index(e.target);
                }
                const nextInput = inputs[targetIndex + 1];

                if (nextInput) {
                    this.$nextTick(() => {
                        nextInput.focus();
                    });
                }
            },

            /**
             * @desc 选择患者
             * <AUTHOR>
             * @date 2018/10/18 12:36:10
             * @params patient
             * @param markShebaoCardInfo {boolean} 是否标记社保刷卡挂号
             */
            async changePatient(patient, markShebaoCardInfo) {
                if (!patient) return false;

                // 去除age中的0
                const { age } = patient;
                if (+age.year === 0) {
                    age.year = null;
                }

                if (+age.month === 0) {
                    age.month = null;
                }

                await this.updatePatientInfo(patient);
                this.$emit('change-patient', patient, markShebaoCardInfo);
            },

            /**
             * @desc 更新selectedItem 和 postData中的patient信息
             * <AUTHOR> Yang
             * @date 2020-12-23 15:04:42
             */
            async updatePatientInfo(patient) {
                this.postData.patient = patient;
                this.postData.shebaoCardInfo = patient.shebaoCardInfo;
                await this.$store.dispatch('setSelectedPatient', {
                    type: this.source,
                    patientId: patient.id,
                });

                let pastHistory = '';
                let allergicHistory = '';
                let tags = [];
                let appFlag = 0;
                let arrearsFlag = 0;
                let selectedPatient = null;
                if (this.source === 'childHealth') {
                    selectedPatient = this.childHealth.selectedPatient;
                } else {
                    selectedPatient = this.outpatient.selectedPatient;
                }
                if (selectedPatient) {
                    pastHistory = selectedPatient.pastHistory || '';
                    allergicHistory = selectedPatient.allergicHistory || '';
                    tags = selectedPatient.tags || [];
                    appFlag = selectedPatient.appFlag || 0;
                    arrearsFlag = selectedPatient.arrearsFlag || 0;
                    const {
                        address, idCard, countryCode, idCardType,
                    } = selectedPatient;
                    this.postData.medicalRecord.pastHistory = pastHistory;
                    this.postData.medicalRecord.allergicHistory = allergicHistory;
                    this.postData.patient.appFlag = appFlag;
                    this.postData.patient.arrearsFlag = arrearsFlag;
                    this.postData.patient.tags = tags;
                    this.postData.patient.address = address;
                    this.postData.patient.idCard = idCard;
                    this.postData.patient.countryCode = countryCode;
                    this.postData.patient.shebaoCardInfo = selectedPatient.shebaoCardInfo;
                    this.postData.patient.idCardType = idCardType;
                    if (this.needChangeTags) {
                        this.$emit('update-patient-tags', tags);
                    }
                }


                await this.changeVisitFee();
            },
            changePatientInfo(patientInfo) {
                // 已诊的情况需要刷新门诊单，重新计算就诊时的年龄
                if (this.status) {
                    // 新框架不需要重新计算
                    // this.$emit('refreshOutpatientDetail');
                    // 更新缓存的postData
                    this.$emit('updatePostDataCache',patientInfo);
                } else {
                    Object.assign(this.postData.patient, patientInfo);
                }
                this.$store.dispatch('setSelectedPatient', {
                    type: this.source,
                    patientInfo,
                });
                this.$store.dispatch('updatePatientInfo', {
                    type: this.source,
                    info: patientInfo,
                });
                this.$emit('change-patient-info');
            },
            /**
             * @desc 费用改变
             * <AUTHOR>
             * @date 2022/06/01 16:07:30
             */
            handleChangeFee() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: this.$t('registrationFeeName'),
                    needCalcFee: true,
                });
            },
            /**
             * @Description: 关闭弹窗
             * <AUTHOR> Cai
             * @date 2022/08/10 15:25:34
            */
            handlePopoverClose() {
                this.showPopover = true;
            },
            handlePopoverOpen() {
                this.showPopover = false;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.outpatient-patient-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    font-size: 0;
}

.custom-registration-fee-popover {
    padding: var(--abc-paddingTB-xl) var(--abc-paddingLR-xl);

    .price-wrapper {
        padding: var(--abc-paddingTB-xl) 0 var(--abc-paddingTB-m);

        .price-item {
            & + .price-item {
                margin-top: var(--abc-paddingTB-m);
            }
        }
    }
}
</style>
