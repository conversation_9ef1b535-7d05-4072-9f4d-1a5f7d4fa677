import { validateIdCard } from 'utils/validate';
import {
    maritalOptions, professionOptions,
} from 'views/crm/data/options';
import nation from '@/assets/configure/nation-row.js';

export function formClass (item) {
    if (item?.key === 'addressDetail' && item?.value) return 'address-detail';
    return '';
}

export const formTitle = (item) => {
    return (item?.key === 'sn' && item?.value) || '';
};

export const needFormStyle = (item, isRegistration = false) => {
    if (['remark', 'idCard','address','addressDetail', 'consultantId'].includes(item?.key)) {
        return { width: !isRegistration ? '328px' : '262px' };
    }
    return '';
};


// 表单类型
const FormTypeEnum = Object.freeze({
    FORM_INPUT: 'input', // 输入框
    FORM_DATE: 'dateTime', // 日期
    FORM_ADDRESS: 'address', // 地址
    FORM_SELECT: 'select', // 下拉框
});

// options type
export const OptionsTypeEnum = Object.freeze({
    EMPLOYEE_LIST: 'employeeList',
});

export const formWidth = (item, isRegistration = false) => {
    if (item?.type === FormTypeEnum.FORM_ADDRESS) {
        return !isRegistration ? 328 : 262;
    }
    if ([FormTypeEnum.FORM_SELECT,FormTypeEnum.FORM_DATE].includes(item?.type)) {
        return !isRegistration ? 150 : 134;
    }
    if (['remark','idCard','addressDetail', 'address', 'consultantId'].includes(item?.key)) {
        return !isRegistration ? 328 : 262;
    }
    return !isRegistration ? 150 : 134;
};

export const isShowFormItem = (formItem) => {
    if (formItem.key !== 'addressDetail') return true;
    return !!formItem.value;
};





export const baseCardConfig = [
    {
        key: 'idCard',
        label: '证件',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        width: 369,
        InputType: 'text',
        maxLength: 18,
        require: false,
        validate: validateIdCard,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'birthday',
        label: '生日',
        value: '',
        type: FormTypeEnum.FORM_DATE,
        require: false,
        width: 120,
        clearable: false,
        editable: true,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'sn',
        label: '档案号',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        labelWidth: '26',
        InputType: 'number-en-char',
        maxLength: 18,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'patientSource',
        label: '来源',
        value: {
            id: '',
            name: '',
            sourceFrom: '',
            sourceFromName: '',
        },
        width: 120,
        type: 'cascader',
        clearable: false,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'ethnicity',
        label: '民族',
        value: '',
        type: FormTypeEnum.FORM_SELECT,
        options: nation,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'marital',
        label: '婚姻',
        value: '',
        type: FormTypeEnum.FORM_SELECT,
        options: maritalOptions,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'weight',
        label: '体重',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        labelWidth: '26',
        InputType: 'number',
        maxLength: 128,
        width: 120,
        config: {
            max: 1000,
            formatLength: 2,
        },
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'profession',
        label: '职业',
        value: '',
        type: FormTypeEnum.FORM_SELECT,
        options: professionOptions,
        optionWidth: 99,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'company',
        label: '单位',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        InputType: 'text',
        maxLength: 128,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'wxNickName',
        label: '微信',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        InputType: 'text',
        maxLength: 10,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'scrmInfo', // 企业微信
        label: '企微',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        InputType: 'text',
        maxLength: 10,
        require: false,
        disabled: true,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'parentName',
        label: '家长名',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        InputType: 'text',
        maxLength: 20,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'visitReason', //到店
        label: '到店',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        options: [],
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'created',
        label: '建档',
        value: '',
        type: FormTypeEnum.FORM_DATE,
        width: 120,
        disabled: true,
        require: false,
        clearable: false,
        editable: true,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'shebaoCardInfo',
        label: '医保号',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        InputType: 'text',
        width: 120,
        disabled: true,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'primaryTherapistId',
        label: '首评治疗师',
        value: '',
        type: FormTypeEnum.FORM_SELECT,
        options: [],
        customOptionsType: OptionsTypeEnum.EMPLOYEE_LIST,
        optionWidth: 99,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'dutyTherapistId',
        label: '责任治疗师',
        value: '',
        type: FormTypeEnum.FORM_SELECT,
        options: [],
        customOptionsType: OptionsTypeEnum.EMPLOYEE_LIST,
        optionWidth: 99,
        width: 120,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'remark',
        label: '备注',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        InputType: 'text',
        maxLength: 300,
        width: 369,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'consultantId',
        label: '咨询师',
        value: '',
        type: FormTypeEnum.FORM_SELECT,
        options: [],
        width: 369,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'address',
        label: '地址',
        value: {
            addressCityId: '',
            addressCityName: '',
            addressProvinceId: '',
            addressProvinceName: '',
            addressDistrictId: '',
            addressDistrictName: '',
            addressDetail: '',
        },
        type: FormTypeEnum.FORM_ADDRESS,
        width: 369,
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
    {
        key: 'addressDetail',
        label: '',
        value: '',
        type: FormTypeEnum.FORM_INPUT,
        width: 369,
        InputType: 'text',
        require: false,
        formTitle,
        formClass,
        isShowFormItem,
        formStyle: needFormStyle,
        formWidth,
    },
];








