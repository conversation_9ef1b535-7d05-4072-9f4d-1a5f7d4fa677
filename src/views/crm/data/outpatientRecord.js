import fecha from 'utils/fecha';
import { DATE_TIME_MM_FORMATE } from '@abc/utils-date';

export const outpatientRecord = [
    {
        label: '就诊时间',
        key: 'created',
        style: {
            width: '148px',
            maxWidth: '148px',
        },
        customRender: (h, row) => {
            const {
                diagnosedDate,created,
            } = row;
            return (
                <abc-table-cell>
                    <abc-text theme="primary">{fecha.format((diagnosedDate || created), DATE_TIME_MM_FORMATE) ?? '-'}</abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        label: '科室',
        key: 'departmentName',
        style: {
            width: '120px',
            maxWidth: '120px',
        },
    },
    {
        label: '医生',
        key: 'doctorName',
        style: {
            width: '86px',
            maxWidth: '86px',
        },
    },
    {
        label: '诊断',
        key: 'diagnosis',
        style: {
            width: '140px',
            flex: 1,
        },
    },
    {
        label: '门店',
        key: 'clinicName',
        style: {
            width: '140px',
            flex: 1,
        },
    },
];

export const inpatientRecord = [
    {
        label: '就诊时间',
        key: 'created',
        style: {
            width: '148px',
            maxWidth: '148px',
        },
        customRender: (h, row) => {
            const {
                created,dischargeTime,
            } = row;
            return (
                <abc-table-cell>
                    <abc-text theme="primary">{`${fecha.format(created, DATE_TIME_MM_FORMATE)} ${dischargeTime ? `至 ${fecha.format(dischargeTime, DATE_TIME_MM_FORMATE)}` : '至今'}`}</abc-text>
                </abc-table-cell>
            );
        },
    },
    {
        label: '科室',
        key: 'departmentName',
        style: {
            width: '120px',
            maxWidth: '120px',
        },
    },

    {
        label: '医生',
        key: 'doctorName',
        style: {
            width: '86px',
            maxWidth: '86px',
        },
    },
    {
        label: '诊断',
        key: 'diagnosisAbstractInfo',
        style: {
            width: '140px',
            flex: 1,
        },
    },
    {
        label: '门店',
        key: 'clinicName',
        style: {
            width: '140px',
            flex: 1,
        },
    },
];
