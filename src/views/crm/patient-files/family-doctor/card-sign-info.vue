<template>
    <div class="crm-module__patient-files__family-doctor__card-sign-info">
        <abc-flex class="head" align="center">
            <abc-space align="center">
                <abc-text :bold="true">
                    签约详情
                </abc-text>
                <abc-text v-if="awaitTipsWording" size="small" theme="warning-light">
                    {{ awaitTipsWording }}
                </abc-text>
            </abc-space>
        </abc-flex>
        <div class="content">
            <abc-descriptions
                :column="3"
                :bordered="false"
                style="margin-bottom: 16px;"
                content-padding="0"
                :label-width="80"
            >
                <abc-descriptions-item label="服务包">
                    {{ servicePackNameWording }}
                </abc-descriptions-item>
                <abc-descriptions-item :span="2" label="服务费用">
                    {{ servicePackUnitPriceWoring }}
                </abc-descriptions-item>
                <abc-descriptions-item label="服务时间">
                    <abc-text :theme="isExpired ? 'warning-light' : 'default'">
                        {{ serviceEndTimeWording }}
                    </abc-text>
                </abc-descriptions-item>
                <abc-descriptions-item :span="2" label="签约门店">
                    {{ signClinicNameWording }}
                </abc-descriptions-item>
                <abc-descriptions-item label="家庭医生">
                    {{ signDoctorNameWording }}
                </abc-descriptions-item>
                <abc-descriptions-item :span="2" label="团队成员">
                    {{ doctorTeamWording }}
                </abc-descriptions-item>
                <abc-descriptions-item label="包含权益" :span="3">
                    <span class="text-list">
                        <abc-text v-for="(text, index) in contentTextList.slice(0, 2)" :key="index">
                            {{ `${index + 1}.${text}` }}
                        </abc-text>
                        <abc-text v-if="contentTextList.length > 2">3.......</abc-text>
                    </span>
                </abc-descriptions-item>
            </abc-descriptions>
            <div class="btns-wrapper">
                <abc-space>
                    <template v-if="isSigning">
                        <abc-button
                            variant="ghost"
                            theme="default"
                            :loading="loadingSigning"
                            @click="handleTodoSign"
                        >
                            发起签约
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            theme="default"
                            :loading="loadingCancel"
                            @click="handleCancelSign"
                        >
                            取消
                        </abc-button>
                    </template>
                    <template v-if="isChargeing">
                        <abc-button
                            variant="ghost"
                            theme="default"
                            :loading="loadingEdit"
                            @click="handleEdit"
                        >
                            编辑
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            theme="default"
                            :loading="loadingUnbind"
                            @click="handleUnbindSign"
                        >
                            解约
                        </abc-button>
                    </template>
                    <template v-if="isEffecting">
                        <abc-button
                            variant="ghost"
                            theme="default"
                            :loading="loadingEdit"
                            @click="handleEdit"
                        >
                            编辑
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            theme="default"
                            @click="handleRenewalSign"
                        >
                            续约
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            theme="default"
                            :loading="loadingUnbind"
                            @click="handleUnbindSign"
                        >
                            解约
                        </abc-button>
                    </template>
                    <template v-if="isExpired || isUnbind">
                        <abc-button
                            variant="ghost"
                            theme="default"
                            @click="handleAgainSign"
                        >
                            重新签约
                        </abc-button>
                    </template>
                    <abc-button
                        variant="ghost"
                        theme="default"
                        @click="handlePreviewProtocol"
                    >
                        查看协议
                    </abc-button>
                </abc-space>
            </div>
        </div>
    </div>
</template>

<script>
    import fecha from 'utils/fecha';
    import { formatMoney } from 'src/filters/index';
    import { getServiceTypeWording } from '@/views/crm/common/package-family-doctor/utils';
    import { DATE_FORMATE } from 'assets/configure/constants';
    import { signStateConst } from '@/views/crm/common/package-family-doctor/constants';
    import FamilyDoctor from '@/views/crm/common/package-family-doctor';

    export default {
        name: 'FamilyDoctor',
        props: {
            patientId: {
                type: String,
                required: true,
            },
            familyDoctorInfo: {
                type: Object,
                default: null,
            },
            servicePackInfo: {
                type: Object,
                default: null,
            },
        },
        data() {
            return {
                loadingUnbind: false,
                loadingCancel: false,
                loadingSigning: false,
                loadingEdit: false,
            };
        },
        computed: {
            // 是否签约中 - 等待患者签约
            isSigning() {
                const { state } = this.familyDoctorInfo || {};
                return state === signStateConst.TOBE_SIGN;
            },
            // 是否缴费中 - 等待患者缴费
            isChargeing() {
                const { state } = this.familyDoctorInfo || {};
                return state === signStateConst.DOING_SIGN;
            },
            // 是否生效中 - 有效期内
            isEffecting() {
                const { state } = this.familyDoctorInfo || {};
                return state === signStateConst.SUCCESS_SIGN;
            },
            // 是否已解约 - 解约状态
            isUnbind() {
                const { state } = this.familyDoctorInfo || {};
                return state === signStateConst.UNBIND_SIGN;
            },
            // 是否到期了 - 到期状态
            isExpired() {
                const { state } = this.familyDoctorInfo || {};
                return state === signStateConst.EXPIRED_SIGN || state === signStateConst.EXPIRATION;
            },
            // 等待提示描述
            awaitTipsWording() {
                let tipsWording = '';
                if (this.isSigning) {
                    tipsWording = '等待患者签约';
                } else if (this.isChargeing) {
                    tipsWording = '等待患者缴费';
                } else if (this.isEffecting) {
                    const { overdueDays } = this.familyDoctorInfo || {};
                    if (overdueDays) {
                        tipsWording = `还有${overdueDays}天到期`;
                    }
                } else if (this.isUnbind) {
                    tipsWording = '服务已解约，请重新签约';
                } else if (this.isExpired) {
                    tipsWording = '服务到期，请重新签约';
                } else {
                    //
                }
                return tipsWording;
            },
            // 家庭医生签约id
            familyDoctorId() {
                const { id } = this.familyDoctorInfo || {};
                return id || '';
            },
            // 服务包id
            servicePackId() {
                const { id } = this.servicePackInfo || {};
                return id || '';
            },
            // 服务包
            servicePackNameWording() {
                const { servicePackName } = this.servicePackInfo || {};
                return servicePackName || '-';
            },
            // 服务费用
            servicePackUnitPriceWoring() {
                const {
                    unitPrice, serviceType,
                } = this.servicePackInfo || {};
                return `${this.$t('currencySymbol')} ${formatMoney(unitPrice || 0)}/${getServiceTypeWording(serviceType, '年')}`;
            },
            // 服务时间
            serviceEndTimeWording() {
                const {
                    beginTime, endTime,
                } = this.familyDoctorInfo || {};
                if (beginTime && endTime) {
                    return `${fecha.format(beginTime, DATE_FORMATE)}~${fecha.format(endTime, DATE_FORMATE)}`;
                } if (beginTime) {
                    return fecha.format(beginTime, DATE_FORMATE);
                } if (endTime) {
                    return fecha.format(endTime, DATE_FORMATE);
                }
                return '-';
            },
            // 签约门店
            signClinicNameWording() {
                const { clinicName } = this.familyDoctorInfo || {};
                return clinicName || '-';
            },
            // 家庭医生
            signDoctorNameWording() {
                const { doctorName } = this.familyDoctorInfo || {};
                return doctorName || '-';
            },
            // 团队成员
            doctorTeamWording() {
                const { doctorTeam } = this.familyDoctorInfo || {};
                return doctorTeam && doctorTeam.length !== 0 ?
                    doctorTeam.map((item) => item.doctorName).join('、') :
                    '-';
            },
            // 权益内容
            contentTextList() {
                const { servicePackDetails } = this.servicePackInfo || {};
                return servicePackDetails || [];
            },
        },
        methods: {
            /**
             * 发起签约
             * <AUTHOR>
             * @date 2021-05-25
             */
            async handleTodoSign() {
                this.loadingSigning = true;

                const props = {
                    familyDoctorId: this.familyDoctorId,//签约id
                };
                const event = {
                    success: () => {
                        this.$emit('sign-success');
                    },
                };
                await FamilyDoctor.handleSigningPanel(props, event);

                this.loadingSigning = false;
            },
            /**
             * 除了编辑
             * <AUTHOR>
             * @date 2021-06-02
             */
            async handleEdit() {
                this.loadingEdit = true;

                const props = {
                    patientId: this.patientId,//签约患者id
                    isEditSign: true,//是否编辑
                    familyDoctorInfo: this.familyDoctorInfo,//需要编辑的家庭医生服务信息
                };
                const event = {
                    success: () => {
                        this.$emit('edit-success');
                    },
                };
                await FamilyDoctor.handleSignIn(props, event);

                this.loadingEdit = false;
            },
            /**
             * 取消
             * <AUTHOR>
             * @date 2021-05-25
             */
            async handleCancelSign() {
                if (this.loadingCancel || !this.familyDoctorId) {
                    return;
                }
                this.loadingCancel = true;
                const res = await FamilyDoctor.handleCancel(this.familyDoctorId);
                res && this.$emit('cancel-success');
                this.loadingCancel = false;
            },
            /**
             * 解约
             * <AUTHOR>
             * @date 2021-05-25
             */
            async handleUnbindSign() {
                if (this.loadingUnbind || !this.familyDoctorId) {
                    return;
                }
                this.loadingUnbind = true;
                const res = await FamilyDoctor.handleUnbind(this.familyDoctorId);
                res && this.$emit('unbind-success');
                this.loadingUnbind = false;
            },
            /**
             * 续约
             * <AUTHOR>
             * @date 2021-05-25
             */
            async handleRenewalSign() {
                const props = {
                    patientId: this.patientId,//签约患者id
                    isRenewalSign: true,//是否续签
                    defaultServicePackId: this.servicePackId,//默认选中服务包
                };
                const event = {
                    success: () => {
                        this.$emit('sign-success');
                    },
                };
                FamilyDoctor.handleSignIn(props, event);
            },
            /**
             * 重新签约
             * <AUTHOR>
             * @date 2021-05-25
             */
            async handleAgainSign() {
                const props = {
                    patientId: this.patientId,//签约患者id
                    defaultServicePackId: this.servicePackId,//默认选中服务包
                };
                const event = {
                    success: () => {
                        this.$emit('sign-success');
                    },
                };
                FamilyDoctor.handleSignIn(props, event);
            },
            /**
             * 查看协议
             * <AUTHOR>
             * @date 2021-05-25
             */
            handlePreviewProtocol() {
                if (this.servicePackInfo && this.familyDoctorInfo) {
                    FamilyDoctor.handlePreviewProtocolByInfo(this.servicePackInfo, this.familyDoctorInfo);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .crm-module__patient-files__family-doctor__card-sign-info {
        background-color: #ffffff;
        border: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        > .head {
            height: 48px;
            padding: 0 16px;
            border-bottom: 1px solid $P6;

            .title {
                font-weight: 500;
                color: $T1;
            }

            .await-pay {
                margin-left: 20px;
                color: $Y2;
            }
        }

        > .content {
            padding: 16px;

            .info-wrapper {
                flex-wrap: wrap;
                width: 100%;

                @include flex(row, flex-start, center);

                .item-info {
                    margin-bottom: 16px;
                    font-size: 14px;
                    line-height: 16px;
                    color: $T1;

                    @include flex(row, flex-start, flex-start);

                    > label {
                        display: inline-block;
                        flex-shrink: 0;
                        width: 72px;
                        color: $T2;
                    }

                    > span {
                        flex: 1;

                        &.expired-tips {
                            color: $Y2;
                        }

                        &.text-list {
                            line-height: 20px;

                            @include flex(column, flex-start, stretch);

                            > span:first-child {
                                margin-bottom: 2px;
                                line-height: 16px;
                            }
                        }
                    }
                }

                .percentage-36 {
                    width: 36%;
                }

                .percentage-64 {
                    width: 64%;
                }

                .percentage-100 {
                    width: 100%;
                }
            }

            .btns-wrapper {
                @include flex(row, flex-start, center);

                padding-top: 16px;
                border-top: 1px dashed $P6;
            }
        }
    }
</style>
