<template>
    <div v-abc-loading="isLoading" class="crm-module__package-card__base-card">
        <div class="btn">
            <template v-if="editor">
                <abc-button
                    size="small"
                    :disabled="noChangeData"
                    :loading="saveLoading"
                    @click="onClickSave"
                >
                    保存
                </abc-button>
                <abc-button size="small" type="blank" @click="onClickCancel">
                    取消
                </abc-button>
            </template>
            <template v-else>
                <abc-button size="small" @click="onClickEdit">
                    编辑资料
                </abc-button>
            </template>
        </div>
        <div class="top padd-box">
            <span class="name">{{ transmit ? postData.name || '新患者' : patientInfo.name }}</span>
            <img v-if="!transmit && patientInfo.memberFlag === 1" src="~assets/images/icon-yellow.png" alt="vip" />
            <span class="sex">{{ transmit ? postData.sex : patientInfo.sex }}</span>
            <span
                class="age"
            >{{
                transmit ? formatAge(postData.age, {
                    monthYear: 150, dayYear: 1
                }) : formatAge(patientInfo.age, {
                    monthYear: 150, dayYear: 1
                })
            }}</span>
        </div>
        <div class="tow padd-box">
            <span>
                {{
                    patientInfo.lastOutpatientDate ?
                        `最近就诊：${fecha.format(patientInfo.lastOutpatientDate, 'YYYY-MM-DD')}` :
                        `新患者 首次就诊`
                }}
            </span>
            <span class="last-span">
                门诊{{ patientBaseInfo.outpatientCount }}次， 付费{{ patientBaseInfo.payCount }}次， 累计消费
                <abc-money :value="patientBaseInfo.cumulativeAmount"></abc-money>
            </span>
        </div>
        <abc-tag-group class="thr clearfix">
            <div
                v-for="tag in showTags"
                :key="tag.tagId"
            >
                <tag-item
                    :tag="tag"
                    @click-close-label="clickCloseLabel"
                ></tag-item>
            </div>
            <abc-popover
                ref="elpop"
                placement="bottom-start"
                trigger="manual"
                :value="showPopover"
                :visible-arrow="false"
                popper-class="add-label-pop"
            >
                <abc-tags-add slot="reference" @click.native="showPopover = !showPopover"></abc-tags-add>
                <div v-abc-click-outside="outside" class="box">
                    <view-labels
                        :show-manage="false"
                        :selected-ids="selectedIds"
                        :filter="false"
                        @change="clickHandleTag"
                    ></view-labels>
                </div>
            </abc-popover>
        </abc-tag-group>
        <div class="padd-box patient-box">
            <abc-form
                v-if="editor"
                ref="postData"
                class="editor-box"
                :label-width="70"
            >
                <div class="item">
                    <div class="text">
                        <abc-form-item
                            label="姓名"
                            :required="patientInfoRequireConfig.name.required"
                        >
                            <abc-space>
                                <abc-input
                                    v-model.trim="postData.name"
                                    :width="198"
                                    :max-length="20"
                                    trim
                                    type="text"
                                    :disabled="!isCanModifyNameInCrm"
                                >
                                </abc-input>
                                <abc-button
                                    v-if="isEnableReadCard"
                                    type="ghost"
                                    icon="read-card"
                                    style="color: #000000;"
                                    @click="readIdCardHandle"
                                ></abc-button>
                            </abc-space>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item
                            label="性别"
                            class="sex-radio-group"
                            :required="patientInfoRequireConfig.sex.required"
                            style="width: 100%;"
                        >
                            <abc-radio-group v-model="postData.sex">
                                <abc-radio label="男">
                                    男
                                </abc-radio>
                                <abc-radio label="女">
                                    女
                                </abc-radio>
                            </abc-radio-group>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item
                            label="手机"
                            :required="patientInfoRequireConfig.mobile.required"
                            :validate-event="handleValidateMobile"
                        >
                            <input-mobile-encrypt
                                v-model="postData.mobile"
                                :country-code.sync="postData.countryCode"
                                :enable-encrypt="!isCanSeePatientMobile"
                                :width="238"
                            >
                            </input-mobile-encrypt>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text patient-age-info">
                        <!--没有月和天，就需要年-->
                        <abc-form-item
                            label="年龄"
                            show-red-dot
                            :required="patientInfoRequireConfig.age.required && !postData.age.month && !postData.age.day"
                            class="patient-age-year"
                        >
                            <abc-input
                                v-model.number="postData.age.year"
                                v-abc-focus-selected
                                :width="45"
                                :input-custom-style="{
                                    padding: '3px 6px', 'text-align': 'center'
                                }"
                                type="number"
                                :config="{
                                    supportZero: true, max: 199
                                }"
                                @enter="enterEvent"
                                @change="changeAge"
                            >
                                <span slot="append">岁</span>
                            </abc-input>
                        </abc-form-item>
                        <!--没有年和天，就需要月-->
                        <abc-form-item
                            class="patient-age-month"
                            style="margin-top: 0;"
                            :required="patientInfoRequireConfig.age.required && !postData.age.day && !postData.age.year"
                            :validate-event="validateMonth"
                        >
                            <abc-input
                                v-model.number="postData.age.month"
                                v-abc-focus-selected
                                :width="45"
                                :input-custom-style="{ 'text-align': 'center' }"
                                type="number"
                                :config="{ supportZero: true }"
                                @enter="enterEvent"
                                @change="changeAge"
                            >
                                <span slot="append">月</span>
                            </abc-input>
                        </abc-form-item>
                        <!--没有月和年，就需要天-->
                        <abc-form-item
                            class="patient-age-day"
                            style="margin-top: 0;"
                            :required="patientInfoRequireConfig.age.required && !postData.age.month && !postData.age.year"
                            :validate-event="validateDay"
                        >
                            <abc-input
                                v-model.number="postData.age.day"
                                v-abc-focus-selected
                                :width="45"
                                :input-custom-style="{ 'text-align': 'center' }"
                                type="number"
                                :config="{ supportZero: true }"
                                @enter="enterEvent"
                                @change="changeAge"
                            >
                                <span slot="append">天</span>
                            </abc-input>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <abc-form-item
                        v-abc-focus-selected
                        label="生日"
                        :required="patientInfoRequireConfig.birthday.required"
                    >
                        <birthday-picker
                            v-model="postData.birthday"
                            :width="238"
                            @change="changeBirthday"
                        >
                        </birthday-picker>
                    </abc-form-item>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item
                            label="证件"
                            :required="patientInfoRequireConfig.certificates.required"
                            :validate-event="_validateIdCard"
                        >
                            <abc-certificates-type
                                ref="crm-id-card"
                                v-model.trim="postData.idCard"
                                :cert-type.sync="postData.idCardType"
                                :width="238"
                                size="small"
                                :disabled="!isCanModifyIdCardInCrm"
                                :is-disabled-cert-type="!isCanModifyIdCardInCrm"
                                @input-change="handleIdCardInput"
                                @select-change="handleIdCardSelect"
                            ></abc-certificates-type>
                        </abc-form-item>
                    </div>
                </div>
                <div v-if="isRecommendAndSourceDisplay" class="item">
                    <div class="text source-item">
                        <abc-form-item label="首诊来源" :required="patientInfoRequireConfig.sourceInfo.required">
                            <abc-cascader
                                ref="visit-source-cascader"
                                v-model="cascaderValue"
                                :props="{
                                    children: 'children',
                                    label: 'name',
                                    value: 'id'
                                }"
                                placeholder="不指定"
                                separation="-"
                                :width="238"
                                :options="sourceList"
                                @enter="enterEvent"
                            >
                                <div class="visit-source-edit-wrapper">
                                    <abc-icon
                                        v-if="isClinicAdmin"
                                        icon="set"
                                        size="14"
                                        color="#94979B"
                                        class="icon"
                                        @click="handleVisitSourceEdit"
                                    ></abc-icon>

                                    <abc-popover
                                        v-else
                                        trigger="hover"
                                        placement="top-start"
                                        :popper-style="{
                                            zIndex: 99999
                                        }"
                                        theme="yellow"
                                    >
                                        <abc-icon
                                            slot="reference"
                                            icon="set"
                                            size="14"
                                            color="#94979B"
                                        ></abc-icon>

                                        <span>修改本次推荐请联系管理员</span>
                                    </abc-popover>
                                </div>
                            </abc-cascader>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="婚否" :required="patientInfoRequireConfig.marital.required">
                            <abc-select
                                v-model="postData.marital"
                                :width="238"
                                placeholder="请选择患者婚姻状况"
                                custom-class="profession-options"
                            >
                                <abc-option
                                    v-for="item in maritalOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="体重" :required="patientInfoRequireConfig.weight.required">
                            <abc-input
                                v-model="postData.weight"
                                :width="212"
                                type="number"
                                :config="{
                                    max: 1000, formatLength: 2,
                                }"
                            >
                                <span slot="append" style="display: inline-block; width: 16px;">kg</span>
                            </abc-input>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item" style="height: 81px;">
                    <div>
                        <div class="text address-box">
                            <abc-form-item label="住址" :required="patientInfoRequireConfig.address.required">
                                <abc-address-selector v-model="postData.address" :width="238"></abc-address-selector>
                            </abc-form-item>
                        </div>
                        <div class="text" style="margin-left: 70px;">
                            <abc-form-item class="no-label" :required="patientInfoRequireConfig.address.required">
                                <abc-input
                                    v-model.trim="postData.addressDetail"
                                    placeholder="详细地址"
                                    :width="238"
                                    trim
                                    type="text"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="职业" :required="patientInfoRequireConfig.profession.required">
                            <abc-select
                                v-model="postData.profession"
                                :width="238"
                                placeholder="请选择患者职业"
                                custom-class="profession-options"
                            >
                                <abc-option
                                    v-for="item in professionOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="工作单位" :required="patientInfoRequireConfig.company.required">
                            <abc-input
                                v-model="postData.company"
                                :width="238"
                                :max-length="128"
                            ></abc-input>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="档案号" :required="patientInfoRequireConfig.sn.required">
                            <abc-input
                                v-model="postData.sn"
                                :width="238"
                                :max-length="16"
                                type="number-en-char"
                                :ignore-composing="true"
                                :disabled="!isCanModifySnInCrm"
                            ></abc-input>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="民族" :required="patientInfoRequireConfig.ethnicity.required">
                            <abc-select
                                v-model="postData.ethnicity"
                                :width="238"
                                placeholder="请选择民族"
                                custom-class="ethnicity-options"
                            >
                                <abc-option
                                    v-for="(item, index) in nationOptions"
                                    :key="index"
                                    :value="item"
                                    :label="item"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="备注" :required="patientInfoRequireConfig.remark.required">
                            <abc-input v-model="postData.remark" :width="238" :max-length="300"></abc-input>
                        </abc-form-item>
                    </div>
                </div>
                <div class="item">
                    <div class="text">
                        <abc-form-item label="到店原因" :required="patientInfoRequireConfig.visitReason.required">
                            <abc-input v-model="postData.visitReason" :width="238" :max-length="300"></abc-input>
                        </abc-form-item>
                    </div>
                </div>
            </abc-form>
            <template v-else>
                <div class="item">
                    <label>生日</label>
                    <div class="text">
                        {{ patientInfo.birthday || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>手机</label>
                    <div class="text">
                        {{ patientInfoMobile || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label :title="`${patientInfo.idCardType || '身份证'}`">{{ patientInfo.idCardType?.slice(0, 4) || '身份证' }}</label>
                    <div class="text">
                        {{ patientInfo.idCard || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>医保号</label>
                    <patient-social
                        v-if="!!patientInfo.shebaoCardInfo"
                        v-model="visibleSocialInfo"
                        :shebao-card-info="patientInfo.shebaoCardInfo"
                    >
                        <abc-button type="text" @click="visibleSocialInfo = !visibleSocialInfo">
                            {{
                                patientInfo.shebaoCardInfo.cardNo
                            }}
                        </abc-button>
                    </patient-social>
                    <div v-else class="text">
                        -
                    </div>
                </div>
                <div v-if="isRecommendAndSourceDisplay" class="item">
                    <label>首诊来源</label>
                    <div class="text">
                        {{ showSourceName || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>婚否</label>
                    <div class="text">
                        {{ getMarital(patientInfo.marital) || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>体重</label>
                    <div class="text">
                        {{ patientWeight }}
                    </div>
                </div>
                <div class="item address">
                    <label>住址</label>
                    <div
                        v-if="
                            !patientInfo.address ||
                                (getAddress(patientInfo.address) === '-' && !patientInfo.address.addressDetail)
                        "
                    >
                        -
                    </div>
                    <div v-else>
                        <div v-if="getAddress(patientInfo.address) !== '-'" class="text">
                            {{ getAddress(patientInfo.address) }}
                        </div>
                        <div v-if="patientInfo.address.addressDetail" class="text">
                            {{ patientInfo.address.addressDetail }}
                        </div>
                    </div>
                </div>
                <div v-if="isOpenMp" class="item">
                    <label>微信</label>
                    <div class="text">
                        {{ patientInfo.wxNickName || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>职业</label>
                    <div class="text">
                        {{ patientInfo.profession || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>工作单位</label>
                    <div class="text">
                        {{ patientInfo.company || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>档案号</label>
                    <div class="text">
                        {{ patientInfo.sn || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>民族</label>
                    <div class="text">
                        {{ patientInfo.ethnicity || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>备注</label>
                    <div class="text">
                        {{ patientInfo.remark || '-' }}
                    </div>
                </div>
                <div class="item">
                    <label>到店原因</label>
                    <div class="text">
                        {{ patientInfo.visitReason || '-' }}
                    </div>
                </div>
                <template v-if="isEnableChildHealth === 1">
                    <div class="health-files">
                        <template v-for="item in bindHealthFiles">
                            <div v-if="item.type === 'child'" :key="item.title" class="child-health">
                                <div class="title-box" @click="onClickFilesDetail(item)">
                                    <span class="tit">{{ item.title }}</span>
                                    <span class="iconfont cis-icon-Arrow_Rgiht"></span>
                                </div>
                                <div v-for="text in item.contents" :key="text" class="cont">
                                    {{ text }}
                                </div>
                            </div>
                        </template>
                    </div>
                    <abc-check-access>
                        <abc-button type="text" @click="$emit('bind-files')">
                            添加健康档案
                        </abc-button>
                    </abc-check-access>
                </template>
            </template>
        </div>

        <card-info-dialog
            v-model="showCardInfo"
            title="患者信息"
            :card-info="cardInfo"
            @confirm="updatePatientByCard"
        ></card-info-dialog>
    </div>
</template>

<script>
    import CrmAPI from 'api/crm';
    import fecha from 'utils/fecha';
    import {
        formatAge, formatMoney,
    } from 'utils/index';
    import ViewLabels from 'views/crm/common/package-label/view-labels.vue';
    import {
        validateMobile,
    } from 'utils/validate';
    import {
        age2birthday, birthday2age,
    } from 'utils/index';
    import {
        professionOptions, maritalOptions,
    } from '@/views/crm/data/options';
    import {
        mapGetters,
        mapActions,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';
    import BirthdayPicker from 'views/layout/birthday-picker/birthday-picker';
    import PatientSocial from '../package-social/index.vue';
    import { RecommendService } from '@/service/recommend';
    import MixinModulePermission from 'views/permission/module-permission';
    import { MaritalStatusEnum } from 'views/crm/constants';
    import CardInfoDialog from 'views/layout/read-card/card-info-dialog.vue';
    import AbcTagsAdd from 'views/crm/component/abc-tags-add';
    import TagItem from 'views/crm/patient-files/card-patient-overview/tag-item';
    import IdCardReaderService from 'views/layout/read-card/id-card-reader/id-card-reader-service';
    import { encryptMobile } from 'utils/crm';
    import InputMobileEncrypt from 'views/crm/common/package-info/input-mobile-encrypt.vue';
    import { defaultCountryCode } from 'utils/country-codes';
    import nation from '@/assets/configure/nation-row.js';

    export default {
        components: {
            ViewLabels,
            BirthdayPicker,
            PatientSocial,
            CardInfoDialog,
            AbcTagsAdd,
            TagItem,
            InputMobileEncrypt,
        },
        mixins: [
            MixinModulePermission,
        ],
        props: {
            idCardLinkageSwitch: {
                type: Number,
                default: 0,
            },//是否开启身份证和生日年龄联动 1开启 0不开启
            // 拉取信息中
            loadingInfo: Boolean,
            // 患者信息
            patientInfo: {
                type: Object,
                required: true,
            },
            // 是否暂时存储数据
            transmit: {
                type: Boolean,
                required: true,
            },
            // 来源数据
            sourceList: {
                type: Array,
                required: true,
            },
            // 已选标签
            showTags: {
                type: Array,
                required: true,
            },
            // 被选中的标签id
            selectedIds: {
                type: Array,
                required: true,
            },
            // 是否有修改标签数据
            changeTags: {
                type: Boolean,
                default: false,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                fecha,
                formatAge,
                formatMoney,
                professionOptions,
                maritalOptions,
                showPopover: false,
                editor: false, // 是否编辑状态
                postData: {},
                saveLoading: false,
                baseInfoLoading: false,
                visibleSocialInfo: false,
                patientBaseInfo: {
                    outpatientCount: '',
                    payCount: '',
                    cumulativeAmount: '',
                },

                cascaderValue: [],
                isCorrectIdCard: false, // 身份证是否校验成功
                saveError: false,
                huashiService: {},
                cardInfo: {},
                showCardInfo: false,
                nationOptions: nation,
            };
        },
        computed: {
            ...mapGetters([
                'isOpenMp',
                'isEnableChildHealth',
                'currentClinic',
                'isCanModifyNameInCrm',
                'isCanModifyIdCardInCrm',
                'isCanModifySnInCrm',
                'clinicBasic',
            ]),
            ...mapGetters('crm', ['crmConfigList']),
            // 是否展示推荐和来源
            isRecommendAndSourceDisplay() {
                return this.clinicBasic.isRecommendAndSourceDisplay;
            },
            patientInfoMobile() {
                const { mobile = '' } = this.patientInfo;
                return this.isCanSeePatientMobile ? mobile : encryptMobile(mobile);
            },
            // 是否有修改信息
            noChangeData() {
                if (this.transmit && this.changeTags) {
                    // 在透传信息时，修改tag也要保存
                    return false;
                }
                return isEqual(this.postData, this.getPostData());
            },
            // 首诊来源展示
            showSourceName() {
                if (!this.patientInfo.patientSource) return '-';

                return this.initCascaderData().map((o) => o.label).join('-');
            },
            // 已经绑定的健康档案
            bindHealthFiles() {
                const healthFiles = [];
                const {
                    childCareArchives,
                    childCareRecords,
                } = this.patientInfo || {};
                if (childCareArchives === 1) {
                    // 已关联儿童健康档案
                    let growthCont = '无';
                    if (childCareRecords && childCareRecords.bodyGrowthData) {
                        // 生长记录
                        const {
                            bmi,
                            headSize,
                            height,
                            isBregmaClose,
                            lowerBodyLength,
                            teeth,
                            upperBodyLength,
                            weight,
                        } = childCareRecords.bodyGrowthData;
                        const strs = [];
                        height && strs.push(`身高${height}cm`);
                        weight && strs.push(`体重${weight}kg`);
                        bmi && strs.push(`BMI ${bmi}`);
                        headSize && strs.push(`头围${headSize}cm`);
                        upperBodyLength && strs.push(`上部量${upperBodyLength}cm`);
                        lowerBodyLength && strs.push(`下部量${lowerBodyLength}cm`);
                        isBregmaClose && strs.push(`前囟${isBregmaClose === 1 ? '开启' : '闭合'}`);
                        teeth && strs.push(`牙齿${teeth}颗`);

                        growthCont = strs.join('/');
                    }
                    let evaluationCont = '发育评测：无';
                    if (childCareRecords && childCareRecords.evaluationData) {
                        const {
                            evaluationName,
                            result,
                        } = childCareRecords.evaluationData;
                        const scores = result.map(
                            (item) => `${parseFloat(item.score)}分/${parseFloat(item.totalScore)}分`,
                        );
                        evaluationCont = `${evaluationName}：${scores.join(' ')}`;
                    }
                    const item = {
                        type: 'child',
                        title: '儿童健康档案',
                        contents: [`生长记录：${growthCont}`, evaluationCont],
                    };
                    healthFiles.push(item);
                }
                return healthFiles;
            },
            patientWeight() {
                return this.patientInfo.weight ? `${this.patientInfo.weight} kg` : '-';
            },

            isLoading() {
                return this.loadingInfo || this.baseInfoLoading;
            },

            isEnableReadCard() {
                return IdCardReaderService.isEnable();
            },
            patientInfoRequireConfig() {
                const model = {
                    name: {
                        required: true,
                    },
                    sex: {
                        required: true,
                    },
                    age: {
                        required: true,
                    },
                    mobile: {
                        required: true,
                    },
                    birthday: {
                        required: false,
                    },
                    certificates: {
                        required: false,
                    },
                    sourceInfo: {
                        required: false,
                    },
                    marital: {
                        required: false,
                    },
                    weight: {
                        required: false,
                    },
                    profession: {
                        required: false,
                    },
                    company: {
                        required: false,
                    },
                    sn: {
                        required: false,
                    },
                    ethnicity: {
                        required: false,
                    },
                    address: {
                        required: false,
                    },
                    remark: {
                        required: false,
                    },
                    visitReason: {
                        required: false,
                    },
                    pastHistory: {
                        required: false,
                    },
                };

                if (this.crmConfigList && Object.keys(this.crmConfigList).length) {
                    Object.keys(model).forEach((key) => {
                        if (this.crmConfigList[key]) {
                            model[key].required = !!this.crmConfigList[key].required;
                        }
                    });
                }

                return model;
            },
        },
        watch: {
            /**
             * desc [改变view-labels的位置]
             * 产品暂时希望不要动（天翔）
             */
            // selectedIds(newVal, oldVal) {
            //     let elpop = this.$refs.elpop
            //     this.$nextTick(() => {
            //         elpop.updatePopper && elpop.updatePopper()
            //     })
            // }
            cascaderValue(val) {
                if (val.length) {
                    if (val.length > 2) {
                        this.postData.sourceId = val[val.length - 2].value;
                        this.postData.sourceFrom = val[val.length - 1] ? val[val.length - 1].value : null;
                    } else if (['顾客推荐', '员工推荐', '医生推荐','转诊医生'].includes(val[0].label)) {
                        this.postData.sourceId = val[0].value;
                        this.postData.sourceFrom = val[1] ? val[1].value : null;
                    } else {
                        this.postData.sourceId = val[val.length - 1].value;
                        this.postData.sourceFrom = null;

                    }
                } else {
                    this.postData.sourceId = null;
                    this.postData.sourceFrom = null;
                }
            },
        },
        async created() {
            if (this.transmit) {
                this.onClickEdit();
            }

            await Promise.all([
                this.fetchPatientBaseInfo(),
                this.fetchCrmConfigList(),
            ]);
        },
        updated() {
            this.$emit('update-popover');
        },
        beforeDestroy() {
            clearTimeout(this._timer);
        },
        methods: {
            ...mapActions('crm', ['fetchCrmConfigList']),
            validateMobile,
            /**
             * 验证年龄月份
             * <AUTHOR>
             * @date 2020-06-29
             * @param {String} value
             * @param {Function} callback
             */
            handleValidateMobile(value,callback) {
                const [countryCode = '', mobile = ''] = value;
                if (!countryCode) {
                    return callback({
                        validate: false,
                        message: '无法确认手机号所在地区，请联系ABC客服！',
                    });
                }

                // 若是加密手机号则使用对应的明文进行校验
                const mobileStr = this.isCanSeePatientMobile ? mobile : `${this.postData.mobile || ''}` ;
                this.validateMobile(mobileStr,callback, this.postData.countryCode);
            },

            handleVisitSourceEdit() {
                this.$refs['visit-source-cascader'].outside();

                this. $emit('open-visit-source');
            },

            async fetchPatientBaseInfo() {
                try {
                    this.baseInfoLoading = true;
                    const { data: res } = await CrmAPI.fetchPatientBaseInfo(this.patientInfo.id);
                    const {
                        outpatientCount,
                        cumulativeAmount,
                        payCount,
                    } = res && res.rows[0] || {};
                    this.patientBaseInfo.outpatientCount = outpatientCount;
                    this.patientBaseInfo.payCount = payCount;
                    this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                    console.log(this.patientBaseInfo);
                    this.baseInfoLoading = false;
                } catch (e) {
                    console.log(e);
                }
            },
            handleIdCardInput(val) {
                if (![15,18].includes(val?.length)) return;
                if (val && this.editor) {
                    this.correctIdCard(val);
                    this.$nextTick(() => {
                        if (this.isCorrectIdCard && this.postData.idCardType === '身份证') {
                            this.handleBirthday(val,val?.length);
                            this.handleSex(val);
                        }
                    });
                }
            },
            handleIdCardSelect() {
                this.handleIdCardInput(this.postData.idCard);
            },

            // 校验身份证是否正确
            correctIdCard(idCard) {
                const certType = this.postData.idCardType;
                const certNo = idCard;
                const values = [certType, certNo];
                this._validateIdCard(values,(res) => {
                    this.isCorrectIdCard = res.validate;
                });
            },
            _validateIdCard(values, callback) {
                const [certType = '', certNo = ''] = values;
                if (!this.patientInfoRequireConfig.certificates.required && !certNo) {
                    return callback({ validate: true });
                }
                if (!certNo) {
                    return callback({ validate: false });
                }
                return this.$refs?.['crm-id-card']?.validateCertNo(certType, certNo, callback);
            },
            handleBirthday(data,len) {
                const idBirthday = this.getIdBirthday(data, len);
                this.postData.birthday = idBirthday;
                this.changeBirthday(idBirthday);
            },
            handleSex(val) {
                let genderCode;
                if (val.length === 15) {
                    genderCode = val.charAt(val.length - 1);
                } else {
                    genderCode = val.charAt(val.length - 2);
                }
                this.postData.sex = genderCode % 2 === 0 ? '女' : '男';
            },
            getIdBirthday(data, len) {
                let arr;
                //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
                if (len === 15) {
                    const reg15 = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
                    arr = data.match(reg15);
                } else {
                    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
                    const reg18 = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
                    arr = data.match(reg18);
                }
                const year = arr[2];
                const month = arr[3];
                const day = arr[4];
                const idBirthday = len === 15 ? `${`19${year}-${month}-${day}`}` : `${`${year}-${month}-${day}`}`;
                return idBirthday;
            },
            validateMonth(value, callback) {
                if (!this.postData.age.month) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-1])$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            /**
             * 验证年龄天数
             * <AUTHOR>
             * @date 2020-06-29
             * @param {String} value
             * @param {Function} callback
             */
            validateDay(value, callback) {
                if (!this.postData.age.day) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-9]|2[0-9]|30)$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },

            initCascaderData() {
                if (!this.patientInfo.patientSource) return [];

                const {
                    id, sourceFromName, sourceFrom, name,
                } = this.patientInfo.patientSource;

                return RecommendService.getInstance().initCascaderValue({
                    visitSourceId: id,
                    visitSourceName: name,
                    visitSourceFrom: sourceFrom,
                    visitSourceFromName: sourceFromName,
                });
            },
            /**
             * 当点击编辑患者资料时
             * <AUTHOR>
             * @date 2020-06-29
             */
            onClickEdit() {
                this.postData = this.getPostData();

                this.cascaderValue = this.initCascaderData();
                this.editor = true;
            },
            /**
             * 当点击取消编辑
             * <AUTHOR>
             * @date 2020-06-29
             */
            onClickCancel() {
                this.postData = null;
                this.editor = false;
                if (this.transmit) {
                    this.$emit('close');
                }
                this.saveError = false;
            },
            /**
             * 选择标签时，点击外侧
             * <AUTHOR>
             * @date 2020-06-29
             */
            outside() {
                if (this.showPopover) {
                    this.showPopover = false;
                }
            },
            /**
             * 填写年龄后，计算生日
             * <AUTHOR>
             * @date 2020-06-29
             */
            changeAge() {
                this.postData.birthday = age2birthday(this.postData.age);
            },
            /**
             * 选择生日时间后，需要计算年龄
             * <AUTHOR>
             * @date 2020-06-29
             * @param {String} birthday 生日 1990-01-01
             */
            changeBirthday(birthday) {
                if (birthday) {
                    const {
                        year,
                        month,
                        day,
                    } = birthday2age(birthday);
                    this.postData.age.year = year || '';
                    this.postData.age.month = month || '';
                    if (!year && !month) {
                        this.postData.age.day = day || 1;
                    } else {
                        this.postData.age.day = day || '';
                    }
                } else {
                    // 清空操作
                    this.postData.age.year = '';
                    this.postData.age.month = '';
                    this.postData.age.day = '';
                }
            },
            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                this.$nextTick(() => {
                    let targetIndex = -1;
                    const inputs = $('.editor-box .abc-input__inner').not(':disabled,.is-disabled');
                    for (let i = 0; i < inputs.length; i++) {
                        if ($(inputs[i]).hasClass('tagname')) {
                            targetIndex = i;
                        }
                    }
                    if (inputs.index(e.target) > -1) {
                        targetIndex = inputs.index(e.target);
                    }
                    const nextInput = inputs[targetIndex + 1];
                    nextInput &&
                        this.$nextTick(() => {
                            nextInput.focus();
                        });
                });
            },
            /**
             * 取消标签
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} tag 点击取消的标签
             */
            clickCloseLabel(tag) {
                this.clickHandleTag({
                    id: tag.tagId,
                    name: tag.tagName,
                });
            },
            /**
             * 处理标签
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} tag 点击取消的标签
             */
            clickHandleTag(tag) {
                this.$emit('switch-tag', {
                    id: tag.id,
                    name: tag.name,
                });
            },
            /**
             * 当点击保存时
             * <AUTHOR>
             * @date 2020-06-29
             */
            onClickSave() {
                this.saveError = false;
                if (this.saveLoading) return;
                this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        if (this.transmit) {
                            // 只透传数据，不做提交
                            const data = clone(this.postData);
                            data.address.addressGeo = data.addressGeo;
                            data.address.addressDetail = data.addressDetail;
                            data.patientSource = {
                                id: data.sourceId,
                                sourceFrom: data.sourceFrom,
                            };
                            delete data.addressGeo;
                            delete data.addressDetail;
                            delete data.sourceId;
                            delete data.sourceFrom;
                            return this.$emit('cache-base', data);
                        }
                        this.saveLoading = true;
                        try {
                            let params = clone(this.postData);
                            params = {
                                ...params,
                                ...params.address,
                            };
                            delete params.address;
                            const { data } = await CrmAPI.updatePatientInfo(this.patientInfo.id, params);
                            this.editor = false;
                            this.$Toast({
                                message: '修改成功',
                                type: 'success',
                            });
                            this.$emit('fetch-info', data);
                        } catch (error) {
                            this.saveError = true;
                            console.log('onClickSave error', error);
                            if (error && (error.code === 10409 || error.code === 409)) {
                                // 存在已经被共享的会员
                                this.$Toast({
                                    message: error.message,
                                    type: 'error',
                                });
                            } else if (error?.code === 13993) {
                                // 身份证被注册
                                this.$Toast({
                                    message: error.message,
                                    type: 'error',
                                });
                            }
                        }
                        this.saveLoading = false;
                    }
                });
            },
            /**
             * 创建一个postData数据结构
             * <AUTHOR>
             * @date 2020-06-29
             * @returns {Object}
             */
            getPostData() {
                const postData = {
                    name: '',
                    mobile: '',
                    countryCode: '',
                    sex: '男',
                    birthday: '',
                    idCard: '',
                    idCardType: '身份证',
                    company: '',
                    marital: '',
                    weight: '',
                    profession: '',
                    sn: '',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    sourceId: null,
                    sourceFrom: null,
                    address: {
                        addressCityId: '',
                        addressCityName: '',
                        addressProvinceId: '',
                        addressProvinceName: '',
                        addressDistrictId: '',
                        addressDistrictName: '',
                    },
                    addressGeo: '',
                    addressDetail: '',
                    remark: '',
                    pastHistory: '',
                    ethnicity: '',
                    visitReason: '',
                };
                if (this.patientInfo) {
                    const {
                        name,
                        mobile,
                        sex,
                        idCard,
                        idCardType,
                        company,
                        marital,
                        weight,
                        profession,
                        sn,
                        age,
                        patientSource,
                        address,
                        remark,
                        pastHistory,
                        countryCode,
                        ethnicity,
                        visitReason,
                    } = this.patientInfo;
                    let { birthday } = this.patientInfo;
                    if (!birthday && (age.year || age.month || age.day)) {
                        birthday = age2birthday(age);
                    }
                    postData.name = name || '';
                    postData.mobile = mobile || '';
                    postData.sex = sex || '男';
                    postData.birthday = birthday || '';
                    postData.idCard = idCard || '';
                    postData.idCardType = idCardType || '身份证';
                    postData.company = company || '';
                    postData.marital = marital || '';
                    postData.weight = weight || '';
                    postData.profession = profession || '';
                    postData.sn = sn || '';
                    postData.remark = remark || '';
                    postData.pastHistory = pastHistory || '';
                    postData.countryCode = countryCode || defaultCountryCode;
                    postData.ethnicity = ethnicity || '';
                    postData.visitReason = visitReason || '';
                    if (age) {
                        postData.age.year = age.year || '';
                        postData.age.month = age.month || '';
                        postData.age.day = age.day || '';
                    }
                    if (patientSource) {
                        postData.sourceId = patientSource.id || null;
                        postData.sourceFrom = patientSource.sourceFrom || null;
                    }
                    if (address) {
                        postData.address.addressCityId = address.addressCityId || '';
                        postData.address.addressCityName = address.addressCityName || '';
                        postData.address.addressProvinceId = address.addressProvinceId || '';
                        postData.address.addressProvinceName = address.addressProvinceName || '';
                        postData.address.addressDistrictId = address.addressDistrictId || '';
                        postData.address.addressDistrictName = address.addressDistrictName || '';
                        postData.addressGeo = address.addressGeo || '';
                        postData.addressDetail = address.addressDetail || '';
                    }
                }
                return postData;
            },
            /**
             * 地址样式
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} address 地址对象
             * @returns {String}
             */
            getAddress(address) {
                const {
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                } = address;
                const arr = [];
                if (addressProvinceName) arr.push(addressProvinceName);
                if (addressCityName) arr.push(addressCityName);
                if (addressDistrictName) arr.push(addressDistrictName);
                if (arr.length !== 0) {
                    return arr.join('/');
                }
                return '-';

            },
            getMarital(marital) {
                switch (marital) {
                    case MaritalStatusEnum.single:
                        return '未婚';
                    case MaritalStatusEnum.married:
                        return '已婚';
                    case MaritalStatusEnum.divorced:
                        return '离异';
                    case MaritalStatusEnum.widow:
                        return '丧偶';
                    default:
                        return '';
                }
            },
            /**
             * 当点击查看健康档案详情
             * <AUTHOR>
             * @date 2020-07-01
             * @param {Object} item
             */
            onClickFilesDetail(item) {
                if (item.type === 'child') {
                    // 查看儿童健康档案详情
                    this.$emit('open-child-health');
                }
            },
            /**
             * 当档案号输入时，允许输入数字（可0开头）
             * <AUTHOR>
             * @date 2021-01-05
             */
            onInputSn() {
                if (this.postData) {
                    this.$nextTick(() => {
                        this.postData.sn = this.postData.sn.replace(/\D/g, '');
                    });
                }
            },

            async readIdCardHandle() {
                const messageOptions = {
                    referenceEl: this.$parent.$el.querySelector('.crm-module__package-card__base-card'),
                    type: 'loading',
                    title: '身份证读取中',
                    dialogContentStyles: 'width: 240px; min-width: 240px; min-height: auto',
                    dialogType: 'tiny',
                    showFooter: false,
                    showClose: false,
                    noDialogAnimation: true,
                };

                try {
                    const { patient: cardInfo } = await IdCardReaderService.readOnly();
                    console.debug('更新的患者信息：', cardInfo);
                    this.cardInfo = cardInfo;
                    this.showCardInfo = true;
                } catch (e) {
                    this.$message({
                        ...messageOptions,
                        type: 'warn',
                        title: e,
                        showFooter: true,
                        showConfirm: true,
                        showCancel: false,
                    });
                }
            },

            updatePatientByCard() {
                if (this.cardInfo) {
                    this.postData.name = this.cardInfo.name;
                    this.postData.idCard = this.cardInfo.idCard;
                    this.postData.idCardType = this.cardInfo.idCardType;
                    this.postData.sex = this.cardInfo.sex;
                    this.postData.addressDetail = this.cardInfo.addressInfo;
                    this.postData.birthday = this.cardInfo.birthday;
                    this.changeBirthday(this.postData.birthday);
                }
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/abc-common.scss';

.crm-module__package-card__base-card {
    position: relative;
    box-sizing: border-box;
    width: 334px;
    padding: 12px 0;
    margin-top: 7px;
    cursor: auto;
    background-color: $S2;
    border: 1px solid $P1;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.24);

    .btn {
        @include flex(row, flex-end, center);

        position: absolute;
        top: 12px;
        right: 12px;
    }

    .top {
        @include flex(row, flex-start, center);

        margin-bottom: 4px;
        line-height: 1.8;

        .name {
            font-size: 16px;
            font-weight: bold;
            color: $T1;
        }

        .age {
            margin-left: 6px;
            font-size: 16px;
            color: $T1;
        }

        .sex {
            @extend .age;

            margin-left: 12px;
        }

        img {
            width: 16px;
            height: 16px;
            margin-left: 4px;
        }
    }

    .tow {
        @include flex(column, flex-start, flex-start);

        > span {
            font-size: 12px;
            line-height: 1;
            color: $T2;

            &:last-child {
                margin-top: 8px;
            }
        }
    }

    .thr {
        padding: 0 8px 4px;
        margin-top: 10px;
        border-bottom: 1px solid $P6;

        .crm-module__package-label__abc-label {
            margin-right: 4px;
            margin-left: 4px;
        }
    }

    .padd-box {
        padding: 0 12px;
    }

    .patient-box {
        padding-top: 8px;
    }

    .item {
        @include flex(row, flex-start, flex-start);

        padding: 2px 0;
        font-size: 14px;
        line-height: 22px;
        color: $T1;

        > label {
            flex-shrink: 0;
            width: 70px;
            color: $T2;
        }

        .text {
            flex: 1;
            text-align: left;
        }

        &.address {
            .text {
                line-height: 24px;
            }
        }
    }

    .health-files {
        padding-bottom: 12px;
        margin-top: 8px;
        border-top: 1px solid $P6;

        .child-health {
            padding: 10px 12px;
            margin-top: 12px;
            background-color: $P5;
            border-radius: 2px;

            .title-box {
                height: 22px;
                cursor: pointer;

                @include flex(row, flex-start, center);

                .tit {
                    font-size: 14px;
                    color: $T1;
                }

                .iconfont {
                    font-size: 16px;
                    color: $T2;
                }
            }

            .cont {
                margin-top: 6px;
                font-size: 12px;
                line-height: 1;
                color: $T2;

                @include ellipsis(1);
            }
        }
    }

    .editor-box {
        .item {
            padding: 6px 0;

            > label {
                line-height: 28px;
            }
        }

        .abc-form-item {
            margin-right: 0 !important;
            margin-bottom: 0 !important;
        }

        .abc-input__inner {
            height: 28px;
            border-radius: 0;
        }

        .abc-input-wrapper {
            .append-input {
                border-radius: 0 !important;
            }
        }

        .abc-date-picker {
            height: 28px;
        }

        .sex-radio-group {
            .abc-form-item-content {
                border: 1px solid $P1;
                // border-radius: var(--abc-border-radius-small);
                .abc-radio-group {
                    display: flex;

                    .abc-radio {
                        flex: 1;
                        justify-content: center;
                        height: 28px;

                        &.is-disabled {
                            background: $abcBgDisabled;
                        }

                        .abc-radio-input {
                            .abc-radio-inner {
                                border: none;

                                &.is-checked {
                                    background-color: #ffffff;
                                }

                                &::after {
                                    top: 46%;
                                    left: 43%;
                                    display: inline-block;
                                    width: 12px;
                                    height: 12px;
                                    font-family: 'iconfont' !important;
                                    font-size: 14px;
                                    font-style: normal;
                                    color: $theme2;
                                    vertical-align: middle;
                                    content: '\e77b';
                                    background: none;
                                    -webkit-font-smoothing: antialiased;
                                    -moz-osx-font-smoothing: grayscale;
                                }
                            }
                        }
                        //第一个abc-radio没有border-left
                        & + .abc-radio {
                            margin-left: 0;
                            border-left: 1px solid $P1;
                        }
                    }
                }
            }
        }

        .patient-age-info {
            display: inline-flex;

            .patient-age-year,
            .patient-age-month,
            .patient-age-day {
                margin-right: 0;

                .abc-input-wrapper {
                    &:not(.is-disabled) .abc-input__inner {
                        &:hover {
                            border: 1px solid #0270c9;
                        }
                    }

                    &:not(.is-disabled) .append-input {
                        background: none;
                    }

                    &,
                    .is-disabled {
                        .append-input {
                            background-color: $abcBgDisabled;
                        }
                    }
                }
            }

            .patient-age-year {
                .abc-input-wrapper {
                    .abc-input__inner {
                        border-right: 0;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }

                    .append-input {
                        width: 22px;
                        min-width: 22px;
                        border-right: 0;
                        border-left: 0;
                        border-radius: 0;
                    }
                }

                &.is-error {
                    .abc-input-wrapper .abc-input__inner {
                        border-right: 1px solid;
                    }
                }
            }

            .patient-age-month {
                .abc-input-wrapper {
                    .abc-input__inner {
                        border-right: 0;
                        border-left: 0;
                        border-radius: 0;
                    }

                    .append-input {
                        width: 64px;
                        text-align: left;
                        border-right: 0;
                        border-left: 0;
                        border-radius: 0;
                    }
                }

                &.is-error {
                    .abc-input-wrapper .abc-input__inner {
                        border-right: 1px solid;
                        border-left: 1px solid;
                    }
                }
            }

            .patient-age-day {
                .abc-input-wrapper {
                    .abc-input__inner {
                        border-right: 0;
                        border-left: 0;
                        border-radius: 0;
                    }

                    .append-input {
                        width: 64px;
                        //border-left: 0;
                        text-align: left;
                        border-radius: 0;
                    }
                }

                &.is-error {
                    .abc-input-wrapper .abc-input__inner {
                        border-right: 1px solid;
                        border-left: 1px solid;
                    }
                }
            }

            .abc-form-item-content {
                height: 100%;
            }
        }

        .customer-input-wrapper {
            height: 28px;
            margin-bottom: 4px;
            line-height: 20px;
            text-align: left;
            border-radius: 0;
        }

        .customer-input-wrapper.address-selector-input {
            padding-left: 8px;

            > div {
                line-height: 20px;
            }
        }

        .source-item {
            height: 28px;

            .abc-cascader {
                .abc-input__inner {
                    height: 28px;
                    line-height: 28px;
                    border-radius: 0;
                }
            }
        }
    }
}

.add-label-pop {
    padding: 0;
    background-color: $S2;
    border: 1px solid $P7;
    border-radius: $borderRadiusMini;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06);
    transform: translateY(-6px);

    .box {
        width: 318px;
        max-height: 306px;
        overflow: auto;

        .alert {
            @include flex(row, center, center);

            height: 46px;
            margin: 12px 12px 0 12px;
            background-color: #f0f7ff;
            border: 1px solid #e0efff;

            .iconfont {
                margin-right: 8px;
            }
        }
    }
}

.custom-no-arrow {
    .popper__arrow {
        display: none;
    }
}

.set-source-more {
    @include flex(row, flex-end, center);

    height: 32px;
    padding: 0 12px;
    border-top: 1px solid $P3;

    .iconfont {
        font-size: 14px;
        color: $T2;
        cursor: pointer;
    }

    .hover-color {
        &:hover {
            color: $theme1;
        }
    }
}
</style>
