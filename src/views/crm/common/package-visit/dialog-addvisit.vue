<template>
    <abc-dialog
        class="dialog-addvisit"
        :title="isCreated ? '新建随访' : '修改随访'"
        :value="true"
        :before-close="beforeCloseDialog"
        content-styles="padding: 0px 24px; width: 664px;min-height: 520px; max-height: 780px; overflow-x: hidden;"
        append-to-body
        @input="(val) => $emit('input', val)"
    >
        <section>
            <abc-form
                ref="postData"
                v-abc-loading="loading"
                label-position="left"
                :label-width="80"
                item-block
            >
                <abc-form-item v-if="!isSupplement" label="任务名称" required>
                    <abc-input v-model="postData.name" :width="536" placeholder="请输入随访任务"></abc-input>
                </abc-form-item>
                <abc-form-item label="随访类型" required>
                    <abc-select v-model="postData.type" disabled :width="536">
                        <abc-option
                            v-for="item in visitOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
                <abc-form-item
                    ref="execution_mode--box"
                    label="随访患者"
                    class="execution_mode--box"
                    required
                    :error="errorInfo"
                >
                    <abc-radio-group v-model="postData.taskType" @change="changeSelectItem">
                        <abc-radio :disabled="!isCreated" :label="0" class="execution_mode--radio">
                            <span>手动选择具体患者</span>
                            <span
                                v-if="postData.taskType === 0 && !disabledModifyTaskType"
                                :style="{
                                    'margin-left': '8px', 'color': style.theme1
                                }"
                                @click.stop.prevent="!disabledChangePatient && $emit('onSelectPatients', selectedPatients, postData.taskType)"
                            >选择</span>
                        </abc-radio>
                        <div class="execution_mode--desc">
                            勾选需要随访的患者，为他们创建随访计划
                        </div>
                        <div v-if="postData.taskType === 0 && selectedPatients.length" class="execution_mode--patient">
                            <div
                                class="select-patient clearfix"
                                :class="{ disabled: disabledChangePatient }"
                            >
                                <template v-if="selectedPatients.length !== 0">
                                    <div class="item-selected-visit">
                                        <span>{{ `${selectedPatients[0].name}等${selectedPatients.length}位患者` }}</span>
                                        <div v-if="!disabledModifyTaskType" class="close" @click="onClickDelete(0, 'all')">
                                            <abc-icon
                                                icon="delete1"
                                                size="12"
                                                color="#DADCE4"
                                                @click="cleanRulesSelect"
                                            ></abc-icon>
                                        </div>
                                    </div>
                                </template>
                                <span v-else class="placeholder">请选择随访患者</span>
                            </div>
                        </div>
                        <template v-if="!isSupplement">
                            <abc-radio
                                :label="1"
                                style="margin-top: 12px;"
                                :disabled="!isAutoCreatedEveryDay || isSupplement || !isCreated || disableAutoCreateRules"
                                class="execution_mode--radio"
                            >
                                <span>自动选择符合条件患者</span>
                                <span
                                    v-if="postData.taskType === 1 && !disabledModifyTaskType"
                                    :style="{
                                        'margin-left': '8px', 'color': style.theme1
                                    }"
                                    @click.stop.prevent="openSelectVisitRules"
                                >选择</span>
                                <template v-if="!isAutoCreatedEveryDay">
                                    <abc-popover
                                        placement="top"
                                        style="margin-left: auto;"
                                        trigger="hover"
                                        theme="yellow"
                                    >
                                        <abc-icon
                                            slot="reference"
                                            icon="info"
                                            color="#dadbe0"
                                            size="14"
                                        ></abc-icon>
                                        <div style="min-width: 120px;">
                                            已有{{ schedulingCount }}个该类型的随访计划正在执行，不可新建
                                        </div>
                                    </abc-popover>
                                </template>
                            </abc-radio>
                            <div class="execution_mode--desc">
                                每天选取符合条件的患者，并自动创建随访任务
                            </div>
                            <div
                                v-if="postData.taskType === 1 && postData.packageFilters.length"
                                class="execution_mode--patient execution_mode--filter"
                                :class="{ 'execution_mode--filter-error': errorInfo.error }"
                            >
                                <div class="execution_mode--filter-content">
                                    <span v-for="(item,index) in postData.packageFilters" :key="index">
                                        {{ item.label }} ;
                                    </span>
                                </div>
                                <abc-icon
                                    icon="delete1"
                                    size="12"
                                    color="#DADCE4"
                                    style="margin-left: 7px;"
                                    @click.stop.prevent="cleanRulesSelect"
                                ></abc-icon>
                            </div>
                        </template>
                    </abc-radio-group>
                </abc-form-item>
                <abc-form-item
                    v-if="!isSupplement"
                    label="执行方式"
                    class="execution_mode--box"
                    required
                >
                    <abc-radio-group v-model="postData.executeMode">
                        <div class="execution_mode--radio">
                            <abc-radio :label="0" :disabled="isUpdate">
                                人工执行随访
                            </abc-radio>
                        </div>
                        <div class="execution_mode--desc">
                            需要随访人联系患者进行随访结果记录
                        </div>
                        <div class="execution_mode--radio" style="margin-top: 12px;">
                            <abc-tooltip :content="`开通微${$app.institutionTypeWording}可用`" :disabled="!disabledAuthVisit">
                                <abc-radio
                                    :label="1"
                                    :disabled="disabledAuthVisit || isUpdate || disableAutoVisit"
                                >
                                    自动执行随访
                                </abc-radio>
                            </abc-tooltip>
                        </div>
                        <div class="execution_mode--desc">
                            系统将在指定时间进行随访消息发送
                        </div>
                        <div v-if="supportVisitSendMsgByScrm && postData.executeMode" class="execution_mode--choice">
                            <abc-radio-group v-model="postData.visitSendMsgWay">
                                <abc-radio :label="2" :disabled="!isOpenScrm || isUpdate">
                                    通过企微聊天发送至患者微信 <span
                                        v-if="!isOpenScrm"
                                        :style="{
                                            'margin-left': '10px' ,'color': style.Y2
                                        }"
                                    >未开通企微管家</span>
                                </abc-radio>
                                <abc-radio :label="1" :disabled="isUpdate" style="margin-top: 12px; margin-left: 0;">
                                    通过公众号消息提醒发送给患者
                                </abc-radio>
                            </abc-radio-group>
                        </div>
                    </abc-radio-group>
                </abc-form-item>


                <div class="line"></div>
                <template v-if="isPlanTask">
                    <abc-form-item
                        key="visited-date-plan"
                        label="随访时间"
                        required
                        class="visit-date"
                    >
                        <abc-radio-group v-model="postData.visitRate">
                            <div class="single-visit">
                                <abc-radio :label="0">
                                    单次随访
                                </abc-radio>
                                <template v-if="postData.visitRate === 0">
                                    <span class="label">随访时间</span>
                                    <abc-date-picker
                                        v-if="!postData.taskType"
                                        v-model="postData.singleVisitDate"
                                        :picker-options="{ disabledDate: disabledPreviewDate }"
                                        :width="140"
                                        :clearable="false"
                                    ></abc-date-picker>
                                    <abc-select v-else v-model="postData.afterOutpatientDay" :width="140">
                                        <abc-option
                                            v-for="item in visitTimeOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                </template>
                            </div>
                            <div class="cycle-visit">
                                <abc-radio :label="1">
                                    周期随访
                                </abc-radio>
                                <template v-if="postData.visitRate === 1">
                                    <template v-if="postData.taskType">
                                        <span class="mei">从</span>
                                        <abc-select v-model="postData.afterOutpatientInitDelay" :width="140">
                                            <abc-option
                                                v-for="item in visitTimeOptions"
                                                :key="item.value"
                                                :value="item.value"
                                                :label="item.label"
                                            ></abc-option>
                                        </abc-select>
                                        <span>开始，每</span>
                                    </template>
                                    <span v-if="!postData.taskType" class="mei">每</span>
                                    <abc-select v-if="!postData.taskType" v-model="postData.dateCount" :width="64">
                                        <abc-option
                                            v-for="item in dateOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                    <abc-select v-else v-model="postData.afterOutpatientFixRate" :width="64">
                                        <abc-option
                                            v-for="item in dateOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                    <template v-if="!postData.taskType">
                                        <span>1次，随访时段</span>
                                        <abc-date-picker
                                            v-if="!postData.taskType"
                                            v-model="postData.cycleDates"
                                            type="daterange"
                                            value-format="YYYY-MM-DD"
                                            style="margin-left: 8px;"
                                            :width="238"
                                            :clearable="false"
                                            :picker-options="{ disabledDate: disabledPreviewDate }"
                                            placeholder="选择日期范围"
                                        >
                                        </abc-date-picker>
                                    </template>
                                    <template v-else>
                                        <span>共随访</span>
                                        <abc-input
                                            v-model="postData.count"
                                            style="margin-right: 8px; margin-left: 8px;"
                                            :config="{
                                                min: 1,
                                                max: 10,
                                            }"
                                            type="number"
                                            :width="34"
                                        ></abc-input>
                                        <span>次</span>
                                    </template>
                                </template>
                            </div>
                            <div v-if="postData.visitRate === 1 && !postData.taskType" class="date-list clearfix">
                                <span v-for="(item, index) in cycleDateArray" :key="index">
                                    {{ `${item.show}${index === cycleDateArray.length - 1 ? '' : '，'}` }}
                                </span>
                            </div>
                            <div class="batch-visit">
                                <abc-radio :label="2">
                                    多次随访
                                </abc-radio>
                                <div v-if="postData.visitRate === 2 && !postData.taskType" class="add-date">
                                    <abc-date-picker
                                        width="30"
                                        :value="''"
                                        :picker-options="{ disabledDate }"
                                        value-format="YYYY-MM-DD"
                                        @input="onInputBatchDate"
                                    >
                                        添加
                                    </abc-date-picker>
                                </div>
                                <div
                                    v-if="postData.visitRate === 2 && postData.taskType"
                                    v-abc-click-outside="closeAddDayTimeBox"
                                    class="add-date"
                                    style="position: relative;"
                                >
                                    <span @click="addDayTimeBox = true">添加</span>
                                    <div v-if="addDayTimeBox" class="add-day-time-box">
                                        <div class="add-day-time-box_check">
                                            <abc-checkbox-group v-model="postData.afterOutpatientDays">
                                                <abc-checkbox
                                                    v-for="item in visitTimeOptions"
                                                    :key="item.value"
                                                    :label="item.value"
                                                >
                                                    {{ item.label }}
                                                </abc-checkbox>
                                            </abc-checkbox-group>
                                        </div>

                                        <div class="add-day-time-box_button--setting">
                                            <abc-button @click.stop.prevent="addDayTimeBox = false">
                                                确定
                                            </abc-button>
                                            <abc-button type="blank" @click.stop.prevent="addDayTimeBox = false">
                                                取消
                                            </abc-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="postData.visitRate === 2 && !postData.taskType" class="date-card clearfix">
                                <div
                                    v-for="item in showManyDates"
                                    :key="item.date"
                                    class="item"
                                    @click="onDeleteManyDates(item)"
                                >
                                    <span>{{ item.show }}</span>
                                    <span class="iconfont cis-icon-cross_small"></span>
                                </div>
                            </div>
                            <div v-if="postData.visitRate === 2 && postData.taskType" class="date-card clearfix">
                                <div
                                    v-for="(item, index) in postData.afterOutpatientDays"
                                    :key="index"
                                    class="item item-large"
                                    @click="onDeleteVisitCheckbox(item)"
                                >
                                    <span>{{ item | getVisitCheckboxName }}</span>
                                    <span class="iconfont cis-icon-cross_small"></span>
                                </div>
                            </div>
                        </abc-radio-group>
                    </abc-form-item>
                </template>
                <template v-if="isManualRevisit && !isSupplement">
                    <abc-form-item
                        ref="visit-target"
                        label="随访目标"
                        class="visit-target"
                        style="margin-top: 16px;"
                        required
                    >
                        <abc-textarea
                            v-model.trim="postData.target"
                            placeholder="填写本次随访目标"
                            trim
                            :height="99"
                            :maxlength="500"
                        ></abc-textarea>
                    </abc-form-item>
                    <div class="template">
                        <abc-button type="text" @click="$emit('showTargetTemplate')">
                            调用模板
                        </abc-button>
                        <abc-button
                            type="text"
                            :disabled="disabledSaveVisitTarget"
                            @click="$emit('saveTargetTemplate', postData.target)"
                        >
                            保存模板
                        </abc-button>
                    </div>
                    <div
                        v-if="postData.revisitFormSheets && postData.revisitFormSheets.length"
                        class="revisit-form-sheets"
                    >
                        <div class="label">
                            随访表
                        </div>
                        <div class="content">
                            <div
                                v-for="form in postData.revisitFormSheets"
                                :key="form.id"
                                class="revisit-form"
                                @click="handleFormSheetClick(form)"
                            >
                                <i class="iconfont cis-icon-update_log"></i>
                                {{ form.formName }}
                            </div>
                        </div>
                    </div>
                </template>
                <template v-if="isPlanTask">
                    <abc-form-item
                        v-if="isManualRevisit"
                        key="visit-name"
                        label="随访人"
                        required
                        class="visit-name"
                    >
                        <abc-select
                            v-model="postData.planExecutorId"
                            with-search
                            :fetch-suggestions="fetchSuggestionPlans"
                            :width="180"
                        >
                            <abc-option v-if="!keywordPlan" :value="''" label="不指定"></abc-option>
                            <abc-option
                                v-for="item in showEmployeeOptionsPlan"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></abc-option>
                        </abc-select>
                        <template v-if="postData.planExecutorId === ''">
                            <span class="iconfont cis-icon-Attention"></span>
                            <span class="alert-text">未指定随访人，将不会收到每日待办的随访任务</span>
                        </template>
                    </abc-form-item>
                    <template v-if="isAutoRevisit">
                        <abc-form-item
                            v-if="!postData.taskType"
                            key="visit-message"
                            label="随访消息"
                            required
                            class="visit-message"
                        >
                            <div class="select-auto-wrapper">
                                <span>微信消息</span>
                                <abc-tooltip content="请选择随访时间" :disabled="!disabledAddAutoVisit">
                                    <span>
                                        <abc-button
                                            type="text"
                                            style="height: 16px;"
                                            :disabled="disabledAddAutoVisit"
                                            @click="onClickHandleMessage(null)"
                                        >
                                            添加
                                        </abc-button>
                                    </span>
                                </abc-tooltip>
                            </div>
                            <div class="auto-message">
                                <div v-for="(item, index) in postData.autoRevisitMessageList" :key="index">
                                    <div class="title-box">
                                        <span class="date">
                                            {{
                                                isSelectedAllDates && postData.autoRevisitMessageList.length === 1 ?
                                                    '全部时间' :
                                                    (item.scheduleDates && item.scheduleDates.map((date) => date.slice(-5)).join('、')) || '--'
                                            }}
                                        </span>
                                        <abc-button
                                            type="text"
                                            style=" width: 32px; min-width: 24px;"
                                            @click="onClickHandleMessage(item, index)"
                                        >
                                            编辑
                                        </abc-button>
                                    </div>
                                    <div class="send-content">
                                        {{ item.message }}
                                    </div>
                                </div>
                            </div>
                        </abc-form-item>
                        <abc-form-item
                            v-else
                            key="visit-message"
                            label="随访消息"
                            required
                            class="visit-message"
                        >
                            <abc-textarea
                                v-model.trim="postData.autoRevisitMessage"
                                placeholder="填写本次消息内容"
                                trim
                                :height="99"
                                :maxlength="500"
                            ></abc-textarea>
                        </abc-form-item>
                        <div v-if="postData.taskType" class="template">
                            <abc-button type="text" @click="$emit('showMessageTemplate')">
                                调用模板
                            </abc-button>
                            <abc-button
                                type="text"
                                :disabled="!postData.autoRevisitMessage"
                                @click="$emit('saveMessageTemplate', postData.autoRevisitMessage)"
                            >
                                保存模板
                            </abc-button>
                        </div>
                    </template>
                </template>
                <template v-else>
                    <div class="one">
                        <abc-form-item
                            key="visited-name"
                            label="随访人"
                            required
                            class="visited-name"
                        >
                            <abc-select
                                v-model="postData.finishExecutorId"
                                with-search
                                :fetch-suggestions="fetchSuggestions"
                                :width="180"
                            >
                                <abc-option
                                    v-for="item in showEmployeeOptions"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <abc-form-item
                            key="visited-date-finish"
                            label="随访时间"
                            required
                            class="visited-date"
                        >
                            <abc-date-picker
                                v-model="postData.visitDate"
                                :picker-options="{ disabledDate: disabledFutureDate }"
                                :width="180"
                            ></abc-date-picker>
                        </abc-form-item>
                    </div>
                    <abc-form-item key="visited-mode" required label="随访方式">
                        <abc-select v-model="postData.revisitMode" :width="536">
                            <abc-option
                                v-for="item in methodOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item
                        ref="visit-result"
                        label="随访结果"
                        required
                        class="visit-result"
                    >
                        <abc-textarea
                            v-model.trim="postData.result"
                            placeholder="填写本次随访结果"
                            trim
                            :height="99"
                            :maxlength="500"
                        ></abc-textarea>
                    </abc-form-item>
                    <div class="template">
                        <abc-button type="text" @click="$emit('showResultTemplate')">
                            调用模板
                        </abc-button>
                        <abc-button
                            type="text"
                            :disabled="disabledSaveVisitResult"
                            @click="$emit('saveResuleTemplate', postData.result)"
                        >
                            保存模板
                        </abc-button>
                    </div>
                    <div class="hou">
                        <abc-form-item label="随访附件">
                            <abc-button type="text" @click="onClickUpload">
                                上传附件
                            </abc-button>
                            <input
                                v-if="showUploadNode"
                                ref="upload-file"
                                class="hidden-inp"
                                type="file"
                                :accept="accessAppend()"
                                @change="onChangeUploadFile"
                            />
                        </abc-form-item>
                    </div>
                    <div class="file">
                        <div v-for="(arr, index) in postAttachmentGroup" :key="index">
                            <item-attachment
                                v-for="info in arr"
                                :key="info.id"
                                can-delete
                                :info="info"
                                @delete="onClickDeleteFile(info)"
                            ></item-attachment>
                        </div>
                    </div>
                </template>
                <template v-if="isManualRevisit && !isSupplement">
                    <abc-form-item label="结果通知" class="notify-emploee">
                        <div class="select-alert-wrapper">
                            <abc-button type="text" @click="$emit('onSelectEmployee')">
                                选择成员
                            </abc-button>
                            <span>成员可从”随访-通知我的“中查看通知给自己的随访单</span>
                        </div>
                        <div v-if="selectedNotifyEmployee.length !== 0" class="selected-employee">
                            <item-employee
                                v-for="(item, index) in selectedNotifyEmployee"
                                :key="item.id"
                                :label="item.name"
                                @close="onClickCloseEmployee(index)"
                            ></item-employee>
                        </div>
                    </abc-form-item>
                </template>
            </abc-form>
        </section>
        <footer slot="footer" class="dialog-footer">
            <template v-if="taskId">
                <abc-button v-if="canStopVisitPlan" type="blank" @click="handleStopPlanClick">
                    终止
                </abc-button>
                <abc-button type="danger" @click="handleDeletePlanClick">
                    删除
                </abc-button>
            </template>
            <abc-button
                style="margin-left: auto;"
                :data-tipsy="disabledSaveBtn"
                :class="{ 'abc-tipsy abc-tipsy--n': !!disabledSaveBtn }"
                :disabled="disabledSubmit || !!disabledSaveBtn"
                :loading="loadingSave"
                @click="onClickSubmit"
            >
                保存
            </abc-button>
            <abc-button type="blank" @click="updateValue(false)">
                取消
            </abc-button>
        </footer>
        <select-visit-rules-dialog v-if="isShowSelectVisitRules" v-model="isShowSelectVisitRules" @confirmSelect="confirmSelect"></select-visit-rules-dialog>
    </abc-dialog>
</template>

<script>
    import style from '@/styles/theme.module.scss';
    import CrmAPI from 'api/crm';
    import ItemAttachment from './item-attachment.vue';
    import ItemEmployee from './item-employee.vue';
    import DialogTemplateList from '../package-template/dialog-template-list.vue';
    import GenerateFormDialog from 'views/chronic-care/form-dialog';
    import SelectVisitRulesDialog from './dialog-select-visit-rules';
    import clone from 'utils/clone';
    import fecha from 'utils/fecha';
    import MixinUpload from '../mixin-upload.js';
    import { isEqual } from 'utils/lodash';
    import {
        visitOptions, dateOptions, methodOptions, visitTimeOptions,
    } from '../../data/visitData.js';
    import {
        DATE_FORMATE, MONTH_DAY_FORMATE, DAY_TIMES, MONTH_TIMES,
    } from 'assets/configure/constants.js';
    import { accessAppend } from 'assets/configure/access-file.js';
    import { createGUID } from 'utils/index.js';
    import { mapGetters } from 'vuex';

    export default {
        name: 'DialogAddvisit',
        components: {
            ItemAttachment,
            ItemEmployee,
            SelectVisitRulesDialog,
        },
        filters: {
            getVisitCheckboxName(value) {
                return visitTimeOptions.find((item) => {
                    return item.value === value;
                }).label;
            },
        },
        mixins: [MixinUpload],
        props: {
            target: {
                type: String,
                default: '',
            },
            taskDetail: {
                type: Object,
                default: null,
            },
            outpatientSheetId: {
                type: String,
                default: '',
            },
            selectedPatients: {
                type: Array,
                default: () => [],
            },
            selectedNotifyEmployee: {
                type: Array,
                default: () => [],
            },
            disabledSelectPatient: {
                type: Boolean,
                default: false,
            },

            // 禁止随访类型修改
            disabledVisitType: {
                type: Boolean,
                default: false,
            },
            // 禁止自动随访
            disableAutoVisit: {
                type: Boolean,
                default: false,
            },
            // 禁止创建规则
            disableAutoCreateRules: {
                type: Boolean,
                default: false,
            },
            // 禁止选择随访状态的已完成
            disableCompleteStatus: {
                type: Boolean,
                default: false,
            },
            disabledModifyTaskType: {
                type: Boolean,
                default: false,
            },
            // 新建默认数据
            defaultTaskDetail: {
                type: Object,
                default: null,
            },
            isSupplement: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            const postData = this.getPostData();
            return {
                style,
                visitOptions,
                dateOptions,
                methodOptions,
                loading: false,
                loadingSave: false,
                postData,
                visitTimeOptions,
                schedulingCount: 0,
                keyword: '', // 随访人筛选关键词
                keywordPlan: '', // 随访人筛选关键词
                employeeList: [], // 门店成员列表
                showUploadNode: true, // 上传节点更新
                currentUpdateIndex: null, // 当前修改自动随访消息索引
                cacheDatesConfig: null, // 缓存一个时间配置 主要用于时间修改后，还原
                disableBack: false,
                errorInfo: {
                    error: false,
                    message: '随访患者不能为空',
                },
                addDayTimeBox: false,
                isShowSelectVisitRules: false, // 是否展示筛选弹窗
            };
        },
        computed: {
            ...mapGetters(['userInfo', 'isOpenMp', 'supportVisitSendMsgByScrm', 'isOpenScrm']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            visitCountByRules() {
                return this.viewDistributeConfig.CRM.visitCountByRules;
            },
            // 是否新增
            isCreated() {
                return !this.taskDetail;
            },
            // 是否修改
            isUpdate() {
                return !!this.taskDetail;
            },
            // 随访任务id
            taskId() {
                const { id } = this.taskDetail || {};
                return id || '';
            },
            isAutoCreatedEveryDay() {
                return this.schedulingCount < this.visitCountByRules;
            },
            // 自动随访依赖微诊所的开通
            disabledAuthVisit() {
                return !this.isOpenMp;
            },
            // 是否可以点击保存随访目标模板，内容为空时不允许
            disabledSaveVisitTarget() {
                return !this.postData.target;
            },
            // 是否可以点击保存随访结果模板，内容为空时不允许
            disabledSaveVisitResult() {
                return !this.postData.result;
            },
            // 是否人工随访，非人工随访时需要隐藏一些字段
            isManualRevisit() {
                const { executeMode } = this.postData || {};
                return executeMode === 0;
            },
            // 是否自动随访，自动随访时需要显示一些字段
            isAutoRevisit() {
                const { executeMode } = this.postData || {};
                return executeMode === 1 || executeMode === 2;
            },
            // 是否计划随访
            isPlanTask() {
                const { status } = this.postData || {};
                return status === 0;
            },
            // 是否已完成随访
            isFinishTask() {
                const { status } = this.postData || {};
                return status === 1;
            },
            // 筛选后的随访人列表
            showEmployeeOptionsPlan() {
                return this.employeeList.filter((item) => {
                    if (this.keywordPlan) {
                        return this.handleFrontFilter(item, this.keywordPlan);
                    }
                    return true;
                });
            },
            // 筛选后的随访人列表
            showEmployeeOptions() {
                return this.employeeList.filter((item) => {
                    if (this.keyword) {
                        return this.handleFrontFilter(item, this.keyword);
                    }
                    return true;

                });
            },
            // 周期时间计算成数组
            cycleDateArray() {
                const arr = [];
                if (this.postData) {
                    const {
                        cycleDates: [startDate, endDate],
                        dateCount,
                    } = this.postData;
                    if (startDate && endDate) {
                        const startTime = new Date(startDate.split('-').join('/')).getTime();
                        const endTime = new Date(endDate.split('-').join('/')).getTime();
                        for (let time = startTime; time <= endTime; time += DAY_TIMES * dateCount) {
                            arr.push({
                                date: fecha.format(time, DATE_FORMATE),
                                show: fecha.format(time, MONTH_DAY_FORMATE),
                            });
                        }
                    }
                }
                return arr;
            },
            // 多次随访时间
            showManyDates() {
                return this.postData.manyDates
                    .map((item) => {
                        const time = new Date(item.split('-').join('/')).getTime();
                        return {
                            time,
                            date: item,
                            show: fecha.format(time, MONTH_DAY_FORMATE),
                        };
                    })
                    .sort((a, b) => a.time - b.time);
            },
            // 是否有改变
            isUpdated() {
                // 获取一次基础数据
                const postData = this.getPostData();
                const {
                    taskType, packageParams, packageParamsCache,
                } = postData;
                if (this.isCreated) {
                    // 新建
                    if (!this.disabledChangePatient && this.selectedPatients?.length !== 0) {
                        return true;
                    }
                    if (this.selectedNotifyEmployee.length !== 0) {
                        return true;
                    }
                    const { id } = this.userInfo;
                    postData.planExecutorId = id;
                    postData.finishExecutorId = id;
                } else if (!taskType) {
                    // 修改
                    let { patients } = this.taskDetail || {};

                    patients = patients || [];
                    if (this.selectedPatients?.length !== patients.length) {
                        return true;
                    }
                    const patientIds = patients.map((item) => item.id);
                    const exitEvery = this.selectedPatients.every((item) => patientIds.includes(item.id));
                    if (!exitEvery) {
                        return true;
                    }
                    let { notifyEmployeeList } = this.taskDetail || {};
                    notifyEmployeeList = notifyEmployeeList || [];
                    if (this.selectedNotifyEmployee.length !== notifyEmployeeList.length) {
                        return true;
                        // eslint-disable-next-line no-else-return
                    } else {
                        const patientIds = notifyEmployeeList.map((item) => item.id);
                        const exitEvery = this.selectedNotifyEmployee.every((item) => patientIds.includes(item.id));
                        if (!exitEvery) {
                            return true;
                        }
                    }
                } else if (taskType) {
                    if (!isEqual(packageParams, packageParamsCache)) {
                        return true;
                    }
                }

                if (!isEqual(postData, this.postData)) {
                    return true;
                }
                return false;
            },
            scheduleRules() {
                const dateConfig = {};
                const {
                    visitRate, taskType, afterOutpatientDay, afterOutpatientInitDelay,afterOutpatientFixRate,count,afterOutpatientDays,
                } = this.postData || {};
                if (taskType) {
                    dateConfig.visitRate = visitRate;
                    if (visitRate === 0) {
                        dateConfig.afterOutpatientDays = [afterOutpatientDay];
                    } else if (visitRate === 1) {
                        dateConfig.afterOutpatientInitDelay = afterOutpatientInitDelay;
                        dateConfig.afterOutpatientFixRate = afterOutpatientFixRate;
                        dateConfig.count = count;
                    } else {
                        dateConfig.afterOutpatientDays = afterOutpatientDays;
                    }
                }
                return dateConfig;
            },
            // 随访时间
            scheduleDates() {
                let times = [];
                const {
                    visitRate, visitDate, singleVisitDate, manyDates,
                } = this.postData || {};
                if (this.isPlanTask) {
                    // 计划中
                    switch (visitRate) {
                        case 0: // 单次随访
                            times = [singleVisitDate];
                            break;
                        case 1: // 周期随访
                            times = this.cycleDateArray.map((item) => item.date);
                            break;
                        case 2: // 多次随访
                            times = manyDates;
                            break;
                        default:
                            break;
                    }
                } else {
                    // 已完成
                    times = [visitDate];
                }
                return times;
            },
            // 是否选中全部时间
            isSelectedAllDates() {
                return this.selectedDates.length === this.scheduleDates.length;
            },
            // 是否可以点击
            // 上次附件时，类型分组
            postAttachmentGroup() {
                return this.getAttachmentGroup(this.postData.attachments);
            },
            // 当前是否在上传文件
            doUploadFileing() {
                return this.postData.attachments.some((item) => item.loading);
            },
            // 是否可以改变随访时间
            disableChangeDates() {
                return !!this.cacheDatesConfig;
            },
            // 是否可以点击添加自动随访消息模板
            disabledAddAutoVisit() {
                return this.scheduleDates.length === 0;
            },
            // 已经被选中的时间
            selectedDates() {
                const { autoRevisitMessageList = [] } = this.postData || {};
                return autoRevisitMessageList.reduce((arr, item) => {
                    arr.push(...item.scheduleDates);
                    return arr;
                }, []);
            },
            // 禁用提交按钮
            disabledSubmit() {
                if (this.isAutoRevisit) {
                    // 自动随访，不允许消息内容为空
                    if (!this.postData?.taskType && this.postData.autoRevisitMessageList.length === 0) {
                        return true;
                    }
                    if (this.postData?.taskType && !this.postData.autoRevisitMessage) {
                        return true;
                    }
                }
                return !this.isUpdated || this.loadingSave || this.doUploadFileing;
            },

            /**
             * @desc 慢病随访不能编辑更换患者
             * <AUTHOR> Yang
             * @date 2021-04-14 15:45:24
             */
            disabledChangePatient() {
                return this.disabledSelectPatient || this.postData.type === 1;
            },

            /**
             * @desc 能否终止计划
             * <AUTHOR> Yang
             * @date 2021-04-20 14:57:58
             */
            canStopVisitPlan() {
                // 已执行 1  已终止 2
                const { status } = this.taskDetail || {};
                return status !== 1 && status !== 2;
            },
            // 是否禁用编辑按钮
            disabledSaveBtn() {
                const {
                    isUpdateAllow, status,
                } = this.taskDetail || {};
                let message = '';
                if (status === 2) {
                    message = '已终止，不允许修改';
                } else if (status === 1) {
                    message = '任务已被执行不可修改';
                } else if (isUpdateAllow === 0) {
                    message = '任务正在执行不可修改，请终止后重新新建';
                }
                return message;
            },
        },
        watch: {
            selectedPatients(list) {
                if (list && list.length !== 0) {
                    this.errorInfo.error = false;
                }
            },
            scheduleDates() {
                if (this.disableBack) {
                    this.disableBack = false;
                    return;
                }
                if (this.disableChangeDates && this.cacheDatesConfig && this.isAutoRevisit) {
                    // 不允许修改时间时，提示
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '更换随访周期后，设置的消息内容将被清空',
                        onConfirm: () => {
                            this.postData.autoRevisitMessageList = [];
                            this.handleScheduleTimesSave();
                        },
                        onCancel: () => {
                            this.disableBack = true;
                            this.handleScheduleTimesBack();
                        },
                    });
                }
            },
        },
        created() {
            if (this.isCreated) {
                const { id } = this.userInfo;
                this.postData.planExecutorId = id;
                this.postData.finishExecutorId = id;
            } else {
                // 修改
                this.handleScheduleTimesSave();
            }
        },
        async mounted() {
            this.loading = true;
            await this.fetchEmployeeList();
            await this.fetchAutoCreateRulesCount();
            this.loading = false;
        },
        methods: {
            accessAppend,
            openSelectVisitRules() {
                this.isShowSelectVisitRules = true;
            },
            // 获取自动创建任务数量
            async fetchAutoCreateRulesCount() {
                try {
                    const { data } = await CrmAPI.fetchAutoCreateRulesCount();
                    if (data?.schedulingCount) {
                        this.schedulingCount = data.schedulingCount;
                    }
                } catch (e) {
                    console.log('error=', e);
                }

            },
            changeSelectItem() {
                this.errorInfo.error = false;
            },
            // 修改创建每日修改
            updatedCreatedEveryDay(type) {
                this.postData.taskType = type;
            },
            cleanRulesSelect() {
                this.postData.packageFilters = [];
                this.postData.packageParams = {};
            },
            confirmSelect(item) {
                this.postData.packageFilters = item?.packageFilters || [];
                this.postData.packageParams = item?.params || {};
                this.isShowSelectVisitRules = false;
                this.changeSelectItem();
            },
            closeAddDayTimeBox() {
                this.addDayTimeBox = false;
            },
            /**
             * 创建postData数据结构
             * <AUTHOR>
             * @date 2020-10-16
             * @returns {Object}
             */
            getPostData() {
                const currentDay = fecha.format(Date.now(), DATE_FORMATE);
                const name = `${fecha.format(new Date(), 'MM-DD').replace(/-/g,'/')} ${!this.isSupplement ? '随访任务' : '补录随访'}`;
                let sendMsgWay = 1;
                if (this.isOpenScrm) {
                    sendMsgWay = 2;
                }
                const postData = {
                    type: 0, // 随访类型 => 0-诊后随访
                    taskType: 0, // 执行方式 0 手动 1 自动
                    target: '', // 随访目标内容
                    result: '', // 随访结果内容
                    status: 0, // 随访状态 => 0-计划中，1-已完成
                    name, // 默认 日期 + 随访任务afterOutpatientDay: 1,
                    afterOutpatientDay: 0,
                    afterOutpatientDays: [], //就诊第几天随访 单次/多次随访 以天为单位
                    afterOutpatientInitDelay: 0,
                    // 随访频次
                    afterOutpatientFixRate: dateOptions[3].value,
                    count: 1, // 规则随访次数
                    executeMode: 0, // 执行方式 0 人工；1 自动 2 企业微信
                    revisitMode: 1, // 随访方式 => 0-其他，1-微信，2-短信，3-电话，4-面对面
                    attachments: [], // 附件列表 => { url: '', fileName: '', imageWidth: 0, imageHeight: 0, sort: 0 }
                    autoRevisitMessageList: [], // 自动随访消息配置
                    autoRevisitMessage: '',
                    planExecutorId: '', // 计划中执行人id
                    finishExecutorId: '', // 已完成执行人id

                    visitRate: 0, // 随访时间 => 0-单次随访，1-周期随访，2-批量随访
                    visitDate: currentDay,
                    visitSendMsgWay: sendMsgWay,//1微信 2企业微信
                    // 单次随访时间
                    singleVisitDate: currentDay,
                    // 周期随访频次：默认一周
                    dateCount: dateOptions[3].value,
                    cycleDates: [currentDay, fecha.format(Date.now() + MONTH_TIMES, DATE_FORMATE)],
                    // 多次随访时间, 默认一个今天
                    manyDates: [],
                    revisitFormSheets: [],
                    packageFilters: [
                    ],
                    packageParams: null,
                    packageParamsCache: null,
                };

                if (this.isSupplement) {
                    postData.status = 1;
                }
                if (this.taskDetail) {
                    const {
                        type, // 随访类型 => 0-诊后随访
                        taskType,
                        executeMode, // 执行方式 0 人工；1 自动
                        target, // 随访目标内容
                        executorId, // 执行人
                        autoRevisitMessageList, // 自动随访消息配置
                        scheduleDatesConfig, // 计划日期选择配置
                        evaluationRecordId,
                        revisitFormSheets = [], // 随访表
                        patientQueryParam, // 存储筛选规则
                        name,
                    } = this.taskDetail;
                    postData.type = type || 0;
                    postData.name = name || '';
                    postData.taskType = taskType;
                    postData.executeMode = executeMode;
                    if (executeMode > 0) {
                        postData.visitSendMsgWay = executeMode;
                        if (executeMode === 2) {
                            postData.executeMode = 1;
                        }
                    }
                    postData.packageParams = clone(patientQueryParam);
                    postData.packageParamsCache = clone(patientQueryParam);
                    const displayText = patientQueryParam?.displayText;
                    if (displayText) {
                        postData.packageFilters = JSON.parse(displayText);
                    }
                    if (executeMode === 0) {
                        // 人工随访
                        postData.target = target;
                        postData.planExecutorId = executorId;
                        postData.finishExecutorId = executorId;
                    } else if (autoRevisitMessageList?.length) {
                        const autoRevisitMessageListCache = clone(autoRevisitMessageList);
                        // 自动随访
                        postData.autoRevisitMessageList = autoRevisitMessageListCache?.map((item) => {
                            item.sendHourOfDay = item.sendHourOfDay.slice(0, 5);
                            item.scheduleDates = item.scheduleDates?.map((item) => item.slice(0, 10));
                            return item;
                        });

                        if (taskType) {
                            postData.autoRevisitMessage = autoRevisitMessageList[0].message;
                        }
                    }
                    if (scheduleDatesConfig) {
                        Object.assign(postData, scheduleDatesConfig);
                        postData.afterOutpatientDay = scheduleDatesConfig.afterOutpatientDays?.length ? scheduleDatesConfig.afterOutpatientDays[0] : null;
                        postData.afterOutpatientInitDelay = scheduleDatesConfig.afterOutpatientInitDelay || 0;
                        postData.afterOutpatientFixRate = scheduleDatesConfig.afterOutpatientFixRate || dateOptions[3].value;
                        postData.count = scheduleDatesConfig.count || 1;
                        postData.afterOutpatientDays = scheduleDatesConfig.afterOutpatientDays || [];
                    }
                    postData.evaluationRecordId = evaluationRecordId;
                    postData.revisitFormSheets = revisitFormSheets;
                } else if (this.defaultTaskDetail) {
                    const {
                        type, // 随访类型 => 0-诊后随访
                        executeMode, // 执行方式 0 人工；1 自动
                        target = '', // 随访目标内容
                        executorId, // 执行人
                        scheduleDatesConfig, // 计划日期选择配置
                        evaluationRecordId, // 慢病评估表id
                        revisitFormSheets = [], // 随访表
                        taskType,
                    } = this.defaultTaskDetail;
                    postData.type = type || 0;
                    postData.executeMode = executeMode;
                    if (executeMode > 0) {
                        postData.visitSendMsgWay = executeMode;
                        if (executeMode === 2) {
                            postData.executeMode = 1;
                        }
                    }
                    postData.target = target;
                    postData.planExecutorId = executorId;
                    postData.finishExecutorId = executorId;
                    if (scheduleDatesConfig) {
                        const formatScheduleDatesConfig = scheduleDatesConfig;
                        const {
                            visitRate,
                            singleVisitDate,
                            dateCount,
                            cycleDates,
                            manyDates,
                            afterOutpatientDays,
                            afterOutpatientInitDelay,
                            afterOutpatientFixRate,
                            count,
                        } = formatScheduleDatesConfig;
                        // 是规则创建
                        if (taskType) {
                            console.log('afterOutpatientDays2', afterOutpatientDays);
                            if (visitRate === 0) {
                                postData.afterOutpatientDay = afterOutpatientDays?.length ? afterOutpatientDays[0] : null;
                            } else if (visitRate === 1) {
                                postData.afterOutpatientInitDelay = afterOutpatientInitDelay;
                                postData.afterOutpatientFixRate = afterOutpatientFixRate;
                                postData.count = count;
                            } else {
                                postData.afterOutpatientDays = afterOutpatientDays;
                            }
                        }
                        Object.assign(postData, {
                            visitRate,
                            singleVisitDate: singleVisitDate || currentDay,
                            dateCount: dateCount || dateOptions[3].value,
                            cycleDates: cycleDates || [
                                currentDay,
                                fecha.format(Date.now() + MONTH_TIMES, DATE_FORMATE),
                            ],
                            manyDates: manyDates || [],
                        });
                    }
                    postData.evaluationRecordId = evaluationRecordId;
                    postData.revisitFormSheets = revisitFormSheets;
                }
                console.log(postData);
                return postData;
            },
            /**
             * 当调用模板后填充内容
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Number} type 模板类型
             * @param {String} content 内容
             */
            handleFillContent(type, content) {
                switch (type) {
                    case DialogTemplateList.options.TARGET.value:
                        this.postData.target = content;
                        this.$nextTick(() => {
                            const visitTarget = this.$refs['visit-target'];
                            visitTarget && visitTarget.validate && visitTarget.validate();
                        });
                        break;
                    case DialogTemplateList.options.RESULT.value:
                        this.postData.result = content;
                        this.$nextTick(() => {
                            const visitResult = this.$refs['visit-result'];
                            visitResult && visitResult.validate && visitResult.validate();
                        });
                        break;
                    case DialogTemplateList.options.MESSAGE.value:
                        if (this.postData.taskType) {
                            this.postData.autoRevisitMessage = content;
                        }

                        break;
                    default:
                        break;
                }
            },
            /**
             * 拉取门店下成员列表
             * <AUTHOR>
             * @date 2020-10-16
             */
            async fetchEmployeeList() {
                try {
                    const { data } = await CrmAPI.fetchEmployeeList();
                    this.employeeList = data.rows;
                } catch (error) {
                    console.log('fetchEmployeeList error', error);
                }
            },
            /**
             * 随访人筛选
             * <AUTHOR>
             * @date 2020-10-16
             * @param {String} keyword 关键词
             */
            fetchSuggestionPlans(keyword) {
                this.keywordPlan = keyword;
            },
            /**
             * 随访人筛选
             * <AUTHOR>
             * @date 2020-10-16
             * @param {String} keyword 关键词
             */
            fetchSuggestions(keyword) {
                this.keyword = keyword;
            },
            /**
             * 删除随访患者
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Number} index 索引
             */
            onClickDelete(index, type = 'simple') {
                if (type === 'all') {
                    this.$emit('updateSelectPatients', []);
                    return false;
                }
                const selectedPatients = clone(this.selectedPatients);
                selectedPatients.splice(index, 1);
                this.$emit('updateSelectPatients', selectedPatients);
            },
            /**
             * 不可选过去的时间
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Date} date 当前时间对象验证
             * @returns {Boolean} false 禁用；true 可用
             */
            disabledPreviewDate(date) {
                return date.getTime() < Date.now() - DAY_TIMES;
            },
            /**
             * 不可选将来的时间
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Date} date 当前时间对象验证
             * @returns {Boolean} false 禁用；true 可用
             */
            disabledFutureDate(date) {
                return date.getTime() > Date.now();
            },
            /**
             * 多次随访时间不可选处理
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Date} date 当前时间对象验证
             * @returns {Boolean} false 禁用；true 可用
             */
            disabledDate(date) {
                const { manyDates } = this.postData;
                const dateStr = fecha.format(date, DATE_FORMATE);
                return manyDates.includes(dateStr) || date.getTime() < Date.now() - DAY_TIMES;
            },
            /**
             * 当新增多次随访时间
             * <AUTHOR>
             * @date 2020-10-16
             * @param {String} date 日期
             */
            onInputBatchDate(date) {
                const { manyDates } = this.postData;
                if (!manyDates.includes(date)) {
                    manyDates.push(date);
                }
            },
            /**
             * 前端搜索匹配
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Object} item
             * @param {String} keyword
             * @returns {Boolean} true 匹配上；false 未匹配
             */
            handleFrontFilter(item, keyword) {
                keyword = keyword.toLocaleUpperCase();
                const namePyUpper = item.namePy.toLocaleUpperCase();
                const namePyFirstUpper = item.namePyFirst.toLocaleUpperCase();
                return (
                    (item.name && item.name.indexOf(keyword) !== -1) ||
                    (item.mobile && item.mobile.indexOf(keyword) !== -1) ||
                    (namePyUpper && namePyUpper.indexOf(keyword) !== -1) ||
                    (namePyFirstUpper && namePyFirstUpper.indexOf(keyword) !== -1)
                );
            },
            /**
             * 当点击关闭成员
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Number} index 索引
             */
            onClickCloseEmployee(index) {
                const selectedNotifyEmployee = this.selectedNotifyEmployee.slice();
                selectedNotifyEmployee.splice(index, 1);
                this.$emit('updateSelectedEmployee', selectedNotifyEmployee);
            },
            /**
             * 当点击处理自动消息发送配置
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Object|null} sendMessageConfig 有则修改、无则新增
             * @param {Object|null} index 有则修改、无则新增
             */
            onClickHandleMessage(sendMessageConfig, index) {
                const selectedDates = this.postData.autoRevisitMessageList
                    ?.filter((item) => item !== sendMessageConfig)
                    .reduce((arr, item) => {
                        arr.push(...item.scheduleDates);
                        return arr;
                    }, []);
                const scheduleDates = this.scheduleDates.slice().map((date) => ({
                    date,
                    disabled: selectedDates.includes(date),
                }));
                this.currentUpdateIndex = sendMessageConfig ? index : null;
                this.$emit('addSendMessageConfig', scheduleDates, sendMessageConfig);
            },
            /**
             * 当改变当前发送消息配置时
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Object} sendMessageConfig 当前消息配置
             * @param {Boolean} isDelete 是否删除当前消息配置
             */
            onChangeSendMessageConfig(sendMessageConfig, isDelete) {
                if (isDelete) {
                    // 删除
                    this.postData.autoRevisitMessageList = this.postData?.autoRevisitMessageList.filter(
                        (item) => item !== sendMessageConfig,
                    );
                } else {
                    if (this.currentUpdateIndex === null) {
                        // 新增
                        this.postData.autoRevisitMessageList.push(sendMessageConfig);
                    } else {
                        // 修改
                        Object.assign(this.postData.autoRevisitMessageList[this.currentUpdateIndex], sendMessageConfig);
                    }
                }

                this.handleScheduleTimesSave();
            },
            /**
             * 处理随访时间的保存
             * <AUTHOR>
             * @date 2020-10-16
             */
            handleScheduleTimesSave() {
                if (this.postData?.autoRevisitMessageList?.length === 0) {
                    this.cacheDatesConfig = null;
                } else {
                    // 当有配置消息模板时，不再允许推送时间周期的修改，缓存一份，方便恢复数据
                    this.cacheDatesConfig = this.getDateConfig();
                }
            },
            /**
             * 获取当前时间配置
             * <AUTHOR>
             * @date 2020-10-21
             * @returns {Object}
             */
            getDateConfig() {
                const {
                    visitRate, // 周期类型
                    singleVisitDate, // 单次随访
                    dateCount, // 步长
                    cycleDates, // 周期时间段
                    manyDates, // 多次随访
                    taskType,
                    afterOutpatientDays,
                    afterOutpatientDay,
                    afterOutpatientInitDelay, // 随访频次
                    afterOutpatientFixRate,
                    count, // 规则随访次数
                } = this.postData;

                const dateConfig = {
                    visitRate,
                    singleVisitDate,
                    dateCount,
                    cycleDates,
                    manyDates,
                };
                if (taskType) {
                    if (visitRate === 0) {
                        dateConfig.afterOutpatientDays = [afterOutpatientDay];
                    } else if (visitRate === 1) {
                        dateConfig.afterOutpatientInitDelay = afterOutpatientInitDelay;
                        dateConfig.afterOutpatientFixRate = afterOutpatientFixRate;
                        dateConfig.count = count;
                    } else {
                        dateConfig.afterOutpatientDays = afterOutpatientDays;
                    }
                }
                return dateConfig;
            },
            /**
             * 处理随访时间的恢复
             * <AUTHOR>
             * @date 2020-10-16
             */
            handleScheduleTimesBack() {
                if (this.cacheDatesConfig) {
                    Object.assign(this.postData, this.cacheDatesConfig);
                }
            },
            /**
             * 删除多次随访时间
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Object} {date}
             */
            onDeleteManyDates({ date }) {
                const { manyDates } = this.postData;
                const index = manyDates.indexOf(date);
                index !== -1 && manyDates.splice(index, 1);
            },
            onDeleteVisitCheckbox(value) {
                this.postData.afterOutpatientDays = this.postData.afterOutpatientDays.filter((item) => {
                    return item !== value;
                });
            },
            /**
             * 在弹窗关闭之前调用
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Function} callback 回调
             */
            async beforeCloseDialog(callback) {
                let close = true;
                if (this.isUpdated) {
                    close = await this.handleCloseBeforeAlert();
                }
                callback(close);
            },
            /**
             * 关闭dialog弹窗
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Boolean} value 是否关闭弹窗
             */
            async updateValue(value) {
                if (!value && this.isUpdated) {
                    const close = await this.handleCloseBeforeAlert();
                    close && this.$emit('input', value);
                } else {
                    this.$emit('input', value);
                }
            },
            /**
             * 提示是否内容未保存
             * <AUTHOR>
             * @date 2020-10-16
             * @returns {Promise}
             */
            handleCloseBeforeAlert() {
                return new Promise((resolve) => {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '你填写的内容尚未保存，确定要离开吗 ？',
                        onConfirm: () => resolve(true),
                        onCancel: () => resolve(false),
                    });
                });
            },
            /**
             * 提示确认保存
             * <AUTHOR>
             * @date 2020-10-16
             * @returns {Promise}
             */
            handleAlertSaveData() {
                return new Promise((resolve) => {
                    this.$confirm({
                        title: '提示',
                        content: this.isCreated ? `是否确定${this.isPlanTask ? '创建' : '补录'}本次随访 ？` : '是否确定修改本次随访 ？',
                        onConfirm: () => resolve(true),
                        onCancel: () => resolve(false),
                    });
                });
            },
            /**
             * 获取提交参数
             * <AUTHOR>
             * @date 2020-10-16
             * @returns {Object}
             */
            getParams() {
                const {
                    type, // 随访类型 => 0-诊后随访
                    target, // 随访目标内容
                    result, // 随访结果内容
                    status, // 随访状态 => 0-计划中，1-初始化
                    revisitMode, // 随访方式 => 0-其他，1-微信，2-短信，3-电话，4-面对面
                    attachments, // 附件列表 => { url: '', fileName: '', imageWidth: 0, imageHeight: 0, sort: 0 }
                    autoRevisitMessageList, // 自动随访消息配置
                    autoRevisitMessage,
                    planExecutorId, // 计划中执行人id
                    finishExecutorId, // 已完成执行人id
                    evaluationRecordId, // 从慢病评估中来评估表id
                    revisitFormSheets, // 慢病评估表
                    taskType,
                    name,
                    packageFilters,
                    packageParams,
                } = this.postData;


                let {
                    executeMode, // 执行方式 0 人工；1 自动 2 企业微信自动
                    // eslint-disable-next-line prefer-const
                    visitSendMsgWay = 1,
                } = this.postData;
                // 如果选择的非人工 并且 visitSendMsgWay 类型为2
                if (executeMode > 0 && visitSendMsgWay === 2) {
                    executeMode = visitSendMsgWay;
                }
                const params = {
                    evaluationRecordId,
                    revisitFormSheets,
                    type, // 随访类型
                    taskType, // 任务类型
                    executeMode, // 执行方式
                    outpatientSheetId: this.outpatientSheetId, // 关联门诊单
                };

                if (this.selectedPatients) {
                    params.patientIds = this.selectedPatients.map((item) => item.id); // 随访患者ids
                }

                params.name = name;
                // 时间选择习惯
                const scheduleDatesConfig = this.getDateConfig();
                params.scheduleDatesConfig = scheduleDatesConfig;

                if (this.isManualRevisit) {
                    // 人工
                    params.status = status; // 随访状态
                    params.target = target.replace(/(\n)+/gi, '\n'); // 随访目标内容 对textarea的输入，多个换行 => 一个换行
                    params.scheduleDates = this.scheduleDates.slice(); // 随访时间列表
                    params.notifyEmployeeList = this.selectedNotifyEmployee.map((item) => item.id); // 通知人员

                    if (this.isPlanTask) {
                        // 计划随访
                        params.executorId = planExecutorId; // 执行人
                    } else {
                        // 已完成随访
                        params.executorId = finishExecutorId; // 执行人
                        params.revisitMode = revisitMode; // 随访方式
                        params.result = result.replace(/(\n)+/gi, '\n'); // 随访结果内容 对textarea的输入，多个换行 => 一个换行
                        if (attachments.length !== 0) {
                            // 附件
                            params.attachments = attachments.map((item, index) => ({
                                sort: index,
                                url: item.url,
                                fileName: item.fileName,
                                imageWidth: item.imageWidth,
                                imageHeight: item.imageHeight,
                            }));
                        }
                    }
                } else {
                    // 自动
                    params.scheduleDates = this.scheduleDates.slice(); // 随访时间列表
                    // 如果是规则创建
                    if (taskType) {
                        params.autoRevisitMessageList = [{
                            message: autoRevisitMessage,
                        }];
                    } else {
                        params.autoRevisitMessageList = autoRevisitMessageList; // 自动随访消息配置
                    }

                }

                if (taskType) {
                    params.patientQueryParam = {
                        ...packageParams,displayText: JSON.stringify(packageFilters),
                    };
                }
                return params;
            },
            /**
             * 点击保存提交时
             * <AUTHOR>
             * @date 2020-10-16
             */
            onClickSubmit() {
                if (!this.postData.taskType && (!this.selectedPatients || this.selectedPatients?.length === 0)) {
                    // 随访患者至少一个
                    this.errorInfo.error = true;
                    this.$refs['execution_mode--box'].isHover = true;
                    this.errorInfo.message = '请选择随访患者';
                    return;
                }

                if (this.postData.taskType && !this.postData.packageParams) {
                    this.$refs['execution_mode--box'].isHover = true;
                    this.errorInfo.error = true;
                    this.errorInfo.message = '请选择随访规则';
                    return;
                }
                if (!this.postData.taskType && this.scheduleDates.length === 0) {
                    return this.$Toast({
                        type: 'error',
                        message: '请选择随访时间',
                    });
                }
                if (this.postData.taskType && this.scheduleRules) {
                    if ([0, 2].includes(this.scheduleRules?.visitRate) && !this.scheduleRules?.afterOutpatientDays?.length) {
                        return this.$Toast({
                            type: 'error',
                            message: '请选择随访时间',
                        });
                    }
                    if (this.scheduleRules?.visitRate === 1 && !this.scheduleRules?.count) {
                        return this.$Toast({
                            type: 'error',
                            message: '请填写随访次数',
                        });
                    }
                }
                if (this.isAutoRevisit) {
                    // 自动随访
                    if (!this.postData.taskType && this.selectedDates.length !== this.scheduleDates.length) {
                        return this.$Toast({
                            type: 'error',
                            message: '有随访时间未被设置自动推送',
                        });
                    }
                    if (this.postData.taskType && !this.postData.autoRevisitMessage) {
                        return this.$Toast({
                            type: 'error',
                            message: '有随访时间未被设置自动推送',
                        });
                    }
                }
                this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        const confirm = await this.handleAlertSaveData();
                        if (confirm) {
                            this.loadingSave = true;
                            if (this.isCreated) {
                                await this.handleCreateRevisit();
                            } else {
                                await this.handleUpdateRevisit();
                            }
                            this.loadingSave = false;
                        }
                    }
                });
            },
            /**
             * 新增发起请求
             * <AUTHOR>
             * @date 2020-10-16
             */
            async handleCreateRevisit() {
                try {
                    const params = this.getParams();
                    const { data } = await CrmAPI.createPatientRevisitV2(params);
                    this.$Toast({
                        type: 'success',
                        message: '新增成功',
                    });
                    this.$emit('create-success', data);
                    this.$emit('input', false);
                } catch (error) {
                    console.log('handleCreateRevisit error', error);
                }
            },
            /**
             * 修改发起请求
             * <AUTHOR>
             * @date 2020-10-16
             */
            async handleUpdateRevisit() {
                try {
                    const params = this.getParams();
                    const { data } = await CrmAPI.updatePatientRevisit(this.taskId, params);
                    this.$Toast({
                        type: 'success',
                        message: '修改成功',
                    });
                    this.$emit('update-success', data);
                    this.$emit('input', false);
                } catch (error) {
                    console.log('handleCreateRevisit error', error);
                }
            },
            /**
             * 点击上传
             * <AUTHOR>
             * @date 2020-10-16
             */
            onClickUpload() {
                if (this.postData.attachments.length >= 5) {
                    return this.$Toast({
                        type: 'error',
                        message: '附件上传限制5个',
                    });
                }
                const uploadFileRef = this.$refs['upload-file'];
                uploadFileRef && uploadFileRef.click();
            },
            /**
             * 更新一下上传节点
             * <AUTHOR>
             * @date 2020-10-16
             */
            updateUploadNode() {
                this.showUploadNode = false;
                this.$nextTick(() => {
                    this.showUploadNode = true;
                });
            },
            /**
             * 当有上次文件选中时
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Event} event 文件上传事件
             */
            async onChangeUploadFile(event) {
                const UUID = createGUID();
                const file = event.target.files[0];
                this.updateUploadNode();
                if (file) {
                    const ext = this.getExtByFile(file);
                    const errMsg = this.handleCheckFile(file, ext);
                    if (errMsg) {
                        return this.$Toast({
                            type: 'error',
                            message: errMsg,
                        });
                    }
                    const info = await this.handleUploadFileInfo(file, ext);
                    this.postData.attachments.push({
                        id: UUID,
                        type: this.getFileType(ext),
                        fileUrl: info.url,
                        fileName: file.name,
                        imageWidth: info.width || '',
                        imageHeight: info.height || '',
                        percentage: 0,
                        loading: true,
                    });
                    const index = this.postData.attachments.findIndex((item) => item.id === UUID);
                    const target = this.postData.attachments[index];
                    const [errMsg1, url] = await this.handleFileUpload(file);
                    if (errMsg1) {
                        this.postData.attachments.splice(index, 1);
                        return this.$Toast({
                            type: 'error',
                            message: errMsg1,
                        });
                    }
                    target.url = url;
                    target.percentage = 100;
                    const _timer = setTimeout(() => {
                        target.loading = false;
                        clearTimeout(_timer);
                    }, 500);

                }
            },
            /**
             * 删除已上传文件
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Object} target 指定文件
             */
            onClickDeleteFile(target) {
                const index = this.postData.attachments.findIndex((item) => item.id === target.id);
                if (index !== -1) {
                    this.postData.attachments.splice(index, 1);
                }
            },
            /**
             * 附件，类型分组
             * <AUTHOR>
             * @date 2020-10-16
             * @param {Array} attachments 附件列表
             * @returns {Object}
             */
            getAttachmentGroup(attachments) {
                const images = [];
                const others = [];
                attachments.forEach((item) => {
                    const ext = this.getExtByFileName(item.fileName);
                    const type = this.getFileType(ext);
                    item.type = type;
                    if (type === 'image') {
                        images.push(item);
                    } else {
                        others.push(item);
                    }
                });
                return {
                    images, others,
                };
            },

            /**
             * @desc 查看随访表单
             * <AUTHOR> Yang
             * @date 2021-04-14 14:47:28
             */
            handleFormSheetClick(form) {
                GenerateFormDialog({
                    formTemplateId: form.formId,
                    disabled: true,
                    canPrintForm: false,
                });
            },

            handleStopPlanClick() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确定终止未执行的随访计划 ？',
                    onConfirm: () => {
                        this.handleStopRevisit();
                    },
                });
            },
            handleDeletePlanClick() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确定删除整个随访计划 ？',
                    onConfirm: () => {
                        this.handleDeleteRevisit();
                    },
                });
            },
            async handleStopRevisit() {
                try {
                    await CrmAPI.stopPatientRevisit(this.taskId);
                    this.$Toast({
                        type: 'success',
                        message: '终止成功',
                    });
                    this.$emit('stop-success');
                    this.$emit('input', false);
                } catch (error) {
                    console.log('handleStopRevisit error', error);
                }
            },
            async handleDeleteRevisit() {
                try {
                    await CrmAPI.deletePatientRevisit(this.taskId);
                    this.$Toast({
                        type: 'success',
                        message: '删除成功',
                    });
                    this.$emit('delete-success');
                    this.$emit('input', false);
                } catch (error) {
                    console.log('handleDeleteRevisit error', error);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .dialog-addvisit {
        ::v-deep .abc-form-item-label {
            color: $T2 !important;
        }

        section {
            padding: 24px 0;

            .abc-form-item {
                margin-bottom: 16px;
            }

            .select-patient {
                box-sizing: border-box;
                min-height: 32px;
                max-height: 100px;
                overflow: auto;
                cursor: pointer;
                border-radius: 2px;

                .placeholder {
                    padding-left: 7px;
                    font-size: 14px;
                    line-height: 30px;
                    color: $T3;
                }
            }

            .select-patient.disabled {
                cursor: not-allowed;
                background-color: $abcBgDisabled;

                &:hover {
                    border-color: $P1;
                }
            }

            .is-error .select-patient {
                z-index: 2;
                background-color: #fef7e9;
                border-color: $Y2 !important;
            }

            .revisit-form-sheets {
                display: flex;
                align-items: center;
                margin-bottom: 16px;

                .label {
                    width: 80px;
                    color: $T2;
                }

                .content {
                    flex: 1;
                }

                .revisit-form {
                    display: inline-flex;
                    align-items: center;
                    padding: 8px;
                    cursor: pointer;
                    background: $S2;
                    border: 1px solid $P6;
                    border-radius: var(--abc-border-radius-small);

                    i.iconfont {
                        margin-right: 12px;
                        color: $B1;
                    }

                    &:hover {
                        background: $P4;
                        border: 1px solid #8d9aa8;
                    }
                }
            }

            .abc-form-item.visit-target,
            .abc-form-item.visit-result {
                align-items: flex-start;
                margin-bottom: 0;

                .abc-form-item-label {
                    line-height: 32px;
                }
            }

            .abc-form-item.execution_mode--box {
                display: flex;
                align-items: flex-start;

                .abc-form-item-label {
                    height: 24px;
                    line-height: 24px;
                }

                .abc-radio-group {
                    @include flex(row, flex-start, center);

                    flex-direction: column;
                    align-items: flex-start;

                    .execution_mode--radio {
                        display: flex;
                        align-items: center;
                        height: 24px;
                        margin-left: 0;
                        font-size: 14px;
                        line-height: 24px;
                        color: $S1;

                        div {
                            display: inline-block;
                        }
                    }

                    .execution_mode--choice {
                        padding-left: 16px;
                        margin-top: 8px;
                    }

                    .execution_mode--desc {
                        height: 16px;
                        font-size: 12px;
                        line-height: 16px;
                        color: $T3;
                    }

                    .execution_mode--patient {
                        max-width: 100%;
                        min-height: 32px;
                        margin-top: 4px;
                        background: $P5;
                        border-radius: var(--abc-border-radius-small);

                        .item-selected-visit {
                            display: inline-block;
                            float: left;
                            height: 26px;
                            padding: 0 3px 0 7px;
                            margin: 2px 3px 2px 0;
                            font-size: 12px;
                            color: $T1;
                            cursor: pointer;
                            background-color: $P5;

                            @include flex(row, center, center);

                            .close {
                                width: 20px;
                                height: 20px;

                                @include flex(row, center, center);

                                .iconfont {
                                    margin-left: 7px;
                                    font-size: 16px;
                                    color: $T3;
                                }
                            }

                            &:hover .iconfont {
                                color: $T1;
                            }
                        }
                    }

                    .execution_mode--filter {
                        display: flex;
                        align-items: center;
                        min-width: 130px;
                        padding: 6px 8px;
                        font-size: 14px;
                        line-height: 20px;
                        color: $S1;
                        cursor: pointer;
                        border-radius: 2px;

                        &-content {
                            flex: 1;

                            @include ellipsis;
                        }

                        &:hover {
                            ::v-deep .iconfont {
                                color: $T3;
                            }
                        }

                        &-error {
                            background-color: #fef7e9;
                            border: 1px solid $Y2;
                        }
                    }
                }
            }

            .abc-form-item.visit-status,
            .abc-form-item.execute-mode {
                height: 20px;

                .abc-radio-group {
                    @include flex(row, flex-start, center);

                    .radio-desc {
                        flex: 1;

                        @include flex(row, flex-start, center);

                        > span {
                            margin-left: 8px;
                            font-size: 12px;
                            color: $T3;
                        }
                    }

                    .abc-radio {
                        height: 20px;

                        &.abc-tipsy {
                            @include flex(row, flex-start, center);
                        }
                    }
                }
            }

            .abc-form-item.notify-emploee {
                align-items: flex-start;

                .abc-form-item-label {
                    line-height: 20px;
                }

                .select-alert-wrapper {
                    height: 20px;

                    @include flex(row, flex-start, center);

                    > button {
                        margin-right: 8px;
                    }

                    > span {
                        font-size: 12px;
                        color: $T3;
                    }
                }

                .selected-employee {
                    @include flex(row, flex-start, center);

                    flex-wrap: wrap;
                    margin-top: 6px;

                    > div {
                        margin-top: 6px;
                        margin-right: 8px;
                    }
                }
            }

            .abc-form-item.visit-message {
                align-items: flex-start;
                margin-top: 16px;
                margin-bottom: 0;

                .abc-form-item-label {
                    line-height: 20px;
                }

                .select-auto-wrapper {
                    height: 20px;

                    @include flex(row, flex-start, center);

                    > span {
                        margin-right: 8px;
                        color: $T1;
                    }
                }

                .auto-message {
                    @include flex(row, flex-start, flex-start);

                    flex-wrap: wrap;

                    > div {
                        width: 264px;
                        padding: 10px 12px;
                        margin-top: 8px;
                        margin-right: 8px;
                        background-color: $P5;
                        border-radius: var(--abc-border-radius-small);

                        .title-box {
                            @include flex(row, flex-start, flex-start);

                            > span {
                                flex: 1;
                                font-size: 12px;
                                line-height: 16px;
                                color: $T1;

                                @include ellipsis(1);
                            }

                            > button {
                                flex-shrink: 0;
                                height: 16px;
                                margin-left: 16px;
                                font-size: 12px;
                                line-height: 16px;
                            }
                        }

                        .send-content {
                            margin-top: 4px;
                            font-size: 12px;
                            line-height: 16px;
                            color: $T2;

                            @include ellipsis(3);
                        }
                    }

                    > div:nth-child(2n) {
                        margin-right: 0;
                    }
                }
            }

            .template {
                display: flex;
                padding-left: 80px;
                margin-top: 8px;
                margin-bottom: 16px;

                .link {
                    margin-right: 12px;
                    font-size: 14px;
                    color: $theme1;
                    cursor: pointer;
                }

                .disabled {
                    color: $T3;
                    cursor: not-allowed;
                }
            }

            .line {
                margin: 24px 0;
                border-bottom: 1px solid $P6;
            }

            .abc-form-item.visit-name {
                .abc-form-item-content {
                    @include flex(row, flex-start, center);

                    > .cis-icon-Attention {
                        margin: 0 4px 0 16px;
                        color: $Y2;
                    }

                    > .alert-text {
                        color: $Y2;
                    }
                }
            }

            .abc-form-item.visit-date {
                align-items: flex-start;
                margin-bottom: 0;

                .abc-form-item-label {
                    line-height: 32px;
                }

                .single-visit {
                    height: 32px;

                    @include flex(row, flex-start, center);

                    .label {
                        margin: 0 8px 0 24px;
                    }

                    .birthday-picker {
                        position: relative;
                    }
                }

                .cycle-visit {
                    height: 32px;
                    margin-top: 16px;

                    @include flex(row, flex-start, center);

                    .mei {
                        margin-left: 24px;
                    }

                    .abc-select-wrapper {
                        margin: 0 8px;
                    }
                }

                .date-list {
                    padding: 8px 10px;
                    margin-top: 8px;
                    font-size: 12px;
                    line-height: 16px;
                    color: $T2;
                    background-color: $P5;

                    span {
                        float: left;
                    }
                }

                .batch-visit {
                    height: 32px;
                    margin-top: 16px;

                    @include flex(row, flex-start, center);

                    .add-date {
                        position: relative;
                        width: 30px;
                        height: 100%;
                        margin-left: 8px;
                        font-size: 14px;
                        line-height: 32px;
                        color: $theme1;
                        text-align: center;
                        cursor: pointer;

                        .add-day-time-box {
                            position: absolute;
                            top: 32px;
                            left: 0;
                            z-index: 200;
                            z-index: 9999;
                            width: 200px;
                            height: 392px;
                            background-color: $S2;
                            border: 1px solid $P6;
                            border-radius: 5px;

                            &_check {
                                padding: 12px 12px 0 12px;
                            }

                            .abc-checkbox-group {
                                display: flex;
                                flex-direction: column;
                                align-items: flex-start;
                            }

                            &_button--setting {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                height: 48px;
                                border-top: 1px solid $P6;
                            }
                        }
                    }
                }

                .date-card {
                    flex-wrap: wrap;
                    margin-top: 8px;

                    @include flex(row, flex-start, center);

                    .item {
                        width: 77px;
                        height: 32px;
                        margin-right: 8px;
                        font-size: 12px;
                        color: $T3;
                        cursor: pointer;
                        background-color: $P5;

                        @include flex(row, center, center);

                        .iconfont {
                            margin-left: 8px;
                            font-size: 12px;
                        }
                    }

                    .item-large {
                        width: 112px;
                    }

                    .item:hover {
                        color: $T1;
                    }
                }
            }

            .one {
                margin-top: 24px;

                @include flex(row, space-between, center);
            }

            .hou {
                margin-top: 16px;

                .link {
                    font-size: 14px;
                    color: $theme1;
                    cursor: pointer;
                }

                .hidden-inp {
                    opacity: 0;
                    transform: translateX(100000px);
                }
            }

            .file {
                padding-left: 80px;
                margin-top: 8px;

                > div {
                    display: flex;
                    flex-wrap: wrap;
                }
            }
        }

        .dialog-footer {
            @include flex(row, flex-end, center);

            .abc-button {
                width: 80px;
                min-width: 80px;
                margin-left: 8px;
            }

            .abc-button-blank {
                border-color: $P1;
            }
        }
    }
</style>
