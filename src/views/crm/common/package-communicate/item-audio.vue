<template>
    <div
        :class="['crm_module__package-communicate__view-message__item-audio', isMine ? 'mine' : 'other']"
        @click="onClickPlay"
    >
        <span v-if="isMine" class="duration">{{ duration }}"</span>
        <span class="audio-box" :class="{ playing: playing }"></span>
        <span v-if="!isMine" class="duration">{{ duration }}"</span>
        <div class="audio-bar">
            <audio ref="audioRef" :src="info.audioUrl"></audio>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'ItemAudio',
        props: {
            // 是否我的消息
            isMine: {
                type: Boolean,
                default: false,
            },
            // 语音消息内容
            info: {
                type: Object,
                required: true,
            },
            // 语音正在播放
            playing: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                isError: false,
            };
        },
        computed: {
            // 语音时长
            duration() {
                const { duration = 1 } = this.info || {};
                return Math.ceil(duration / 1000);
            },
        },
        watch: {
            playing(newValue) {
                if (this.isError) {
                    this.$emit('audio-stop');
                    return;
                }
                const { audioRef } = this.$refs;
                if (newValue) {
                    audioRef.currentTime = 0;
                    audioRef.play();
                } else {
                    audioRef.pause();
                }
            },
        },
        mounted() {
            const { audioRef } = this.$refs;
            audioRef.onended = () => {
                this.$emit('audio-stop');
            };
            audioRef.onerror = () => {
                this.$emit('audio-stop');
                this.isError = true;
            };
        },
        methods: {
            /**
             * 点击播放语音
             * <AUTHOR>
             * @date 2020-05-21
             */
            onClickPlay() {
                if (this.playing) {
                    // 正在播放-暂停
                    this.$emit('audio-stop');
                } else {
                    // 没有播放-播放
                    this.$emit('audio-play');
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .crm_module__package-communicate__view-message__item-audio {
        position: relative;
        box-sizing: border-box;
        min-width: 76px;
        max-width: 244px;
        height: 36px;
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        cursor: pointer;
        border-radius: 2px;

        @include flex(row, flex-start, center);

        .audio-bar {
            width: 0;
            height: 0;
            overflow: hidden;
            opacity: 0;
        }

        .audio-box {
            display: inline-block;
            width: 24px;
            height: 24px;
            background-size: cover;
        }

        &.other {
            justify-content: flex-start;
            color: $T1;
            background-color: #ffffff;
            border: 1px solid $P6;

            .audio-box {
                margin-right: 2px;
                background-image: url('~assets/images/audio/<EMAIL>');

                &.playing {
                    animation: audio-playing-right 1.2s ease infinite;
                }
            }

            &::after {
                position: absolute;
                top: 14px;
                left: -5px;
                width: 8px;
                height: 8px;
                content: '';
                background-color: #ffffff;
                border-bottom: 1px solid rgba(218, 219, 224, 1);
                border-left: 1px solid rgba(218, 219, 224, 1);
                border-bottom-left-radius: 2px;
                transform: rotate(45deg);
            }
        }

        &.mine {
            justify-content: flex-end;
            color: #ffffff;
            background-color: $G2;

            .audio-box {
                margin-left: 2px;
                background-image: url('~assets/images/audio/<EMAIL>');

                &.playing {
                    animation: audio-playing 1.2s ease infinite;
                }
            }

            &::after {
                position: absolute;
                top: 14px;
                right: -4px;
                width: 8px;
                height: 8px;
                content: '';
                background-color: $G2;
                border-top-right-radius: 2px;
                transform: rotate(45deg);
            }
        }
    }

    @keyframes audio-playing {
        0%,
        33% {
            background-image: url('~assets/images/audio/<EMAIL>');
        }

        40%,
        60% {
            background-image: url('~assets/images/audio/<EMAIL>');
        }

        67%,
        100% {
            background-image: url('~assets/images/audio/<EMAIL>');
        }
    }

    @keyframes audio-playing-right {
        0%,
        33% {
            background-image: url('~assets/images/audio/<EMAIL>');
        }

        40%,
        60% {
            background-image: url('~assets/images/audio/<EMAIL>');
        }

        67%,
        100% {
            background-image: url('~assets/images/audio/<EMAIL>');
        }
    }
</style>
