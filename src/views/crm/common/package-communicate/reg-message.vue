<template>
    <message-card>
        <main class="registration-message">
            <p>请问您是想预约医生还是咨询项目？</p>
            <section>
                <button>预约医生</button>
                <button>咨询项目</button>
            </section>
        </main>
    </message-card>
</template>

<script>
    import MessageCard from './components/message-card.vue';
    export default {
        name: 'RegMessage',
        components: {
            MessageCard,
        },
    };
</script>

<style lang="scss" scoped>
@import '../../../../styles/abc-common';

.registration-message {
    p {
        margin-top: 0;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
        text-align: justify;
        letter-spacing: 0;
    }

    section {
        display: flex;
        justify-content: space-between;

        button {
            width: 108px;
            height: 28px;
            font-size: 12px;
            color: #007aff;
            background: #ffffff;
            border: 1px solid #007aff;
            border-radius: var(--abc-border-radius-small);
        }
    }
}
</style>
