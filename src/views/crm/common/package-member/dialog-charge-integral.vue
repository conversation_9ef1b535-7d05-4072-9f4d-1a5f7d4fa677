<template>
    <abc-dialog
        title="积分发放"
        :value="true"
        size="medium"
        content-styles="min-height: 234px;"
        append-to-body
        class="crm-module__package-member__charge-integral"
        @input="(val) => $emit('input', val)"
    >
        <crm-box-item
            style="width: 412px; margin-bottom: 16px;"
            :patient="patient"
            is-show-points
            :is-can-see-patient-mobile="isCanSeePatientMobile"
        ></crm-box-item>
        <abc-form
            ref="postData"
            label-position="top"
            item-block
        >
            <abc-form-item label="发放积分" required>
                <abc-input
                    v-model="postData.points"
                    type="number"
                    :max-length="7"
                    :config="{ formatLength: 0 }"
                    :width="412"
                ></abc-input>
            </abc-form-item>
            <abc-form-item label="发放备注" style="margin-bottom: 0;">
                <abc-input v-model="postData.remark" :width="412" :config="{ maxLength: 30 }"></abc-input>
            </abc-form-item>
        </abc-form>
        <template slot="footer">
            <abc-flex justify="flex-end">
                <abc-button :loading="loadingConfirm" :disabled="diabledConfirm" @click="onClickConfirm">
                    发放
                </abc-button>
                <abc-button variant="ghost" @click="$emit('input', false)">
                    取消
                </abc-button>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import CrmBoxItem from './crm-box-item';
    import { paddingMoney } from 'utils';
    import IntegralApi from 'api/marketing/integral.js';

    export default {
        name: 'DialogChargeIntegral',
        components: {
            CrmBoxItem,
        },
        props: {
            patient: {
                type: Object,
                required: true,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                paddingMoney,
                loadingCharge: false,
                loadingConfirm: false,
                postData: {
                    points: '', // 发放积分
                    remark: '', // 发放备注
                },
            };
        },
        computed: {
            diabledConfirm() {
                const { points } = this.postData;
                return !points;
            },
        },
        methods: {
            /**
             * 获取入参数据
             * <AUTHOR>
             * @date 2021-03-10
             * @returns {Object}
             */
            getParams() {
                const {
                    points, // 发放积分
                    remark, // 发放备注
                } = this.postData;
                const params = {
                    points, // 发放积分
                    remark, // 发放备注
                    patientId: this.patient.patientId, // 患者id
                };
                return params;
            },
            /**
             * 当点击确认发放积分
             * <AUTHOR>
             * @date 2021-03-10
             */
            onClickConfirm() {
                this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        this.loadingConfirm = true;
                        try {
                            const params = this.getParams();
                            await IntegralApi.handlePointsAddManual(params);
                            this.$Toast({
                                type: 'success',
                                message: '发放成功',
                            });
                            this.$emit('refresh');
                        } catch (error) {
                            console.log('onClickConfirm error', error);
                        } finally {
                            this.loadingConfirm = false;
                        }

                    }
                });
            },
        },
    };
</script>
