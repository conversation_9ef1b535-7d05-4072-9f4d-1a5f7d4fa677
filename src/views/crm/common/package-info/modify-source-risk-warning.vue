<template>
    <div v-abc-loading="loading" class="warn-confirm">
        <div class="warn-header">
            修改首诊来源后，{{ isExist ? '将会按如下规则发放奖励' : '不再发放老带新活动奖励' }}
        </div>

        <div class="reward-wrapper">
            <div v-if="isExist" class="reward-rules">
                <div v-if="isExist && getRewardStr(referralPatientRewards)" class="rule-item">
                    1.立即给【{{ patientName }}】发放：{{ getRewardStr(referralPatientRewards) }}
                </div>
                <div class="rule-item">
                    {{ referralPatientRewards.length + 1 }}.【{{ patientName }}】后续消费给【{{ newReferrerName }}】发放奖励
                </div>
            </div>

            <div v-if="showRewardsSection" :class="['reward-content',{ 'is-exist-reward-rules': isExist }]">
                <template v-if="patientReward.length || patientName">
                    <span v-if="patientName">当前已奖励给【{{ patientName }}】的</span>
                    <span v-if="patientReward.length" class="desc">{{ getRewardStr(patientReward) }}</span>
                </template>
                <template v-if="referrerReward">
                    {{ patientReward.length && referrerReward ? '以及' : '' }}奖励给【{{ referrerName }}】的
                    <span class="desc">{{ referrerReward.rewardValue }} {{ getRewardTypeStr(referrerReward.rewardType) }}</span>
                </template>
                <span v-if="referrerRewardCoupon" class="desc">{{ referrerRewardCoupon }}</span>
                <span v-if="showRewardsSection">，如需退回，需门店自行线下协商处理</span>
            </div>
        </div>
    </div>
</template>

<script>
    import MarketingAPI from 'api/marketing';
    import {
        PATIENT_REWARD_TYPE, REFERRER_REWARD_TYPE,
    } from 'views/crm/constants';

    export default {
        name: 'ModifySourceRiskWarning',
        props: {
            patientRewardData: {
                type: Object,
                default: () => {},
            },
            referrerId: {
                type: String,
                default: '',
            },
            referrerType: [Number, String],
            newReferrer: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                PATIENT_REWARD_TYPE,
                REFERRER_REWARD_TYPE,
                loading: false,
                referralPatientRewards: [],
                existPromotionResult: {},
            };
        },
        computed: {
            showRewardsSection() {
                return this.patientReward.length || this.referrerReward || this.referrerRewardCoupon;
            },
            isExist() {
                return !!this.existPromotionResult?.isExist;
            },
            patientName() {
                return this.patientRewardData?.patientName || '';
            },
            // 推荐人姓名
            referrerName() {
                return this.patientRewardData?.referrer?.name || '';
            },
            newReferrerName() {
                const len = this.newReferrer?.length || 0;
                return this.newReferrer?.[len - 1]?.label || '';
            },
            // 奖励信息
            patientReward() {
                return this.patientRewardData?.patientReward || [];
            },
            referrerReward() {
                return this.patientRewardData?.referrerReward;
            },
            referrerRewardCoupon() {
                if (this.patientRewardData?.referrerRewardCoupon?.length) {
                    return this.patientRewardData.referrerRewardCoupon.map((item) => `${item.name}*${item.rewardCount}张`).join(',');
                }
                return '';
            },
        },
        async created() {
            this.loading = true;
            try {
                if (this.referrerId) {
                    const params = {
                        referrerId: this.referrerId,
                        referrerType: +this.referrerType,
                        status: 1, // 不传查所有活动；1：只查现在进行中的活动
                    };
                    const { data } = await MarketingAPI.referrer.fetchReferrerPromotionExist(params);
                    this.existPromotionResult = data;
                }
                await this.fetchReferrerActivityDetail(this.existPromotionResult?.latestReferralDetailView?.id);
            } catch (e) {
                console.log(e);
            } finally {
                this.loading = false;
            }
        },
        methods: {
            getRewardTypeStr(type) {
                if (type === REFERRER_REWARD_TYPE.INTEGRAL) return '积分';
                if (type === REFERRER_REWARD_TYPE.COMMISSION) return '提成';
                if (type === REFERRER_REWARD_TYPE.COUPON) return '优惠券';
            },
            async fetchReferrerActivityDetail(id) {
                if (!id) return;
                try {
                    const { data } = await MarketingAPI.referrer.getReferrerDetail(id);
                    this.referralPatientRewards = data.referralPatientRewards;
                } catch (err) {
                    console.log('getReferrerDetail error', err);
                }
            },
            getRewardStr(list) {
                const arr = list?.filter((item) => item.rewardType === PATIENT_REWARD_TYPE.COUPON) ?? [];
                return arr.map((item) => `${item.name || ''}优惠券${item.rewardCount || 0}张`).join('、');
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.warn-confirm {
    width: 420px;

    .reward-wrapper {
        display: flex;
        flex-direction: column;

        .reward-rules {
            width: 420px;
            max-height: 600px;
            padding: 12px;
            margin-top: 8px;
            overflow: auto;
            background: #f7f7f7;
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            .rule-item {
                font-size: 14px;
                line-height: 20px;
                color: $T2;

                & + .rule-item {
                    margin-top: 8px;
                }
            }
        }

        .reward-content {
            margin-top: 12px;
            font-size: 14px;
            line-height: 20px;

            &.is-exist-reward-rules {
                margin-top: 24px;
            }

            .desc {
                color: $T2;
            }
        }
    }
}
</style>
