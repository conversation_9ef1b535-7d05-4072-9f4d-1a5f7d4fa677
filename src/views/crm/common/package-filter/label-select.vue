<template>
    <abc-select
        ref="select"
        :width="getCssStyleVal(width)"
        :max-width="getCssStyleVal(maxWidth)"
        :show-value="showValue"
        custom-class="crm-module__label-select"
        :show-empty="false"
        placeholder="请选择标签"
        @open="openSelect"
    >
        <view-labels
            :editor="false"
            :show-manage="false"
            :selected-ids="selectedIds"
            :disabled-hover="true"
            @change="changeSelected"
        ></view-labels>
    </abc-select>
</template>

<script>
    import ViewLabels from '../package-label/view-labels.vue';
    import clone from 'utils/clone';
    import {
        getCssStyleVal,
    } from 'utils/dom';
    export default {
        name: 'LableSelect',
        components: {
            ViewLabels,
        },
        props: {
            selected: {
                type: Array,
                default: () => [],
            },
            width: {
                type: [String, Number],
                default: '770px',
            },
            maxWidth: {
                type: [String, Number],
                default: '770px',
            },
        },
        data() {
            return {
                cacheSelected: [],
            };
        },
        computed: {
            selectedIds() {
                return this.cacheSelected.map((item) => item.id);
            },
            showValue() {
                return this.selected
                    .map((item) => item.name)
                    .join('、');
            },
        },
        methods: {
            getCssStyleVal,
            /**
             * desc [打开选项列表]
             */
            openSelect() {
                this.cacheSelected = clone(this.selected);
            },
            /**
             * desc [切换标签选中状态]
             */
            changeSelected(item) {
                const index = this.selectedIds.indexOf(item.id);
                if (index === -1) {
                    this.cacheSelected.push(item);
                } else {
                    this.cacheSelected.splice(index, 1);
                }
                this.$emit('change', this.cacheSelected);
            },
            /**
             * desc [关闭选项列表]
             */
            closeOptions() {
                this.$refs.select.outside();
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.crm-module__label-select {
    top: 4px !important;
    width: 442px;
    min-width: 442px !important;

    .view-labels {
        width: 440px;
        max-height: 268px;
        overflow: auto;
    }

    .handler {
        @include flex(row, flex-end, center);

        padding: 12px;
    }
}
</style>
