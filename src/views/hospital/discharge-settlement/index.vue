<template>
    <div>
        <abc-dialog
            ref="dialog"
            v-model="visible"
            :title="isSettled ? '出院明细' : '出院结算'"
            append-to-body
            content-styles="width: 820px;padding: 16px 24px"
            custom-class="discharge-settlement-dialog"
            @close="handleClose"
        >
            <div
                v-abc-loading="contentLoading"
                v-abc-scroll-loader="{
                    methods: fetchHospitalChargeList, isLast: isLast
                }"
                class="dialog-content clearfix"
            >
                <div class="discharge-settlement-header-info">
                    <div class="patient-info">
                        <div class="label">
                            患者：
                        </div>
                        <div class="content">
                            {{ patientInfo.name }}  {{ patientInfo.sex }} {{ formatAge(patientInfo.age) }}
                        </div>
                    </div>

                    <div class="diagnosis-info">
                        <div class="label">
                            诊断：
                        </div>
                        <div class="content ellipsis" :title="diagnosisStr">
                            {{ diagnosisStr }}
                        </div>
                    </div>

                    <div class="discharge-reason">
                        <div class="label">
                            出院原因：
                        </div>
                        <div class="content ellipsis" :title="hospitalInfo.dischargeReason || ''">
                            <abc-form v-if="waitSettle" ref="form">
                                <abc-form-item required>
                                    <abc-select v-model="reason" size="small" :width="114">
                                        <abc-option
                                            v-for="opt in reasonOptions"
                                            :key="opt"
                                            :value="opt"
                                            :label="opt"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-form>
                            <template v-else>
                                {{ hospitalInfo.dischargeReason }}
                            </template>
                        </div>
                    </div>
                </div>

                <hospital-info-card
                    :hospital-info="hospitalInfo"
                    disabled
                    disabled-expand
                    disabled-switch-outpatient-type
                ></hospital-info-card>

                <changhu-settlement-table
                    style="margin-top: 16px;"
                ></changhu-settlement-table>

                <discharge-settlement-table
                    :data="data"
                    :out-diagnosis-data="outDiagnosisData"
                ></discharge-settlement-table>
            </div>
            <div slot="footer" class="dialog-footer">
                <div v-if="!contentLoading">
                    <div class="small-gray">
                        <span>院内：<abc-money :value="hospitalSheetSummary.totalPrice" :is-show-space="true"></abc-money></span>
                    </div>
                    <div class="small-gray">
                        <span>外诊：<abc-money :value="hospitalSheetSummary.externalTotalPrice" :is-show-space="true"></abc-money></span>
                    </div>
                </div>
                <abc-space>
                    <template v-if="!contentLoading">
                        <template v-if="!isSettled">
                            <div class="settle-amount">
                                <span>医保待上报：</span><abc-money :value="hospitalSheetSummary.totalPriceContainExternal" :is-show-space="true"></abc-money>
                            </div>
                            <div class="settle-amount">
                                <span>院内待收：</span><abc-money :value="totalFee" :is-show-space="true"></abc-money>
                            </div>
                        </template>

                        <abc-popover
                            v-if="hospitalTransactions && hospitalTransactions.length > 0"
                            width="265px"
                            placement="top"
                            :open-delay="200"
                            trigger="click"
                            theme="custom"
                            :visible-arrow="false"
                        >
                            <settle-details :details="settlementDetails"></settle-details>
                            <div slot="reference" class="settle-details">
                                明细
                            </div>
                        </abc-popover>

                        <abc-button v-if="isRefund" type="blank" @click="handleClickRenew">
                            重新结算
                        </abc-button>
                        <abc-button v-if="isRefund" type="blank" @click="handleClickReIn">
                            重新入院
                        </abc-button>
                        <abc-button v-else-if="isSettled" type="blank" @click="handleClickRefund">
                            撤销结算
                        </abc-button>
                        <abc-button
                            v-else
                            @click="handleClickConfirm"
                        >
                            {{ isDischarge ? '结算' : '确定' }}
                        </abc-button>
                        <abc-button
                            v-if="showOpenInvoiceBtn"
                            type="blank"
                            @click="openInvoiceDialog"
                        >
                            开票
                        </abc-button>
                        <print-popper
                            v-model="printOpt.printSelect"
                            size="small"
                            :width="64"
                            :options="printOptions"
                            :box-style="{
                                width: '128px', right: 0
                            }"
                            style="margin-left: 0;"
                            disable-setting-item
                            @print="printHandler"
                        >
                        </print-popper>
                    </template>

                    <abc-button type="blank" @click="handleClose">
                        取消
                    </abc-button>
                </abc-space>
            </div>
        </abc-dialog>

        <refund-way-dialog
            v-if="showRefundDialog"
            ref="refundWay"
            v-model="showRefundDialog"
            :patient-name="patientInfo.name"
            :hospital-sheet-id="hospitalSheetId"
            :refund-fee="hospitalSheetSummary.netIncomeFee"
            :refund-data="refundData"
            :receivable-fee="hospitalSheetSummary.receivableFee"
            :net-income-fee="hospitalSheetSummary.netIncomeFee"
            :refund-type="refundType"
            :charge-config="chargeConfig"
            :is-hospital-sheet="!!hospitalInfo"
            :payment-summary-infos.sync="hospitalSheetSummary.paymentSummaryInfos"
            :charge-transactions="hospitalTransactions"
            @finish="refundFinish"
        ></refund-way-dialog>
    </div>
</template>

<script>
    import HospitalInfoCard from 'src/views/hospital/hospital-info-card.vue';
    import DischargeSettlementTable from 'src/views/hospital/discharge-settlement-table.vue';
    import AbcChargeDialog from '@/service/charge/components/dialog-charge';
    const RefundWayDialog = () => import('src/views/cashier/refund-way-dialog.vue');
    import PrintPopper from 'views/print/popper';
    import {
        ChargeType, PayModeEnum, ChargeStatusEnum,
    } from '@/service/charge/constants';
    import {
        formatAge, noop,
    } from 'utils/index';
    import {
        isProductForm, isPrescriptionForm,
    } from 'src/views/cashier/utils/index.js';
    import {
        OutpatientHospitalStatusEnum, HospitalSheetChargeStatus,
    } from '../constants.js';

    import HospitalAPI from 'api/hospital/index.js';
    import ChargeAPI from 'api/charge.js';
    import { SettleStatusEnum } from 'views/hospital/constants.js';
    import { RefundTypeEnum } from '@/service/charge/constants.js';
    import { InvoiceBusinessScene } from 'views/cashier/invoice/constants';
    import { mapGetters } from 'vuex';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import SettleDetails from './settle-details';
    import {
        windowOpen, navigateToInvoiceConfig,
    } from '@/core/navigate-helper';
    import InvoiceDialogOld from 'views/cashier/invoice/shebao-invoice';
    import ChanghuSettlementTable from '@/views/hospital/settlement-table/index.vue';

    export default {
        name: 'DischargeSettlementDialog',
        components: {
            HospitalInfoCard,
            DischargeSettlementTable,
            RefundWayDialog,
            PrintPopper,
            SettleDetails,
            ChanghuSettlementTable,
        },
        props: {
            pcRouterVm: Object,

            // 系统全局 chargeConfig
            chargeConfig: Object,

            hospitalInfo: {
                type: Object,
                required: true,
            },
            onSuccess: Function,
            onClose: {
                type: Function,
                default: noop,
            },
            resolvePromise: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                formatAge,
                visible: false,
                contentLoading: false,
                isLast: false,
                reason: '',
                reasonOptions: [
                    '死亡',
                    '好转',
                    '家属申请终止',
                    '护理机构申请终止',
                    '搬家',
                    '住院',
                    '有效期到期',
                    '其他原因',
                ],
                fetchParams: {
                    offset: 0,
                    limit: 5,
                },
                data: [],
                outDiagnosisData: [],
                chargeStatus: ChargeStatusEnum.UN_CHARGE,
                settleStatus: SettleStatusEnum.WAIT_SETTLE,

                hospitalSheetSummary: {},
                hospitalTransactions: [],
                hospitalOrderId: '',
                showRefundDialog: false,
                refundType: RefundTypeEnum.NORMAL,
                refundData: {},
                printOpt: {
                    printSelect: '',
                    finishSelect: [],
                },
                settlementDetails: {},
                invoiceStatus: 0,
            };
        },
        computed: {
            ...mapGetters([
                'printHospitalBillConfig',
                'printMedicalListConfig',
                'userInfo',
            ]),
            ...mapGetters('invoice', ['isOpenInvoice', 'medicalElectronicAPIConfig', 'writeInvoiceConfig', 'invoiceConfigList']),
            isDischarge() {
                return this.hospitalInfo.status === OutpatientHospitalStatusEnum.DISCHARGE;
            },
            patientInfo() {
                return this.hospitalInfo.patient || {};
            },
            diagnosisStr() {
                const { extendDiagnosisInfos = [] } = this.hospitalInfo;
                if (extendDiagnosisInfos &&
                    Array.isArray(extendDiagnosisInfos) &&
                    extendDiagnosisInfos.filter((it) => it.value.length).length) {
                    const _arr = [];
                    extendDiagnosisInfos.forEach((item) => {
                        item.value.forEach((it) => {
                            it.name && _arr.push(it.name);
                        });
                    });
                    return _arr.join('，');
                }
                return '';
            },

            hospitalSheetId () {
                return this.hospitalInfo.hospitalSheetId;
            },
            showOpenInvoiceBtn() {
                if (this.hospitalInfo) {
                    const {
                        hospitalSheet = {},
                    } = this.hospitalInfo;

                    return [
                        HospitalSheetChargeStatus.CHARGED,
                    ].indexOf(hospitalSheet.status) > -1;
                }
                return false;
            },

            waitSettle() {
                return this.settleStatus === SettleStatusEnum.WAIT_SETTLE;
            },

            isSettled() {
                return this.settleStatus === SettleStatusEnum.SETTLED;
            },
            isShowPrint() {
                return this.hospitalTransactions.find((item) => {
                    return item.payMode === 5;
                });
            },

            printOptions() {
                return [{
                    value: '结算单',
                    disabled: !this.isShowPrint,
                    tips: '',
                }, {
                    value: '医疗收费清单',
                    disabled: !this.isShowPrint,
                    tips: '',
                }];
            },

            isRefund() {
                return this.chargeStatus === ChargeStatusEnum.REFUND;
            },

            totalFee() {
                const {
                    needPayFee,
                    receivedFee,
                } = this.hospitalSheetSummary;
                return this.isSettled ? receivedFee : needPayFee;
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        created() {
            this.initHandler();
        },
        methods: {
            initHandler() {
                this.fetchParams.offset = 0;
                this.data = [];
                this.fetchHospitalChargeList(true);
            },

            async fetchHospitalChargeList(showLoading = false) {
                this.contentLoading = showLoading && true;
                const { data } = await ChargeAPI.fetchHospitalChargeList(this.hospitalSheetId, this.fetchParams);
                this.hospitalSheetSummary = data.hospitalSheetSummary;
                this.hospitalTransactions = data.hospitalTransactions;
                this.invoiceStatus = data.invoiceStatus;
                this.hospitalOrderId = data.hospitalOrderId;
                switch (data.status) {
                    case HospitalSheetChargeStatus.UNCHARGED:
                        this.chargeStatus = ChargeStatusEnum.UN_CHARGE;
                        break;
                    case HospitalSheetChargeStatus.CHARGED:
                        this.chargeStatus = ChargeStatusEnum.CHARGED;
                        break;
                    case HospitalSheetChargeStatus.PART_CHARGED:
                        this.chargeStatus = ChargeStatusEnum.PART_CHARGED;
                        break;
                    case HospitalSheetChargeStatus.PART_REFUNDED:
                        this.chargeStatus = ChargeStatusEnum.PART_REFUND;
                        break;
                    case HospitalSheetChargeStatus.REFUNDED:
                        this.chargeStatus = ChargeStatusEnum.REFUND;
                        break;
                    default:
                        this.chargeStatus = ChargeStatusEnum.UN_CHARGE;
                        break;
                }
                this.settleStatus = data.settleStatus;
                if (this.data.length >= data.total) {
                    this.isLast = true;
                }
                if (data.offset === this.fetchParams.offset) {
                    this.formatChargeSheetHandler(data.rows);
                    this.fetchParams.offset += this.fetchParams.limit;
                    this.data = this.data.concat(data.rows || []);
                }
                this.settlementDetails = {
                    chargedTime: data.chargedTime,
                    chargedBy: data.chargedBy,
                    hospitalSheetSummary: data.hospitalSheetSummary,
                    hospitalTransactions: data.hospitalTransactions,
                };
                this.contentLoading = false;
                this.outDiagnosisData = data?.longCareOutpatientChargeSheet?.outpatientChargeSheetItems || [];

                this.$nextTick(() => {
                    this.$refs.dialog.updateDialogHeight();
                });
            },

            formatChargeSheetHandler(data) {
                data.forEach((chargeSheet) => {
                    chargeSheet.chargeForms.forEach((form, formIndex) => {
                        if (isProductForm(form)) {
                            form.formSortIndex = (form.sort || formIndex) + 10;
                        } else if (isPrescriptionForm(form)) {
                            form.formSortIndex = (form.sort || formIndex) + 100;
                        }
                    });
                    chargeSheet.chargeForms.sort((a, b) => a.formSortIndex - b.formSortIndex);
                    let sortCount = 1;
                    chargeSheet.chargeForms.forEach((form) => {
                        form.chargeFormItems.forEach((item) => {
                            this.$set(item, 'sortIndex', sortCount);
                            sortCount++;
                        });
                    });
                });
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            async handleClickConfirm() {
                if (this.isDischarge) {
                    this.cashierDischargeHandler();
                } else {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            this.outpatientDischargeHandler();
                        }
                    });
                }

            },
            async outpatientDischargeHandler() {
                await HospitalAPI.hospitalDischarge(this.hospitalInfo.id, {
                    dischargeReason: this.reason,
                });
                this.handleClose();
                this.onSuccess();
                this.$Toast({
                    message: '出院结算成功',
                    type: 'success',
                });
            },

            cashierDischargeHandler() {
                this._chargeDialogInstance = new AbcChargeDialog({
                    chargeType: ChargeType.HOSPITAL,
                    pcRouterVm: this.pcRouterVm,
                    chargeStatus: this.chargeStatus,
                    chargeSheetSummary: this.hospitalSheetSummary,
                    hospitalSheetId: this.hospitalSheetId,
                    disableAdjustmentBtn: true,
                    disablePrintBtn: true,
                    disableDispensingBtn: true,
                    hiddenPayModeList: [
                        PayModeEnum.MEMBER_CARD,
                        PayModeEnum.PATIENT_CARD,
                        PayModeEnum.ARREARS,
                    ],
                    onChargeSuccess: () => {
                        this.onSuccess();
                    },
                    onClose: () => {
                        this.initHandler();
                    },
                });
                this._chargeDialogInstance.generateDialog({
                    parent: this,
                });
            },

            /**
             * @desc 发起重新结算
             * <AUTHOR>
             * @date 2022-02-24 18:03:43
             */
            async handleClickRenew() {
                try {
                    await ChargeAPI.renewHospitalChargeSheet(this.hospitalSheetId);
                    this.initHandler();
                    this.hospitalInfo.status = OutpatientHospitalStatusEnum.DISCHARGE;
                    this.onSuccess();
                } catch (err) {
                    console.error(err);
                }
            },

            /**
             * @desc 点击撤销结算按钮
             * <AUTHOR>
             * @date 2022-02-22 17:18:58
             */
            handleClickRefund() {
                this.showRefundDialog = true;
            },
            refundFinish() {
                this.showRefundDialog = false;
                this.handleClose();
                this.onSuccess();
                this.$Toast({
                    message: '撤销结算成功',
                    type: 'success',
                });
            },
            /**
             * @desc 重新入院
             * <AUTHOR>
             * @date 2022/04/19 17:34:21
             */
            async handleClickReIn() {
                try {
                    await HospitalAPI.renInHospital(this.hospitalInfo.id);
                    this.handleClose();
                } catch (err) {
                    console.error(err);
                }
            },
            printHandler(selected) {
                if (selected.includes('结算单')) {
                    this.printSocialSettlementSheet(selected);
                }
                if (selected.includes('医疗收费清单')) {
                    this.printFeeList();
                }
            },
            async printSocialSettlementSheet(selected) {
                const query = {
                    name: this.patientInfo?.name,
                    hospitalSheetId: this.hospitalOrderId,
                };
                if (!selected.includes('结算单')) {
                    return;
                }
                const printResponse = await this.$abcSocialSecurity.longCareSheetPrint(query);
                if (printResponse.status === false) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: printResponse.message || '调用结算单打印出错',
                    });
                }
                const url = printResponse.data || {};
                if (url) {
                    const baseUrl = `data:application/pdf;base64,${url}`;
                    windowOpen().document.write(`<iframe width='100%' height='100%' src='${baseUrl}'></iframe>`);
                }
            },
            /**
             * @Description: 住院开票弹窗
             * <AUTHOR> Cai
             * @date 2022/07/18 11:49:09
            */

            async openInvoiceDialog() {
                const _trans = this.chargeTransactions && this.chargeTransactions.find((item) => {
                    return item.payMode === 5 && item.thirdPartyPayInfo;
                });
                let buyerName = '';
                let disabledBuyerName = false;
                if (_trans) {
                    disabledBuyerName = true;
                    buyerName = _trans.thirdPartyPayInfo.cardOwner || '';
                }
                buyerName = buyerName || this.patientInfo.name;

                await new InvoiceDialogOld({
                    chargeSheetId: this.hospitalSheetId,
                    chargeStatus: this.chargeStatus,
                    patientInfo: {
                        patientId: this.patientInfo.id,
                        buyerPhone: this.patientInfo.mobile,
                        idCard: this.patientInfo.idCard,
                        buyerName,
                        disabledBuyerName,
                    },
                    printBillConfig: this.printHospitalBillConfig,
                    printMedicalListConfig: this.printMedicalListConfig,
                    medicalElectronicAPIConfig: this.medicalElectronicAPIConfig,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    invoiceConfigList: this.invoiceConfigList,
                    isOpenInvoice: this.isOpenInvoice,
                    invoiceStatus: this.invoiceStatus,
                    userInfo: this.userInfo,
                    businessType: InvoiceBusinessScene.HOSPITAL_CHARGE,
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                    updateInvoiceStatus: (status) => {
                        this.invoiceStatus = status;
                    },

                }).generateDialog({ parent: this });
            },
            /**
             * @Description: 打印医疗清单
             * <AUTHOR> Cai
             * @date 2022/07/29 15:40:06
            */
            async printFeeList() {
                const { data = {} } = await ChargeAPI.fetchChanghuPrint(this.hospitalSheetId);
                console.log(data);
                const postData = {
                    ...data,
                    diagnosisInfos: data.hospitalPatientOrder?.extendDiagnosisInfos[0]?.value,
                    chargedByName: data.lastChargedByName,
                    chargedTime: data.lastChargedTime,
                    feeListType: 'hospital',
                };
                const { format } = this.printMedicalListConfig;
                if (!format) {
                    return console.error('没有format，无法挂载AbcPint');
                }
                const templateName = `medicalFeeList${format[0].toUpperCase()}${format.slice(1)}`;
                console.log(`正在初始化模板:[${templateName}]`);
                await AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates[templateName],
                    printConfigKey: ABCPrintConfigKeyMap.feeList,
                    data: postData,
                    extra: {
                        $abcSocialSecurity: this.$abcSocialSecurity,
                    },
                    isDevTools: false,
                });
            },

            handleClose() {
                this.visible = false;
                if (typeof this.resolvePromise.resolve === 'function') {
                    this.resolvePromise.resolve();
                }
                if (typeof this.onClose === 'function') {
                    this.onClose();
                }
            },
        },
    };
</script>
<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';

    .discharge-settlement-dialog {
        .abc-form-item {
            margin-right: 0;
            margin-bottom: 0;
        }

        .discharge-settlement-header-info {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .patient-info,
            .discharge-reason {
                width: 25%;
                padding: 0 8px;
            }

            .discharge-reason {
                padding: 0 0 0 8px;
            }

            .diagnosis-info {
                width: 50%;
                padding: 0 8px;
            }

            > div {
                display: flex;
                align-items: center;

                .label {
                    color: $T2;
                }
            }
        }

        .dialog-footer {
            justify-content: space-between !important;

            >div {
                display: flex;
                align-items: center;
            }
        }

        .small-gray {
            font-size: 12px;
            color: $T2;

            &:first-child {
                margin-right: 16px;
            }
        }

        .settle-amount {
            font-size: 16px;
            color: $Y2;

            span {
                font-size: 14px;
                color: $T2;
            }
        }

        .settle-details {
            font-size: 14px;
            color: $T2;
            cursor: pointer;
        }
    }
</style>
