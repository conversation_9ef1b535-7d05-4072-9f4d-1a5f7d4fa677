<template>
    <div class="abc-charge-table discharge-settlement-table">
        <div class="table-header">
            <div class="th">
                <!--<h5>结算项目</h5>-->
                <abc-tabs
                    v-model="curTab"
                    :option="tabOptions"
                    size="middle"
                    :border-style="{
                        borderBottom: 'none'
                    }"
                >
                </abc-tabs>
            </div>
            <div class="th date">
                医保
            </div>
            <div class="th date">
                时间
            </div>
            <div class="th unitPrice">
                单价
            </div>
            <div class="th unitCount">
                数量
            </div>
            <div class="th totalPrice">
                金额
            </div>
        </div>

        <div class="table-body" style="min-height: 36px; max-height: 400px; overflow: scroll;">
            <template v-if="curTab === 1">
                <template v-for="chargeSheet in data">
                    <template v-for="form in chargeSheet.chargeForms">
                        <goods-readonly-charge-table-tr
                            v-for="(item, index) in form.chargeFormItems"
                            :key="`${form.id}${item.keyId }${ index}`"
                            :item-index="index"
                            :item="item"
                            :is-charged="isSheetCharged(chargeSheet)"
                            :source-form-type="form.sourceFormType"
                            show-date-col
                            :shebao-card-info="shebaoCardInfo"
                        ></goods-readonly-charge-table-tr>
                    </template>
                </template>
            </template>
            <template v-if="curTab === 2">
                <goods-readonly-charge-table-tr
                    v-for="(item, index) in outDiagnosisData"
                    :key="`${item.keyId }${ index}`"
                    :item-index="index"
                    :item="item"
                    :is-charged="isSheetCharged(outDiagnosisData)"
                    show-date-col
                    :shebao-card-info="shebaoCardInfo"
                ></goods-readonly-charge-table-tr>
            </template>
        </div>
    </div>
</template>

<script>
    import GoodsReadonlyChargeTableTr from 'src/views/layout/goods/goods-readonly-charge-table-tr.vue';
    import { ChargeStatusEnum } from '@/service/charge/constants';
    export default {
        name: 'DischargeSettlementTable',
        components: {
            GoodsReadonlyChargeTableTr,
        },
        props: {
            data: {
                type: Array,
                required: true,
            },
            outDiagnosisData: {
                type: Array,
                default() {
                    return [];
                },
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
        },
        data() {
            return {
                curTab: 1,
                tabOptions: [
                    {
                        label: '本院',
                        value: 1,
                    },
                    {
                        label: '外诊',
                        value: 2,
                    },
                ],
            };
        },
        methods: {
            isSheetCharged(chargeSheet) {
                return chargeSheet.status !== ChargeStatusEnum.UN_CHARGE && chargeSheet.status !== ChargeStatusEnum.RETAIL;
            },
        },
    };
</script>
<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';

    .abc-charge-table.discharge-settlement-table {
        .table-header {
            padding: 0 22px 0 12px;

            .th {
                &.unitPrice {
                    width: 70px;
                    min-width: 70px;
                    max-width: 70px;
                    padding-right: 6px;
                    padding-left: 0;
                    text-align: right;
                }

                &.date {
                    width: 70px;
                    min-width: 70px;
                    max-width: 70px;
                    padding-left: 6px;
                    text-align: start;
                }
            }
        }

        .table-body {
            .tr {
                padding: 0 12px;
            }

            .tr:first-child {
                border-top: none;
            }

            .td,
            .th {
                &.unitPrice {
                    width: 70px;
                    min-width: 70px;
                    max-width: 70px;
                    padding-right: 6px;
                    padding-left: 0;
                    text-align: right;
                }

                &.date {
                    width: 70px;
                    min-width: 70px;
                    max-width: 70px;
                    padding-left: 6px;
                    text-align: start;
                }

                &.unitCount {
                    width: 66px;
                    min-width: 66px;
                    max-width: 66px;
                }

                &.manufacturer {
                    width: 80px;
                    min-width: 80px;
                    max-width: 80px;
                }
            }
        }
    }
</style>
