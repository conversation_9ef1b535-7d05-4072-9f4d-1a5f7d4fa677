<template>
    <abc-table
        :render-config="renderConfig"
        :data-list="renderDataList"
    >
    </abc-table>
</template>

<script>
    import SettlementTable from './table';

    export default {
        name: 'ChanghuSettlementTable',
        props: {
            data: {
                type: Array,
                required: true,
            },
            outDiagnosisData: {
                type: Array,
                default() {
                    return [];
                },
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
        },
        data() {
            return {
                curTab: 1,
                tabOptions: [
                    {
                        label: '本院',
                        value: 1,
                    },
                    {
                        label: '外诊',
                        value: 2,
                    },
                ],
            };
        },
        computed: {
            renderConfig() {
                return SettlementTable.getRenderConfig();
            },
            renderDataList() {
                return this.data;
            },
        },
        methods: {

        },
    };
</script>
