import BaseProTable from '@/views/layout/tables/base-pro-table.js';
export default class SettlementTable extends BaseProTable {
    name = 'SettlementTable';
    static #curTab = 1;
    static staticReturnConfig = {
        hasInnerBorder: true,
        list: [{
            'key': 'name',
            'label': '',
            'testValue': '',
            'colType': 'text',
            'isCheckbox': false,
            'style': {
                'flex': 1,
                'maxWidth': '',
                'minWidth': '',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'left',
            },
            // eslint-disable-next-line no-unused-vars
            headerAppendRender: (h) => {
                return (
                    <abc-tabs
                        value={SettlementTable.#curTab}
                        size="middle"
                        option={
                            [
                                {
                                    label: '本院',
                                    value: 1,
                                },
                                {
                                    label: '外诊',
                                    value: 2,
                                },
                            ]
                        }
                        onChange={(value) => {
                            console.log(value);
                            SettlementTable.#curTab = value;
                            console.log(SettlementTable.#curTab);
                        }}
                    >
                    </abc-tabs>
                );
            },
        },{
            'key': 'manufacturer',
            'label': '厂家',
            'testValue': '',
            'colType': 'text',
            'isCheckbox': false,
            'style': {
                'maxWidth': '80px',
                'minWidth': '80px',
                'width': '80px',
                'paddingLeft': '',
                'paddingRight': '',
            },
        },{
            'key': 'selfPayProp',
            'label': '医保',
            'testValue': '',
            'colType': 'text',
            'isCheckbox': false,
            'style': {
                'maxWidth': '70px',
                'minWidth': '70px',
                'width': '70px',
                'paddingLeft': '',
                'paddingRight': '',
            },
        },{
            'key': 'created',
            'label': '时间',
            'testValue': '',
            'colType': 'date',
            'isCheckbox': false,
            'style': {
                'maxWidth': '70px',
                'minWidth': '70px',
                'width': '70px',
                'paddingLeft': '',
                'paddingRight': '',
            },
        },{
            'key': 'unitPrice',
            'label': '单价',
            'testValue': '',
            'colType': 'money',
            'isCheckbox': false,
            'style': {
                'maxWidth': '70px',
                'minWidth': '70px',
                'width': '70px',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'right',
            },
        },{
            'key': 'unitCount',
            'label': '数量',
            'testValue': '',
            'colType': 'text',
            'isCheckbox': false,
            'style': {
                'maxWidth': '66px',
                'minWidth': '66px',
                'width': '66px',
                'paddingLeft': '',
                'paddingRight': '',
            },
        },{
            'key': 'totalPrice',
            'label': '金额',
            'testValue': '',
            'colType': 'money',
            'isCheckbox': false,
            'style': {
                'maxWidth': '76px',
                'minWidth': '76px',
                'width': '76px',
                'paddingLeft': '',
                'paddingRight': '',
                'textAlign': 'right',
            },
        }],
    };

    static getRenderConfig() {
        return {
            ...this.staticReturnConfig,
            list: this.staticReturnConfig.list,
        };
    }
}
