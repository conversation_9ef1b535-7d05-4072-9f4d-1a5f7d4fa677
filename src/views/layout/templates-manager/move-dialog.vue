<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="移动"
        :custom-top="customTop"
        content-styles="padding: 24px;"
    >
        <abc-form ref="addForm" label-position="left" :label-width="74">
            <template v-if="currentCategoryIsPersonal">
                <abc-form-item label="分类" style="margin-right: 0;">
                    <abc-select v-model="postData.category" adaptive-width @change="selectFolder({})">
                        <abc-option
                            v-for="(op,idx) in categoryOptions"
                            :key="idx"
                            v-bind="op"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
            </template>

            <abc-form-item
                label="目录"
                required
                style="margin: 0;"
                hidden-red-dot
            >
                <abc-popover
                    ref="catalogues-selector"
                    class="catalogues-selector"
                    trigger="click"
                    placement="bottom"
                    width="auto"
                    theme="white"
                    :visible-arrow="false"
                    :popper-style="{ padding: 0 }"
                >
                    <div slot="reference" style="padding-bottom: 4px;">
                        <abc-input v-model="parentNodeName" :width="254" readonly></abc-input>
                    </div>
                    <div class="catalogues-folder-popover" style="width: 254px;">
                        <abc-tree
                            :data="folders"
                            :max-depth="3"
                            :indent="18"
                            @node-click="selectFolder"
                        >
                            <template #default="{ node }">
                                <div class="custom-node-wrapper" style="cursor: pointer;">
                                    <img src="~assets/images/<EMAIL>" alt="" />
                                    <span>{{ node.name }}</span>
                                </div>
                            </template>
                        </abc-tree>
                    </div>
                </abc-popover>
            </abc-form-item>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="buttonLoading" @click="save('addForm')">
                确认
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import OutpatientAPI from 'api/outpatient';
    import Clone from 'utils/clone';
    import {
        OutpatientTemplateCategory,
        OwnerType,
    } from 'views/layout/templates-manager/constants';

    export default {
        name: 'MoveTemplateDialog',

        components: {},

        props: {
            value: Boolean,
            customTop: String,
            currentCategory: Number,
            templateType: {
                type: String,
                required: true,
                validator (value) {
                    return ['medicalrecord', 'prescription'].indexOf(value) !== -1;
                },
            },
            templateId: {
                required: true,
                type: [Number, String],
            },
            catalogues: {
                type: Array,
                required: true,
            },
            clinicCatalogues: {
                type: Array,
                default: () => ([]),
            },
        },

        data() {
            return {
                buttonLoading: false,
                parentNodeName: '',
                commonFolders: [],
                clinicFolders: [],
                postData: {
                    parentId: null,
                    sort: 0,
                    category: '',
                },
            };
        },

        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            currentCategoryIsPersonal() {
                return this.currentCategory === OutpatientTemplateCategory.personal;
            },

            folders() {
                if (this.currentCategoryIsPersonal) {
                    let folders = [];
                    if (this.postData.category === OutpatientTemplateCategory.clinic) {
                        folders = this.clinicFolders;
                    } else {
                        folders = this.commonFolders;
                    }
                    return folders;
                }
                return this.commonFolders;
            },

            categoryOptions() {
                return [
                    {
                        label: '个人专用',
                        value: OutpatientTemplateCategory.personal,
                    },
                    {
                        label: `${this.$app.institutionTypeWording}公用`,
                        value: OutpatientTemplateCategory.clinic,
                    },
                ];
            },
        },

        created() {
            this.commonFolders = Clone(this.catalogues);
            this.clinicFolders = Clone(this.clinicCatalogues);
            // 移动个人专用模板时，设置默认值
            if (this.currentCategoryIsPersonal) {
                this.postData.category = OutpatientTemplateCategory.personal;
            }
        },

        methods: {
            selectFolder(node) {
                this.$refs['catalogues-selector'].showPopper = false;
                this.postData.parentId = node.id;
                this.parentNodeName = node.name;
                this.postData.classification = node.name;
            },

            save(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.submit();
                    }
                });
            },
            async submit() {
                try {
                    const { data } = await OutpatientAPI.moveTemplate(
                        this.templateType,
                        this.templateId,
                        {
                            ...this.postData,
                            // 当个人专用模板移动且指定移动到诊所公用时，需要指定移动到门店公用
                            ownerType: this.currentCategory === OutpatientTemplateCategory.personal ? (
                                this.postData.category === OutpatientTemplateCategory.clinic ? OwnerType.CLINIC : OwnerType.PERSONAL
                            ) : undefined,
                        },
                    );
                    this.$Toast({
                        message: '移动成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    const isPersonalMoveToClinic = this.currentCategory === OutpatientTemplateCategory.personal &&
                        this.postData.category === OutpatientTemplateCategory.clinic;
                    this.$emit('move-success', data, isPersonalMoveToClinic);
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>
