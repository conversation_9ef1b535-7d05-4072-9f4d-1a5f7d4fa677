<template>
    <div style="height: 100%;">
        <div class="prescription-template">
            <div class="right-header">
                <abc-form-item v-if="templateId" required>
                    <abc-input
                        v-if="hasPermission"
                        v-model.trim="currentName"
                        max-length="30"
                        :width="500"
                        @blur="confirmRename"
                        @enter="confirmRename"
                    ></abc-input>
                    <h3 v-else>
                        {{ currentName }}
                    </h3>
                </abc-form-item>

                <div
                    v-if="!disabledUse"
                    class="close-icon"
                    aria-label="Closes"
                >
                    <abc-delete-icon
                        size="hugely"
                        theme="dark"
                        data-cy="abc-dialog-close-btn"
                        variant="outline-square"
                        @delete="$emit('close')"
                    ></abc-delete-icon>
                </div>
            </div>

            <div
                v-abc-loading="contentLoading"
                class="right-content"
                :class="{ 'has-footer': templateId && !disabledUse }"
            >
                <div v-show="templateId" class="template-info">
                    <span v-if="viewData.createdByName">创建人：{{ viewData.createdByName }} </span>
                    <!--                    <span>范围：</span>-->

                    <div style="margin-left: auto;">
                        <template v-if="hasPermission">
                            <abc-button
                                variant="ghost"
                                size="small"
                                @click="showEditDialog = true"
                            >
                                编辑
                            </abc-button>
                            <abc-button
                                variant="ghost"
                                theme="danger"
                                size="small"
                                @click="deleteConfirm"
                            >
                                删除
                            </abc-button>
                        </template>
                        <div v-else class="tips">
                            公共模板不可编辑
                        </div>
                    </div>
                </div>

                <div v-if="templateId" class="medical-record-wrapper is-disabled">
                    <div class="medical-record-item">
                        <label>分类</label>
                        <abc-form-item>
                            <abc-edit-div v-model="viewData.classification" disabled spellcheck="false"></abc-edit-div>
                        </abc-form-item>
                        <label class="second-label">来源</label>
                        <abc-form-item class="tcm-syndrome">
                            <abc-edit-div
                                v-model="viewData.source"
                                style="border-radius: 0 var(--abc-border-radius-small) 0 0;"
                                disabled
                                spellcheck="false"
                            ></abc-edit-div>
                        </abc-form-item>
                    </div>
                    <div class="medical-record-item">
                        <label>功用</label>
                        <abc-form-item>
                            <abc-edit-div v-model="viewData.effect" disabled spellcheck="false"></abc-edit-div>
                        </abc-form-item>
                    </div>
                    <div class="medical-record-item">
                        <label>主治</label>
                        <abc-form-item>
                            <abc-edit-div v-model="viewData.majorDisease" disabled spellcheck="false"></abc-edit-div>
                        </abc-form-item>
                    </div>
                    <div class="medical-record-item">
                        <label>加减</label>
                        <abc-form-item>
                            <abc-edit-div
                                v-model="viewData.revision"
                                disabled
                                :maxlength="1000"
                                spellcheck="false"
                            ></abc-edit-div>
                        </abc-form-item>
                    </div>
                </div>

                <!--西药处方-->
                <div
                    v-for="form in viewData.detail.prescriptionWesternForms"
                    :key="form.id"
                    class="prescription-table-detail-wrapper western-table"
                >
                    <div class="prescription-header">
                        <h5>{{ _westernPrescriptionNameText }}</h5>
                        <jing-ma-dropdown v-model="form.psychotropicNarcoticType" style="margin-left: auto;" disabled></jing-ma-dropdown>
                    </div>
                    <div class="prescription-table">
                        <ul class="table-body">
                            <li
                                v-for="(item, index) in form.prescriptionFormItems"
                                ref="tableTr"
                                :key="item.id || item.keyId || index"
                                class="table-tr"
                            >
                                <div class="table-td group">
                                    {{ item.groupId || '-' }}
                                </div>

                                <!--cadn-->
                                <div class="table-td cadn">
                                    <!--药品cadn-->
                                    <span class="in-block-cadn ellipsis">{{ item.name }}</span>

                                    <div class="in-block-specification">
                                        {{ item.productInfo | getSpec }}
                                    </div>
                                </div>

                                <!--皮试-->
                                <div class="table-td ast">
                                    {{ item.ast === AstEnum.PI_SHI ? '皮试' : '' }}
                                </div>

                                <!--用法-->
                                <div class="table-td usage ellipsis" :title="item.usage">
                                    {{ item.usage }}
                                </div>

                                <!--频率-->
                                <div class="table-td freq">
                                    {{ item.freq }}
                                </div>

                                <!--剂量-->
                                <div class="table-td dosage">
                                    {{ item.dosage }}{{ item.dosageUnit }}
                                </div>

                                <!--天数-->
                                <div class="table-td days">
                                    {{ item.days }}天
                                </div>

                                <!--开药量-->
                                <div class="table-td count">
                                    {{ item.unitCount }}{{ item.unit }}
                                </div>
                                <div class="table-td remark" :title="item.specialRequirement">
                                    <div>
                                        {{ item.specialRequirement }}
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <!--注射输液-->
                <div
                    v-for="form in viewData.detail.prescriptionInfusionForms"
                    :key="form.id"
                    class="prescription-table-detail-wrapper infusion-table"
                >
                    <div class="prescription-header">
                        <h5>输注处方</h5>
                        <jing-ma-dropdown v-model="form.psychotropicNarcoticType" style="margin-left: auto;" disabled></jing-ma-dropdown>
                    </div>
                    <div
                        v-for="group in transGroupList(form.prescriptionFormItems)"
                        :key="group.groupId"
                        class="infusion-prescription-group"
                    >
                        <div class="prescription-table">
                            <div class="table-group-no">
                                {{ group.groupId }}
                            </div>
                            <ul class="table-body">
                                <li class="infusion-description">
                                    <span>{{ group.usage }}</span>
                                    <span>{{ group.freq }}</span>
                                    <span>{{ group.days }}天</span>
                                    <span>{{ group.ivgtt }}{{ group.ivgttUnit }}</span>
                                </li>

                                <li
                                    v-for="(item, index) in group.list"
                                    :key="item.id || item.keyId || index"
                                    class="table-tr"
                                >
                                    <!--cadn-->
                                    <div class="table-td cadn">
                                        <!--药品cadn-->
                                        <span class="in-block-cadn ellipsis">{{ item.name }}</span>

                                        <div class="in-block-specification">
                                            {{ item.productInfo | getSpec }}
                                        </div>
                                    </div>

                                    <!--皮试-->
                                    <div class="table-td ast">
                                        {{ item.ast === AstEnum.PI_SHI ? '皮试' : '' }}
                                    </div>

                                    <!--剂量-->
                                    <div class="table-td dosage">
                                        {{ item.dosage }}{{ item.dosageUnit }}
                                    </div>

                                    <!--开药量-->
                                    <div class="table-td count">
                                        {{ item.unitCount }}{{ item.unit }}
                                    </div>

                                    <div class="table-td remark" :title="item.specialRequirement">
                                        <div>
                                            {{ item.specialRequirement }}
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!--中药处方-->
                <div
                    v-for="form in viewData.detail.prescriptionChineseForms"
                    :key="form.id"
                    class="prescription-table-detail-wrapper chinese-table"
                >
                    <div class="prescription-header">
                        <h5>
                            中药处方{{ form.specification ? `-${ form.specification.replace('中药', '')}` : '-饮片' }}
                        </h5>
                        <jing-ma-dropdown v-model="form.psychotropicNarcoticType" style="margin-left: auto;" disabled></jing-ma-dropdown>
                    </div>

                    <div class="prescription-table">
                        <div class="table-body">
                            <div
                                v-for="(medicine, index) in form.prescriptionFormItems"
                                :key="medicine.id || medicine.keyId || index"
                                class="table-td"
                                :class="{
                                    'is-last-row': isLastRow(form.prescriptionFormItems, index),
                                    'is-last-col': (index + 1) % 4 === 0,
                                }"
                            >
                                <div class="name-wrapper">
                                    <div class="cadn">
                                        <div class="name ellipsis">
                                            {{ medicine.name || medicine.medicineCadn }}
                                        </div>
                                    </div>
                                </div>

                                <div class="count-wrapper">
                                    <span>{{ medicine.unitCount }}{{ medicine.unit || 'g' }}</span>
                                    <span>{{ medicine.specialRequirement }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="prescription-footer">
                        <div class="chinese-description">
                            <div class="dose-count">
                                {{ form.doseCount || 1 }}剂
                            </div>

                            <div class="usages">
                                {{ form.usage }}
                            </div>

                            <div class="daily-dosage">
                                {{ form.dailyDosage }}
                            </div>

                            <div class="freq">
                                {{ form.freq }}
                            </div>

                            <div class="usage-level">
                                {{ form.usageLevel }}
                            </div>

                            <div class="usage-level">
                                {{ form.usageDays }}
                            </div>

                            <div class="requirement">
                                {{ form.requirement }}
                            </div>
                        </div>
                    </div>
                </div>

                <!--外治处方-->
                <div
                    v-for="(form, formIndex) in viewData.detail.prescriptionExternalForms"
                    :key="formIndex"
                    class="prescription-table-detail-wrapper external-table"
                >
                    <div class="prescription-header">
                        <h5>外治处方</h5>
                        <jing-ma-dropdown v-model="form.psychotropicNarcoticType" style="margin-left: auto;" disabled></jing-ma-dropdown>
                    </div>
                    <div class="prescription-table">
                        <div v-for="(item, index) in form.prescriptionFormItems" :key="index" class="table-body">
                            <div class="name">
                                {{ index + 1 }}.{{ item.name }}
                            </div>

                            <div v-if="item.externalGoodsItems && item.externalGoodsItems.length" class="acupoints">
                                <span
                                    v-for="(goods, goodsIndex) in item.externalGoodsItems.filter((it) => it.goodsId || it.name)"
                                    :key="goods.goodsId || goods.keyId || goodsIndex"
                                >
                                    {{ goods.name }}  {{ goods.unitCount }}{{ goods.unit }}
                                </span>
                            </div>

                            <div v-if="item.acupoints" class="acupoints">
                                <span
                                    v-for="(acu, acuIndex) in item.acupoints.filter((acu) => acu.id || acu.name)"
                                    :key="acu.id || acu.keyId || acuIndex"
                                >
                                    {{ acu.position }} {{ acu.name }}
                                </span>
                            </div>

                            <div class="external-usage">
                                <div class="dose-count">
                                    {{ item.dosage || 1 }} 次
                                </div>
                                <div class="freq">
                                    {{ item.freq }}
                                </div>
                                <div class="total-count">
                                    <template v-if="isBasedOnAcupoint(form) && item.acupoints?.length">
                                        每次 {{ dosageCount(item) }} {{ externalUnit(form) }}，
                                        共 {{ dosageCount(item) * (item.dosage || 0) }} {{ externalUnit(form) }}，
                                    </template>
                                    共{{ item.unitCount }}{{ item.unit }}
                                </div>
                                <div class="requirement">
                                    {{ item.specialRequirement }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="templateId && !disabledUse" class="right-footer">
                <div class="buttons-wrapper">
                    <abc-button @click="useTemplate">
                        使用
                    </abc-button>
                </div>
            </div>
        </div>

        <prescription-form-dialog
            v-if="showFormDialog"
            v-model="showFormDialog"
            :data="showAddDialog ? null : viewData"
            :template-id="templateId"
            :catalogues="catalogues"
            :category="category"
            :category-id="categoryId"
            @change-success="changeTemplateSuccess"
        ></prescription-form-dialog>
    </div>
</template>

<script>
    import OutpatientAPI from 'api/outpatient';
    import { mapGetters } from 'vuex';
    import { OwnerType } from './constants';
    import PrescriptionFormDialog from './prescription-form-dialog';
    import {
        AstEnum, ExternalPRUsageTypeEnum,
    } from 'views/layout/prescription/constant.js';
    import JingMaDropdown from 'src/views/layout/prescription/common/jing-ma-dropdown.vue';
    import { isNumber } from '@/utils';

    export default {
        name: 'PrescriptionTemplate',

        components: {
            PrescriptionFormDialog,
            JingMaDropdown,
        },

        props: {
            templateName: {
                type: String,
            },
            ownerType: {
                type: Number,
                required: true,
            },
            templateId: {
                required: true,
                validator: (prop) => typeof prop === 'string' || typeof prop === 'number' || prop === null,
            },

            catalogues: {
                type: Array,
                required: true,
                default: () => {
                    return [];
                },
            },
            category: {
                type: Number,
                required: true,
            },
            categoryId: {
                required: true,
                validator: (prop) => typeof prop === 'string' || typeof prop === 'number' || prop === null,
            },
            currentSelectedCategory: Object,
            selectedSearchResult: {
                type: Object,
            },
            // 是有有使用按钮
            disabledUse: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                AstEnum,
                showAddDialog: false,
                showEditDialog: false,
                contentLoading: false,
                saveLoading: false,
                isEditing: false,
                // 模板内容展示结构
                viewData: {
                    ownerType: 0,
                    name: '',
                    createdByName: '',
                    category: '',
                    majorDisease: '',
                    classification: '',
                    revision: '',
                    source: '',
                    effect: '',
                    detail: {
                        prescriptionChineseForms: [],
                        prescriptionInfusionForms: [],
                        prescriptionWesternForms: [],
                        prescriptionExternalForms: [],
                        prescriptionGlassesForms: [],
                    },
                },
            };
        },

        computed: {
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),

            currentName: {
                get() {
                    return this.viewData.name || '';
                },
                set(val) {
                    this.viewData.name = val;
                },
            },

            showFormDialog: {
                get() {
                    return this.showAddDialog || this.showEditDialog;
                },
                set(val) {
                    if (!val) {
                        this.showAddDialog = false;
                        this.showEditDialog = false;
                    }
                },
            },

            hasPermission() {
                switch (this.ownerType) {
                    case 1:
                        return this.viewData.ownerType === OwnerType.CHAIN;
                    case 2:
                        return (
                            this.viewData.ownerType === OwnerType.CLINIC ||
                            this.viewData.ownerType === OwnerType.PERSONAL
                        );
                    case 3:
                        return this.viewData.ownerType === OwnerType.PERSONAL;
                    default:
                        return false;
                }
            },
        },

        watch: {
            templateId(val) {
                if (val) {
                    this.initDetail(val);
                } else {
                    this.resetDetail();
                }
            },
        },

        async created() {
            const {
                westernPrescriptionNameText,
            } = this.viewDistributeConfig.Outpatient;
            this._westernPrescriptionNameText = westernPrescriptionNameText;
            if (this.templateId) {
                this.initDetail();
            }
        },

        methods: {
            rename(name) {
                this.viewData.name = name;
            },

            showAddFormHandle() {
                this.showAddDialog = true;
            },

            async initDetail() {
                this.contentLoading = true;
                await this.fetchTemplateDetail(this.templateId);
                this.contentLoading = false;
            },
            resetDetail() {
                this.viewData = {
                    name: '',
                    createdByName: '',
                    category: '',
                    majorDisease: '',
                    effect: '',
                    classification: '',
                    revision: '',
                    source: '',
                    detail: {
                        prescriptionChineseForms: [],
                        prescriptionInfusionForms: [],
                        prescriptionWesternForms: [],
                        prescriptionExternalForms: [],
                        prescriptionGlassesForms: [],
                    },
                };
            },

            /**
             * @desc 输液处方 转化成 分组
             * <AUTHOR>
             * @date 2018/12/06 15:36:23
             */
            transGroupList(list) {
                const cacheMap = new Map();

                list.forEach((item) => {
                    const groupItem = cacheMap.get(item.groupId);

                    if (groupItem) {
                        groupItem.list.push(item);
                    } else {
                        cacheMap.set(item.groupId, {
                            groupId: item.groupId,
                            usage: item.usage,
                            freq: item.freq,
                            days: item.days,
                            ivgtt: item.ivgtt,
                            ivgttUnit: item.ivgttUnit,
                            list: [item],
                        });
                    }
                });
                const groupList = [];
                cacheMap.forEach((item) => {
                    groupList.push(item);
                });
                return groupList;
            },

            /**
             * @desc 从模板详情删除模板
             * <AUTHOR>
             * @date 2020/03/31 15:51:49
             */
            deleteConfirm() {
                this.$emit('delete', {
                    id: this.templateId,
                    isFolder: 0,
                });
            },

            /**
             * @desc 获取处方模板详情
             * <AUTHOR>
             * @date 2020/05/15 17:13:51
             */
            async fetchTemplateDetail(id) {
                try {
                    const { data } = await OutpatientAPI.fetchTemplateDetail('prescription', id);
                    data.detail = data.detail || {
                        prescriptionChineseForms: [],
                        prescriptionInfusionForms: [],
                        prescriptionWesternForms: [],
                        prescriptionExternalForms: [],
                        prescriptionGlassesForms: [],
                    };
                    this.viewData = Object.assign({}, data);
                    this.defaultClassification();
                } catch (e) {
                    console.error(e);
                }
            },

            /**
             * @desc 新增/修改模板成功
             * <AUTHOR>
             * @date 2020/05/19 16:47:48
             * @params data 接口更新后的数据
             * @params type change 类型 ['add', 'edit']
             */
            changeTemplateSuccess(data, type, parentData) {
                Object.assign(this.viewData, data);
                this.defaultClassification();
                this.$emit('change-success', data, type, parentData);
            },

            /**
             * @desc 自建模板没有分类。默认填充文件目录名
             * <AUTHOR> Yang
             * @date 2021-03-17 08:58:05
             */
            defaultClassification() {
                if (this.viewData.ownerType !== OwnerType.SYSTEM) {
                    if (this.selectedSearchResult) {
                        this.viewData.classification = this.selectedSearchResult.folderName || '';
                    } else if (this.currentSelectedCategory) {
                        this.viewData.classification = this.currentSelectedCategory.name || '';
                    }
                }
            },

            async confirmRename(e) {
                if (!this.currentName) {
                    return false;
                }
                $(e.target).blur();
                try {
                    const {
                        id, name, parentId,
                    } = await OutpatientAPI.rename('prescription', this.templateId, {
                        name: this.currentName,
                    });
                    this.$emit('rename', {
                        id,
                        name,
                        parentId,
                    });
                } catch (e) {
                    console.error(e);
                }
            },

            dosageCount(item) {
                let count = 0;
                item.acupoints?.forEach((it) => {
                    if (it.name) {
                        if (isNumber(it.position)) {
                            count += parseFloat(it.position);
                        } else if (it.position === '双') {
                            count += 2;
                        } else {
                            count++;
                        }
                    }
                });
                return count || 1;
            },

            externalUnit() {
                return '穴';
            },

            useTemplate() {
                this.$emit('useTemplate', this.viewData);
            },

            isLastRow(medicineList, index) {
                const len = medicineList.length;
                const row = Math.ceil(len / 4);
                const lastRow = Math.ceil((index + 1) / 4);
                return lastRow >= row;
            },

            isBasedOnAcupoint(form) {
                return [
                    ExternalPRUsageTypeEnum.tieFu,
                    ExternalPRUsageTypeEnum.zhenCi,
                    ExternalPRUsageTypeEnum.aiJiu,
                ].includes(form.usageType);
            },
        },
    };
</script>
