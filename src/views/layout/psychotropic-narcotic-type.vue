<template>
    <div class="psychotropic-narcoticType-type-box">
        <img
            v-if="tags.includes(AdviceTagEnum.OPERATE_ING)"
            class="psychotropic-narcoticType-type"
            src="~assets/images/hospital/operate-ing.png"
            alt="术中"
        />
        <img
            v-if="tags.includes(AdviceTagEnum.OPERATE_AFTER)"
            class="psychotropic-narcoticType-type"
            src="~assets/images/hospital/operate-after.png"
            alt="术后"
        />
        <img
            v-if="tags.includes(AdviceTagEnum.JING_1)"
            class="psychotropic-narcoticType-type"
            src="~assets/images/prescription/jing-1.png"
            alt="精1"
        />
        <img
            v-else-if="tags.includes(AdviceTagEnum.JING_2)"
            class="psychotropic-narcoticType-type"
            src="~assets/images/prescription/jing-2.png"
            alt="精2"
        />
        <img
            v-else-if="tags.includes(AdviceTagEnum.MA_ZUI)"
            class="psychotropic-narcoticType-type"
            src="~assets/images/prescription/ma-zui.png"
            alt="麻醉"
        />
        <img
            v-else-if="tags.includes(AdviceTagEnum.DU)"
            class="psychotropic-narcoticType-type"
            src="~assets/images/prescription/du.png"
            alt="毒"
        />
    </div>
</template>

<script>
    import {
        AdviceTagEnum,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    export default {
        name: 'PsychotropicNarcoticType',
        props: {
            tags: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                AdviceTagEnum,
            };
        },
    };
</script>

<style lang="scss" scoped>
.psychotropic-narcoticType-type {
    width: auto;
    min-width: 18px;
    height: 16px;
    margin-right: 4px;

    &-box {
        display: flex;
        align-items: center;
    }
}
</style>
