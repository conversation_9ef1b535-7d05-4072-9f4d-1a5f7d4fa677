<template>
    <abc-dropdown
        class="jing-ma-dropdown-wrapper"
        custom-class="jing-ma-dropdown-popover"
        placement="bottom-start"
        :disabled="disabled"
        data-cy="dropdown-精麻"
        @change="handleChange"
    >
        <div slot="reference" data-cy="dropdown-精麻-reference">
            <abc-tag-v2
                v-if="value"
                size="tiny"
                theme="default"
                variant="outline"
                :style="{
                    cursor: disabled ? 'default' : 'pointer',
                    margin: '0 6px',
                    borderColor: 'var(--abc-tag-hover-border-color)',
                    width: 'auto !important',
                    height: '16px',
                    padding: '0 2px',
                    fontSize: '11px',
                    borderRadius: '3px',
                }"
            >
                {{ PsychotropicNarcoticTypeEnumStr[value] }}
            </abc-tag-v2>
            <template v-else>
                <abc-button
                    v-if="!disabled"
                    icon="s-medicinetag-line"
                    variant="text"
                    theme="default"
                    size="small"
                >
                </abc-button>
            </template>
        </div>
        <abc-dropdown-item
            v-for="item in options"
            :key="item.value"
            :value="item.value"
            :data-cy="`dropdown-精麻-item-${item.label}`"
        >
            {{ item.label }}<abc-icon
                v-if="value === item.value"
                style="margin-left: 4px;"
                :size="12"
                icon="positive_"
                :color="themeStyle.theme2"
            ></abc-icon>
        </abc-dropdown-item>
    </abc-dropdown>
</template>

<script type="text/ecmascript-6">
    import {
        PsychotropicNarcoticTypeEnum,
        PsychotropicNarcoticTypeEnumStr,
    } from 'views/outpatient/constants.js';
    import themeStyle from '@/styles/theme.module.scss';
    export default {
        name: 'JingMaDropdown',

        props: {
            value: {
                type: Number,
                require: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            isNeedJingMaDu: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                themeStyle,
                PsychotropicNarcoticTypeEnum,
                PsychotropicNarcoticTypeEnumStr,
            };
        },
        computed: {
            options() {
                if (this.isNeedJingMaDu) {
                    return [
                        {
                            value: PsychotropicNarcoticTypeEnum.MA_ZUI,
                            label: '麻醉',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.JING_1,
                            label: '精一',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.JING_2,
                            label: '精二',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.DU,
                            label: '毒',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.GENERAL,
                            label: '普通',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.CHILD,
                            label: '儿科',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.CHRONIC,
                            label: '慢病',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.OLD,
                            label: '老年病',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.EMERGENCY,
                            label: '急诊',
                        },
                        {
                            value: PsychotropicNarcoticTypeEnum.LONG_TERM,
                            label: '长期',
                        },
                    ];
                }
                return [
                    {
                        value: PsychotropicNarcoticTypeEnum.GENERAL,
                        label: '普通',
                    },
                    {
                        value: PsychotropicNarcoticTypeEnum.CHILD,
                        label: '儿科',
                    },
                    {
                        value: PsychotropicNarcoticTypeEnum.CHRONIC,
                        label: '慢病',
                    },
                    {
                        value: PsychotropicNarcoticTypeEnum.OLD,
                        label: '老年病',
                    },
                    {
                        value: PsychotropicNarcoticTypeEnum.EMERGENCY,
                        label: '急诊',
                    },
                    {
                        value: PsychotropicNarcoticTypeEnum.LONG_TERM,
                        label: '长期',
                    },
                ];
            },
        },
        methods: {
            handleChange(value) {
                if (this.value === value) {
                    this.$emit('input', PsychotropicNarcoticTypeEnum.NONE);
                    this.$emit('change', PsychotropicNarcoticTypeEnum.NONE);
                    return;
                }
                this.$emit('input', value);
                this.$emit('change', value);
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    .jing-ma-dropdown-wrapper {
        &.abc-dropdown-wrapper .reference {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            > div {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0;
            }
        }
    }

    .abc-dropdown-options-wrapper.jing-ma-dropdown-popover {
        min-width: 64px;
        padding: 4px 0;

        .abc-dropdown-item {
            display: flex;
            align-items: center;
            min-height: 24px;
            padding: 5px 10px;
            font-size: 12px;
            line-height: 14px;

            .abc-icon {
                margin-left: 6px;
            }

            &.is-selected,
            &.is-hover {
                color: var(--abc-color-T1) !important;
                background-color: #ffffff !important;

                .abc-icon {
                    color: var(--abc-color-theme2) !important;
                }

                &.is-hover:hover {
                    color: var(--abc-color-T1) !important;
                    background-color: var(--abc-color-P4) !important;
                }
            }
        }
    }
</style>
