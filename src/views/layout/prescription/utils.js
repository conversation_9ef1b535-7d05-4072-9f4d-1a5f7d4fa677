import WesternMedicineConfig from 'assets/configure/western-medicine-config';
import {
    UsageRuleTypeEnum, UsageListEnum,
} from 'views/settings/outpatient-setting/usage-config.js';
import {
    createGUID,
} from 'utils/index';
import { AstEnum } from 'views/layout/prescription/constant.js';
import {
    GoodsSubTypeEnum, GoodsTypeEnum,
} from '@abc/constants';
import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
import { SourceFormTypeEnum } from '@/service/charge/constants.js';
import SettingAPI from 'api/settings.js';
import { SearchSceneTypeEnum } from 'views/common/enum.js';
import CdssAPI from 'api/cdss';

// 计算每组 freqTime
function calFreqTimeHandler(medicine) {
    medicine.days = medicine.days || null;
    if (!medicine.goodsId || medicine.dosageUnit === '适量') return false;
    let freqTime;
    const _freq = WesternMedicineConfig.freq.find((item) => {
        return medicine.freq === item.name || medicine.freq === item.en;
    });
    if (_freq) {
        freqTime = 24 / _freq.time;
    } else {
        if (medicine.freq) {
            const _num = +medicine.freq.replace(/[^0-9]/gi, '');
            if (/^q\d+d$/.test(medicine.freq)) {
                freqTime = 1 / _num;
            } else if (/^q\d+w$/.test(medicine.freq)) {
                freqTime = 24 / (168 * _num);
            } else if (/^q\d+h$/.test(medicine.freq)) {
                freqTime = 24 / _num;
            }
        }
    }
    if (!freqTime || freqTime === Infinity) return false;
    return freqTime;
}

// 处理有分组编号的项目
function calGroupedGoods(goods) {
    const map = new Map();

    const getGroupUid = (item) =>
        `${item.keyId}_${item.groupId}`;
    let astCount = 0;
    astCount = goods && goods.filter((goodItem) => goodItem.ast === AstEnum.PI_SHI).length;
    for (let i = 0; i < goods.length; i++) {
        const item = goods[i];
        item.astCount = astCount;
        const uid = getGroupUid(item);
        const target = map.get(uid);
        if (!target) {
            map.set(uid, item);
        } else {
            if (target.ast !== AstEnum.PI_SHI && item.ast === AstEnum.PI_SHI) {
                map.set(uid, item);
            }
        }
    }

    return [...map.values()];
}

function resolvePrescriptionFormsData(goods) {
    let astCount = 0;
    astCount = goods && goods.filter((goodItem) => goodItem.ast === AstEnum.PI_SHI).length;
    const unGroupedGoods = goods.filter((item) => !item.groupId);
    const infusionUsageNames = UsageListEnum.map((item) => item.name);
    // 没分组的输液、注射、雾化、皮试item
    const infusionUnGroupedGoods = unGroupedGoods.filter((item) => infusionUsageNames.includes(item.usage) || item.ast === AstEnum.PI_SHI);

    const uniqueInfusionUnGroupedGoods = [];
    infusionUnGroupedGoods.forEach((item) => {
        item.astCount = astCount;
        uniqueInfusionUnGroupedGoods.push(item);
    });

    // 带分组的item
    const groupedGoodsFilters = goods.filter((item) => item.groupId);

    const groupedGoods = calGroupedGoods(groupedGoodsFilters);

    const res = [...uniqueInfusionUnGroupedGoods, ...groupedGoods];

    res.forEach((item) => {
        if (item.groupId === 1 || !item.groupId) {
            item.isFirstGroup = true;
        } else {
            item.isFirstGroup = false;
        }
    });
    return res;
}

export function getUsageParams(goods) {
    const calGoods = resolvePrescriptionFormsData(goods);
    const map = new Map();
    calGoods.forEach((item) => {
        const {
            unitCount, dosage, usage, astCount, days,
        } = item;
        const counts = !dosage ? 0 : (unitCount / dosage);
        const calFreqTime = calFreqTimeHandler(item) || 0;
        const calDays = Math.ceil(counts / calFreqTime) || 0;
        const tmp = {
            groupId: item.keyId,
            isFirstGroup: item.isFirstGroup,
            days: days || calDays || 0,
            freqTime: calFreqTime,
            counts,
            astCount,
        };
        const astKey = '皮试';

        if (item.ast === AstEnum.PI_SHI) {
            const target = map.get(astKey);
            if (!target) {
                map.set(astKey, [tmp]);
            } else {
                target.push(tmp);
            }
        }

        const target = map.get(usage);
        if (!target) {
            map.set(usage, [tmp]);
        } else {
            target.push(tmp);
        }
    });
    const res = [...map.entries()].map(([key, value]) => {
        return {
            usage: key,
            groups: value,
        };
    });

    return res;
}

function setDaysRuleGoodItemsCount(goodsItems, param) {
    const { groups } = param;
    const arr = [];
    const countList = groups && groups.map((item) => +item.days * (+item.freqTime > 1 ? 1 : +item.freqTime));
    const maxCount = Math.max(...countList);
    goodsItems.forEach((item) => {
        arr.push({
            ...item,
            unitCount: Math.ceil(maxCount * item.unitCount),
        });
    });
    return arr;
}

function setGroupFixedRuleGoodItemsCount(goodsItems, param) {
    const { groups } = param;
    const arr = [];

    groups.forEach((group) => {
        goodsItems.forEach((item) => {
            const {
                freq = 'each-day-each-time', unitCount,
            } = item;
            const {
                days, freqTime, counts,
            } = group;
            // 每组每次  --- 设置次数 * 每天次数 * 天数
            if (freq === 'each-group-each-time') {
                arr.push({
                    ...item,
                    unitCount: freqTime ? Math.ceil(unitCount * freqTime * days) : Math.ceil(counts * unitCount),
                });
            }
            // 每组每天 -- 设置次数 * 天数
            if (freq === 'each-day-each-time') {
                arr.push({
                    ...item,
                    unitCount: Math.ceil((+freqTime > 1 ? 1 : freqTime) * unitCount * days),
                });
            }
        });
    });

    return arr;
}

function setGroupSTepRuleGoodItemsCount(goodsItems, param) {
    const { groups } = param;

    const hasOnlyFirstGroup = groups && groups.every((item) => item.isFirstGroup);
    let calcGoodsItems = [];
    if (goodsItems[0]) {
        calcGoodsItems = hasOnlyFirstGroup ? [goodsItems[0]] : goodsItems;
    }

    const arr = [];
    groups.forEach((group) => {
        const {
            days, freqTime = 'each-day-each-time' , isFirstGroup, counts,
        } = group;
        // 每组每次  --- 设置次数 * 每天次数 * 天数
        calcGoodsItems.forEach((item) => {
            const {
                freq, unitCount, sort,
            } = item;
            if (sort === 0 && isFirstGroup) {
                // 每组每次  --- 设置次数 * 每天次数 * 天数
                if (freq === 'each-group-each-time') {
                    arr.push({
                        ...item,
                        unitCount: isFirstGroup ? (freqTime ? Math.ceil(unitCount * freqTime * days) : Math.ceil(counts)) * unitCount : 0,
                    });
                }
                // 每组每天 -- 设置次数 * 天数
                if (freq === 'each-day-each-time') {
                    arr.push({
                        ...item,
                        unitCount: isFirstGroup ? Math.ceil((+freqTime > 1 ? 1 : freqTime) * unitCount * days) : 0,
                    });
                }
            }

            if (sort === 1 && !isFirstGroup) {
                // 每组每次  --- 设置次数 * 每天次数 * 天数
                if (freq === 'each-group-each-time') {
                    arr.push({
                        ...item,
                        unitCount: !isFirstGroup ? (freqTime ? Math.ceil(unitCount * freqTime * days) : Math.ceil(counts * unitCount)) : 0,
                    });
                }
                // 每组每天 -- 设置次数 * 天数
                if (freq === 'each-day-each-time') {
                    arr.push({
                        ...item,
                        unitCount: !isFirstGroup ? Math.ceil((+freqTime > 1 ? 1 : freqTime) * unitCount * days) : 0,
                    });
                }
            }
        });
    });
    return arr;
}

function setOnlyOnceRuleGoodItemsCount(goodsItems, param) {
    // groupId 相同，是同一个处方，同一个处方只算一次
    const { groups } = param;
    // 去重，保留次数最大的哪一条记录
    const uniqueGroups = [];
    groups.forEach((item) => {
        const hasExist = uniqueGroups && uniqueGroups.find((group) => group.groupId === item.groupId);
        if (!hasExist) {
            uniqueGroups.push(item);
        }
    });

    const arr = [];
    goodsItems.forEach((item) => {
        arr.push({
            ...item,
            unitCount: item.unitCount * (uniqueGroups && uniqueGroups.length) ,
        });
    });
    return arr;
}

function setTimesRuleGoodItemsCount(goodsItems, param) {
    const {
        groups, usage,
    } = param;
    const arr = [];

    if (usage === '皮试') {
        goodsItems.forEach((item) => {
            const { unitCount } = item;
            arr.push({
                ...item,
                unitCount: unitCount * (groups && groups[0].astCount),
            });
        });
    } else {
        groups.forEach((group) => {
            const {
                days, freqTime,
            } = group;
            goodsItems.forEach((item) => {
                const { unitCount } = item;
                arr.push({
                    ...item,
                    unitCount: Math.ceil(days * unitCount * freqTime),
                });
            });
        });
    }
    return arr;
}

// 每一个规则下的项目
function getPerRuleGoodItems(relatedGoodItems, usageParam) {
    let res = [];
    relatedGoodItems.forEach((ruleItem) => {
        const {
            usageRuleType, goodsItems,
        } = ruleItem;
        if (usageRuleType === UsageRuleTypeEnum.DAYS) {
            const dayGroupRes = setDaysRuleGoodItemsCount(
                goodsItems,
                usageParam,
            );
            res = res.concat(dayGroupRes);
        }
        if (usageRuleType === UsageRuleTypeEnum.GROUP_FIXED) {
            const fixedGroupRes = setGroupFixedRuleGoodItemsCount(
                goodsItems,
                usageParam,
            );
            res = res.concat(fixedGroupRes);
        }
        if (usageRuleType === UsageRuleTypeEnum.GROUP_STEP) {
            const stepRes = setGroupSTepRuleGoodItemsCount(
                goodsItems,
                usageParam,
            );
            res = res.concat(stepRes);
        }
        if (usageRuleType === UsageRuleTypeEnum.ONLY_ONCE) {
            const onceRes = setOnlyOnceRuleGoodItemsCount(
                goodsItems,
                usageParam,
            );
            res = res.concat(onceRes);
        }
        if (usageRuleType === UsageRuleTypeEnum.TIMES) {
            const timesRes = setTimesRuleGoodItemsCount(goodsItems, usageParam);
            res = res.concat(timesRes);
        }
    });

    return res;
}

// 该处方下所有用法的关联项目
export function getTypeGoodsList(usageParams, allGoodsItems) {
    let arr = [];
    if (!allGoodsItems || allGoodsItems.length === 0) return arr;
    usageParams.forEach((param) => {
        const index = allGoodsItems.findIndex(
            (type) => {
                const usages = type?.usages || [];
                return usages.indexOf(param.usage) !== -1;
            });
        if (index !== -1) {
            const perArr = allGoodsItems[index].relatedGoodsRules.filter(
                (item) => item.isUsed,
            );
            const res = getPerRuleGoodItems(perArr, param);
            arr = arr.concat(res);
        }
    });
    return arr;
}

/**
 * @desc 构造处方 prescriptionItem 结构
 * <AUTHOR>
 * @date 2022-10-12 16:17:07
 * @param {object} goods
 * @return {object} prescriptionItem
 */
export function getPrescriptionItemStruct(goods) {
    const {
        medicineCadn,
        name,
        noStocks,
        id,
        type,
        subType,
        customTypeName,
        medicineNmpn,
        manufacturer,
        dismounting,
        pieceUnit,
        packageUnit,
        piecePrice,
        packagePrice,
        stockPieceCount,
        stockPackageCount,
        displaySpec,

        pharmacyNo,
        pharmacyType,
        pharmacyName,
    } = goods || {};


    let displayName = '';
    if (medicineCadn && name) {
        displayName = `${medicineCadn}(${name})`;
    } else {
        displayName = medicineCadn || name;
    }

    let unit = '';
    let unitPrice = 0;

    const useDismounting = +(dismounting && unit === pieceUnit && unit !== packageUnit);

    // 中药兼容单独处理
    if (
        type === GoodsTypeEnum.MEDICINE &&
        subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine
    ) {
        unit = pieceUnit || 'g';
        unitPrice = piecePrice;
    } else {
        unit = dismounting ? pieceUnit : packageUnit;
        unitPrice = useDismounting ? piecePrice : packagePrice;
    }


    return {
        keyId: createGUID(),
        noStocks,
        goodsId: id,
        type,
        subType,
        customTypeName,
        medicineNmpn,
        medicineCadn,
        name: displayName,
        manufacturer,
        ast: null,
        usage: '',
        ivgtt: '',
        ivgttUnit: '',
        freq: '',
        dosage: '',
        dosageUnit: pieceUnit,
        days: '',
        specialRequirement: '',
        dismounting,
        unitCount: '',
        doseCount: 1,
        pieceUnit,
        packageUnit,
        unit,

        unitPrice,

        stockPieceCount,
        stockPackageCount,
        displaySpec,

        pharmacyNo,
        pharmacyType,
        pharmacyName,

        sort: 0,
        useDismounting,
        payType: null,

        productInfo: goods, // goods信息
    };
}


export async function completePrescriptionItem({
    goods,
    prescriptionItem,
    patientInfo,
    physicalExamination,
    isInfusion = false,
    prescriptionFormItems,
    westernMedicineConfig,
}) {
    /**
     * @desc 获取最近用法
     * <AUTHOR>
     * @date 2019/04/21 15:27:14
     */
    const { data } = await CdssAPI.fetchMedicineUsage({
        medicineCadn: prescriptionItem.medicineCadn,
        goodsId: prescriptionItem.goodsId,
        type: prescriptionItem.type,
        patientInfo,
        physicalExamination,
    });
    /**
     * @desc 统一天数填充规则，默认填入相同用法天数
     * <AUTHOR>
     * @date 2022-02-21 16:00:36
     */
    if (!isInfusion && prescriptionFormItems) {
        const prevMedicine = prescriptionFormItems.find((item) => item.usage === data.usage);
        prescriptionItem.days = prevMedicine ? prevMedicine.days : null;
    }

    let freq = isInfusion ? prescriptionItem.freq : data.freq;
    const isExistConfig = (westernMedicineConfig || WesternMedicineConfig).freq.find((it) => it.en === freq);
    // 自定义规则，q开头 + 非0正整数 + d|w|h 结尾
    const isCustomFreq = /^q[1-9][0-9]*(d|w|h)$/.test(freq);
    if (!isExistConfig && !isCustomFreq) {
        freq = '';
    }
    Object.assign(prescriptionItem, {
        dosage: data.dosage || prescriptionItem.dosage,
        dosageUnit: data.dosageUnit || prescriptionItem.dosageUnit,
        unit: data.unit || prescriptionItem.unit,
        /**
         * @desc 输液处方 用自定义设置的
         * <AUTHOR>
         * @date 2018/12/19 18:09:50
         */
        usage: isInfusion ? prescriptionItem.usage : data.usage,
        freq,
    });
    if (prescriptionItem.dosageUnit === '适量') {
        prescriptionItem.dosage = '';
    }

    // 根据开药记录重新填充单位后,需要重新判断useDismounting字段
    prescriptionItem.useDismounting = +(goods.dismounting &&
        prescriptionItem.unit === goods.pieceUnit &&
        prescriptionItem.unit !== goods.packageUnit);

    prescriptionItem.unitPrice = prescriptionItem.useDismounting ?
        prescriptionItem.productInfo.piecePrice :
        prescriptionItem.productInfo.packagePrice;

}

/**
 * @desc 是否需要展开备注判断
 * <AUTHOR>
 * @date 2022-10-14 17:48:53
 * @param {object} item
 * @param {object} defaultPharmacy
 * @param {number} type  0处方 1诊疗项目
 */
export function needExpandRemark(item, defaultPharmacy, type = 0) {
    let remark = item.specialRequirement;
    if (type === 1) {
        remark = item.remark;
    }
    return remark ||
        item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE ||
        (defaultPharmacy && item.pharmacyNo !== defaultPharmacy.no);
}

/**
 * @desc 根据药房号重置库存
 * <AUTHOR>
 * @date 2022-10-19 11:12:33
 */
export function resetStockByPharmacyNo(item) {
    if (!item.productInfo) return;
    const res = item.productInfo.pharmacyGoodsStockList?.find((it) => it.pharmacyNo === item.pharmacyNo);
    const {
        stockPieceCount = 0,
        stockPackageCount = 0,
        pharmacyName = '',
        lastPackageCostPrice = 0,
    } = res || {};
    item.stockPieceCount = stockPieceCount;
    item.stockPackageCount = stockPackageCount;
    item.productInfo.stockPieceCount = stockPieceCount;
    item.productInfo.stockPackageCount = stockPackageCount;
    item.productInfo.lastPackageCostPrice = lastPackageCostPrice;
    item.productInfo.pharmacyName = pharmacyName || item.pharmacyName;
}

export async function updateOutpatientComposeGoods(params) {
    const {
        departmentId,
        productForms,
    } = params;

    let goodsIds = [];
    productForms.forEach((form) => {
        if (form.sourceFormType === SourceFormTypeEnum.COMPOSE && !form.chargeStatus) {
            goodsIds = form.productFormItems.map((item) => item.productId);
        }
    });
    if (!goodsIds || goodsIds.length === 0) return;

    const { data } = await SettingAPI.commonPrescription.fetchPrescriptionTemplateStock({
        sceneType: SearchSceneTypeEnum.outpatient,
        departmentId: departmentId || undefined,
        goodsIds,
        withShebaoCode: 1,
    });
    const goodsStock = (data && data.list) || [];
    goodsStock.forEach((goodsInfo) => {
        productForms.forEach((form) => {
            if (form.sourceFormType === SourceFormTypeEnum.COMPOSE && !form.chargeStatus) {
                form.productFormItems.forEach((item) => {
                    if (item.productId === goodsInfo.id) {
                        item.productInfo = goodsInfo;
                    }
                });
            }
        });
    });
}

/**
 * @desc 把门诊处方结构转成收费算快递费结构
 * <AUTHOR>
 * @date 2023-08-30 14:01:13
 */
export function trans2ChargeForm(prescriptionForm) {
    const chargeForm = {
        keyId: prescriptionForm.keyId,
        sourceFormType: prescriptionForm.sourceFormType,
        chargeFormItems: [],
        deliveryInfo: prescriptionForm.deliveryInfo,
        pharmacyNo: prescriptionForm.pharmacyNo,
        pharmacyType: prescriptionForm.pharmacyType,
    };
    prescriptionForm.prescriptionFormItems.forEach((item) => {
        chargeForm.chargeFormItems.push({
            keyId: item.keyId,
            name: item.name,
            unit: item.unit,
            unitCount: item.unitCount,
            doseCount: item.doseCount,
            productId: item.productId,
            productType: item.productType,
            productSubType: item.productSubType,
            useDismounting: item.useDismounting,
            pharmacyType: item.pharmacyType,
            pharmacyNo: item.pharmacyNo,
            feeComposeType: item.productInfo?.feeComposeType,
            feeTypeId: item.productInfo?.feeTypeId,
        });
    });
    return chargeForm;
}

// 是否是精麻药
export function showPsychotropicNarcoticTips(item, verifyOutpatient) {
    const checkItems = verifyOutpatient?.verifyItems?.FITNESS?.find((it) => it.name === 'ControlledSubstancesRule')?.checkItems || [];
    return !!checkItems.find((it) => {
        try {
            if (!it.detail) return false;
            const itemDetail = JSON.parse(it.detail);
            return itemDetail.goodsId === item.goodsId;
        } catch (e) {
            console.error(e);
            return false;
        }
    });
}

/**
 * @desc 获取换药的item参数
 * <AUTHOR>
 * @date 2024/08/07 18:37:34
 * @param {Object} item 处方item
 * @return {Object}
 */
export function getFindMedicineItemStruct(item) {
    const {
        barCode,
        componentContentNum,
        componentContentUnit,
        extendSpec,
        goodsId,
        manufacturer,
        medicineCadn,
        medicineDosageNum,
        medicineDosageUnit,
        medcineNmpn,
        name,
        packageUnit,
        pieceUnit,
        displaySpec,
        shebaoNationalCode: shebaoCode,
    } = item.productInfo || {};
    // item 上没有displaySpec，换药之后goods会被替换，换药之前先存一份
    item.displaySpec = item.displaySpec || displaySpec;
    return {
        keyId: item.keyId,
        goodsId: goodsId || item.goodsId,
        manufacturer: manufacturer || item.manufacturer,
        medicineCadn: medicineCadn || item.medicineCadn || item.name,
        name: name || item.name,
        displaySpec: displaySpec || item.displaySpec,
        shebaoCode: shebaoCode || item.shebaoCode,
        barCode,
        medcineNmpn: medcineNmpn || item.medcineNmpn,
        componentContentNum,
        componentContentUnit,
        extendSpec,
        medicineDosageNum,
        medicineDosageUnit,
        packageCount: item.packageCount,
        packageUnit: packageUnit || item.packageUnit,
        pieceCount: item.unitCount,
        pieceUnit: pieceUnit || item.pieceUnit,
    };
}
