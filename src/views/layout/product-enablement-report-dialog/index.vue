<template>
    <abc-dialog
        class="product-enablement-report__dialog-wrapper"
        :title="''"
        :value="true"
        :disabled-keyboard="disabled"
        content-styles="width: 800px;padding: 0;box-sizing: border-box;"
        @input="(val) => $emit('input', val)"
    >
        <div class="product-enablement-report__dialog-header">
            <p class="product-enablement-report__clinic-name">
                {{ `${dataInfo.clinicName}，欢迎使用ABC${dataInfo.hisTypeName}` }}
            </p>
            <p class="product-enablement-report__title">
                这是您的产品启用报告，请查阅
            </p>
        </div>
        <div class="product-enablement-report__dialog-body">
            <div
                v-for="(serviceItem, serviceIndex) in dataInfo.forms"
                :key="serviceIndex"
                class="product-enablement-report__service-item-wrapper"
            >
                <p class="product-enablement-report__service-item-title">
                    {{ serviceItem.displayName }}
                </p>

                <template v-for="(item, index) in serviceItem.items">
                    <div
                        :key="`${serviceIndex }${ index}`"
                        class="product-enablement-report__service-item"
                    >
                        <span>
                            <img v-if="item.status" src="~assets/images/Icon-chosen-success.png" alt="" />
                            <span v-else class="radio-normal-box"></span>
                        </span>
                        <div>
                            <span class="product-enablement-report__service-item-label">{{ `${index + 1}、${item.name}` }}</span>
                            <p v-if="item.reason" class="product-enablement-report__service-item-reason">
                                备注： {{ getReasonLabel(item) }}
                            </p>
                        </div>
                    </div>
                </template>
            </div>
            <div class="product-enablement-report__client-sign">
                <p>
                    客户签名：<img
                        :src="dataInfo.customerSign"
                        alt="客户签名"
                    />
                </p>
                <p>专属销售：<span>{{ dataInfo.staffName || '' }}</span></p>
            </div>
        </div>
        <div class="product-enablement-report__dialog-footer">
            <abc-button type="primary" @click="handleConfirmClick">
                我已确认
            </abc-button>
        </div>
    </abc-dialog>
</template>
<script>
    import { productEnablementReportDialogService } from 'views/layout/product-enablement-report-dialog/product-enablement-report-dialog';

    export default {
        data() {
            return {
                dataInfo: {},
            };
        },
        computed: {
            // 是否禁用关闭弹窗，true不允许关闭
            disabled() {
                return true;
            },
        },
        mounted() {
            this.dataInfo = productEnablementReportDialogService.getStaffSopSheet() || {};
        },
        methods: {
            handleConfirmClick() {
                this.$emit('handleConfirmClick');
                this.$emit('input', false);
            },
            getReasonLabel(item) {
                const { rejectReasonOptions } = item.config;
                if (!rejectReasonOptions) {
                    return '';
                }
                const option = rejectReasonOptions.find((option) => item.reason === option.value);
                return option ? option.label : '';
            },
        },
    };
</script>
<style lang="scss">
@import 'src/styles/theme.scss';

.product-enablement-report__dialog-wrapper.abc-dialog-wrapper {
    .abc-dialog {
        border-radius: 10px;

        .abc-dialog-body {
            max-height: 80vh;
            overflow-y: unset;

            .product-enablement-report__dialog-header {
                width: 100%;
                height: 140px;
                padding: 24px;
                background-color: #01a2ff;
                background-image: url('~assets/images/<EMAIL>');
                background-size: 100%;
                border-radius: 10px 10px 0 0;

                .product-enablement-report__clinic-name {
                    margin-bottom: 4px;
                    font-size: 14px;
                    line-height: 26px;
                    color: $S2;
                    letter-spacing: 0;
                }

                .product-enablement-report__title {
                    font-size: 22px;
                    font-weight: 500;
                    line-height: 26px;
                    color: $S2;
                    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
                    letter-spacing: 0;
                }
            }

            .product-enablement-report__dialog-body {
                max-height: calc(80vh - 205px);
                padding: 24px;
                overflow-y: auto;

                .product-enablement-report__service-item-wrapper {
                    padding-bottom: 24px;
                    border-bottom: 1px dashed  $P6;
                }

                .product-enablement-report__service-item-wrapper + .product-enablement-report__service-item-wrapper {
                    margin-top: 24px;
                }

                .product-enablement-report__service-item {
                    display: flex;
                    align-items: flex-start;
                    justify-content: flex-start;
                    margin-top: 16px;

                    img {
                        width: 16px;
                        height: 16px;
                    }

                    .radio-normal-box {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        background: $P6;
                        border-radius: 50%;
                    }

                    .product-enablement-report__service-item-label {
                        padding-left: 12px;
                        line-height: 20px;
                    }

                    .product-enablement-report__service-item-reason {
                        padding-top: 8px;
                        padding-left: 12px;
                        font-size: 12px;
                        line-height: 16px;
                        color: #96a4b3;
                    }
                }

                .product-enablement-report__service-item-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: $T1;
                }

                .product-enablement-report__client-sign {
                    padding: 24px 0;

                    > p {
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;

                        > img {
                            width: 98px;
                            height: 36px;
                            background: $P5;
                            border-radius: var(--abc-border-radius-small);
                        }

                        >span {
                            display: inline-block;
                            width: 98px;
                            text-align: center;
                        }
                    }

                    p + p {
                        margin-top: 8px;
                    }
                }
            }

            .product-enablement-report__dialog-footer {
                width: 100%;
                height: 64px;
                padding: 0 24px;
                line-height: 64px;
                text-align: right;
                border-top: 1px solid #f4f4f4;
            }
        }
    }
}
</style>
