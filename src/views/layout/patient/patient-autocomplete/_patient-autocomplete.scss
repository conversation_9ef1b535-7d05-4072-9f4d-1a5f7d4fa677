@import "../../../../styles/theme";
@import "../../../../styles/abc-common.scss";

.patient-autocomplete-wrapper {
    position: relative;

    .empty-alert {
        position: absolute;
        top: 36px;
        z-index: 9999;
        width: 100%;
        padding: 24px 0;
        background-color: $S2;
        border: 1px solid var(--abc-color-P7);
        border-radius: var(--abc-border-radius-small);
        box-shadow: var(--abc-shadow-1);

        @include flex(column, center, center);

        .search-empty {
            font-size: 14px;
            color: $T3;
        }
    }

    .append-input {
        width: auto;
        min-width: 30px;
        border: 1px solid $P1;
        border-left: none;
        border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;

        &:hover {
            background-color: $P4;
        }
    }

    .card-btn-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 62px;
        height: 100%;

        .cis-icon-read-card {
            margin-right: 4px;
            font-size: 14px;
            color: $T1;
        }
    }
}

.patient-autocomplete-suggestions-popper {
    top: 36px;
    max-width: 520px;

    .suggestion-title {
        display: flex;
        align-items: center;
        padding: 0 16px;

        .patient-info {
            width: 300px;

            &.health-public-patient-info {
                width: 330px;
            }
        }
    }

    .patient-suggestions.suggestions-item {
        z-index: 1009;
        display: flex;
        align-items: center;
        min-height: 36px;
        padding: 0 16px;
        overflow: hidden;
        white-space: nowrap;
        cursor: pointer;
        user-select: none;
        outline: 0;

        &[disabled] {
            color: #687481;
            cursor: not-allowed;
        }

        .public-health-name-wrapper {
            display: flex;
            flex: none;
            width: 110px;

            .public-health-name {
                max-width: 72px;
            }

            .public-health-tag {
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 20px;
                padding: 0 4px;
                margin-left: 4px;
                font-size: 12px;
                color: $G1;
                background: $G4;
                border: 1px solid $G5;
                border-radius: var(--abc-border-radius-small);
            }
        }

        .name {
            flex: none;
            width: 80px;

            .vip::after {
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-left: 4px;
                content: '';
                background: url('~assets/images/vip.png') no-repeat;
                background-size: 12px 12px;
            }
        }

        .mobile {
            width: 110px;
            padding-left: 10px;
        }

        .sex {
            width: 40px;
            text-align: right;
        }

        .age {
            width: 70px;
            text-align: right;
        }

        .mr {
            flex: 1;
            color: $T2;
        }

        .bed-info {
            width: 40px;
        }

        &.selected {
            .mr {
                color: #ffffff;
            }
        }
    }
}

// 患者搜索字段自定义-样式处理
.patient-autocomplete-suggestions-popper-custom {
    top: 36px;
    max-width: 900px;

    .suggestion-title {
        display: flex;
        align-items: center;
        padding-left: 16px;

        .patient-info {
            width: 300px;

            &.health-public-patient-info {
                width: 330px;
            }
        }
    }

    .suggestion-title-custom {
        .name {
            width: 80px;
            margin-left: 10px;
        }

        .mobile {
            width: 88px;
            margin-left: 10px;
        }

        .sex {
            width: 28px;
            margin-left: 10px;
        }

        .age {
            width: 84px;
            margin-left: 10px;
        }

        .mr {
            flex: 1;
            margin-left: 10px;
            color: $T2;
        }

        .sn {
            width: 48px;
            margin-left: 10px;
        }

        .company {
            width: 70px;
            margin-left: 10px;
        }

        .remark {
            width: 70px;
            margin-left: 10px;
        }

        .address {
            width: 98px;
            margin-left: 10px;
        }

        .last {
            flex: 1;
            max-width: 177px;
            margin-left: 10px;
        }

        .idCard {
            width: 144px;
            margin-left: 10px;
        }

        div:first-child {
            margin-left: 0;
        }
    }

    .patient-suggestions.suggestions-item {
        z-index: 1009;
        display: flex;
        align-items: center;
        min-height: 36px;
        padding: 0 16px;
        overflow: hidden;
        white-space: nowrap;
        cursor: pointer;
        user-select: none;
        outline: 0;

        div:first-child {
            flex: none;
            margin-left: 0;
        }

        &[disabled] {
            color: #687481;
            cursor: not-allowed;
        }

        .public-health-name-wrapper {
            display: flex;
            flex: none;
            width: 110px;

            .public-health-name {
                max-width: 72px;
            }

            .public-health-tag {
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 20px;
                padding: 0 4px;
                margin-left: 4px;
                font-size: 12px;
                color: $G1;
                background: $G4;
                border: 1px solid $G5;
                border-radius: var(--abc-border-radius-small);
            }
        }

        .name {
            flex: none;
            width: 80px;
            margin-left: 10px;

            .vip::after {
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-left: 4px;
                content: '';
                background: url('~assets/images/vip.png') no-repeat;
                background-size: 12px 12px;
            }
        }

        .mobile {
            width: 88px;
            margin-left: 10px;
        }

        .sex {
            width: 28px;
            margin-left: 10px;
        }

        .age {
            width: 84px;
            margin-left: 10px;
        }

        .mr {
            flex: 1;
            max-width: 180px;
            margin-left: 10px;
            color: $T2;
        }

        .sn {
            width: 48px;
            margin-left: 10px;
        }

        .company {
            width: 70px;
            margin-left: 10px;
        }

        .remark {
            width: 70px;
            margin-left: 10px;
        }

        .address {
            width: 98px;
            margin-left: 10px;
        }

        .last {
            flex: 1;
            max-width: 180px;
            margin-left: 10px;
        }

        .idCard {
            width: 144px;
            margin-left: 10px;
        }

        .bed-info {
            width: 40px;
        }

        &.selected {
            .mr {
                color: #ffffff;
            }
        }
    }

    .suggestion-fixed-footer-wrapper {
        height: 32px;
        padding: 0 var(--abc-paddingLR-s);
        background: var(--abc-color-cp-grey2);
        border-top: 1px solid var(--abc-color-P8);
        border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);
    }
}
