<script>
    export default {
        name: 'GoodsSuggestionTitle',
        functional: true,
        props: {
            titles: {
                type: Array,
                required: true,
                default: () => {
                    return [];
                },
            },
        },
        render (createElement, context) {
            const { titles } = context.props;
            return createElement(
                'div',
                {
                    attrs: {
                        class: 'suggestion-title',
                    },
                },
                titles.map((title) => {
                    return createElement(
                        'div',
                        {
                            attrs: {
                                style: title.style,
                            },
                        },
                        title.label,
                    );
                }),
            );
        },
    };
</script>
