import AbcSuccess from 'components/an-success/index.vue';
import BindPatientDialog from './bind-patient-dialog.vue';
import PatientListDialog from './patient-list-dialog.vue';

import ScanCode from 'utils/ScanCode';
import CrmAPI from 'api/crm';
import SettingsAPI from 'api/settings';
import {
    mapState, mapGetters, mapMutations,
} from 'vuex';
import {
    isWuhanHealthyCode, isLength77HealthyCode, formatAge, noop,
} from '@/utils';
import HealthCardImg from '@/assets/images/social/<EMAIL>';
import IdCardImg from '@/assets/images/social/<EMAIL>';
import IdCardImgLarge from '@/assets/images/social/reader-id-demo.png';
import {
    ReadCardShortcutType,
    ReadCardType,
} from 'views/layout/read-card/costants.js';
import { debounce } from 'utils/lodash';
import Store from 'utils/localStorage-handler';
import {
    ReaderStatusEnum,
    ReadIdCardOptions,
    ReadIdCardType,
} from 'views/layout/read-card/id-card-reader';
import IdCardReaderService from 'views/layout/read-card/id-card-reader/id-card-reader-service';
import Response from 'src/utils/Response.js';
import { clone } from '@abc/utils';
const SOCIAL_ID_CARD_READER_KEY = 'social_id_card_reader';

const stepConst = {
    SELECT_TYPE: 0, // 选择读卡类型
    AWAIT_QRCODE: 1, // 等待扫码
    AWAIT_PASSWORD: 2, // 等待密码输入
    AWAIT_READING: 3, // 等待读取信息
    ALERT_SUCCESS: 4, // 提示成功
    ALERT_ERROR: 5, // 提示错误
    DISPLAY_INFO: 6, // 展示信息
    AWAIT_IDCARDNO: 7, // 等待输入身份证号
    SELECT_ID_CARD_READER: 8, // 身份证阅读器设置
    CONNECTING: 9, // 建立通信中
    CUSTOM_PAGE1: 10, // 自定义页面1
    SHORTCUT_PREPARE: 11, // 快捷操作
};

const SceneTypeEnum = Object.freeze({
    READ_CARD: 'READ_CARD',//读卡挂号
    SOCIAL_MARK: 'SOCIAL_MARK',//医保标识
    HUASHI_ID_CARD: 'HUASHI_ID_CARD', // 华视读取身份证
});

export default {
    components: {
        AbcSuccess,
        BindPatientDialog,
        PatientListDialog,
    },
    props: {
        // 仅支持医保刷卡
        fillShebaoCardInfo: {
            type: Boolean,
            default: false,
        },
        // 患者就诊单id
        patientOrderId: {
            type: String,
            default: '',
        },
        // 患者信息
        patientName: {
            type: String,
            default: '',
        },
        // 科室编码 - 成都电子健康卡获取信息需要
        departmentId: {
            type: String,
            default: '',
        },
        // 调用的场景
        confirmText: {
            type: String,
            default: '确定',
        },
        // 只读卡不做任何绑定操作
        isReadCardOnly: {
            type: Boolean,
            default: false,
        },
        // 只获取卡信息
        isGetCardInfoOnly: {
            type: Boolean,
            default: false,
        },
        // 确定
        onConfirm: {
            type: Function,
            default: noop,
        },
        // 取消
        onCancel: {
            type: Function,
            default: noop,
        },
        // 快捷操作
        shortcut: {
            type: Object,
            default: null,
        },
        businessType: String,
    },
    data() {
        return {
            visible: false,

            sceneType: SceneTypeEnum.READ_CARD, // 标志view-card-info展示样式
            stepConst,
            step: '',
            cardType: '', // 读卡类型
            cardImg: '', // 读卡类型图片
            tempPassword: '', // 临时输入密码
            tempCallBack: null, // 临时回调函数
            alertErrorTitle: '', // 失败时，标题
            alertErrorMessage: '', // 失败时，详细信息
            alertErrorBtnText: '', // 失败时，确认按钮文字
            alertSuccessTitle: '', // 读卡成功提示标题
            awaitReadingTitle: '', // 等待读卡提示标题
            awaitQrcodeTitle: '', // 等待扫码提示标题
            scanCode: null,
            errorPasswordCount: 0, // 密码错误次数
            loadingConfirm: false,
            cardInfoResponse: null,
            idCardNo: '', // 身份证号码
            psnName: '', // 人员姓名
            insuplcAdmdvs: [], // 参保地
            psnCertType: '',//人员证件类型
            keyword: '', // 参保地搜索

            cardInfoViewInstance: null,

            showBindInfo: false,
            showPatientList: false,
            matchedPatients: [], // 当前匹配到的患者数量
            bindedPatientList: [], // 当前医保卡绑定的患者信息
            repeatErrText: '',
            repeatErrPatient: null,

            targetStep: null,
            // 身份证读卡信息
            idCardInfo: null,
            idCardService: {},
            readCardType: ReadCardType.SHE_BAO, // 0 社保卡  1身份证
            showCardInfoDialog: false,

            cardTypeOptions: [],
            idCardReaderPicker: null,
        };
    },
    computed: {
        ReaderStatusEnum() {
            return ReaderStatusEnum;
        },
        ...mapState('socialPc', [
            'basicInfo',
        ]),
        ...mapGetters([
            'isEnableWuhanHealthyCard',
            'isEnableChengduHealthCard',
            'isEnableMianyangHealthCard',
            'isEnableIdCardReader',
        ]),

        // 当前选中读卡类型
        currentCardType() {
            const target = this.cardTypeOptions.find((item) => item.value === this.cardType) ||
                this.idCardReaderPicker.readers.find((item) => item.value === this.cardType);
            return target || null;
        },
        // 是否健康卡
        isHealthyCard() {
            return !!(this.currentCardType?.isHealthyCard);
        },
        // 是否社保卡
        isSocialCard() {
            return !this.isHealthyCard && !this.isIdCardReader;
        },
        // 新中新身份证读卡
        isXzx() {
            return IdCardReaderService.isIdCardReader('xzxIdCard');
        },
        isIdCardReader() {
            return IdCardReaderService.isIdCardReader(this.cardType);
        },
        // 信息展示title
        displayInfoTitle() {
            const { label = '' } = this.currentCardType || {};
            return this.isSocialCard ? '社保卡' : label;
        },
        // 等待输入密码的提示文字
        awaitPasswordAlertTxt() {
            // 社保卡密码输入时提示信息
            let alertTxt = '';
            if (this.tempPassword.length === 0) {
                // 什么都没有输入
                alertTxt =
                    this.errorPasswordCount < 2 ?
                        '等待密码输入...' :
                        '<span style="color: #FF9933">密码错误，等待重新输入...</span>';
            } else {
                // 密码输入中
                alertTxt = '密码输入中...';
            }
            return alertTxt;
        },
        // 是否不同患者姓名
        isDiffPatientName() {
            if (this.cardInfoResponse) {
                const { name } = this.cardInfoResponse.data || {};
                return name !== this.patientName;
            }
            return false;
        },

        currentCardInfo() {
            if (this.cardInfoResponse) {
                if (this.readCardType === ReadCardType.SHE_BAO) {
                    return {
                        ...this.cardInfoResponse.data,
                    };
                }
                return this.idCardInfo;
            }
            return {};
        },
        // 社保卡信息
        shebaoCardInfo() {
            if (this.cardInfoResponse) {
                return this.cardInfoResponse.data;
            }
            return null;
        },
        // pc 读卡信息展示
        displayInfoList() {
            let infoList = [];
            infoList = [
                {
                    label: '姓名',
                    value: this.currentCardInfo.name || '-',
                },
                {
                    label: '性别',
                    value: this.currentCardInfo.sex || '-',
                },
                {
                    label: '生日',
                    value: this.currentCardInfo.birthday || '-',
                },
            ];
            if (this.isHealthyCard) {
                infoList.push(
                    {
                        label: '手机',
                        value: this.currentCardInfo.mobile || '-',
                    },
                );
            }
            infoList.push(
                {
                    label: '身份证号',
                    value: this.currentCardInfo.idCard || '-',
                },
            );
            if (this.isIdCardReader) {
                infoList.push({
                    label: '住址',
                    value: this.currentCardInfo.addressInfo,
                });
            }
            return infoList;
        },
        // 渲染可选参保地区划
        renderInsuplcAdmdvsOptions() {
            const { cityAreaCodeOptions } = this.$abcSocialSecurity.$national.options;
            const options = [];
            const objMap = new Map();
            for (const item of clone(cityAreaCodeOptions)) {
                const areaCode = item.value;
                let parentCode = '';
                const areaCodeTwoLength = areaCode.slice(0, 2);
                const areaCodeFourLength = areaCode.slice(0, 4);
                const isGovernmentCity = areaCodeTwoLength === '11' ||
                    areaCodeTwoLength === '12' ||
                    areaCodeTwoLength === '31' ||
                    areaCodeTwoLength === '50';
                if (isGovernmentCity || areaCode === `${areaCodeFourLength}00`) {
                    // 市级或直辖市地区设置父级区划为省级
                    parentCode = `${areaCodeTwoLength}0000`;
                }
                if (!areaCode.endsWith('00') && !isGovernmentCity) {
                    // 区级设置父级区划为市级
                    parentCode = `${areaCodeFourLength}00`;
                }
                if (areaCode === '341499') {
                    // 如果是巢湖区特殊编码归类到合肥市
                    parentCode = '340100';
                }
                if (areaCode === '419001') {
                    // 如果是济源市特殊编码归类到济源市
                    parentCode = '411800';
                }
                if (objMap.has(areaCode)) {
                    objMap.set(areaCode, {
                        ...item, ...objMap.get(areaCode),
                    });
                } else {
                    objMap.set(areaCode, item);
                }
                const treeItem = objMap.get(areaCode);
                if (parentCode === areaCode) {
                    options.push(treeItem);
                } else {
                    if (!objMap.get(parentCode)) {
                        objMap.set(parentCode, {});
                    }
                    if (!objMap.get(parentCode).childs) {
                        const parentItem = clone(objMap.get(parentCode));
                        if (parentItem.value?.endsWith('0000')) {
                            Object.assign(parentItem, {
                                name: `${parentItem.value} (${parentItem.name})`,
                                parentCode,
                            });
                        }
                        objMap.get(parentCode).childs = [parentItem];
                    }
                    Object.assign(treeItem, {
                        name: `${treeItem.value} (${treeItem.name})`,
                        parentCode,
                    });
                    objMap.get(parentCode).childs.push(treeItem);
                }
            }
            return {
                options,
                objMap,
            };
        },
        isShowSelectIdCardReaderSetting() {
            return [stepConst.SELECT_TYPE, stepConst.AWAIT_READING, stepConst.ALERT_ERROR, stepConst.AWAIT_IDCARDNO].includes(this.step);
        },
        currentSelectReader() {
            return this.idCardReaderPicker && this.idCardReaderPicker.readers.find((it) => it.readerId === this.idCardReaderPicker.currentSelectReaderId);
        },
        currentConnectReader() {
            return this.idCardReaderPicker && this.idCardReaderPicker.readers.find((it) => it.readerId === this.idCardReaderPicker.currentConnectReaderId);
        },
        isDisabledSocial() {
            return this.$abcSocialSecurity.isDisabledSocial;
        },
        isEnableTransProvincial() {
            return this.$abcSocialSecurity.config.isSupportTransProvincial;
        },
        isEnablePsnCertType() {
            return this.$abcSocialSecurity.config.isSupportSelectPsnCertType;
        },
        isEnableTransProvincialAndPsnCertType() {
            return this.$abcSocialSecurity.isEnableSocial && (this.isEnableTransProvincial || this.isEnablePsnCertType);
        },
    },
    created() {
        this.initCardTypeOptions();
        this.initStep();
        this._debounceSearch = debounce(this.handleSearch, 400, true);
        if (this.shortcut) {
            if (this.shortcut.type === ReadCardShortcutType.READER) {
                if (this.isShowShortcutPrepare(this.shortcut.data)) {
                    this.step = stepConst.SHORTCUT_PREPARE;
                } else {
                    this.confirmCardTypeHandler(this.shortcut.data);
                }
            }
            if (this.shortcut.type === ReadCardShortcutType.SETTING) {
                this.showSelectIdCardReader();
            }
        }
    },
    beforeDestroy() {
        clearTimeout(this._timer);
        this.destroyScanCode();

        this.idCardReaderPickerDestroy();
    },
    methods: {
        ...mapMutations('crm', ['updateSourceList']),
        isShowShortcutPrepare(data) {
            const {
                isPicker, readers, currentSelectReaderId,
            } = data || {};
            let isSocialCardReader = false;
            if (isPicker) {
                const currentReader = readers.find((it) => it.readerId === currentSelectReaderId);
                if (!currentReader) {
                    return false;
                }
                isSocialCardReader = currentReader.isSocialCardReader;
            }
            if (!this.isEnableTransProvincialAndPsnCertType) {
                return false;
            }
            if (!isPicker) {
                return true;
            }
            return isSocialCardReader;
        },
        handleContinueShortcutRead() {
            if (this.shortcut.type === ReadCardShortcutType.READER) {
                this.confirmCardTypeHandler(this.shortcut.data);
            } else if (this.shortcut.type === ReadCardShortcutType.SETTING) {
                this.confirmCardTypeHandler(
                    this.idCardReaderPicker,
                );
            }
        },
        /**
         * 处理搜索，赋值关键词
         * <AUTHOR>
         * @date 2021-12-28
         * @param {String} keyword 关键词
         */
        handleSearch(keyword) {
            this.keyword = keyword.toLocaleLowerCase();
        },

        idCardReaderPickerDestroy() {
            if (this.idCardReaderPicker?.readers?.length) {
                this.idCardReaderPicker.readers.forEach((reader) => {
                    reader.service?.destroy?.();
                });
            }
        },

        showSelectIdCardReader() {
            this.idCardReaderPicker.currentConnectReaderId = this.idCardReaderPicker.currentSelectReaderId || this.idCardReaderPicker.readers[0].readerId;
            this.step = stepConst.SELECT_ID_CARD_READER;
        },

        showAwaitCode(awaitQrcodeTitle) {
            this.step = stepConst.AWAIT_QRCODE;
            this.awaitQrcodeTitle = awaitQrcodeTitle || '';
        },

        showReading(awaitReadingTitle) {
            this.step = stepConst.AWAIT_READING;
            this.awaitReadingTitle = awaitReadingTitle || '';
        },

        showSuccess(alertSuccessTitle) {
            this.step = stepConst.ALERT_SUCCESS;
            this.alertSuccessTitle = alertSuccessTitle || '';
        },

        async showCardInfo() {
            if (this.isSocialCard) {
                const bindPattientInfo = this.getBindedPatientInfo();
                await this.$abcSocialSecurity.showCardInfo({
                    cardInfo: this.shebaoCardInfo,
                    confirmText: this.confirmText,
                    userInfoList: bindPattientInfo ? [{
                        label: '绑定患者',
                        value: this.getBindedPatientInfo(),
                    }] : [],
                    onConfirm: () => {
                        this.onClickConfirm();
                    },
                }, (instance) => {
                    this.cardInfoViewInstance = instance;
                });
                this.onClickClose();
            } else {
                this.step = stepConst.DISPLAY_INFO;
                // this.showCardInfoDialog = true;
            }
        },
        /**
         * 等待密码输入
         * <AUTHOR>
         * @date 2020-12-30
         * @returns {Promise}
         */
        createPasswordPromise() {
            return new Promise((resolve) => {
                this.step = stepConst.AWAIT_PASSWORD;
                this.tempPassword = '';
                this.tempCallBack = (password) => {
                    password && resolve(password);
                };
            });
        },
        /**
         * 等待身份证号输入
         * <AUTHOR>
         * @date 2021-18-25
         * @returns {Promise}
         */
        createIdCardNoPromise() {
            return new Promise((resolve) => {
                this.step = stepConst.AWAIT_IDCARDNO;
                this.idCardNo = '';
                this.psnName = '';
                this.tempCallBack = (idCardNo, psnName) => {
                    idCardNo && resolve({
                        idCardNo,
                        psnName,
                    });
                };
            });
        },
        // 错误信息提示
        awaitErrorHandler(btnText = '', title = '', message = '') {
            return new Promise((resolve) => {
                this.step = stepConst.ALERT_ERROR;
                this.alertErrorBtnText = btnText;
                this.alertErrorTitle = title;
                this.alertErrorMessage = message;
                if (btnText) {
                    this.tempCallBack = (isAgain) => {
                        this.alertErrorTitle = '';
                        this.alertErrorMessage = '';
                        this.alertErrorBtnText = '';
                        resolve(isAgain);
                    };
                }
            });
        },

        /**
         * @desc 扫电子健康卡业务逻辑
         * <AUTHOR>
         * @date 2022-07-04 18:18:13
         */
        async scanCodeHandler() {
            let isAgain = false;
            do {
                isAgain = false;
                this.showAwaitCode(this.currentCardType?.scanCodeConf?.title);
                this.destroyScanCode();
                const response = await this.createScanCodePromise();

                this.destroyScanCode();
                if (response.status === false) {
                    isAgain = await this.createAgainPromise('重新扫码', '扫码失败', response.message);
                } else {
                    // 一般扫码成功都不需要在走输入密码流程，因此直接读卡信息
                    this.directReadCardHandler('', response.data.qrCode);
                }
            } while (isAgain);
        },

        /**
         * @desc 需要输入密码的社保流程
         *       1. 循环读卡
         *       2. 输入密码校验
         *       3. 第一次读卡带入的密码是空
         * <AUTHOR>
         * @date 2022-07-05 11:09:09
         * @params
         * @return
         */
        async inputPasswordHandler() {
            this.errorPasswordCount = 0;
            let isAgain = false;
            let password = '';
            do {
                isAgain = false;
                const response = await this.loopReadCardHandler(password);
                if (response.status === false) {
                    // 密码错误，没有提示再次输入密码
                    if (response.isWrongPassword) {
                        this.errorPasswordCount += 1;
                        password = await this.createPasswordPromise();
                        isAgain = true;
                    } else {
                        // 读卡除了密码错误其他失败原因
                        this.awaitErrorHandler('', '读取失败', response.message);
                    }
                }
            } while (isAgain);
        },

        /**
         * @desc 立即输入密码，然后通过输入的密码去读卡
         * <AUTHOR>
         * @date 2022-07-05 11:26:55
         */
        async inputPasswordImmediateHandler() {
            let password = '';
            password = await this.createPasswordPromise();
            const response = await this.loopReadCardHandler(password);
            if (response.status === false) {
                this.awaitErrorHandler('', '读取失败', response.message);
            }
        },

        /**
         * @desc 身份证刷卡
         *       1. 显示身份证输入框
         * <AUTHOR>
         * @date 2022-07-05 11:57:18
         * @params
         * @return
         */
        async inputIdCardHandler() {
            const {
                needInputIdCardNo,
            } = this.currentCardType;

            const inputInfo = await this.createIdCardNoPromise();
            const response = await this.loopReadCardHandler('', '', inputInfo.idCardNo, inputInfo.psnName);
            if (response.status === false && needInputIdCardNo) {
                await this.awaitErrorHandler('', '读取失败', response.message);
            }
        },

        /**
         * @desc 直接读卡，
         * 成功处理数据，失败直接终止
         * <AUTHOR>
         * @date 2022-07-05 11:05:43
         * @params
         * @return
         */
        async directReadCardHandler(...arg) {
            this.showReading(this.currentCardType?.awaitReadingTitle);
            const response = await this.readCardHandler(...arg);

            if (!response) {
                this.awaitErrorHandler('', '读取失败');
                return Response.error();
            }

            if (response.status === false) {
                // 读卡失败场景下，如果没有message或者用户主动取消，直接关闭
                if (!response.message || response.isUserCancel) {
                    this.onClickClose();
                    return;
                }
                if (response.isSocialError) {
                    await this.$abcSocialSecurity.showSocialErrorMessage({
                        title: '读取失败',
                        response,
                    });
                } else {
                    this.awaitErrorHandler('', '读取失败', response.message);
                }
            } else {
                this.readSuccessHandler(response);
            }
        },
        /**
         * @desc 循环读卡，
         *       1.专网异常，重试
         *       2.判断身份证，身份证手动输入，终止
         *       3.其他异常，终止
         * <AUTHOR>
         * @date 2022-07-05 11:05:43
         * @params
         * @return
         */
        async loopReadCardHandler(...arg) {
            let isAgain = false;
            let response = null;
            do {
                isAgain = false;
                this.showReading(this.currentCardType.awaitReadingTitle);
                response = await this.readCardHandler(...arg);

                if (!response) {
                    this.awaitErrorHandler('', '读取失败');
                    return Response.error();
                }
                // 读卡失败场景下，如果没有message或者用户主动取消，直接关闭
                if (!response.status && (!response.message || response.isUserCancel)) {
                    this.onClickClose();
                    return;
                }
                if (response.status) {
                    this.readSuccessHandler(response);
                } else if (response.isWrongReadIdCard) {
                    let message = response.message === '连接设备失败' ? response.message : '居民身份证读取失败';
                    if (this.isXzx) {
                        message = response.message;
                    }
                    isAgain = await this.awaitErrorHandler('重新读取', message, '');
                } else if (response.status === false && response.data === 'readCardError') {
                    const needInputIdCard = await this.awaitErrorHandler('手动输入', '读取失败', response.message);
                    if (needInputIdCard) {
                        await this.inputIdCardHandler();
                    }
                } else if (response.status === false && response.data === 'enterCardId') {
                    await this.inputIdCardHandler();
                } else if (response.status === false && response.isSocialError) {
                    await this.$abcSocialSecurity.showSocialErrorMessage({
                        title: '读取失败',
                        response,
                    });
                } else if (response.status === false && response.isNetwordError) {
                    isAgain = await this.awaitErrorHandler('重新读取', '专网异常', response.message);
                } else if (response.status === false) {
                    isAgain = await this.awaitErrorHandler('重新读取', '读取失败', response.message);
                }
            } while (isAgain);

            return response;
        },
        /**
         * @desc 读卡
         *       1. 判断社保卡还是健康卡，
         *       2. 返回读取的数据.
         * <AUTHOR>
         * @date 2022-07-05 10:51:57
         * @params
         * @return response.status true 成功 false 失败
         */
        async readCardHandler(password = '', qrCode = '', idCardNo = '', psnName = '') {
            let response = null;
            if (this.isHealthyCard) {
                response = await this.readHealthyCard(qrCode);
            } else if (this.isIdCardReader) {
                response = await this.handleReadIdCard();
            } else {
                // 广州手动输入身份证信息配置，开启后将跳过读卡器读身份证
                const readIdCardAction = window.$platform.context.store.getters['social/readIdCardActions'];
                // 选择的身份证 && 配置有读身份证方式，并且为手动输入身份证号，当前无身份证号和姓名信息
                if (this.currentCardType.value === '02' && readIdCardAction && readIdCardAction === 2 && !idCardNo && !psnName) {
                    response = {
                        status: false,
                        data: 'enterCardId',
                    };
                } else {
                    const params = {
                        cardType: this.cardType,
                        password,
                        qrCode,
                        idCardNo,
                        insuplcAdmdvs: this.insuplcAdmdvs[this.insuplcAdmdvs.length - 1]?.value || '',
                        psnCertType: this.psnCertType,
                        psnName,
                        businessType: this.businessType,
                    };
                    response = await this.readSocialCard(params);
                }
            }
            return response;
        },

        /**
         * 初始化步骤
         * <AUTHOR>
         * @date 2020-12-30
         */
        initStep() {
            this.step = stepConst.SELECT_TYPE;
        },

        async confirmCardTypeHandler({
            value,
            isPicker,
        }) {
            if (isPicker) {
                if (!this.idCardReaderPicker.currentSelectReaderId) {
                    this.showSelectIdCardReader();
                } else {
                    const currentCardType = this.idCardReaderPicker.readers.find((it) => it.readerId === this.idCardReaderPicker.currentSelectReaderId);
                    if (currentCardType) {
                        await this.confirmCardTypeHandler(currentCardType);
                    } else {
                        this.showSelectIdCardReader();
                    }
                }
                return;
            }
            this.cardType = value || this.cardType;
            this.readCardType = this.isIdCardReader ? ReadCardType.ID_CARD : ReadCardType.SHE_BAO;
            await this.$nextTick();
            const {
                needScanCode,
                needPassword,
                needInputIdCardNo,
                needImmediatePassword,
            } = this.currentCardType;
            if (needScanCode) {
                // 需要扫码
                await this.scanCodeHandler();
            } else if (needPassword) {
                // 需要密码
                await this.inputPasswordHandler();
            } else if (needImmediatePassword) {
                await this.inputPasswordImmediateHandler();
            } else if (needInputIdCardNo) {
                // 需要输入身份证号码
                await this.inputIdCardHandler();
            } else {
                // 直接读信息
                await this.loopReadCardHandler();
            }
        },

        /**
         * 处理读卡成功
         * <AUTHOR>
         * @date 2020-12-30
         * @param {Object} response 卡信息响应
         */
        async readSuccessHandler(response) {
            const isShowSuccess = this.isShowSuccess === undefined || this.isShowSuccess;
            isShowSuccess && this.showSuccess(this.currentCardType.alertSuccessTitle);
            this.cardInfoResponse = response;
            this.idCardInfo = response.idCardInfo || null;

            if (this.isGetCardInfoOnly) {
                let response = null;
                // 身份证读卡直接返回身份证信息
                if (this.isIdCardReader) {
                    response = {
                        ...(this.idCardInfo || {}),
                        cardType: this.readCardType,
                    };
                } else {
                    // 这里要转换一下数据结构，因为社保卡和身份证读卡的数据结构不一样
                    const { data } = this.cardInfoResponse;
                    response = {
                        name: data.name,
                        sex: data.sex,
                        birthday: data.birthday,
                        idCard: data.idCardNo,
                        cardType: this.readCardType,
                    };
                }

                this._timer = setTimeout(() => {
                    this.emitConfirmHandler(response);
                    this.onClickClose();
                }, 1500);

                return;
            }


            // 非补刷社保卡的逻辑才会拉取绑定的患者数据
            if (!this.fillShebaoCardInfo || this.isIdCardReader) {
                await this.fetchBindedPatientList({
                    cardNo: this.currentCardInfo.cardNo,
                    cardType: this.readCardType,
                    idCard: this.currentCardInfo.idCard,
                    mobile: this.currentCardInfo.mobile,
                    name: this.currentCardInfo.name,
                    sex: this.currentCardInfo.sex,
                });
            }

            if (this.isShowCardInfoImmediate) {
                await this.showCardInfo();
            } else {
                this._timer = setTimeout(() => {
                    this.showCardInfo();
                }, 1500);
            }
        },
        /**
         * 读健康卡信息
         * <AUTHOR>
         * @date 2020-12-30
         * @param {String} qrCode 二维码数据
         * @returns {Promise<Response>}
         */
        async readHealthyCard(qrCode) {
            try {
                const departmentMedicalCode = await this.fetchDepartmentMedicalCode();
                const { data } = await CrmAPI.queryPatientByExternalCode(qrCode, departmentMedicalCode);
                return {
                    status: true,
                    data: {
                        ...data,
                        healthCard: {
                            qrCode,
                            healthCardId: data.healthCardIds?.[0],
                        },
                    },
                };
            } catch (error) {
                return {
                    status: false, message: '无法查询到该卡号的信息',
                };
            }
        },

        async handleReadIdCard() {
            try {
                if (!await this.currentCardType.service.checkConnected()) {
                    return {
                        status: false,
                        isWrongReadIdCard: true,
                        message: '读卡失败，请确认读卡器是否安装正常，读卡软件是否正常启动',
                    };
                }
                const data = await this.currentCardType.service.readCard();
                return {
                    status: true,
                    idCardInfo: data,
                };
            } catch (e) {
                return {
                    status: false,
                    isWrongReadIdCard: true,
                    message: e,
                };
            }
        },
        /**
         * 读社保卡信息
         * <AUTHOR>
         * @date 2020-12-30
         * @param {Object} params 参数
         * @returns {Promise<Response>}
         */
        async readSocialCard(params) {
            const response = await this.$abcSocialSecurity.readCardInfo(params);
            if (response.status === false && this.currentCardType?.value === '02' && !this.currentCardType?.needInputIdCardNo) {
                return {
                    ...response,
                    data: 'readCardError',
                };
            }
            return response;
        },

        /**
         * 处理点击确认再次尝试
         * <AUTHOR>
         * @date 2020-12-30
         * @returns {Promise<Boolean>}
         */
        createAgainPromise(btnText, title, message = '') {
            return new Promise((resolve) => {
                this.step = stepConst.ALERT_ERROR;
                this.alertErrorTitle = title;
                this.alertErrorMessage = message;
                this.alertErrorBtnText = btnText;
                this.tempCallBack = (isAgain) => {
                    this.alertErrorTitle = '';
                    this.alertErrorMessage = '';
                    this.alertErrorBtnText = '';
                    resolve(isAgain);
                };
            });
        },
        /**
         * 获取扫码数据
         * <AUTHOR>
         * @date 2020-12-30
         * @returns {Promise}
         */
        createScanCodePromise() {
            return new Promise((resolve) => {
                try {
                    const {
                        validators,
                        regExp,
                        alertText,
                    } = this.currentCardType.scanCodeConf;
                    const options = {
                        el: null,
                        validators,
                        regExp,
                    };
                    const listener = (event, status, qrCode) => {
                        if (status === ScanCode.Status.ERROR) {
                            // 扫码失败
                            resolve({
                                status: false,
                                message: alertText,
                            });
                        } else if (status === ScanCode.Status.SUCCESS) {
                            // 扫码成功
                            resolve({
                                status: true,
                                data: { qrCode },
                            });
                        } else {
                            // 其他消息不管
                        }
                    };
                    this.scanCode = new ScanCode(options, listener);
                    this.scanCode.startDetect();
                } catch (error) {
                    console.log('getScanCode error', error);
                    resolve({
                        status: false,
                        message: error.message,
                    });
                }
            });
        },
        /**
         * 销毁扫码动作监听器
         * <AUTHOR>
         * @date 2020-12-30
         */
        destroyScanCode() {
            if (this.scanCode) {
                this.scanCode.stopDetect();
                this.scanCode = null;
            }
        },
        /**
         * @desc 点击开始业务逻辑
         * <AUTHOR>
         * @date 2021-09-27 10:54:49
         * @params 1. 当前医保卡已绑定唯一的患者，直接走业务
         *         2. 医保卡绑定多个患者，弹出患者列表，选择患者或者新建患者进行医保卡的绑定
         *         3. 没有绑定患者，需要根据患者的姓名+身份证号，姓名+手机号进行匹配
         * @return
         */
        async onClickConfirm() {
            if (this.loadingConfirm) return;
            if (this.fillShebaoCardInfo && this.isDiffPatientName) {
                // 患者社保卡信息补充，需要患者姓名和持卡人姓名相同
                return this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: '操作失败：医保卡姓名与患者姓名不一致，<br/>请更正患者姓名或使用本人医保卡后重试。',
                    onClose: () => this.onClickClose(),
                });
            }

            if (this.isReadCardOnly) {
                let response = null;
                // 身份证读卡直接返回身份证信息
                if (this.isIdCardReader) {
                    response = this.cardInfoResponse.idCardInfo;
                } else {
                    // 这里要转换一下数据结构，因为社保卡和身份证读卡的数据结构不一样
                    const { data } = this.cardInfoResponse;
                    response = {
                        name: data.name,
                        sex: data.sex,
                        birthday: data.birthday,
                        idCard: data.idCardNo,
                    };
                }
                this.emitConfirmHandler(response);
                this.onClickClose();
                return;
            }

            let patient = null;
            if (this.isHealthyCard) {
                // 健康卡
                patient = this.cardInfoResponse.data;
                this.emitConfirmHandler(patient, this.isSocialCard, this.shebaoCardInfo);
                this.onClickClose();
            }
            if (this.isSocialCard || this.isIdCardReader) {
                // 预约的患者补刷医保卡
                if (this.fillShebaoCardInfo) {
                    const fillPatientInfoResponse = await this.updatePatientHandler(this.shebaoCardInfo);
                    if (fillPatientInfoResponse.status === false) {
                        return false;
                    }
                    this.emitConfirmHandler(patient, this.isSocialCard, this.shebaoCardInfo);
                    this.onClickClose();
                } else {
                    // 未绑定患者
                    if (!this.bindedPatientList.length) {
                        await this.fetchMatchedPatients();
                        this.showBindInfo = true;
                    }
                    // 该医保卡绑定了多个患者，需要给出患者列表然后绑定唯一的患者
                    if (this.bindedPatientList.length > 1) {
                        this.showPatientList = true;
                    }
                    // 已绑定了唯一的患者
                    if (this.bindedPatientList.length === 1) {
                        // 调用bind 请求实现一次更新社保信息的操作。。返回的患者信息带有shebaoCardInfo
                        const postData = this.transformBindPatientData();
                        const { data } = await CrmAPI.bindPatientByCard(this.bindedPatientList[0].id, postData);
                        this.emitConfirmHandler(data, this.isSocialCard, this.shebaoCardInfo);
                        this.onClickClose();
                    }
                }
            }
        },
        emitConfirmHandler(patient, isShebaoCard, shebaoCardInfo) {
            this.onConfirm({
                patient,
                isShebaoCard,
                shebaoCardInfo,
            });
        },
        /**
         * @desc 获取医保卡绑定的患者数据
         * <AUTHOR>
         * @date 2021-09-27 11:01:16
         */
        async fetchBindedPatientList(cardInfo) {
            if (!cardInfo) return;
            try {
                const { data } = await CrmAPI.fetchBindedPatients(cardInfo);
                this.bindedPatientList = data && data.rows || [];
            } catch (e) {
                console.log(e);
            }
        },

        /**
         * @desc 绑定的患者信息
         */
        getBindedPatientInfo() {
            if (!this.bindedPatientList) return '';
            if (!this.bindedPatientList.length) return '未绑定';
            if (this.bindedPatientList.length === 1) {
                const patient = this.bindedPatientList[0];
                return `<span>${patient.name}</span>
                                <span>${patient.sex}</span>
                                <span>${formatAge(patient.age)}</span>
                                <span>${patient.mobile || ''}</span>`;
            }
            if (this.bindedPatientList.length > 1) {
                return '未确认';
            }
            return '';
        },
        /**
         * @desc 新建患者数据
         * <AUTHOR>
         * @date 2021-09-27 15:56:26
         * @params
         * @return
         */
        createPatientHandler() {
            this.showPatientList = false;
            this.showBindInfo = true;
            this.matchedPatients = [];
        },

        /**
         * @desc 绑定社保和绑定身份证数据结构不一样，需要区分不同的场景
         * <AUTHOR>
         * @date 2022-06-30 14:24:47
         * @params
         * @return
         */
        transformBindPatientData() {
            let postData = null;
            if (this.readCardType === ReadCardType.ID_CARD) {
                postData = {
                    cardType: ReadCardType.ID_CARD, // 不走社保
                    addressInfo: this.idCardInfo.addressInfo,
                    name: this.idCardInfo.name,
                    sex: this.idCardInfo.sex,
                    birthday: this.idCardInfo.birthday,
                    idCard: this.idCardInfo.idCard,
                };
            } else {
                postData = {
                    cardType: 0, // 社保绑定逻辑
                    shebaoCardInfo: this.shebaoCardInfo,
                };
            }
            return postData;
        },

        /**
         * @desc 通过身份证号 + 手机 + 姓名 匹配患者
         * <AUTHOR>
         * @date 2021-09-22 15:18:39
         */
        async fetchMatchedPatients() {
            this.loadingConfirm = true;
            if (!this.shebaoCardInfo) return;
            const {
                name,
                mobile,
                idCardNo,
            } = this.shebaoCardInfo;
            try {
                const { data } = await CrmAPI.fetchMatchedPatients({
                    name,
                    mobile,
                    idCard: idCardNo,
                });
                this.matchedPatients = data.rows || [];

            } finally {
                this.loadingConfirm = false;
            }
        },
        cancelConfirmPatientHandler() {
            this.showBindInfo = false;
            this.showPatientList = false;
            this.loadingConfirm = false;
        },
        /**
         * @desc 绑定患者或者新建患者信息确定
         * <AUTHOR>
         * @date 2021-09-22 15:52:54
         */
        async confirmPatientHandler(patient) {
            this.emitConfirmHandler(patient, this.isSocialCard, this.shebaoCardInfo);
            this.cancelConfirmPatientHandler();
            this.onClickClose();
        },
        /**
         * 处理补刷社保卡
         * <AUTHOR>
         * @date 2020-08-13
         * @param {Object} shebaoCardInfo 社保卡信息
         */
        async updatePatientHandler(shebaoCardInfo) {
            this.loadingConfirm = true;
            try {
                await CrmAPI.fillPatientOrderInfo(this.patientOrderId, { shebaoCardInfo });
                this.loadingConfirm = false;
                return { status: true };
            } catch (error) {
                console.log('updatePatientHandler error', error);
                this.loadingConfirm = false;
                return {
                    status: false,
                    message: error.message,
                };
            }
        },
        /**
         * 获取科室标准编码
         * <AUTHOR>
         * @date 2021-03-24
         * @returns {Promise<String>}
         */
        async fetchDepartmentMedicalCode() {
            let departmentMedicalCode = '';
            if (this.departmentId) {
                try {
                    const { data } = await SettingsAPI.clinic.fetchDepartmentById(this.departmentId);
                    const {
                        mainMedicalCode,
                        secondMedicalCode,
                    } = data;
                    departmentMedicalCode = secondMedicalCode || mainMedicalCode || '';
                    if (departmentMedicalCode && departmentMedicalCode.length < 4) {
                        departmentMedicalCode = (`${departmentMedicalCode}0000`).slice(0, 4);
                    }
                } catch (error) {
                    console.log('fetchDepartmentMedicalCode error', error);
                }
            }
            return departmentMedicalCode;
        },
        /**
         * 当点击关闭弹窗时
         * <AUTHOR>
         * @date 2020-12-30
         */
        onClickClose() {
            if (this.cardInfoViewInstance) {
                this.cardInfoViewInstance.close();
                this.cardInfoViewInstance = null;
            }
            this.visible = false;

            this.onCancel();

            // 主动取消读卡的处理
            this.$abcSocialSecurity.promiseHandlerCancel();
        },

        // 根据地区，展示可选读卡类型
        initCardTypeOptions() {
            const options = [
                // 开通武汉健康卡读信息
                {
                    value: 'wuhanHealthCard',
                    label: '健康卡',
                    icon: HealthCardImg, // 健康卡图标地址
                    needScanCode: true,
                    scanCodeConf: {
                        title: '请扫描患者的健康卡二维码',
                        validators: [isWuhanHealthyCode],
                        regExp: new RegExp(/^[A-Za-z0-9:1]{1}$/),
                        alertText: '无法识别该卡，请扫描健康卡',
                    },
                    needPassword: false,
                    awaitReadingTitle: '健康卡读取中...',
                    alertSuccessTitle: '健康卡读取成功',
                    enable: this.isEnableWuhanHealthyCard,
                    isHealthyCard: true,
                },
                // 开通成都健康卡读信息
                {
                    value: 'chengduHealthCard',
                    label: '电子健康卡',
                    icon: HealthCardImg, // 健康卡图标地址
                    needScanCode: true,
                    scanCodeConf: {
                        title: '请扫描患者的电子健康卡二维码',
                        validators: [isLength77HealthyCode],
                        regExp: new RegExp(/^[A-Za-z0-9:]{1}$/),
                        alertText: '无法识别该卡，请扫描电子健康卡',
                    },
                    needPassword: false,
                    awaitReadingTitle: '电子健康卡读取中...',
                    alertSuccessTitle: '电子健康卡读取成功',
                    enable: this.isEnableChengduHealthCard,
                    isHealthyCard: true,
                },
                // 开通绵阳健康卡读信息
                {
                    value: 'mianyangHealthCard',
                    label: '电子健康卡',
                    icon: HealthCardImg, // 健康卡图标地址
                    needScanCode: true,
                    scanCodeConf: {
                        title: '请扫描患者的电子健康卡二维码',
                        validators: [isLength77HealthyCode],
                        regExp: new RegExp(/^[A-Za-z0-9:]{1}$/),
                        alertText: '无法识别该卡，请扫描电子健康卡',
                    },
                    needPassword: false,
                    awaitReadingTitle: '电子健康卡读取中...',
                    alertSuccessTitle: '电子健康卡读取成功',
                    enable: this.isEnableMianyangHealthCard,
                    isHealthyCard: true,
                },
            ].filter((it) => it.enable);

            const idCardReaderPicker = {
                label: '身份证',
                icon: IdCardImg,
                iconLarge: IdCardImgLarge,
                isPicker: true,
                readers: [],
                cardRemark: '未开通',
                currentSelectReaderId: null,
                currentConnectReaderId: null,
            };

            /**
             * 1.客户端环境
             * 2.开通了医保
             */
            if (this.$abcSocialSecurity.isSupportPay) {
                this.$abcSocialSecurity.readCardOptions.forEach((it) => {
                    // 身份证相关的放到picker中
                    const readerItem = {
                        ...it,
                        readerId: it.value,
                    };

                    const isEnableSocialReader = this.isEnableSocialReader === undefined || this.isEnableSocialReader;

                    if (isEnableSocialReader && this.$abcSocialSecurity.idCardMdtrtCertTypeMedList.includes(readerItem.value)) {
                        idCardReaderPicker.readers.push({
                            ...readerItem,
                            icon: require('@/assets/images/social/reader-logo-chs.png'),
                            iconStyle: 'width: 41px;',
                            label: '医保读卡器',
                            cardRemark: '支持医保设备或直接输入身份证号',
                            readerStatus: ReaderStatusEnum.ONLINE,
                            isSocialCardReader: true,
                        });
                    } else {
                        options.push(readerItem);
                    }
                });
            }

            idCardReaderPicker.readers.push(...ReadIdCardOptions);

            options.push(idCardReaderPicker);
            this.idCardReaderPicker = idCardReaderPicker;
            this.cardTypeOptions = options;

            // 设置读卡器初始值
            const selectReaderCache = this.getSelectReaderCache();
            // 优先从缓存获取
            if (selectReaderCache && selectReaderCache.readerId) {
                // 缓存中保存的reader是否已经不存在
                if (!idCardReaderPicker.readers.find((it) => it.readerId === selectReaderCache.readerId)) {
                    console.error('读卡设备不存在');
                } else {
                    console.debug('当前保存的读卡器配置', selectReaderCache);
                    idCardReaderPicker.currentConnectReaderId = selectReaderCache.readerId;
                    idCardReaderPicker.currentSelectReaderId = selectReaderCache.readerId;
                }
            } else {
                // 从后台获取配置
                // 开通了哪种类型的身份证阅读器
                if (this.isEnableIdCardReader) {
                    idCardReaderPicker.currentConnectReaderId = this.isEnableIdCardReader;
                    idCardReaderPicker.currentSelectReaderId = this.isEnableIdCardReader;
                }
            }

            // 初始化读卡器的状态
            this.initIdCardReadersService(idCardReaderPicker.readers);

            this.updateSelectReaderCache();
        },

        getSelectReaderCache() {
            return Store.get(SOCIAL_ID_CARD_READER_KEY, true, true);
        },

        updateSelectReaderCache() {
            const { currentSelectReaderId } = this.idCardReaderPicker;
            Store.set(SOCIAL_ID_CARD_READER_KEY, {
                readerId: currentSelectReaderId,
                updateTime: Date.now(),
            }, true);
        },

        // 选择读卡类型
        handleSelectReader(item) {
            this.idCardReaderPicker.currentConnectReaderId = item.readerId;
        },

        // 初始化读卡器的service
        initIdCardReadersService(readers) {
            readers.forEach(async (reader) => {
                console.debug('检查设备通信状态', reader);
                if (reader.getService && !reader.disabled) {
                    reader.service = reader.getService();
                    const res = await reader.service.checkReady();
                    if (reader.readerId === ReadIdCardType.JING_LUN || reader.readerId === ReadIdCardType.SHEN_SI) {
                        reader.readerStatus = res === 1 ? ReaderStatusEnum.ONLINE : ReaderStatusEnum.OFFLINE;
                    } else {
                        reader.readerStatus = ReaderStatusEnum.ONLINE;
                    }
                } else {
                    reader.readerStatus = ReaderStatusEnum.ONLINE;
                }
            });
        },

        async handleConfirmSelectReader(reader) {
            if (reader.readerStatus === ReaderStatusEnum.OFFLINE) {
                await IdCardReaderService.readerInstall(this, reader);
            }
            this.idCardReaderPicker.currentSelectReaderId = reader.readerId;
            this.step = stepConst.SELECT_TYPE;
            this.updateSelectReaderCache();

            this.handleSelectReaderChange?.(reader);

            if (this.shortcut) {
                if (this.isShowShortcutPrepare(this.idCardReaderPicker)) {
                    this.step = stepConst.SHORTCUT_PREPARE;
                } else {
                    await this.confirmCardTypeHandler(
                        this.idCardReaderPicker,
                    );
                }
            }
        },
    },
};
