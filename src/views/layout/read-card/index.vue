<template>
    <abc-popover
        v-model="showReadCardPopover"
        :visible-arrow="false"
        trigger="hover"
    >
        <abc-button
            slot="reference"
            class="read-card-btn-in-search"
            size="small"
            variant="text"
            theme="default"
            icon="s-card-line"
        >
        </abc-button>

        <read-card-panel
            v-show="showReadCardPopover"
            @handleClickShortcut="handleOpenReadPanel"
        ></read-card-panel>
    </abc-popover>
</template>

<script>
    import ReadCardPanel from 'views/layout/read-card/read-card-panel.vue';
    import IdCardReaderService from 'views/layout/read-card/id-card-reader/id-card-reader-service';
    import ReadCard from 'views/common/read-card';

    export default {
        name: 'ReadCard',
        components: {
            ReadCardPanel,
        },
        mixins: [ ReadCard ],
        props: {
            businessType: String,
        },
        data() {
            return {
                showReadCardPopover: false,
            };
        },
        methods: {
            async handleOpenReadPanel(shortcut) {
                try {
                    if (this._isReadCard) {
                        return;
                    }
                    this._isReadCard = true;
                    this.showReadCardPopover = false;
                    const response = await IdCardReaderService.read({
                        businessType: this.businessType,
                        isGetCardInfoOnly: true,
                        shortcut,
                        onCancel: () => {
                            this._isReadCard = false;
                        },
                    });
                    this.$emit('read-card', response);
                    this._isReadCard = false;
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import '~src/styles/theme.scss';

    .read-card-btn-in-search {
        .abc-iconfont-svg {
            font-size: 16px !important;
            line-height: 16px !important;
            color: var(--abc-color-T3) !important;
        }
    }
</style>
