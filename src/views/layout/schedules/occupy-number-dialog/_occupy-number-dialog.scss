@import '~styles/theme.scss';

.occupy-number-dialog-wrapper {
    .abc-dialog-header {
        height: 48px;
        padding: 10px 0;
        line-height: 28px;
    }

    .occupy-number-dialog__content_header {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0 24px;

        .header-operation {
            display: flex;

            .doctor-name {
                font-size: 16px;
                font-weight: bold;
                color: #333333;
            }

            .time {
                margin-left: 16px;
                font-size: 16px;
                color: #333333;
            }
        }

        .status-list {
            padding-right: 54px;
            margin-left: auto;

            span {
                display: inline-block;
                height: 12px;
                font-size: 12px;
                line-height: 12px;
                user-select: none;

                &::before {
                    position: relative;
                    top: -2px;
                    display: inline-block;
                    width: 16px;
                    height: 4px;
                    margin-right: 5px;
                    content: '';
                    background: #0a8cea;
                    border-radius: 2px;
                }

                &.gray::before {
                    background: $P1;
                }

                &.white::before {
                    background: #ffffff;
                    border: 1px solid var(--abc-color-P10);
                }

                &.orange::before {
                    background: $Y2;
                }

                &.lightblue::before {
                    background: #0092c9;
                }
            }

            span + span {
                margin-left: 24px;
            }
        }

        &.is-select-mode {
            .status-list {
                padding-right: 0;
            }
        }
    }

    .occupy-number-dialog__content {
        position: relative;
        min-height: 200px;

        .title {
            height: 16px;
            margin: 24px 0 20px;
            font-size: 16px;
            font-weight: bold;
            line-height: 16px;
        }

        &.is-select-mode .occupy-card ul li {
            // &:nth-child(-n + 15) {
            //     border-top: none;
            // }

            // &:nth-child(-n + 15) {
            //     border-top: 1px solid $P6;
            // }
            cursor: pointer;
        }

        .order-wrapper {
            display: flex;
            flex-direction: column;
            width: 100%;

            &.c-mode {
                flex-direction: row;
                flex-wrap: wrap;
            }
        }

        .occupy-card {
            display: flex;
            flex-direction: column;
            width: auto;
            margin-bottom: 20px;
            user-select: none;

            .item {
                & + .item {
                    margin-top: 8px;
                }
            }

            &.c-mode {
                margin-right: 12px;
            }

            > .card-header {
                padding: 24px 0 20px;
                font-size: 16px;
                font-weight: bold;
                line-height: 16px;

                &.c-mode {
                    padding: 0 0 12px;
                    font-size: 14px;
                    line-height: 14px;
                }
            }

            ul {
                display: flex;
                flex-wrap: wrap;

                li {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 50px;
                    height: 50px;
                    margin-right: -1px;
                    border: 1px solid $P6;
                    border-top: none;

                    // &:nth-child(-n + 10) {
                    //     border-top: none;
                    // }

                    &:nth-child(-n + 15) {
                        border-top: 1px solid $P6;
                    }

                    &[disabled='disabled'] {
                        color: $T3;
                        background-color: #eff3f6;

                        &:hover {
                            .tips {
                                visibility: visible;
                            }
                        }
                    }

                    &:not([disabled='disabled']) {
                        &.editable {
                            cursor: pointer;

                            &::after {
                                position: absolute;
                                top: 2px;
                                right: 2px;
                                display: inline-block;
                                width: 12px;
                                height: 12px;
                                content: '';
                                background-image: url("~assets/images/<EMAIL>");
                                background-repeat: no-repeat;
                                background-size: 12px 12px;
                            }
                        }

                        &.selected {
                            &::after {
                                background-image: url("~assets/images/<EMAIL>");
                            }
                        }

                        // 选择模式，选中态
                        &.checked {
                            background-color: $G2;

                            .number {
                                color: #ffffff;
                            }

                            .time {
                                color: #ffffff;
                            }
                        }
                    }

                    .line {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 2px;

                        // 会员预留号
                        &.is-member {
                            background: $Y2;
                        }

                        // 现场预留号
                        &.is-locale {
                            background: #0092c9;
                        }
                    }

                    .number {
                        margin-top: 2px;
                        font-size: 18px;
                        font-weight: bold;
                    }

                    .time {
                        margin-top: 2px;
                        font-size: 12px;
                        color: $T3;
                    }

                    .icon-tip {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        font-size: 12px;
                        color: $G2;
                    }

                    .tips {
                        position: absolute;
                        top: 49px;
                        left: -1px;
                        z-index: 99;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 116px;
                        height: 46px;
                        font-size: 14px;
                        font-weight: 400;
                        color: #000000;
                        visibility: hidden;
                        background: #fffdec;
                        border: 1px solid #cbb816;
                    }
                }
            }
        }

        .selection-rect {
            position: absolute;
            display: none;
            background: #ced0daaa;
        }
    }

    .occupy-number-dialog__footer {
        text-align: right;
    }

    .empty-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 118px;
    }
}
