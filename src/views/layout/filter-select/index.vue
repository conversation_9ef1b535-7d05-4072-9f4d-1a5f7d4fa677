<template>
    <abc-form :label-position="label ? 'inner' : 'top'" item-no-margin>
        <abc-form-item :label="label">
            <abc-select
                v-model="selectedModel"
                :style="customStyle"
                :width="width"
                :max-width="maxWidth"
                :custom-class="customClass"
                :clearable="clearable"
                :inner-width="innerWidth"
                :with-search="isSupportSearch"
                :fetch-suggestions="fetchSuggestionsKey"
                :placeholder="placeholder"
                :multiple="multiple"
                :disabled="disabled"
                multi-label-mode="text"
                :max-tag="1"
                show-empty
                @input="changeHandler"
            >
                <abc-option
                    v-for="(item, index) in filterSuggestOptions"
                    :key="`${item.id}-${item.name}-${index}`"
                    :label="item.name"
                    :value="idWithName ? `${item.id}-idWithName-${item.name}` : item.id"
                ></abc-option>
                <slot name="custom-options"></slot>
            </abc-select>
        </abc-form-item>
    </abc-form>
</template>

<script>
    export default {
        name: 'FilterSelect',
        props: {
            value: {
                type: [String, Number, Array],
                required: true,
            },
            options: {
                type: Array,
                default: () => [],
            },
            width: {
                type: [Number, String],
                default: 100,
            },
            maxWidth: {
                type: [Number, String],
                default: 150,
            },
            innerWidth: {
                type: [Number, String],
            },
            customStyle: {
                type: Object,
                default: () => ({}),
            },
            customClass: {
                type: String,
                default: '',
            },
            clearable: {
                type: Boolean,
                default: true,
            },
            widthSearch: {
                type: Boolean,
                default: true,
            },
            placeholder: {
                type: String,
                default: '',
            },
            normalizeKey: {
                type: String,
                default: '',
            },
            normalizeId: {
                type: String,
                default: '',
            },
            idWithName: {
                type: Boolean,
                default: false,
            },
            /**
             * 是否支持多选
             */
            multiple: {
                type: Boolean,
                default: () => false,
            },
            label: {
                type: String,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: () => false,
            },
        },
        data() {
            return {
                filterKey: '',
            };
        },
        computed: {
            isSupportSearch() {
                return this.options?.length > 10;
            },
            filterSuggestOptions() {
                const newOptions = this.normalizeOptions(this.options, this.normalizeKey, this.normalizeId);
                if (this.filterKey) {
                    return newOptions.filter((item) => {
                        return (
                            (item.name?.indexOf(this.filterKey) > -1) ||
                            (item.namePy?.indexOf(this.filterKey) > -1) ||
                            (item.namePyFirst?.indexOf(this.filterKey) > -1)
                        );
                    });
                }
                return newOptions;
            },
            selectedModel: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        methods: {
            fetchSuggestionsKey(key) {
                this.filterKey = key.trim().toLocaleLowerCase();
            },
            changeHandler(val) {
                this.$emit('change', val);
            },
            normalizeOptions(list = [], selectedKey = '', selectedId = '') {
                const newList = list;
                return newList?.map((item) => {
                    return {
                        ...item,
                        name: item?.label || item.name || item[selectedKey],
                        id: item.id || item[selectedId],
                    };
                });
            },
        },
    };
</script>
