<template>
    <div class="fee-item-table-form">
        <abc-table
            type="excel"
            :data-list="dataSource"
            :render-config="tableFormHeader"
            :support-delete-tr="!disabledOpt.del"
            :need-min-heigth="false"
            :show-content-empty="!isAdmin"
            @delete-tr="idx => $emit('delete', idx)"
        >
            <!-- 名称 -->
            <template #name="{ trData: item }">
                <abc-table-cell>
                    <abc-flex justify="space-between" align="center" class="fee-item-name-type fit-height">
                        <span :title="item.name" class="name-span ellipsis">
                            {{ item.name }}
                        </span>
                        <span
                            :title="item.feeTypeName"
                            class="fee-type ellipsis"
                        >
                            {{ item.feeTypeName }}
                        </span>
                    </abc-flex>
                </abc-table-cell>
            </template>

            <!-- 次数 -->
            <template #count="{ trData: item }">
                <abc-form-item>
                    <abc-input
                        v-model="item[getPropName(item, 'count')]"
                        v-abc-focus-selected
                        type="number"
                        :readonly="disabledOpt.count"
                        class="fee-item-count"
                        @input="computePrice(4, item, 'totalPrice')"
                    >
                    </abc-input>
                </abc-form-item>
            </template>

            <!-- 单位 -->
            <template #unit="{ trData: item }">
                <abc-form-item>
                    <abc-input
                        v-model="item.packageUnit"
                        v-abc-focus-selected
                        :readonly="!!item.id"
                        :max-length="15"
                        :title="item.packageUnit"
                    >
                    </abc-input>
                </abc-form-item>
            </template>

            <template #fee-item-unit="{ trData: item }">
                <abc-form-item>
                    <abc-select
                        v-model="item.composeUseDismounting"
                        adaptive-width
                        :show-value="getUnit(item)"
                        :title="getUnit(item)"
                        no-icon
                        @change="v => handleChangeUnit(v, item)"
                    >
                        <abc-option
                            v-for="(option,k) in getCanSelectUnit(item)"
                            :key="k"
                            v-bind="option"
                        >
                        </abc-option>
                    </abc-select>
                </abc-form-item>
            </template>

            <!-- 单价 -->
            <template #unitPrice="{ trData: item }">
                <abc-form-item>
                    <abc-popover
                        :visible-arrow="false"
                        trigger="hover"
                        placement="bottom-end"
                        style="height: 100%;"
                        :disabled="
                            item[getPropName(item, 'goodsPrice')] === item[getPropName(item, 'feeItemPrice')]
                        "
                    >
                        <abc-input
                            slot="reference"
                            v-model="item[getPropName(item, 'feeItemPrice')]"
                            v-abc-focus-selected
                            type="money"
                            :config="{
                                formatLength: 2, supportZero: true, max: 9999999
                            }"
                            :input-custom-style="{
                                'text-align': 'right',
                            }"
                            class="fee-item-price"
                            :class="{
                                warn: item[getPropName(item, 'goodsPrice')] !== item[getPropName(item, 'feeItemPrice')]
                            }"
                            :readonly="calcLoading || disabledOpt.unitPrice"
                            @input="computePrice(1, item, 'totalPrice')"
                        >
                            <abc-loading-spinner
                                v-if="calcLoading && item.currentLoadingName === 'unitPrice'"
                                slot="prepend"
                                small
                                no-cover
                            ></abc-loading-spinner>
                        </abc-input>

                        <div class="fee-table-item-popover">
                            原价：<abc-money :value="item[getPropName(item, 'goodsPrice')]" is-show-space></abc-money>
                        </div>
                    </abc-popover>
                </abc-form-item>
            </template>

            <!-- 售价 -->
            <template #salePrice="{ trData: item }">
                <abc-form-item>
                    <abc-popover
                        :visible-arrow="false"
                        trigger="hover"
                        placement="bottom-end"
                        style="height: 100%;"
                        :disabled="checkItemCountPrice(item)"
                    >
                        <abc-input
                            slot="reference"
                            v-model="item.composePrice"
                            v-abc-focus-selected
                            type="money"
                            :config="{
                                formatLength: 2, supportZero: true, max: 9999999
                            }"
                            :input-custom-style="{ 'text-align': 'right' }"
                            class="fee-item-price"
                            :class="{ warn: !checkItemCountPrice(item) }"
                            :readonly="calcLoading || disabledOpt.totalPrice"
                            @input="computePrice(2, item, 'unitPrice')"
                        >
                            <abc-loading-spinner
                                v-if="calcLoading && item.currentLoadingName === 'totalPrice'"
                                slot="prepend"
                                small
                                no-cover
                            ></abc-loading-spinner>
                        </abc-input>

                        <div class="fee-table-item-popover">
                            原价：<abc-money :value="computeTotalPriceByGoodsPrice(item)" is-show-space></abc-money>
                        </div>
                    </abc-popover>
                </abc-form-item>
            </template>

            <!-- 成本价 -->
            <template #costPrice="{ trData: item }">
                <abc-table-cell>
                    <div style="overflow: hidden; white-space: nowrap;">
                        {{ formatMoney(computeCostPrice(item)) }}
                    </div>
                </abc-table-cell>
            </template>

            <!-- 对码 -->
            <template #shebaoCode="{ trData: item }">
                <abc-table-cell>
                    <div
                        v-if="socialCodeMap[item.id]"
                        v-abc-shebao-popver="{
                            socialInfo: socialCodeMap[item.id],
                            shebaoCodeType: 3,
                            goodsType: 'diagnosis-treatment',
                            openDelay: 0,
                        }"
                    >
                        已对码
                    </div>

                    <div v-else>
                        未对码
                    </div>
                </abc-table-cell>
            </template>

            <template v-if="showAddInput" #footer>
                <abc-form-item style="width: 100%; margin-bottom: 0;">
                    <div class="fee-item-footer">
                        <add-fee-item
                            :disabled="disabledOpt.add"
                            v-on="$listeners"
                        ></add-fee-item>
                    </div>
                </abc-form-item>
            </template>
        </abc-table>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { formatMoney } from '@/filters';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import AbcShebaoPopver from '@/views/inventory/components/social-code-autocomplete/directive/shebao-popper';
    import AddFeeItem from './add-fee-item.vue';

    const UNIT_TYPE = {
        big: 0,
        small: 1,
    };

    export default {
        name: 'TableForm',
        directives: {
            AbcShebaoPopver,
        },
        components: {
            AbcLoadingSpinner,
            AddFeeItem,
        },
        props: {
            dataSource: {
                type: Array,
                default: () => [],
            },
            disabledOpt: {
                type: Object,
                default: () => ({}),
            },
            socialCodeMap: {
                type: Object,
                default: () => ({}),
            },
            calcLoading: {
                type: Boolean,
                default: false,
            },
            goodsType: {
                type: Object,
                default: () => ({
                    type: '',
                    subType: '',
                }),
            },
        },
        computed: {
            ...mapGetters([
                'isAdmin',
            ]),
            tableFormHeader() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            label: '费用项',
                            key: 'name',
                            style: {
                                width: '243px',
                            },
                        },
                        {
                            label: '数量',
                            key: 'count',
                            style: {
                                width: '48px',
                            },
                        },
                        {
                            label: '单位',
                            key: 'fee-item-unit',
                            style: {
                                width: '48px',
                            },
                        },
                        {
                            label: '单价',
                            key: 'unitPrice',
                            style: {
                                width: '74px',
                                textAlign: 'right',
                                justifyContent: 'flex-end',
                            },
                        },
                        {
                            label: '售价',
                            key: 'salePrice',
                            style: {
                                width: '74px',
                                textAlign: 'right',
                                justifyContent: 'flex-end',
                            },
                        },
                        {
                            label: '成本',
                            key: 'costPrice',
                            style: {
                                width: '74px',
                                textAlign: 'right',
                                justifyContent: 'flex-end',
                            },
                        },
                        {
                            label: '医保码',
                            key: 'shebaoCode',
                            style: {
                                width: '63px',
                            },
                        },
                    ],
                };
            },
            showAddInput() {
                return this.isAdmin;
            },
        },
        methods: {
            computePrice(...props) {
                this.$emit('compute-price', ...props);
            },

            checkItemCountPrice(item) {
                const goodsPriceName = this.getPropName(item, 'goodsPrice');
                const countName = this.getPropName(item, 'count');

                return (item[goodsPriceName] * item[countName]).toFixed(2) === Number(item.composePrice).toFixed(2);
            },

            formatMoney,

            // 获取goods的可选单位
            getCanSelectUnit(goods) {
                const res = [];

                const {
                    dismounting,
                    pieceUnit,
                    packageUnit,
                } = goods;

                if (!!dismounting && pieceUnit) {
                    res.push({
                        label: pieceUnit,
                        value: UNIT_TYPE.small,
                    });
                }

                res.push({
                    label: packageUnit,
                    value: UNIT_TYPE.big,
                });
                return res;
            },

            handleChangeUnit(v, item) {
                const feeItemPriceName = this.getPropName(item, 'feeItemPrice');
                const countName = this.getPropName(item, 'count');
                item[feeItemPriceName] = v === UNIT_TYPE.big ? item.packagePrice : item.piecePrice;
                item[countName] = v === UNIT_TYPE.big ? item.composePieceCount : item.composePackageCount;
                this.computePrice(1, item, 'totalPrice');
            },

            getUnit(goods) {
                const {
                    composeUseDismounting,
                    packageUnit,
                    pieceUnit,
                } = goods;

                return +composeUseDismounting === UNIT_TYPE.big ? packageUnit : pieceUnit;
            },

            getPropName(goods, name) {
                const count = {
                    [UNIT_TYPE.big]: 'composePackageCount',
                    [UNIT_TYPE.small]: 'composePieceCount',
                };

                const feeItemPrice = {
                    [UNIT_TYPE.big]: 'composePackagePrice',
                    [UNIT_TYPE.small]: 'composePiecePrice',
                };

                const goodsPrice = {
                    [UNIT_TYPE.big]: 'packagePrice',
                    [UNIT_TYPE.small]: 'piecePrice',
                };

                if (name === 'count') {
                    return count[goods.composeUseDismounting];
                }

                if (name === 'feeItemPrice') {
                    return feeItemPrice[goods.composeUseDismounting];
                }

                return goodsPrice[goods.composeUseDismounting];
            },

            computeCostPrice(item) {
                const costPrice = item.packageCostPrice || 0;
                const pieceNum = item.pieceNum || 0;

                if (+item.composeUseDismounting === UNIT_TYPE.big) {
                    return costPrice * (item.composePackageCount || 0);
                }

                return (costPrice / pieceNum) * item.composePieceCount;
            },

            computeTotalPriceByGoodsPrice(item) {
                const goodsPriceName = this.getPropName(item, 'goodsPrice');
                const countName = this.getPropName(item, 'count');

                return item[goodsPriceName] * item[countName];
            },
        },
    };
</script>

<style lang='scss'>
.fee-item-table-form {
    .fit-height {
        height: 100%;
    }

    .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .disabled-medical-code {
        display: flex;
        flex: 1;
        align-items: center;
        padding: 0 10px;
    }

    .fee-item-name-type {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .name-span {
            flex: 1;
            margin-right: 10px;
        }

        .fee-type {
            max-width: 80px;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            color: $T3;
        }
    }

    .fee-item-count {
        .abc-input__inner {
            padding: 3px 10px;
        }

        .append-input {
            padding-right: 10px;
            padding-left: 0;
            background: transparent;
            border: none;

            >span {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 30px;

                >span {
                    @extend .ellipsis;
                }
            }
        }
    }

    .fee-item-price {
        &.warn {
            .abc-input__inner {
                color: $Y2;
            }
        }
    }

    .fee-item-footer {
        .abc-input__inner {
            padding-left: 30px !important;
        }
    }
}
</style>
