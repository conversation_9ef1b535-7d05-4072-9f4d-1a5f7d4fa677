<template>
    <div class="x-panel-number" @click="$emit('click')">
        <div class="row">
            <div class="cell" @click.stop="$emit('input', '1')">1</div>
            <div class="cell" @click.stop="$emit('input', '2')">2</div>
            <div class="cell" @click.stop="$emit('input', '3')">3</div>
        </div>
        <div class="row">
            <div class="cell" @click.stop="$emit('input', '4')">4</div>
            <div class="cell" @click.stop="$emit('input', '5')">5</div>
            <div class="cell" @click.stop="$emit('input', '6')">6</div>
        </div>
        <div class="row">
            <div class="cell" @click.stop="$emit('input', '7')">7</div>
            <div class="cell" @click.stop="$emit('input', '8')">8</div>
            <div class="cell" @click.stop="$emit('input', '9')">9</div>
        </div>
        <div class="row">
            <div class="cell" :class="{ disabled: disablePoint }" @click="!disablePoint && $emit('input', '.')">.</div>
            <div class="cell" @click.stop="$emit('input', 0)">0</div>
            <div class="cell" @click.stop="$emit('input', 'delete')">
                <img :src="require('assets/images/<EMAIL>')" alt="删除按钮" />
            </div>
        </div>
        <div class="row">
            <div class="cell close" @click.stop="$emit('close')">关闭</div>
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            disablePoint: {
                type: Boolean,
                default: false,
            },
        },
    };
</script>
