<template>
    <div class="abc-video">
        <video
            v-show="false"
            ref="abcVideoBox"
            :src="url"
            class="abc-video-box"
        ></video>
        <div
            v-if="url"
            class="abc-video-player"
            :class="{ 'playing-trumpet': isPlaying }"
            @click="playVoice"
        ></div>
    </div>
</template>

<script>
    export default {
        name: 'AbcVideo',
        props: {
            url: String,
        },
        data() {
            return {
                playTimer: null, //
                isPlaying: false, // 是否播放
                voiceTime: 0,
            };
        },
        methods: {
            playVoice() {
                if (this.isPlaying) {
                    return;
                }
                // 获取视频播放时长
                let duration = this.$refs.abcVideoBox?.duration || 2;
                duration = Number(duration.toFixed(3)) * 1000;
                this.isPlaying = true;
                this.playTimer = setTimeout(() => {
                    this.isPlaying = false;
                    clearTimeout(this.playTimer);
                }, duration);
                this.$refs.abcVideoBox.play();
            },
        },
    };
</script>

<style scoped lang="scss">
@import "src/styles/theme";

.abc-video {
    &-player {
        width: 18px;
        height: 16px;
        cursor: pointer;
        background-image: url("~assets/images/trumpet-grey.png");
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;

        &:hover {
            background-image: url("~assets/images/trumpet-blue-0.png");
        }

        &.playing-trumpet {
            animation: auto-play 0.8s infinite;
        }

        @keyframes auto-play {
            0% {
                background-image: url("~assets/images/trumpet-blue-1.png");
            }

            50% {
                background-image: url("~assets/images/trumpet-blue-2.png");
            }

            100% {
                background-image: url("~assets/images/trumpet-blue-3.png");
            }
        }
    }
}
</style>
