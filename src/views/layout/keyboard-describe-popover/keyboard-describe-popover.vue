<template>
    <abc-popover
        placement="bottom-end"
        z-index="10000"
        trigger="hover"
        theme="yellow"
    >
        <section class="keyboard-describe-popover">
            <div class="describe-content">
                <p v-if="!disabledChangeSpecification">
                    <span class="icon">/</span><span class="function-name">切换</span>
                    <span class="function-describe">输入 / 可切换药品搜索范围</span>
                </p>
                <p>
                    <span class="icon">.</span>
                    <span class="function-name">删除</span>
                    <span class="function-describe">如需删除第2项则输入 .2</span>
                </p>
                <p>
                    <span class="icon">*</span>
                    <span class="function-name">剂数</span>
                    <span class="function-describe">如3剂，输入 *3</span>
                </p>
                <p>
                    <span class="icon">-</span>
                    <span class="function-name">结算</span>
                    <span class="function-describe">输入 - 号回车后触发结算</span>
                </p>
                <p>
                    <span class="icon">F9</span>
                    <span class="function-name">结算</span>
                    <span class="function-describe">按F9触发结算</span>
                </p>
            </div>
            <div v-if="showGlassesTips" class="describe-content">
                <p>
                    <span class="icon">R</span>
                    <span class="function-name">折射率</span>
                    <span class="function-describe">折射率1.61，输入 r1.61</span>
                </p>
                <p>
                    <span class="icon">S</span>
                    <span class="function-name">球镜</span>
                    <span class="function-describe">球镜含-2.00，输入 s-2</span>
                </p>
                <p>
                    <span class="icon search">C</span>
                    <span class="function-name">柱镜</span>
                    <span class="function-describe">柱镜含-2.00，输入 c-2</span>
                </p>
                <p>
                    <span class="icon">A</span>
                    <span class="function-name">下加光</span>
                    <span class="function-describe">下加光含-2.00，输入 a-2</span>
                </p>
                <p>
                    <span class="icon">P</span>
                    <span class="function-name">后顶焦度</span>
                    <span class="function-describe">度数含-2.00，输入 p-2</span>
                </p>
                <p class="tips">
                    注：大小写字母均可，若组合搜索需加空格
                </p>
            </div>
        </section>

        <div slot="reference">
            <abc-icon icon="s-keyboard-fill" color="var(--abc-color-T3)" size="14"></abc-icon>
        </div>
    </abc-popover>
</template>

<script>
    export default {
        props: {
            disabledChangeSpecification: {
                type: Boolean,
                default: false,
            },
            showGlassesTips: Boolean,
        },
    };
</script>
