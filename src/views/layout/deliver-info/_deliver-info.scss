@import "src/styles/theme.scss";

.deliver-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .abc-radio-group {
        display: flex;
        width: 192px;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);

        .abc-radio {
            flex: 1;
            justify-content: center;
            height: 30px;

            &.is-disabled {
                background: $abcBgDisabled;
            }

            .abc-radio-input {
                .abc-radio-inner {
                    border: none;

                    &::after {
                        top: 46%;
                        left: 43%;
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        font-family: "iconfont" !important;
                        font-size: 14px;
                        font-style: normal;
                        color: $theme2;
                        vertical-align: middle;
                        content: "\e77b";
                        background: none;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                    }
                }
            }

            //第一个abc-radio没有border-left
            & + .abc-radio {
                margin-left: 0;
                border-left: 1px solid $P1;
            }
        }
    }

    .abc-form-item {
        width: 100%;
        margin-right: 0;
        margin-bottom: 0;
    }

    .address-selector {
        position: relative;
        z-index: 6;
        flex: 1;
        background-color: #ffffff;

        .abc-input-wrapper {
            width: 100%;

            input {
                cursor: pointer;
            }
        }
    }

    &.is-disabled {
        .address-selector .abc-input-wrapper input {
            cursor: not-allowed;
        }
    }

    .item-title {
        padding-bottom: 8px;
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 500;
        border-bottom: 1px dashed $P6;
    }

    .address-rule-info {
        display: flex;
        align-items: center;

        > label {
            align-self: flex-start;
            width: 72px;
            min-width: 72px;
            max-width: 72px;
            color: $T2;
        }

        > div {
            display: flex;
            flex: 1;
            align-items: center;
        }

        .price {
            > span {
                font-size: 14px;
                font-weight: normal;
                color: $T2;
            }

            font-size: 16px;
            font-weight: 500;
        }

        .tips {
            margin-left: 14px;
            font-size: 12px;
            color: $T2;
        }

        .error-tips {
            margin-left: 14px;
            font-size: 12px;
            color: $Y2;
        }
    }

    .rule-tips {
        margin-top: 12px;
        line-height: 14px;
        color: $T2;
    }
}
