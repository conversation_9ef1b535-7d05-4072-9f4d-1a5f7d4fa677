<template>
    <div class="hospital-medical-prescription-table-wrapper">
        <abc-table
            ref="abcTablePro"
            type="pro"
            theme="white"
            class="hospital-medical-prescription-table"
            fill-height
            :loading="loading"
            :show-content-empty="showContentEmpty"
            :render-config="renderConfig"
            :data-list="dataList"
            :disabled-item-func="disabledItemFunc"
            :custom-tr-class="customTrClass"
            :custom-td-class="customTdClass"
            @handleClickTr="handleClickTr"
        >
        </abc-table>
    </div>
</template>

<script>
    import TableConfig from './index.js';

    export default {
        name: 'MedicalPrescriptionProTable',
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            dataList: {
                type: Array,
                default: () => [],
            },
            patientId: {
                type: String,
                default: '',
            },
            showContentEmpty: {
                type: Boolean,
                default: true,
            },
            operatePermission: {
                type: Boolean,
                default: true,
            },
            disabledItemFunc: {
                type: Function,
            },
        },
        data() {
            return {
                curSelectTr: null,
            };
        },
        computed: {
            renderConfig() {
                return TableConfig.getRenderConfig({
                    patientId: this.patientId,
                    operatePermission: this.operatePermission,
                });
            },
        },
        methods: {
            handleClickTr(item) {
                this.curSelectTr = item;
            },
            customTrClass(item) {
                const {
                    id,
                } = item;
                const _arr = ['clickable'];

                if (this.curSelectTr && this.curSelectTr.id === id) {
                    _arr.push('is-selected');
                }
                return _arr.join(' ');
            },
            customTdClass(config, { stopTime }) {
                const _arr = [];
                if (config.key === 'stopTime' && stopTime) {
                    _arr.push(new Date().getTime() - new Date(stopTime).getTime() < 0 ? 'warn' : '');
                }
                return _arr;
            },
            handleClearSelected() {
                this.curSelectTr = null;
                const { handleClearSelected } = this.$refs.abcTablePro || {};
                handleClearSelected && handleClearSelected();
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .hospital-medical-prescription-table-wrapper {
        .abc-table-normal-wrapper .abc-table-td .table-cell {
            padding: 0 8px;
        }

        position: relative;

        .medical-prescription-advices-td {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .advice-content-wrap {
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 40px;

            .ast-tag {
                display: inline-block;
                width: 56px;
                min-width: 56px;
                line-height: 24px;
                text-align: center;
                background: $P4;
                border-radius: var(--abc-border-radius-small);

                &.green {
                    color: $G2;
                }

                &.red {
                    color: #ff0000;
                }

                &.blue {
                    color: #004c97;
                }
            }

            &.red {
                color: $R1;
            }

            &.success {
                color: $G1;
            }
        }
    }
</style>

