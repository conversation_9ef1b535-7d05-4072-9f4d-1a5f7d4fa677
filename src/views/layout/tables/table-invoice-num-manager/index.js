import BaseProTable from '@/views/layout/tables/base-pro-table.js';

export default class InvoiceNumManagerTable extends BaseProTable {
    name = 'InvoiceNumManagerTable';

    // 由产品.设计提供的静态配置, 开发只能修改key
    static staticConfig = {
        hasInnerBorder: true,
        list: [
            {
                key: 'type',
                label: '发票类型',
                style: {
                    width: '110px',
                    maxWidth: '110px',
                    minWidth: '110px',
                    textAlign: 'left',
                },
            },
            {
                key: 'code',
                label: '发票代码',
                style: {
                    textAlign: 'left',
                },
            },
            {
                key: 'startNumber',
                label: '起始号码',
                style: {
                    textAlign: 'left',
                },
            },
            {
                key: 'endNumber',
                label: '终止号码',
                style: {
                    textAlign: 'left',
                },
            },
            {
                key: 'lastUsedNumber',
                label: '当前使用号码',
                style: {
                    textAlign: 'left',
                },
            },
            {
                key: 'totalCount',
                label: '总份数',
                style: {
                    textAlign: 'left',
                },
            },
            {
                key: 'leftCount',
                label: '剩余份数',
                style: {
                    textAlign: 'left',
                },
            },
            {
                key: 'created',
                label: '录入日期',
                style: {
                    textAlign: 'left',
                },
            },
            {
                key: 'createdByName',
                label: '录入人',
                style: {
                    width: '72px',
                    maxWidth: '72px',
                    minWidth: '72px',
                    textAlign: 'left',
                },
            },
            {
                key: 'operation',
                label: '操作',
                style: {
                    textAlign: 'left',
                },
            },
        ],
    };

    static summaryRenderKeys = [];

    static getRenderConfig() {
        return this.staticConfig;
    }
}
