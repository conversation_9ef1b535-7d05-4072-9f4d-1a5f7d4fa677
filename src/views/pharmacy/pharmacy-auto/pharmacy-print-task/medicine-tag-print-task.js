import PrintTask from 'views/pharmacy/pharmacy-auto/pharmacy-print-task/print-task.js';

import PrintAPI from 'api/print.js';
import clone from 'utils/clone.js';
import vueStore from '@/store';
import { getAbcPrintOptions } from '@/printer/print-handler.js';
import AbcPrinter from '@/printer/index.js';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';
import Store from '@/store';
import Logger from 'utils/logger';
import { SourceFormTypeEnum } from '@/service/charge/constants';

export class MedicineTagPrintTask extends PrintTask {
    async prepareData(params) {
        const {
            dispensingId,printable = {},
        } = params;
        const { printDispensingFormIdList } = printable;
        const { data } = await PrintAPI.printDispensingMedicineTag(dispensingId, printDispensingFormIdList, true);
        const printData = {
            w: [],c: [], patient: data.patient,
        };

        data.dispensingForms.forEach((form) => {
            const newForm = clone(form);
            newForm.formItems = newForm.dispensingFormItems;
            if (newForm.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN || newForm.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION) {
                printData.w.push(newForm);
            } else if (newForm.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                printData.c.push(newForm);
            }
        });

        const forms = printData.w.concat(printData.c);
        // 获取收费单对应的皮试结果
        try {
            const infusionForms = forms.filter((form) => {
                return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN;
            });
            if (infusionForms && infusionForms.length) {
                const astResultData = await PrintAPI.getAstResultByDispensing(dispensingId, true);
                const astResultList = astResultData.data?.list || [];
                infusionForms.forEach((infusionForm) => {
                    infusionForm.dispensingFormItems.forEach((infusionFormItem) => {
                        const formItemAstData = astResultList.find((astResultItem) => astResultItem.formItemId === infusionFormItem.id);
                        if (formItemAstData) {
                            infusionFormItem.ast = formItemAstData.ast || 0;
                            infusionFormItem.astResult = formItemAstData.astResult;
                        }
                    });
                });
            }
        } catch (e) {
            console.error('药房-自动打印用药标签-获取发药单皮试结果失败\n', e);
        }

        const result = {
            forms,
            patient: data.patient,
            doctorName: data.doctorName,
            patientOrderNo: data.patientOrderNo,
            clinicName: Store.getters.currentClinic.clinicName,
        };

        Logger.report({
            scene: 'auto_print_pharmacy',
            data: {
                scene: 'medicine_tag_data',
                uuid: this.uuid,
                info: '请求用药标签数据',
                data: {
                    printData: result,
                },
            },
        });

        return result;
    }

    getPrinterConfig() {
        const { printerConfig } = vueStore.getters;
        let prPrinter = null;
        if (printerConfig) {
            prPrinter = printerConfig.pharmacy && printerConfig.pharmacy.medicineTag;
        }
        return prPrinter;
    }

    printHandler(data) {
        if (!data) {
            Logger.report({
                scene: 'auto_print_pharmacy',
                data: {
                    scene: 'medicine_tag_data_is_empty',
                    uuid: this.uuid,
                    info: '用药标签数据为空',
                    data: {
                        printData: data,
                    },
                },
            });
            return;
        }

        console.log('自动打印-用药标签');
        const printOptions = getAbcPrintOptions('用药标签', data);
        AbcPrinter.abcPrint({
            ...printOptions, isAutoPrint: true,
        }, this.uuid);

    }

    getTaskName() {
        return 'MedicineTagPrintTask';
    }

    static isEnable(config) {
        return config.isAutoPrintMedicineTag;
    }


    static isPrintAble(printable) {
        return printable && printable.medicineTag;
    }

    static getLabel() {
        return getViewDistributeConfig().Print.printOptions.MEDICINE_TAG.label;
    }
}
