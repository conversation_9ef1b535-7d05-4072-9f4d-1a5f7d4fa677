<template>
    <div class="print_area_inventory_record">
        <div class="form-body">
            <div class="title">
                {{currentClinic.name}}入库单
            </div>
            <div class="base-title">
                <div class="record-id"><span class="strong">单号：</span><span>{{recordId}}</span></div>
                <div class="supplier"><span class="strong">供应商：</span><span>{{supplier}}</span></div>
            </div>
            <div class="abc-table">
                <div class="abc-thead">
                    <div class="abc-tr">
                        <div class="abc-td th1">
                            通用名
                        </div>
                        <div class="abc-td th2">
                            厂家
                        </div>
                        <div class="abc-td th3">
                            规格
                        </div>
                        <div class="abc-td th4">
                            进价
                        </div>
                        <div class="abc-td th5">
                            数量
                        </div>
                        <div class="abc-td th6">
                            总价
                        </div>
                    </div>
                </div>
                <div class="abc-tbody">
                    <div class="abc-tr" v-for="tr in editList">
                        <div class="abc-td td1">
                            <div class="cell">
                                <span>{{tr.cadn}}</span>
                            </div>
                        </div>
                        <div class="abc-td td2">
                            <div class="cell">
                                <span>{{tr.manufacturer}}</span>
                            </div>
                        </div>
                        <div class="abc-td td3 ellipsis">
                            {{formatAlertStandardPackage(tr.specification , tr.packaging , tr.dosageUnit , tr.unit)}}
                        </div>
                        <div class="abc-td td4 ellipsis">
                            {{tr.costUnitPrice}}
                        </div>
                        <div class="abc-td td5 ellipsis">
                            {{tr.inCount}}
                        </div>
                        <div class="abc-td td6 ellipsis">
                            {{(Number(tr.inCount) || 0) * (Number(tr.costUnitPrice) || 0) | toMoney}}
                        </div>
                    </div>
                    <div class="abc-tr" style="text-align: right">
                        <div class="abc-td td7">
                            <strong>金额：</strong><span>{{totalPrice | toMoney}}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="record-footer">
                <div class="inventory-date">
                    <strong>入库日期：</strong><span>{{inDate}}</span>
                </div>
                <div class="inventory-employee">
                    <strong>入库人：</strong><span>{{employeeName}}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import {parseTime , formatAlertStandardPackage} from 'utils/index'
    export default {
        name: 'InventoryRecordPrintArea',
        props: {
            currentClinic: {},
            editList: {},
            supplier: {},
            recordId: {},
            employeeName: {},
            inDate: {}
        },
        data() {
            return {
                formatAlertStandardPackage
            }
        },
        computed: {
            totalPrice() {
                let total = 0;
                this.editList && this.editList.length > 0 && this.editList.map((item) => {
                    total +=((Number(item.inCount) || 0) * (Number(item.costUnitPrice) || 0));
                })
                return total;
            }
        }
    }
</script>