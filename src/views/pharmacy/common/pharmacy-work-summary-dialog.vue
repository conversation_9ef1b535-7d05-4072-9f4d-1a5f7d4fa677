<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        custom-class="pharmacy-work-summary-dialog"
        :auto-focus="false"
        size="hugely"
        responsive
        title="药房看板"
        @open="handleOpen"
    >
        <abc-layout v-abc-loading="loading" preset="dialog-table" style="height: 100%; padding: 0 0 !important;">
            <abc-layout-header>
                <abc-space>
                    <abc-date-picker-bar
                        v-model="currentDateLabel"
                        :options="datePickerBarOptions"
                        value-format="YYYY-MM-DD"
                        :picker-options="pickerOptions"
                        @change="changeDate"
                    >
                    </abc-date-picker-bar>


                    <abc-checkbox-button
                        v-model="viewRange"
                        type="number"
                        @change="viewRangeChange"
                    >
                        只看我经手的
                    </abc-checkbox-button>
                </abc-space>
                <abc-flex style="margin: 16px 0 24px 0;" justify="space-between" :gap="12">
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C1"
                        :title="summaryData.dispensingNum"
                        content="发药总单数"
                        icon="s-order-1-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C2"
                        :title="summaryData.westernPrescriptionNum"
                        content="成药处方数"
                        icon="s-order-4-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        v-if="showChinesePrescriptionNum"
                        style="flex: 1;"
                        variant="colorful"
                        theme="C3"
                        :title="summaryData.chinesePrescriptionNum"
                        content="中药处方数"
                        icon="s-order-3-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C4"
                        :title="summaryData.retailNum"
                        content="零售单数"
                        icon="s-group-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        v-if="showProcessNum"
                        style="flex: 1;"
                        variant="colorful"
                        theme="C5"
                        :title="summaryData.processNum"
                        content="加工单数"
                        icon="s-processing-fill"
                    >
                    </abc-statistic>
                </abc-flex>

                <abc-text bold tag="div">
                    发药明细
                </abc-text>
            </abc-layout-header>

            <abc-layout-content ref="layoutContent">
                <abc-table
                    :data-list="incomeTableData"
                    :empty-opt="{ label: `暂无记录` }"
                    :loading="incomeDataLoading"
                    :render-config="renderConfig"
                    :pagination="tablePagination"
                    @pageChange="changePageIndex"
                >
                </abc-table>
            </abc-layout-content>
        </abc-layout>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import StatisticsAPI from 'views/statistics/core/api/operation';

    import ModulePermission from 'views/permission/module-permission';
    import ICON_EMPTY from 'src/assets/images/icon/<EMAIL>';
    import { AbcDatePickerBar } from '@abc/ui-pc'; const { DatePickerBarOptions } = AbcDatePickerBar;
    import {
        prevDate, formatDate,
    } from '@abc/utils-date';
    import localStorage from 'utils/localStorage-handler.js';

    export default {
        name: 'OutpatientWorkSummaryDialog',
        components: {
        },
        mixins: [ModulePermission],
        props: {
            value: Boolean,
        },
        data() {
            return {
                viewRange: 0,
                currentDateLabel: DatePickerBarOptions.DAY.label,
                datePickerBarOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    {
                        label: DatePickerBarOptions.YESTERDAY.label,
                        name: DatePickerBarOptions.YESTERDAY.name,
                        getValue() {
                            return [
                                prevDate(new Date()),
                                prevDate(new Date()),
                            ];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                ],
                pickerStartDate: '',
                pickerOptions: {
                    onPick: ({ minDate }) => {
                        if (minDate) {
                            this.pickerStartDate = minDate.getTime();
                        }
                    },
                    disabledDate: (date) => {
                        const day30 = 30 * 24 * 3600 * 1000;
                        if (this.pickerStartDate !== '') {
                            let maxTime = this.pickerStartDate + day30;
                            const minTime = this.pickerStartDate - day30;
                            if (maxTime > new Date()) {
                                maxTime = new Date();
                            }
                            return date.getTime() > maxTime ||
                                date.getTime() < minTime ||
                                date.getTime() > Date.now();
                        }
                        return date.getTime() > Date.now();
                    },
                },

                selectedTab: 0,
                tabsOption: [
                    {
                        label: '发药明细',
                        value: 0,
                    },
                ],
                fetchParams: {
                    offset: 0,
                    size: 10,
                    beginDate: '',
                    endDate: '',
                    employeeId: '',
                },
                loading: true,
                incomeDataLoading: false,

                summaryData: {
                    dispensingNum: 0, //总单数
                    westernPrescriptionNum: 0, //收费总额
                    chinesePrescriptionNum: 0, //营业收费
                    retailNum: 0, //充值收费
                    processNum: 0, //还款收费
                },

                incomeTableData: [],
                incomeTableTotalCount: 0,


                renderConfig: {
                    hasInnerBorder: false,
                    list: [],
                },
            };
        },
        computed: {
            ...mapGetters(['userInfo', 'currentClinic']),
            ...mapGetters('viewDistribute', [ 'viewDistributeConfig' ]),
            tablePagination() {
                return {
                    showTotalPage: true,
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.incomeTableTotalCount,
                };
            },
            showChinesePrescriptionNum() {
                return this.viewDistributeConfig.Pharmacy.showChinesePrescriptionNum;
            },
            showProcessNum() {
                return this.viewDistributeConfig.Pharmacy.showProcessNum;
            },
            summaryStyle() {
                return {
                    borderBottom: (this.tableHeight === 432 && this.incomeTableData.length < 9 || this.tableHeight === 352 && this.incomeTableData.length < 7) ? '1px solid #e6eaee' : 'none',
                    fontWeight: 'bold',
                };
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            tableHeight() {
                if (window.screen.width > 1366) {
                    // 10条数据（400） + header(32)
                    return 432;
                }
                // 8条数据（320） + header(32)
                return 352;
            },
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },

            emptyIncomeTips() {
                return {
                    imgUrl: ICON_EMPTY,
                    label: '暂无记录',
                };
            },

            pageParams() {
                const {
                    size: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },

            queryUserId() {
                // 查看所有账目
                if (this.viewRange === 0) {
                    return '';
                }
                return this.userInfo?.id;
            },
        },
        async mounted() {
            // 优先获取viewRange 再获取数据
            this.viewRange = localStorage.getObj('pharmacy_kanban_view_range', this.currentClinic.userId, true) || 0;
            await this.$store.dispatch('initDataPermission');
        },
        methods: {
            async handleOpen() {
                // 先处理header (layout-mounted计算错误)
                this.fetchParams.beginDate = formatDate(new Date());
                this.fetchParams.endDate = formatDate(new Date());

                await this.fetchPharmacyTransaction(true);
                await this.$nextTick();
                const data = this.$refs.layoutContent.calcHandler();
                // 表格最少 8条

                this.fetchParams.size = data.paginationLimit || 10;
                await this.fetchData();
            },

            viewRangeChange(val) {
                this.fetchParams.offset = 0;
                this.fetchData();
                localStorage.setObj('pharmacy_kanban_view_range', this.currentClinic.userId, val);
            },
            changeTab(index, item) {
                this.selectedTab = item.value;
                if (this.selectedTab) {
                    this.fetchPharmacyTransaction();
                } else {
                    this.fetchCashierCheckAccount();
                }
            },
            changeDate(picker) {
                if (picker && picker.length === 2) {
                    this.fetchParams.beginDate = picker[0];
                    this.fetchParams.endDate = picker[1];
                } else {
                    this.fetchParams.beginDate = formatDate(new Date());
                    this.fetchParams.endDate = formatDate(new Date());
                    this.currentDateLabel = DatePickerBarOptions.DAY.label;
                }
                this.currentDate = 'range';
                this.fetchParams.offset = 0;
                this.fetchData();
            },

            changePageIndex(page) {
                this.fetchParams.offset = (page - 1) * this.fetchParams.size;
                this.incomeDataLoading = true;
                this.fetchPharmacyTransaction();
            },

            async fetchData() {
                try {
                    this.loading = true;
                    await Promise.all([
                        this.fetchPharmacyOverview(),
                        this.fetchPharmacyTransaction(),
                    ]);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            async fetchPharmacyOverview() {
                const {
                    beginDate,
                    endDate,
                } = this.fetchParams;
                const { data } = await StatisticsAPI.overview.fetchPharmacyOverview({
                    clinicId: this.clinicId,
                    employeeId: this.queryUserId,
                    beginDate,
                    endDate,
                });
                Object.assign(this.summaryData, data);
            },

            /**
             * @desc 拉取发药明细
             * <AUTHOR>
             * @date 2022-03-22 15:48:56
             */
            async fetchPharmacyTransaction(onlyHandleHeader = false) {
                const {
                    beginDate,
                    endDate,
                    offset,
                    size,
                } = this.fetchParams;
                const { data } = await StatisticsAPI.overview.fetchPharmacyTransaction({
                    clinicId: this.clinicId,
                    employeeId: this.queryUserId,
                    beginDate,
                    endDate,
                    offset,
                    size,
                });
                this.incomeDataLoading = false;
                const {
                    header: tableHeader = [],
                    data: tableData = [],
                    total: incomeTotal = {},
                } = data;
                if (incomeTotal && incomeTotal.offset !== offset) return;

                if (onlyHandleHeader) {
                    const widthMap = {
                        'typeText': 60,
                        'sourceType': 60,
                        'logTime': 158,
                        'dispensingContent': 148,
                    };

                    this.renderConfig.list = tableHeader.map((o) => {
                        const style = {
                            'width': `${widthMap[o.prop] || 86}px`,
                            'minWidth': `${widthMap[o.prop] || 86}px`,
                            'textAlign': o.align,
                        };


                        if (o.prop === 'dispensingContent') {
                            style.flex = 1;
                        } else {
                            style.maxWidth = `${widthMap[o.prop] || 86}px`;
                        }

                        return {
                            key: o.prop,
                            label: o.label,
                            style,
                        };
                    });

                    return;
                }

                this.incomeTableData = tableData || [];
                this.incomeTableTotalCount = incomeTotal ? incomeTotal.count : 0;
            },

        },
    };
</script>

<style lang="scss">
    @import '~styles/theme.scss';

    .pharmacy-work-summary-dialog {
        .summary-handler {
            display: flex;
            align-items: center;

            .title {
                font-size: 16px;
                font-weight: bold;
            }

            .quick-select-date-bar {
                display: flex;
                align-items: center;
                margin-left: 16px;
            }

            .view-checkbox-wrapper {
                display: flex;
                align-items: center;
                height: 32px;
                margin-left: 8px;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                &:hover {
                    background-color: $P5;
                }

                .abc-checkbox-wrapper {
                    width: 100%;
                    height: 100%;
                    padding: 0 11px;
                    margin-right: 0;
                }
            }
        }

        .outpatient-summary {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 24px 24px 24px;
            margin-top: 8px;
        }

        .summary-content {
            position: relative;

            .abc-tabs {
                height: 28px;
                margin-top: 16px;
                margin-bottom: 8px;
                line-height: 28px;
                border-bottom: none;

                .abc-tabs-item {
                    height: 28px;
                    line-height: 28px;
                }

                .abc-tabs-item + .abc-tabs-item {
                    margin-left: 24px;
                }
            }

            .abc-fixed-table.income-table {
                position: relative;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                .detail-table-container::after {
                    border-bottom: none;
                }

                .table-title {
                    height: 33px;
                    padding-right: 10px;
                    line-height: 33px;
                    border-top: none;
                }

                .table-tr {
                    height: 40px;
                    padding-right: 10px;
                    line-height: 40px;
                    border-right: none;
                    border-left: none;

                    &:last-child {
                        border-bottom: none;
                    }
                }

                .label-describe {
                    color: $T2;
                }

                .table-td {
                    padding: 0 2px;
                }

                .table-empty {
                    top: 50%;

                    .label {
                        margin-top: 0;
                        font-size: 14px;
                        color: $T3;
                    }

                    .icon {
                        display: none;
                    }
                }

                .abc-table__header-wrapper {
                    border-bottom: 1px solid $P1;

                    table th {
                        color: $T2;
                        border: none;
                    }
                }

                .abc-table__body-wrapper,
                .abc-table__footer-wrapper {
                    table tr {
                        border-right: none;
                        border-left: none;
                    }

                    tbody td {
                        border-right: none !important;
                        border-left: none;
                    }
                }

                .abc-table__footer-wrapper {
                    background-color: #fafbfc;

                    tbody tr {
                        border: none;
                        border-top: 1px solid $P1;
                    }
                }

                table td div.cell,
                table th div.cell {
                    padding: 0;
                    text-overflow: unset;
                }
            }

            .reconciliation-table {
                margin-bottom: 16px;
                overflow: hidden;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                .table-header {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 32px;
                    color: $T2;
                    background: $P5;
                    border-bottom: 1px solid $P1;
                }

                .table-body {
                    min-height: 40px;

                    .table-tr {
                        display: flex;

                        &:not(:last-child) {
                            border-bottom: 1px solid $P6;
                        }
                    }

                    .table-cell {
                        display: flex;
                        align-items: center;
                        width: 25%;
                        height: 40px;

                        label {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 106px;
                            height: 100%;
                            border-right: 1px solid $P6;
                        }

                        .content {
                            display: flex;
                            flex: 1;
                            align-items: center;
                            height: 100%;
                            padding: 0 12px;
                            border-right: 1px solid $P6;
                        }

                        &.no-right-border {
                            .content {
                                border-right: 0;
                            }
                        }
                    }

                    .no-permission {
                        align-items: center;
                        justify-content: center;
                        height: 80px;
                        color: $T3;
                    }
                }
            }
        }

        .close-wrapper {
            position: absolute;
            top: 8px;
            right: 8px;
        }
    }
</style>
