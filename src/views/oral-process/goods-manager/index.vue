<template>
    <div class="oral-process-goods-manager-wrapper">
        <div class="templates-manager-content">
            <div class="left-col">
                <ul v-abc-loading="loadingCatalogues">
                    <abc-tree
                        :data="catalogues"
                        :indent="18"
                        :selecteds="[currentSelectedCategory.id]"
                        disabled-expand
                        draggable
                        :max-depth="1"
                        @trigger-toast="handleTriggerToast"
                        @node-drop="handleDragEnd"
                        @node-click="selectCategory"
                        @node-expand="expandCategory"
                    >
                        <template #default="{ node }">
                            <div
                                class="custom-node-wrapper"
                                :class="{
                                    'is-operation': node.id === currentOperationNodeId,
                                }"
                            >
                                <node-name
                                    v-model="currentEditName"
                                    :node="node"
                                    :show-icon="false"
                                    :current-edit-id="currentEditNameId"
                                    @confirmRename="confirmCatRename"
                                ></node-name>

                                <abc-popover
                                    v-if="!node.disabled"
                                    class="catalogues-operation"
                                    trigger="manual"
                                    :value="node.id === currentOperationNodeId"
                                    placement="bottom"
                                    width="auto"
                                    theme="white"
                                    :popper-style="{ padding: 0 }"
                                >
                                    <div slot="reference" class="operation-icon">
                                        <div class="operation-icon" @click.stop="operationNode(node)">
                                            <abc-icon icon="three_dot" size="14"></abc-icon>
                                        </div>
                                    </div>
                                    <div
                                        v-abc-click-outside="
                                            () => {
                                                currentOperationNodeId = null;
                                            }
                                        "
                                        class="template-catalogues-popover"
                                    >
                                        <ul>
                                            <li @click="addGoodsHandle(node)">
                                                新增项目
                                            </li>
                                            <li @click="renameNode(node)">
                                                重命名
                                            </li>
                                            <li @click="deleteFolder(node)">
                                                删除
                                            </li>
                                        </ul>
                                    </div>
                                </abc-popover>
                            </div>
                        </template>
                    </abc-tree>
                </ul>
                <div v-if="hasPermission" class="bottom-button">
                    <div class="add-btn" @click="addCatalogues">
                        <abc-icon icon="a-plus13px" size="14"></abc-icon>
                        新增分类
                    </div>
                </div>
            </div>

            <abc-form class="right-col">
                <ul v-abc-loading="loadingGoodsList">
                    <li class="catalogue-node-title">
                        {{ currentSelectedCategory.name }}
                    </li>
                    <li
                        v-for="node in goodsList"
                        :key="node.id"
                        class="custom-node-wrapper"
                        style="padding: 0 12px;"
                        :class="{
                            'is-operation': node.id === currentOperationNodeId,
                            'is-selected': currentGoodsId === node.id,
                            'is-disabled': node.disabled,
                        }"
                    >
                        <node-name
                            v-model="currentEditName"
                            :node="node"
                            :show-icon="false"
                            :current-edit-id="currentEditNameId"
                            @confirmRename="confirmGoodsRename"
                        ></node-name>
                        <abc-popover
                            v-if="!node.disabled"
                            class="catalogues-operation"
                            trigger="manual"
                            :value="node.id === currentOperationNodeId"
                            placement="bottom"
                            width="auto"
                            theme="white"
                            :popper-style="{ padding: 0 }"
                        >
                            <div slot="reference" class="operation-icon">
                                <div class="operation-icon" @click.stop="operationNode(node)">
                                    <abc-icon icon="three_dot" size="14"></abc-icon>
                                </div>
                            </div>
                            <div
                                v-abc-click-outside="
                                    () => {
                                        currentOperationNodeId = null;
                                    }
                                "
                                class="template-catalogues-popover"
                            >
                                <ul>
                                    <li @click="renameNode(node)">
                                        重命名
                                    </li>
                                    <li @click="moveTo(node)">
                                        移动
                                    </li>
                                    <li @click="handleDeleteGoods(node)">
                                        删除
                                    </li>
                                </ul>
                            </div>
                        </abc-popover>
                    </li>
                </ul>
                <div v-if="hasPermission" class="bottom-button">
                    <div class="add-btn" @click="addGoodsHandle(null)">
                        <abc-icon icon="a-plus13px" size="14"></abc-icon>
                        新增项目
                    </div>
                </div>
            </abc-form>
        </div>


        <dialog-add
            v-if="showAddDialog"
            v-model="showAddDialog"
            :catalogues="catalogues"
            :is-folder="isAddFolder"
            :default-category="currentOperationCategory"
            @add-success="handleAddSuccess"
        ></dialog-add>

        <dialog-move
            v-if="showMoveDialog"
            v-model="showMoveDialog"
            :catalogues="catalogues"
            :goods="currentMoveGoods"
            :default-category="currentOperationCategory"
            @move-success="handleMoveSuccess"
        ></dialog-move>
    </div>
</template>

<script>
    import OutpatientAPI from 'api/outpatient';
    import { debounce } from 'utils/lodash';

    import NodeName from 'src/views/layout/templates-manager/compoenents/node-name';
    import GoodsAPI from 'api/goods';

    import DialogAdd from './dialog-add.vue';
    import DialogMove from './dialog-move.vue';
    import SettingAPI from 'api/settings';

    export default {
        name: 'TemplateManager',
        components: {
            NodeName,
            DialogAdd,
            DialogMove,
        },

        props: {
        },

        data() {
            return {
                contentLoading: false,
                saveLoading: false,
                loadingCatalogues: false,
                loadingGoodsList: false,

                showAddDialog: false,
                showMoveDialog: false,

                currentGoodsId: null, // 当前选中的文件ID
                currentSelectedCategory: {
                    id: null,
                    name: '',
                }, // 当前选中的文件夹item
                currentOperationCategory: {
                    id: null,
                    name: '',
                }, // 当前操作的文件夹item
                selecteds: [],

                isAddFolder: 0,

                // 分类
                catalogues: [],
                // 项目
                goodsList: [],

                // 操作中
                expands: [],
                currentOperationNodeId: null,
                currentMoveGoods: null,
                currentEditNameId: '',
                currentEditName: '',
            };
        },

        computed: {
            hasPermission() {
                return true;
            },
        },

        watch: {

        },

        async created() {
            this._goodsListCache = new Map();
            this._sortCatalogues = debounce(this.sortCatalogues, 500, true);

            await this.initCatalogues();
        },

        beforeDestroy() {
        },

        methods: {
            async initCatalogues(needLoading = true) {
                this.loadingCatalogues = needLoading && true;
                await this.fetchCatalogues();
                this.loadingCatalogues = false;

                if (!this.currentSelectedCategory.id) {
                    this.selectCategory(this.catalogues[0], false);
                }
            },

            async initGoodsList() {
                this.currentEditName = '';
                if (this.currentSelectedCategory.id) {
                    this.loadingGoodsList = true;
                    await this.fetchGoodsList(this.currentSelectedCategory.id);
                    this.loadingGoodsList = false;
                }
            },

            /**
             * @desc 拉取目录🌲
             * <AUTHOR> Yang
             * @date 2020-06-22 11:22:01
             * @params parentId 有代表拉取子目录
             */
            async fetchCatalogues() {
                const { data } = await GoodsAPI.fetchSecondaryClassification(54);
                this.formatCatalogues(data.rows);
            },

            formatCatalogues(list) {
                list = list || [];
                this.catalogues = list.map((it) => {
                    it.isLeaf = true;
                    return it;
                });
            },

            /**
             * @desc 拉取模板
             * <AUTHOR> Yang
             * @date 2020-06-22 11:23:08
             */
            async fetchGoodsList(customTypeId) {
                const { data } = await SettingAPI.goods.fetchGoodsList({
                    offset: 0,
                    limit: 99,
                    type: 20,
                    customTypeId,
                });
                this.goodsList = data.rows || [];
                this._goodsListCache.set(customTypeId, this.goodsList);
            },

            /**
             * @desc 点击node后的三个点进行操作
             * <AUTHOR>
             * @date 2020/04/01 15:06:50
             */
            operationNode(node) {
                this.currentOperationNodeId = node.id;
            },

            /**
             * @desc 展开目录
             * <AUTHOR> Yang
             * @date 2020-06-22 11:07:16
             */
            async expandCategory(category) {
                const index = this.expands.findIndex((item) => item.id === category.id);
                if (index > -1) {
                    !category.expand && this.expands.splice(index, 1);
                } else {
                    category.expand && this.expands.push(category.id);
                }
            },
            /**
             * @desc 选择目录
             * <AUTHOR>
             * @date 2020/05/15 17:47:53
             */
            selectCategory(category, needExpand = true) {
                category = category || {
                    id: null, name: '',
                };
                // 单击也展开
                if (category.id && needExpand) {
                    category.expand = !category.expand;
                    this.expandCategory(category);
                }

                if (this.currentSelectedCategory.id === category.id) return;
                this.currentSelectedCategory = category;

                const cache = this._goodsListCache.get(this.currentSelectedCategory.id);
                if (cache) {
                    this.goodsList = cache;
                } else {
                    this.initGoodsList();
                }
            },

            deleteFolder(node) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除？',
                    onConfirm: () => {
                        this.deleteCatSubmit(node);
                    },
                });
            },
            async deleteCatSubmit(node) {
                try {
                    const postData = {
                        typeId: 54,
                        customTypes: this.catalogues.map((it) => {
                            if (it.id === node.id) {
                                it.isDeleted = 1;
                            }
                            return it;
                        }),
                    };
                    await GoodsAPI.updateSecondaryClassification(postData);
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    this.initCatalogues(false);
                } catch (e) {
                    console.error(e);
                }
            },

            /**
             * @desc 删除模板或者文件夹
             * <AUTHOR>
             * @date 2020/03/31 15:51:12
             */
            handleDeleteGoods(node) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除？',
                    onConfirm: () => {
                        this.deleteGoodsSubmit(node);
                    },
                });
            },
            async deleteGoodsSubmit({ id }) {
                try {
                    await GoodsAPI.deleteGoods(id);
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    this.goodsList = this.goodsList.filter((item) => {
                        return item.id !== id;
                    });
                    this._goodsListCache.set(this.currentSelectedCategory.id, this.goodsList);
                } catch (e) {
                    console.error(e);
                }
            },


            /**
             * @desc 触发重命名
             * <AUTHOR>
             * @date 2020/05/15 17:35:47
             */
            renameNode({
                id, name,
            }) {
                this.currentEditNameId = id;
                this.currentEditName = name;
                this.currentOperationNodeId = null;
            },

            /**
             * @desc 重命名输入框click禁止冒泡
             * <AUTHOR>
             * @date 2020/05/26 11:21:26
             */
            inputClick(e) {
                e.stopPropagation && e.stopPropagation();
            },

            async confirmCatRename(node) {
                if (!this.currentEditName) {
                    this.currentEditNameId = null;
                    return false;
                }
                try {
                    const postData = {
                        typeId: 54,
                        customTypes: this.catalogues.map((it) => {
                            if (it.id === node.id) {
                                it.name = this.currentEditName;
                            }
                            return it;
                        }),
                    };
                    await GoodsAPI.updateSecondaryClassification(postData);
                    this.currentEditNameId = '';
                    this.currentEditName = '';
                } catch (e) {
                    console.error(e);
                }
            },
            async confirmGoodsRename(node) {
                if (!this.currentEditName) {
                    this.currentEditNameId = null;
                    return false;
                }
                try {
                    await GoodsAPI.updateGoods(node.id, Object.assign(node, {
                        name: this.currentEditName,
                    }));
                    node.name = this.currentEditName;
                    this.currentEditNameId = '';
                    this.currentEditName = '';
                } catch (e) {
                    console.error(e);
                }
            },

            /**
             * @desc 移动
             * <AUTHOR>
             * @date 2020/05/18 16:55:33
             */
            moveTo(node) {
                this.currentMoveGoods = node;
                this.showMoveDialog = true;
            },

            addCatalogues() {
                this.isAddFolder = 1;
                this.showAddDialog = true;
            },

            addGoodsHandle(node) {
                if (node) {
                    this.currentOperationCategory = node;
                } else {
                    this.currentOperationCategory = this.currentSelectedCategory;
                }
                this.isAddFolder = 0;
                this.showAddDialog = true;
            },

            async handleAddSuccess(data) {
                if (this.isAddFolder === 1) {
                    this.formatCatalogues(data.rows);
                    this.selectCategory(this.catalogues[this.catalogues.length - 1]);
                } else {
                    this.initGoodsList();
                }
            },

            /**
             * @desc 移动模板成功
             * <AUTHOR>
             * @date 2020/05/21 09:09:09
             */
            async handleMoveSuccess(data) {
                await this.initGoodsList();
                this._goodsListCache.delete(data.customTypeId);
            },

            handleDragEnd() {
                // disabledExpand代表是一级目录 一级目录交互直接用sort排序接口
                this._sortCatalogues();
            },

            /**
             * @desc 一级目录排序
             * <AUTHOR>
             * @date 2020/03/31 16:52:15
             */
            async sortCatalogues() {
                try {
                    const postData = {
                        typeId: 54,
                        customTypes: this.catalogues,
                    };
                    await GoodsAPI.updateSecondaryClassification(postData);
                } catch (e) {
                    console.error(e);
                }
            },

            /**
             * @desc 移动文件夹
             * <AUTHOR> Yang
             * @date 2020-06-23 10:08:31
             */
            async moveCatalogues(id, parentId, sort = 0) {
                await OutpatientAPI.moveTemplate(this.templateType, id, {
                    parentId,
                    sort,
                });
            },

            handleTriggerToast(options) {
                console.log(options);
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';
@import 'src/styles/mixin.scss';

.oral-process-goods-manager-wrapper {
    height: 100%;
    border: 1px solid $P3;

    .templates-manager-content {
        display: flex;
        height: 100%;

        .left-col,
        .right-col {
            display: flex;
            flex-direction: column;
            height: 100%;

            ul {
                flex: 1;
                overflow-y: auto;
                overflow-y: overlay;

                @include scrollBar;
            }

            .expand-icon {
                position: relative;
                display: flex;
                align-items: center;
                width: 18px;
                height: 100%;
                margin: 0 2px;
                color: $T3;
                cursor: pointer;
            }

            .flip-list-move {
                transition: transform 0.3s;
            }

            .no-move {
                transition: transform 0s;
            }

            .bottom-button {
                display: flex;
                align-items: center;
                width: 100%;
                height: 33px;
                padding: 0 12px;
                border-top: 1px solid $P3;

                .add-btn {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    height: 32px;
                    color: $T3;
                    cursor: pointer;

                    .iconfont {
                        margin-right: 6px;
                        font-size: 12px;
                        vertical-align: middle;
                        cursor: pointer;
                    }

                    &:hover {
                        color: $T1;
                    }
                }
            }

            .icon-wrapper {
                margin-right: 6px;

                i {
                    font-size: 14px;
                    color: $B3;
                }
            }

            .catalogues-operation {
                position: absolute;
                top: 0;
                right: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 34px;
                height: 32px;
                background-color: transparent;
                opacity: 0;

                > div {
                    outline: none;
                }

                .operation-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 16px;
                    color: $T3;
                    cursor: pointer;
                    background-color: transparent;
                    outline: none;

                    &:hover {
                        background-color: $P5;
                    }
                }

                i {
                    font-size: 14px;
                    outline: none;
                }
            }

            .is-selected {
                > .abc-tree-node-content {
                    background-color: #d4e7fd;

                    .operation-icon:hover {
                        background-color: #d4e7fd;
                    }
                }

                > .catalogues-operation {
                    .operation-icon:hover {
                        background-color: #d4e7fd;
                    }
                }
            }

            .custom-node-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                height: 32px;
                padding-right: 6px;
                cursor: pointer;

                .node-name {
                    display: flex;
                    flex: 1;
                    align-items: center;
                    width: 0;

                    > img {
                        width: 12px;
                        height: 12px;
                        margin-right: 6px;
                        user-select: none;
                    }

                    > span {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        word-break: break-all;
                        word-wrap: break-word;
                        white-space: nowrap;
                        user-select: none;
                    }
                }

                .abc-input__inner {
                    background-color: #ffffff;
                }

                &.is-disabled {
                    .icon-wrapper {
                        cursor: default;

                        .iconfont {
                            color: $B6;
                        }
                    }
                }

                &:hover .catalogues-operation,
                &.is-operation .catalogues-operation {
                    color: $T1;
                    opacity: 1;
                }

                &.is-selected {
                    background-color: #d4e7fd;
                }

                &.sortable-chosen {
                    cursor: move;
                }
            }

            .catalogue-node-title {
                display: flex;
                align-items: center;
                height: 20px;
                padding: 0 12px;
                margin-bottom: 2px;
                font-size: 12px;
                color: $T3;
            }

            .is-show-popover .operation-icon {
                color: $T1;
                opacity: 1;
            }
        }

        .left-col {
            width: 167px;
            background-color: $P5;
            border-right: 1px solid $P3;
        }

        .right-col {
            position: relative;
            flex: 1;
            width: 0;
        }
    }
}
</style>
