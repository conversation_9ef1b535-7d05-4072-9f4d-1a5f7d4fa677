<template>
    <div class="visit-source-item">
        <div
            class="item"
            :class="{
                'disabled': item.disabled,
                'selected': activeParentId === item.id,
                'hover': item.hovering && !item.disabled
            }"
            @click="handleItemClick"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
        >
            <div
                v-if="!item.editing"
                class="ellipsis"
                :style="{ maxWidth: confirmBtnVisible || modifyBtnVisible ? '160px' : '270px' }"
            >
                <template v-if="isChildSource">
                    <abc-icon
                        v-if="item.relatedType === relatedType.EMPLOYEE_ROLE"
                        icon="patient-group"
                        size="12"
                        color="#2680f7"
                    ></abc-icon>
                    <abc-icon
                        v-else-if="item.relatedType === relatedType.SINGLE_EMPLOYEE"
                        icon="patient"
                        size="12"
                        color="#2680f7"
                    ></abc-icon>
                    <abc-icon
                        v-else-if="item.relatedType === relatedType.ALL_PATIENT"
                        icon="patient-group"
                        size="12"
                        color="#0eba52"
                    ></abc-icon>
                    <abc-icon
                        v-else-if="item.relatedType === relatedType.SINGLE_PATIENT"
                        icon="patient"
                        size="12"
                        color="#0eba52"
                    ></abc-icon>
                    <abc-icon
                        v-else-if="item.relatedType === relatedType.NOT_RELATED"
                        icon="patient"
                        size="12"
                        color="#ff9933"
                    ></abc-icon>
                </template>
                {{ item.name }}
            </div>

            <input
                v-else
                ref="input"
                v-model.trim="inputValue"
                type="text"
                class="custom-input"
                maxlength="20"
            />

            <!-- 显示编辑按钮并且可编辑 -->
            <div v-show="modifyBtnVisible" class="button-wrapper">
                <abc-button type="text" class="btn" @click.stop="handleEdit">
                    修改
                </abc-button>
                <abc-button type="text" class="btn delete-btn" @click.stop="handleDel(item)">
                    删除
                </abc-button>
            </div>

            <div v-show="confirmBtnVisible" class="button-wrapper">
                <abc-button type="text" class="btn" @click.stop="handleConfirm(item)">
                    确定
                </abc-button>
                <abc-button type="text" class="btn cancel-btn" @click.stop="handleCancel">
                    取消
                </abc-button>
            </div>
        </div>
    </div>
</template>

<script>
    import PatientsApi from 'api/patients';
    import { relatedType } from 'views/registration/visit-source-dialog/constant';

    export default {
        name: 'VisitSourceItem',
        inject: ['main'],

        props: {
            item: {
                required: true,
                type: Object,
            },

            activeParentId: {
                type: String,
                default: '',
            },

            isEditing: {
                type: Boolean,
                default: false,
            },
            isChildSource: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                relatedType,
                inputValue: '',
            };
        },
        computed: {
            modifyBtnVisible() {
                return !this.item.disabled && this.item.hovering && !this.item.editing;
            },

            confirmBtnVisible() {
                return !this.item.disabled && this.item.hovering && this.item.editing;
            },
        },
        methods: {
            focus() {
                this.$refs.input.focus();
            },

            handleMouseEnter() {
                if (this.isEditing) return;

                this.$emit('update-item-info', {
                    hovering: true,
                });
            },

            handleMouseLeave() {
                if (this.isEditing) return;

                this.$emit('update-item-info', {
                    hovering: false,
                    editing: false,
                });

                this.inputValue = '';
            },

            handleEdit() {
                // 未关联
                if (this.item.relatedType === relatedType.NOT_RELATED) {
                    this.inputValue = this.item.name;
                    this.$emit('update-item-info', {
                        editing: true,
                    });

                    this.$emit('changeNodeMode', true);

                    this.$nextTick(() => {
                        this.$refs.input.focus();
                    });
                } else {
                    this.$emit('batch-update-item-info');
                }
            },

            handleCancel() {
                this.$emit('changeNodeMode', false);

                this.inputValue = '';

                if (this.item.isNew) {
                    this.$emit('cancel-add-node');
                    return;
                }

                this.$emit('update-item-info', {
                    editing: false,
                });
            },

            async handleConfirm(item) {
                if (!this.inputValue.trim()) {
                    this.$Toast({
                        type: 'error',
                        message: '名称不能为空',
                    });

                    return;
                }

                const reg = /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g;

                if (reg.test(this.inputValue)) {
                    this.$Toast({
                        type: 'error',
                        message: '名称不合法',
                    });

                    return;
                }

                if (item.isNew) {
                    try {
                        await PatientsApi.createVisitSource({
                            parentId: item.parentId,
                            name: this.inputValue,
                        });

                        this.$emit('changeNodeMode', false);

                        this.$Toast({
                            type: 'success',
                            message: '创建成功',
                        });

                        this.main.fetchAllDoctor();
                    } catch (error) {
                        console.error(error);
                    }
                } else {
                    try {
                        await PatientsApi.updateVisitSource(item.id, {
                            name: this.inputValue,
                            parentId: item.parentId,
                        });

                        this.$Toast({
                            type: 'success',
                            message: '更改成功',
                        });

                        this.$emit('changeNodeMode', false);

                        this.main.fetchAllDoctor();

                    } catch (error) {
                        console.error(error);
                    }
                }

            },

            handleDel(item) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: `删除后不可恢复，是否确认删除【${item.name}】?`,
                    onConfirm: async () => {
                        await PatientsApi.deleteVisitSource(item.id);

                        if (item.id === this.activeParentId) {
                            this.$emit('clearActiveParentInfo');
                        }

                        this.$Toast({
                            type: 'success',
                            message: '删除成功',
                        });

                        this.main.fetchAllDoctor();
                    },
                });
            },

            handleItemClick() {
                if (this.item.disabled) return;

                if (!this.item.parentId) {
                    this.$emit('expand', this.item);
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.visit-source-item {
    .item {
        position: relative;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        padding: 0 16px;
        color: $T3;
        border-right: 1px solid $P6;
        border-bottom: 1px solid $P6;

        &.disabled {
            cursor: not-allowed;
            // background-color: #f2f5f7;
        }

        &.selected {
            color: $B1;
            background: $P4;
        }

        &.hover {
            border: 1px solid $P6;
            box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.3);
        }

        .abc-icon {
            margin-right: 8px;
        }
    }

    .custom-input {
        height: 24px;
        color: #000000;
        background: transparent;
        border: 0;
        outline: 0;
    }

    .cancel-btn {
        color: $T2;
    }

    .delete-btn {
        color: $R1 !important;
    }

    .btn {
        min-width: auto;
        padding: 0;
    }
}
</style>
