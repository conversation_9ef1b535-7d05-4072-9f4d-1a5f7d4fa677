<!--点击完成并收费后弹出框-->
<template>
    <div>
        <abc-dialog
            v-model="showDialog"
            :title="readonly ? '预约信息' : registrationId ? '编辑预约' : '新增预约'"
            custom-class="registration-form-dialog"
            content-styles="width:690px;padding: 0;min-height:342px;"
        >
            <div v-if="!readonly" slot="title-append" class="registration-setting">
                <i
                    class="iconfont cis-icon-set"
                    :class="{ expand: showRegSetting }"
                    @click="showRegSetting = !showRegSetting"
                ></i>
                <registration-setting
                    v-if="showRegSetting"
                    v-model="showRegSetting"
                    :enable-mr="true"
                    :business-type="BusinessType.THERAPY"
                >
                </registration-setting>
            </div>

            <abc-form
                ref="form"
                v-abc-loading="loading"
                class="dialog-content clearfix registration-form"
                label-position="left"
                :label-width="70"
            >
                <div class="left-content" :style="{ width: `${leftContainerWidth }px` }">
                    <patient-form
                        ref="patientForm"
                        v-model="patient"
                        :patient-scan-code-info-id="patientScanCodeInfoId"
                        :patient-id="patientId"
                        :is-open-scan-code="true"
                        :disabled="disabledForm"
                        :patient-source-type="patientSourceType"
                        :required-source="regsRequiredSource"
                        :required-mobile="regsRequiredMobile"
                        :required-id-card="regsRequiredIdCard"
                        :required-company="regsRequiredCompany"
                        :regs-hidden-id-card="regsHiddenIdCard"
                        :regs-hidden-company="regsHiddenCompany"
                        :regs-hidden-profession="regsHiddenProfession"
                        :regs-hidden-source="regsHiddenSource"
                        :regs-hidden-address="regsHiddenAddress"
                        :regs-hidden-sn="regsHiddenSn"
                        :regs-hidden-tags="regsHiddenTags"
                        :regs-hidden-remark="regsHiddenRemark"
                        :department-id="registration.departmentId"
                        :is-can-see-patient-mobile="isCanSeePatientMobileInRegistration"
                        sn-placeholder="若不指定，预约后将自动生成"
                        @modifySn="modifySn"
                        @onFetchPatientInfo="$emit('refresh', false)"
                    >
                    </patient-form>
                </div>

                <div class="right-content">
                    <abc-form-item label="理疗师" class="block-item">
                        <abc-input
                            v-if="registrationId"
                            :width="240"
                            disabled
                            :value="registration.doctorName"
                        ></abc-input>
                        <doctor-selector
                            v-else
                            v-model="registration.doctorId"
                            :employees="doctors"
                            placeholder="选择理疗师"
                            @enter="enterEvent"
                            @change="selectDoctor"
                        >
                        </doctor-selector>
                    </abc-form-item>

                    <abc-form-item label="时间" required>
                        <!--预约时间-->
                        <div>
                            <abc-input
                                v-model="appointmentTime"
                                :disabled="disabledForm"
                                :width="240"
                                class="appointment-input"
                                readonly
                                icon="cis-icon-time"
                                placeholder="选择预约时间"
                                @click="openAppointment"
                                @icon-click="openAppointment"
                                @enter="enterEvent"
                            >
                            </abc-input>
                            <span
                                v-show="displayOrderNo"
                                style="position: absolute; top: 50%; right: 34px; transform: translateY(-50%);"
                            >{{ displayOrderNo }}</span>
                        </div>
                    </abc-form-item>

                    <abc-form-item
                        v-show="!regsHiddenVisitRemark"
                        class="therapy-visit-source-item"
                        label="备注"
                    >
                        <visit-remark
                            ref="therapyRemark"
                            :show-remark.sync="registration.remark"
                            class="therapy-remark"
                            :disabled="disabledForm"
                            type="therapyRemark"
                            placeholder="本次就诊备注"
                            :max-length="300"
                            :width="240"
                            focus-show-options
                            placement="bottom-start"
                            :readonly="false"
                            @enter="enterEvent"
                        >
                        </visit-remark>
                    </abc-form-item>
                </div>
            </abc-form>

            <div slot="footer" class="dialog-footer" style="min-height: 32px;">
                <template v-if="!loading && !readonly">
                    <template v-if="registrationId">
                        <!--
                        没有取消可以打印
                        -->
                        <div
                            v-if="registration.status !== TherapyStatus.CANCELED"
                            style=" display: flex; margin-right: auto;"
                        >
                            <abc-check-access>
                                <print-dropdown
                                    :loading="printLoading"
                                    style="margin-right: 4px;"
                                    @print="print"
                                    @select-print-setting="openPrintConfigSettingDialog"
                                >
                                    打印预约单
                                </print-dropdown>
                            </abc-check-access>

                            <abc-check-access>
                                <abc-button
                                    type="blank"
                                    :loading="refundButtonLoading"
                                    @click="showRefundDialog"
                                >
                                    退号
                                </abc-button>
                            </abc-check-access>
                        </div>

                        <abc-check-access>
                            <abc-button
                                v-if="registration.status === TherapyStatus.WAITING_SIGN_IN"
                                :loading="buttonLoading"
                                @click="signIn"
                            >
                                签到
                            </abc-button>
                        </abc-check-access>
                    </template>

                    <abc-check-access v-else>
                        <abc-button
                            :loading="buttonLoading"
                            @click="confirm()"
                        >
                            完成预约
                        </abc-button>
                    </abc-check-access>
                </template>
            </div>
        </abc-dialog>

        <!--选择预约-->
        <appointment-dialog
            v-if="showAppointment"
            v-model="showAppointment"
            :date-picker="datePicker"
            :department-id="registration.departmentId"
            :prop-doctor-id="registration.doctorId"
            :has-departments="false"
            :business-type="BusinessType.THERAPY"
            @selectTime="selectAppointmentTime"
        >
        </appointment-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import RegistrationsAPI from 'api/registrations/index';
    import PrintAPI from 'api/print';

    import { mapGetters } from 'vuex';
    import localStorage from 'utils/localStorage-handler';

    import PatientForm from '../layout/patient/patient-form/patient-form';
    import RegistrationSetting
        from './registration-setting-dialog/registration-setting-dialog';
    import AppointmentDialog from './appointment-dialog/appointment-dialog';

    import DoctorSelector from '../layout/doctor-selector/doctor-selector';

    import inputSelect from 'views/common/input-select';

    import { zeroFill } from './common/utils';

    import {
        BusinessType,
        TherapySignInStatus,
        TherapyStatus,
    } from './common/constants';
    import { getAbcPrintOptions } from '@/printer/print-handler.js';
    import AbcPrinter from '@/printer/index.js';
    import PrintDropdown from 'views/print/print-dropdown';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import VisitRemark from 'views/registration/visit-source-remark';

    export default {
        name: 'TherapyFormDialog',
        components: {
            PatientForm,
            RegistrationSetting,
            AppointmentDialog,
            DoctorSelector,
            PrintDropdown,
            VisitRemark,
        },

        mixins: [
            inputSelect,
        ],
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            registrationId: {
                type: String,
                default: '',
            },
            // 医生默认是未指定
            doctorId: null,

            // 支持带入 patientId
            // patientId: '',

            readonly: {
                type: Boolean,
                required: false,
            },
            doctors: {
                type: Array,
                required: true,
            },
            patientSourceType: {
                type: Array,
                required: true,
            },
            datePicker: { // 当前默认日期
                type: String,
                default: null,
            },
            patientScanCodeInfoId: { // 流行病登记面板患者ID
                type: [String,Number],
                default: 0,
            },
            isShowScanRegisterDetail: { // 是否展示登记
                type: Boolean,
                required: false,
            },
            scanQrCodePatientList: { // 流调登记
                type: Array,
                default: () => {
                    return [];
                },
            },

        },
        data() {
            return {
                TherapyStatus,
                TherapySignInStatus,
                BusinessType,
                specifyRegistVisible: false,
                showRegSetting: false,
                showAppointment: false,
                showSelectDiscount: false,
                showChargeDialog: false,
                showRefund: false,

                loading: false,
                buttonLoading: false,
                refundButtonLoading: false,
                printLoading: false,
                isFirstPrint: true,
                type: 0,

                patient: {
                    id: '',
                    name: '',
                    sex: '男',
                    birthday: '',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    mobile: '',
                    isMember: null,
                    addressCityId: '',
                    addressCityName: '',
                    addressDetail: '',
                    addressDistrictId: '',
                    addressDistrictName: '',
                    addressProvinceId: '',
                    addressProvinceName: '',
                    idCard: '',
                    company: '',
                    source: {
                        id: '',
                        name: '',
                        sourceFrom: '',
                        sourceFromName: '',
                    },
                    sn: '',
                    tags: [],
                    remark: '',
                },

                patientId: '',

                registration: {
                    departmentId: '',
                    departmentName: '',
                    doctorId: this.doctorId,
                    doctorName: '',
                    remark: '', // 就诊备注
                    reserveDate: '',
                    reserveStart: '',
                    reserveEnd: '',
                    reserveTime: {
                        start: '',
                        end: '',
                    },
                    signIn: 0, // 0不需要签到，1需要签到，2已经签到
                    type: 0, // type: 3，微诊所预约/挂号。2，门诊快速接诊挂号。1，微信预约/挂号（老接口）。0，pc预约/挂号
                    orderNos: [],
                },

                timeOptions: [
                    {
                        id: 1,
                        date: '今天',
                        time: '上午',
                        value: {
                            start: '00:00',
                            end: '12:00',
                        },
                    },
                    {
                        id: 2,
                        date: '今天',
                        time: '下午',
                        value: {
                            start: '12:00',
                            end: '18:00',
                        },
                    },
                    {
                        id: 3,
                        date: '今天',
                        time: '晚上',
                        value: {
                            start: '18:00',
                            end: '24:00',
                        },
                    },
                ],

                discountFee: 0,
                departmentObj: Object.create(null),
                patientOrderId: '',
                registrationFee: null,
                curRefundType: '', // 当前退费类型
                refundTotalFee: 0, // 需要退的费用
                refundData: {}, // 项目退费时，{chargeForms, refundFee, needRefundFee}
            };
        },
        watch: {
            regsHiddenIdCard(value) { // 如果身份证不展示，身份证信息清除
                if (!this.disabledForm && value && !this.$refs?.patientForm?.disabledIdCard) {
                    this.patient.idCard = '';
                }
            },
            regsHiddenAddress(value) { //如果地址不展示
                if (!this.disabledForm && value && !this.$refs?.patientForm?.disabledAddress) {
                    this.patient.addressCityId = '';
                    this.patient.addressCityName = '';
                    this.patient.addressDistrictId = '';
                    this.patient.addressDistrictName = '';
                    this.patient.addressProvinceId = '';
                    this.patient.addressProvinceName = '';
                }
                if (!this.disabledForm && value && !this.$refs?.patientForm?.disabledAddressDetail) {
                    this.patient.addressDetail = '';
                }
            },
            regsHiddenProfession(value) {// 如果职业不展示
                if (!this.disabledForm && value && !this.patient.id) {
                    this.patient.profession = '';
                }
            },
            regsHiddenRemark(value) { // 如果备注不展示
                if (!this.disabledForm && value) {
                    this.patient.remark = '';
                }
            },
            regsHiddenSn(value) { // 如果档案号不展示
                if (!this.disabledForm && value && !this.patient.id) {
                    this.patient.sn = '';
                }
            },
            regsHiddenCompany(value) { // 如果工作单位不展示
                if (!this.disabledForm && value && !this.patient.id) {
                    this.patient.company = '';
                }
            },
        },

        async created() {
            /**
             * @desc 拉取挂号详情状态
             * 1：未收费需要计算折扣(已收费直接读取)
             * <AUTHOR>
             * @date 2019/09/02 17:30:53
             */
            await Promise.all([
                this.$store.dispatch('initTherapyRemarkList'), // 理疗就诊备注
                this.$store.dispatch('crm/fetchCrmConfigList'), // 理疗就诊备注
            ]);

            if (this.registrationId) {
                await this.fetchDetail();
            } else {
                /**
                 * @desc 新增挂号
                 * 1：设置诊所默认挂号费；
                 * 2：需要默认选中第一个科室；若传入了doctorId，则需要默认选中医生所在的科室
                 * 3：初始化同时收费 和 同时打印，需要根据当前是挂号还是预约进行判断
                 * 4：计算折扣；
                 * 5：初始化同时打印小票
                 * 6: 计算号数
                 * <AUTHOR>
                 * @date 2019/09/02 17:31:44
                 */
                // 2:
                this.selectOnlyDoctor(this.doctors);

                // 3:
                this._key = `${this.currentClinic?.clinicId}_${this.currentClinic?.userId}`;
                localStorage.setObj('reg_fee_delay', this._key, null);

                // 如果带入了诊室，走一下医生切换逻辑：包含了算费和算号
                if (this.doctorId) {
                    this.selectDoctor(this.doctorId);
                } else {
                    // 6:
                    this.resetReserveDate();
                }
                this.$nextTick(() => {
                    if (this.isShowScanRegisterDetail && this.patientScanCodeInfoId && this.scanQrCodePatientList.length) {
                        this.$refs.patientForm.initPatientScanCodeInfo();
                    }
                });

            }
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'clinicConfig',
                'printRegistrationConfig',
                'userConfig',
                'chargeConfig', // 收费设置
                'isCanSeePatientMobileInRegistration',
            ]),
            ...mapGetters('crm', ['crmConfigList']),

            labelWidth() {
                return 70;
            },

            leftContainerWidth() {
                return 358;
            },

            clinicFee() {
                return this.clinicConfig && this.clinicConfig.defaultRegUnitPrice || 0;
            },

            regsHiddenIdCard() {
                if (this.crmConfigList?.certificates?.required) {
                    return false;
                }
                return this.userConfig && !!this.userConfig.therapyHiddenIdCard;
            },
            regsHiddenCompany() {
                if (this.crmConfigList?.company?.required) {
                    return false;
                }
                return this.userConfig && !!this.userConfig.therapyHiddenCompany;
            },
            regsHiddenProfession() {
                if (this.crmConfigList?.profession?.required) {
                    return false;
                }
                return this.userConfig && !!this.userConfig.therapyHiddenProfession;
            },
            regsHiddenSource() {
                return this.userConfig && !!this.userConfig.therapyHiddenSource;
            },
            regsHiddenAddress() {
                if (this.crmConfigList?.address?.required) {
                    return false;
                }
                return this.userConfig && !!this.userConfig.therapyHiddenAddress;
            },
            regsHiddenSn() {
                if (this.crmConfigList?.sn?.required) {
                    return false;
                }
                return this.userConfig && !!this.userConfig.therapyHiddenSn;
            },
            regsHiddenTags() {
                return this.userConfig && !!this.userConfig.therapyHiddenTags;
            },
            regsHiddenRemark() {
                if (this.crmConfigList?.remark?.required) {
                    return false;
                }
                return this.userConfig && !!this.userConfig.therapyHiddenRemark;
            },

            regsRequiredMobile() {
                return this.clinicConfig && this.clinicConfig.regsRequiredMobile;
            },
            regsRequiredSource() {
                return this.clinicConfig && this.clinicConfig.regsRequiredSource;
            },
            regsRequiredIdCard() {
                return this.clinicConfig && this.clinicConfig.regsRequiredIdCard;
            },
            regsRequiredCompany() {
                return this.clinicConfig && this.clinicConfig.regsRequiredCompany;
            },
            regsHiddenVisitRemark() {
                return this.userConfig && !!this.userConfig.therapyHiddenVisitRemark;
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            disabledForm() {
                return !!this.registrationId || this.readonly;
            },

            appointmentTime: {
                get() {
                    const {
                        reserveStart, reserveEnd,
                    } = this.registration;
                    let { reserveDate } = this.registration;
                    if (!reserveDate) return '';
                    reserveDate = reserveDate.split('-').splice(1, 2).join('-');
                    return `${reserveDate} ${reserveStart}~${reserveEnd}`;
                },
            },

            displayOrderNo() {
                if (this.registrationId) {
                    if (this.registration.orderNos && this.registration.orderNos.length) {
                        const { orderNo } = this.registration.orderNos[ 0 ];
                        const { timeOfDay } = this.registration.orderNos[ 0 ];
                        // 存在的挂号单，直接读取
                        let precision = 2;
                        if (orderNo >= 100) {
                            precision = 3;
                        }
                        return `${timeOfDay + zeroFill(orderNo, precision)}号`;
                    }
                    return '';
                }
                if (!this.registration.orderNos || this.registration.orderNos.length === 0) {
                    return '';
                }
                const { timeOfDay } = this.registration.orderNos[ 0 ];
                let precision = 2;
                if (this.registration.orderNos[0].orderNo >= 100) {
                    precision = 3;
                }
                return `${timeOfDay + zeroFill(this.registration.orderNos[0].orderNo, precision)}号`;

            },
        },
        methods: {
            modifySn(sn) {
                this.$set(this.patient,'sn',sn);
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'registration' }).generateDialogAsync({ parent: this });
            },
            // 确定查看当日日期
            showTodayNoNumberSure() {
                this.showTodayNoNumber = false;
                this.showAppointment = true;
            },
            /**
             * @desc 如果该科室只有一个医生，默认选中该医生
             * <AUTHOR>
             * @date 2019/08/30 16:04:15
             * @params doctors[Array]
             */
            selectOnlyDoctor(doctors) {
                if (!doctors || !Array.isArray(doctors)) return false;
                if (doctors.length === 1) {
                    this.registration.doctorId = doctors[0].doctorId;
                    this.registration.doctorName = doctors[0].doctorName;
                }
            },

            openAppointment() {
                if (this.disabledForm) return;
                this.showAppointment = true;
            },

            resetReserveDate() {
                // 预约
                this.registration.reserveTime = {
                    start: '',
                    end: '',
                };
                this.registration.reserveStart = '';
                this.registration.reserveEnd = '';
                this.registration.reserveDate = '';
                this.registration.orderNos = [];
            },

            /**
             * @desc 选择医生后，还要填入挂号费
             * <AUTHOR>
             * @date 2019/08/26 15:20:10
             */
            selectDoctor(doctorId) {
                // const doctor = this.doctors.find( doctor => {
                //     return doctor.doctorId === doctorId
                // } )
                // 切换医生需要清除预约时间
                console.log(doctorId);
                this.resetReserveDate();
            },

            confirm() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        this.create();
                    }
                });
            },

            /**
             * @desc 获取挂号详情
             * <AUTHOR>
             * @date 2019/08/26 17:03:24
             */
            async fetchDetail() {
                this.loading = true;
                const { data } = await RegistrationsAPI.fetchTherapy(this.registrationId);
                data.patient.source = data.patient.patientSource || {
                    id: '',
                    name: '',
                    sourceFrom: '',
                    sourceFromName: '',
                };
                Object.assign(data.patient, data.patient.address);

                Object.assign(this.patient, data.patient);

                Object.assign(this.registration, data.therapyRegistration);

                this.registration.doctorName = this.registration.doctorName || '不指定';

                this.loading = false;
            },

            /**
             * @desc 选择预约时间
             * <AUTHOR>
             * @date 2019/08/27 10:31:22
             */
            selectAppointmentTime(item) {
                console.log('selectAppointmentTime', item);
                Object.assign(this.registration, item.registration);
                this.registration.reserveDate = item.registration.reserveDate;
                this.registration.reserveStart = item.registration.reserveTime.start;
                this.registration.reserveEnd = item.registration.reserveTime.end;
                this.registration.doctorId = item.registration.doctorId;
                this.registration.doctorName = item.registration.doctorName;
                this.registration.orderNos = item.registration.orderNos;
            },

            /**
             * @desc 创建理疗预约单
             * <AUTHOR>
             * @date 2020/07/07 20:12:05
             * @params
             * @return
             */
            async create() {
                try {
                    this.buttonLoading = true;

                    const postData = {
                        patient: {
                            ...this.patient,
                            address: {
                                addressCityId: this.patient.addressCityId,
                                addressCityName: this.patient.addressCityName,
                                addressDetail: this.patient.addressDetail,
                                addressDistrictId: this.patient.addressDistrictId,
                                addressDistrictName: this.patient.addressDistrictName,
                                addressProvinceId: this.patient.addressProvinceId,
                                addressProvinceName: this.patient.addressProvinceName,
                            },
                            patientSource: this.patient.source,
                        },
                        orderNos: this.registration.orderNos,
                        reserveDate: this.registration.reserveDate,
                        doctorId: this.registration.doctorId,
                        doctorName: this.registration.doctorName,
                        remark: this.registration.remark,
                    };

                    const { data } = await RegistrationsAPI.createTherapy(postData);

                    this.buttonLoading = false;
                    this.showDialog = false;

                    this.$emit('finish-registration', {
                        before: this.registration,
                        after: data,
                    });

                    this.$emit('refresh');
                } catch (err) {
                    this.buttonLoading = false;
                    const {
                        code,
                        message,
                    } = err;
                    if (code === 17005) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    } else if (code === 15123 || code === 15122 || code === 15125) {
                        // 该事件已经被预订，需要清空预约时间
                        this.resetReserveDate();
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }
                }
            },

            /**
             * @desc 提示退号
             * <AUTHOR>
             * @date 2019/11/29 16:59:45
             * @params
             * @return
             */
            showRefundDialog() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '退号后不可恢复，是否继续？',
                    onConfirm: this.cancelReg,
                });
            },

            /**
             * @desc 签到
             * <AUTHOR>
             * @date 2019/09/23 16:22:08
             * @params
             * @return
             */
            async signIn() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        if (this.registration.isSignTime) {
                            this.signInSubmit();
                        } else {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                content: `患者预约时间（${this.registration.reserveDate}）不是今天。签到后会更改预约时间，是否确定`,
                                onConfirm: () => {
                                    this.signInSubmit();
                                },
                            });
                        }
                    }
                });
            },
            async signInSubmit() {
                try {
                    this.buttonLoading = true;

                    const { data } = await RegistrationsAPI.therapySignIn(this.registrationId);
                    this.$Toast({
                        message: '签到成功',
                        type: 'success',
                    });
                    this.$emit('finish-registration', {
                        before: this.registration,
                        after: data,
                    });
                    this.$emit('refresh', false);
                    this.showDialog = false;
                } catch (err) {
                    console.error(err);
                }
                this.buttonLoading = false;
            },

            async cancelReg() {
                try {
                    this.refundButtonLoading = true;
                    await RegistrationsAPI.cancelTherapy(this.registrationId);
                    this.refundButtonLoading = false;
                    this.showDialog = false;

                    this.$Toast({
                        message: '退号成功',
                        type: 'success',
                    });

                    this.$emit('refresh', false);
                } catch (err) {
                    this.refundButtonLoading = false;
                }
            },

            print() {
                this.$nextTick(async () => {
                    this.printLoading = true;
                    const { data } = await PrintAPI.treatmentPrint(this.registrationId);
                    data.REGISTRATION_TYPE = 'treatment';
                    const options = getAbcPrintOptions('挂号小票', data);
                    await AbcPrinter.abcPrint(options);
                    this.printLoading = false;
                });
            },

            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                this.$nextTick(() => {
                    const inputs = $('.dialog-content.registration-form .abc-input__inner').not(':disabled');
                    const targetIndex = inputs.index(e.target);
                    const nextInput = inputs[ targetIndex + 1 ];

                    nextInput && this.$nextTick(() => {
                        nextInput.focus();
                    });
                });
            },
            zeroFill(num, size) {
                const s = `000000000${num}`;
                return s.substr(s.length - size);
            },
        },
    };
</script>
