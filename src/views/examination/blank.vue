<template>
    <div id="abc-container__blank">
        <abc-container-center-top-head></abc-container-center-top-head>

        <abc-container-center-main-content v-if="examination.isBlank" class="blank-index">
            <abc-content-empty value="暂无检验患者"></abc-content-empty>
        </abc-container-center-main-content>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';

    export default {
        computed: {
            ...mapGetters([
                'examination',
            ]),
        },
    };
</script>
