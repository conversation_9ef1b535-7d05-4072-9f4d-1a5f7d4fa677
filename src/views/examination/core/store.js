import BasePageStore from '@/core/page/store.js';
import SettingAP<PERSON> from 'api/settings';
import WalletAPI from 'api/wallet';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';

export default class ExaminationPageStore extends BasePageStore {
    constructor() {
        const namespace = '@examination';
        const state = {
            examinationDeviceList: [],
            autoPayConfig: {},
            examinationEmployees: [],
        };
        super(namespace, state);
    }

    get autoPayConfig() {
        return this.state.autoPayConfig;
    }

    set autoPayConfig(value) {
        this.state.autoPayConfig = value;
    }

    async init() {
        await Promise.all([
            this.fetchExaminationDeviceList(),
            this.fetchAutoPayConfigConfig(),
            this.fetchExaminationEmployees(),
        ]);
    }

    async fetchExaminationDeviceList() {
        try {
            const { data } = await SettingAPI.inspect.fetchDeviceList();
            this.state.examinationDeviceList = data.rows || [];
        } catch (e) {
            console.error(e);
        }
    }

    async fetchAutoPayConfigConfig() {
        try {
            // 云检自动支付场景值
            const CLOUD_EXAM_AUTO_PAY_SCENE = 104;

            const { data } = await WalletAPI.fetchAutoPayConfig({
                scene: CLOUD_EXAM_AUTO_PAY_SCENE,
            });
            this.autoPayConfig = data;
        } catch (e) {
            console.error(e);
        }
    }

    async updateAutoPayConfig(data) {
        try {
            const res = await WalletAPI.updateAutoPayConfig(data);
            this.autoPayConfig = res.data;
        } catch (e) {
            console.error(e);
        }
    }

    async fetchExaminationEmployees() {
        // 获取检验角色列表
        try {
            this.state.examinationEmployees = await getViewDistributeConfig().Examination.fetchExaminationEmployee();
        } catch (e) {
            console.error('获取检验角色用户列表失败：',e);
        }
    }
}
