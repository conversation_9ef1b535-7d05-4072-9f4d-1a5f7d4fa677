<template>
    <div class="stat-report-table-wrapper">
        <template v-if="data && data.length > 0">
            <section
                v-for="(tablePageItem,idx) in data"
                :key="idx"
                class="report-table-card"
            >
                <div v-if="isShowDashLine" class="dashed-line"></div>
                <pro-report-card-header v-if="idx === 0" :report-title="title" :date-filter$="dateFilter$">
                    <slot v-if="$slots.subTitle" slot="subTitle" name="subTitle"></slot>
                </pro-report-card-header>
                <pro-report-card-table
                    :data="tablePageItem"
                    :loading="loading"
                    :header="header"
                    :summary-data="summaryData"
                    :show-total="idx === data.length - 1 ? showTotal : false"
                ></pro-report-card-table>
                <div v-if="idx === data.length - 1 && totalInfo" class="footer-remarks" v-html="totalInfo"></div>
            </section>
        </template>
        <template v-else>
            <section class="report-table-card">
                <pro-report-card-header :report-title="title" :date-filter$="dateFilter$"></pro-report-card-header>
                <pro-report-card-table
                    :data="[]"
                    :loading="loading"
                    :header="header"
                    :min-height="350"
                ></pro-report-card-table>
            </section>
        </template>
    </div>
</template>
<script>
    import ProReportCardHeader
        from 'views/statistics/common/pro-report-table-card/components/pro-report-card-header.vue';
    import ProReportCardTable from 'views/statistics/common/pro-report-table-card/components/pro-report-card-table.vue';

    export default {
        name: 'ProReportTableCard',
        components: {
            ProReportCardTable, ProReportCardHeader,
        },
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            title: {
                type: String,
                default: '',
            },
            dateFilter$: {
                type: Object,
                default: () => {},
            },
            data: {
                type: Array,
                default: () => [],
            },
            header: {
                type: Array,
                default: () => [],
            },
            showTotal: {
                type: Boolean,
                default: false,
            },
            totalInfo: {
                type: String,
                default: '',
            },
            summaryData: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                isShowDashLine: false,
            };
        },
        mounted() {
            this.$abcEventBus.$on('report-header-slice', (val) => {
                this.isShowDashLine = !val;
            }, this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
    };
</script>

<style lang="scss">
.report-table-card {
    position: relative;
    width: 1123px;
    height: 794px;
    padding: 24px 12px;
    margin: 24px auto 0;
    background: #ffffff;
    border: 1px solid var(--abc-color-P8);
    box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.1);

    &:last-child {
        margin-bottom: 8px;
    }

    .dashed-line {
        position: absolute;
        top: 0;
        left: 1104px;
        z-index: 1;
        width: 1px;
        height: 100%;
        border-right: 1px dashed var(--abc-color-Theme2, #0090ff);
    }

    .footer-remarks {
        position: absolute;
        right: 0;
        bottom: -24px;
        font-size: 12px;
        color: var(--abc-color-T2, #7a8794);
    }

    &-header {
        &-title {
            font-size: 20px;
            font-weight: 600;
            line-height: 32px;
        }

        &-subTitle {
            margin: 12px 0 14px 0;
            line-height: 16px;
        }
    }
}
</style>
