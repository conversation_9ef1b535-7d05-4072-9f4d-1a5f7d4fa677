<template>
    <div class="stat__sell-no-filter-item">
        <abc-input
            v-model="computedSellNo"
            :width="sellNoWidth"
            style="margin-left: 0;"
            :placeholder="placeholder"
            clearable
            @input="handleChange"
        ></abc-input>
        <abc-delete-icon
            v-if="canDelete"
            class="delete-icon"
            variant="fill"
            size="small"
            @delete="removeFilter"
        ></abc-delete-icon>
    </div>
</template>
<script>
    export default {
        name: 'SellNoFilterItem',
        props: {
            placeholder: {
                type: String,
                default: '销售单号',
            },
            sellNoWidth: {
                type: Number,
                default: 100,
            },
            filterEventName: {
                type: String,
                required: true,
            },
            value: {
                type: String,
                default: '',
            },
            canDelete: {
                type: [Number, Boolean],
                default: true,
            },
        },
        data() {
            return {
                computedSellNo: this.value,
            };
        },
        methods: {
            handleChange(val) {
                this.$emit(this.filterEventName, val);
            },
            removeFilter() {
                this.$emit('remove');
            },
        },
    };
</script>
<style lang="scss">
.stat__sell-no-filter-item {
    position: relative;
    display: inline-block;

    .delete-icon {
        position: absolute;
        top: -8px;
        right: -8px;
        z-index: 100;
        display: none;
        width: 16px;
        height: 16px;
    }

    &:hover {
        .delete-icon {
            display: block;
        }
    }
}
</style>
