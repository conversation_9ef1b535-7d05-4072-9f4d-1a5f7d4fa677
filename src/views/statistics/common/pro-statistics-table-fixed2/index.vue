<template>
    <abc-table-fixed2
        ref="tableFixed2Ref"
        :style="{
            marginTop: marginTop
        }"
        :loading="loading"
        :data="data"
        :max-height="maxHeight"
        :min-height="minHeight"
        :header="header"
        :summary-method="summaryMethod"
        :show-total="showTotal"
        :empty-opt="{ label: '暂无数据' }"
    >
        <template v-if="showPagination" #tableFooter>
            <abc-pagination
                :pagination-params="paginationParams"
                :count="totalCount"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li v-if="totalInfo" v-html="totalInfo"></li>
                    <li v-else>
                        共 <span>{{ totalCount }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </template>
    </abc-table-fixed2>
</template>
<script>
    export default {
        name: 'ProStatisticTableFixed2',
        props: {
            totalCount: {
                type: Number,
                default: 0,
            },
            loading: {
                type: Boolean,
                default: false,
            },
            data: {
                type: Array,
                default: () => [],
            },
            header: {
                type: Array,
                default: () => [],
            },
            summaryData: {
                type: Object,
                default: () => ({}),
            },
            totalInfo: {
                type: String,
                default: '',
            },
            paginationParams: {
                type: Object,
                default: () => ({}),
            },
            summaryMethod: {
                type: Function,
                default: null,
            },
            showTotal: {
                type: Boolean,
                default: false,
            },
            showPagination: {
                type: Boolean,
                default: true,
            },
            maxHeight: {
                type: [Number, String],
                default: '',
            },
            /**
             * Table 的最小高度。合法的值为数字或者单位为 px 的高度。
             */
            minHeight: {
                type: [Number, String],
                default: 'auto',
            },
            // 兼容，以后不要用这个值
            marginTop: {
                type: String,
                default: '16px',
            },
        },
        methods: {
            handlePageIndexChange(index) {
                this.$emit('current-change', index);
            },
        },
    };
</script>
