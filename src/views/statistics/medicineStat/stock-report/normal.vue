<template>
    <div>
        <stat-toolbar
            ref="statToolbarRef"
            :clinic-id-filter.sync="params.clinicId"
            :enable-features="toolbarFeatures"
            :patient-width="130"
            :date-filter.sync="params.dateFilter$"
            :is-year-limit-date="isYearLimitDate"
            :handle-export="handleExport"
            :export-task-type="exportTaskType"
            :setting-options="settingOptions"
            @change-date="handleDateChange"
            @change-clinic="handleClinicChange"
            @setting-item-click="handleOpenStatDimensionSettingDialog"
        >
            <filter-select
                v-if="multiPharmacyCanUse && pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY && !currentPharmacyNo"
                v-model="params.pharmacyNos"
                :width="140"
                placeholder="库房"
                multiple
                :options="pharmacyList"
                @change="handlePharmacyList"
            >
            </filter-select>
            <abc-cascader
                v-if="inventoryStatType === 2"
                ref="feeTypeRef"
                v-model="params.feeTypeList"
                :props="{
                    children: 'children',
                    label: 'name',
                    value: 'value',
                }"
                placeholder="项目分类"
                multiple
                :width="100"
                :options="feeTypeFilterOptions"
                @change="handleFeeTypeChange"
            >
            </abc-cascader>

            <abc-button
                slot="right"
                type="blank"
                :disabled="printDisabled"
                @click="handlePrint"
            >
                打印
            </abc-button>
        </stat-toolbar>

        <pro-report-table-card
            ref="reportTableCard"
            :title="tableKeyData[0]?.text"
            :data="tablePageData"
            :date-filter$="params.dateFilter$"
            :loading="loading"
            :header="tableHeader"
        >
            <!--这里要给打印使用 所以直接用了行内样式-->
            <span slot="subTitle" style="display: flex; margin: 12px 0 14px;">
                <span style="flex: 1;">
                    统计时间：{{ params.dateFilter$.begin || '-' }} ～ {{ params.dateFilter$.end || '-' }}
                </span>
                <span v-if="currentPharmacyName" style="flex: 1; text-align: center;">
                    {{ currentPharmacyName }}
                </span>
                <span style="flex: 1;"></span>
            </span>
        </pro-report-table-card>

        <setting-stat-type-dialog v-if="formDialogVisible" v-model="formDialogVisible" @refresh="handleSettingFinish"></setting-stat-type-dialog>
    </div>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import { mapGetters } from 'vuex';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import { isEqual } from 'utils/lodash';
    import { PharmacyTypeEnum } from '@abc/constants';
    import StockEntryAPI from 'views/statistics/core/api/stock-entry';
    import StockEntryReportAPI from 'views/statistics/core/api/stock-report';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import ProReportTableCard from 'views/statistics/common/pro-report-table-card/index.vue';
    import ReportTableUtilsMixin from 'views/statistics/mixins/report-table-utils-mixin';
    import { FunctionalProReportTable } from 'views/statistics/common/pro-report-table-card/pro-report-table';
    import {
        ABCPrintConfigKeyMap, PrintMode,
    } from '@/printer/constants';
    import AbcPrinter from '@/printer';
    import SettingStatTypeDialog from './setting-stat-type-dialog.vue';
    import StockEntryAPi from 'views/statistics/core/api/stock-entry.js';


    export default {
        name: 'NormalGoodsReport',
        components: {
            StatToolbar,
            FilterSelect,
            ProReportTableCard,
            SettingStatTypeDialog,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins, ReportTableUtilsMixin],

        props: {
            /**
             * 指定库房
             */
            currentPharmacyNo: {
                type: String,
                default: '',
            },
        },

        data() {
            return {
                PharmacyTypeEnum,
                params: {
                    clinicId: '',
                    pharmacyNos: [this.currentPharmacyNo],
                    feeTypeList: [],
                    feeType1: '',
                    feeType2: '',
                },
                loading: false,
                exportTaskType: 'goods-inventory-report',
                pharmacyList: [],
                feeTypeFilterOptions: [],
                isYearLimitDate: true,
                settingOptions: [
                    {
                        text: '按一级分类统计',
                        value: '',
                        isOpen: false,
                        groupName: '',
                    },
                ],
                formDialogVisible: false,
            };
        },

        watch: {
            '$route.query.mode': {
                handler() {
                    this.params.clinicId = '';
                    this.params.pharmacyNos = [];
                    this.params.feeTypeList = [];
                    this.params.feeType1 = '';
                    this.params.feeType2 = '';
                    this.getTableData();
                    this.fetchFeeTypeSelectOptions();
                    if (this.multiPharmacyCanUse) {
                        this.getPharmacyList();
                    }
                },
            },
        },

        computed: {
            ...mapGetters(['isSingleStore', 'multiPharmacyCanUse','inventoryStatType']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            toolbarFeatures() {
                return [StatToolbar.Feature.DATE, StatToolbar.Feature.EXPORT, StatToolbar.Feature.CLINIC,StatToolbar.Feature.SETTING];
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },
            pharmacyType() {
                const { mode } = this.$route.query;
                if (mode === 'airPharmacy') {
                    return PharmacyTypeEnum.VIRTUAL_PHARMACY;
                }
                return PharmacyTypeEnum.LOCAL_PHARMACY;
            },
            printDisabled() {
                const data = this.tableData;
                return !(Array.isArray(data) && data.length !== 0);
            },
            currentPharmacyName() {
                return this.tableKeyData[1]?.text ?? '';
            },
        },

        async created() {
            await this.$store.dispatch('fetchInventoryStatType');
            this.settingOptions = [
                {
                    text: this.inventoryStatType === 1 ? '按一级分类统计' : '按二级分类统计',
                    value: '',
                    isOpen: false,
                    groupName: '',
                },
            ];
            if (this.isYearLimitDate) {
                this.formatDateToOneYear();
            }
            this.exportService = new ExportService();
            this.getTableData();
            this.fetchFeeTypeSelectOptions();

            if (this.multiPharmacyCanUse) {
                this.getPharmacyList();
            }
        },

        beforeDestroy() {
            this.exportService.destroy();
        },

        methods: {
            handleDateChange() {
                this.params.feeTypeList = [];
                this.params.feeType1 = '';
                this.params.feeType2 = '';
                this.getTableData();
            },

            getTableParams(isExport = false) {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    pharmacyNos,feeType1,feeType2,
                } = this.params;
                const {
                    enableCostInStatistics,
                } = this.$store.getters;
                const _pharmacyNos = pharmacyNos ? pharmacyNos.filter(Boolean) : [];
                return {
                    beginDate,
                    clinicId: this.queryClinicId,
                    pharmacyNos: isExport ? _pharmacyNos : (_pharmacyNos ? _pharmacyNos.join(',') : []),
                    pharmacyType: this.pharmacyType,
                    endDate,
                    enableCost: enableCostInStatistics,
                    classifyStatisticsType: this.inventoryStatType,
                    fee1: feeType1,
                    fee2: feeType2,
                };
            },

            handleClinicChange() {
                this.params.feeTypeList = [];
                this.params.feeType1 = '';
                this.params.feeType2 = '';
                this.getTableData();
            },

            async handleExport() {
                const params = this.getTableParams(true);
                try {
                    await this.exportService.startExport(this.exportTaskType, params);
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            async getTableData() {
                await this.$nextTick();
                const params = this.getTableParams();
                this.loading = true;
                try {
                    const {
                        data,
                    } = await StockEntryReportAPI.normal({
                        ...params,
                    });
                    if (isEqual(params, this.getTableParams())) {
                        this.setTableData(false, data);
                    }
                } catch (err) {
                    this.loading = false;
                    console.log(err);
                    this.setTableData(true);
                } finally {
                    this.loading = false;
                }
            },

            async getPharmacyList() {
                try {
                    const { data } = await StockEntryAPI.getPharmacyOptions({
                        clinicId: this.queryClinicId,
                        pharmacyType: this.pharmacyType,
                    });

                    if (data) {
                        this.pharmacyList = data.map((item) => {
                            return {
                                name: item.pharmacyName,
                                id: `${item.pharmacyNo}`,
                            };
                        });
                    }

                } catch (e) {
                    console.log(e);
                    this.pharmacyList = [];
                }
            },

            handlePharmacyList() {
                this.getTableData();
            },

            async handlePrint() {
                const _proReportTableInstance = new FunctionalProReportTable({
                    data: this.tableData,header: this.tablePrintHeader,
                });
                const headerDom = this.$refs.reportTableCard.$el.querySelector('.report-table-card-header');

                const tableHtml = _proReportTableInstance.generateHtml();
                const html = headerDom.outerHTML + tableHtml;
                await AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates.statReportTableTemplate,
                    printConfigKey: ABCPrintConfigKeyMap.statistics,
                    data: {},
                    extra: {
                        getHTML: () => {
                            return `<div>${html}</div>`;
                        },
                    },
                    mode: PrintMode.Electron,
                });
            },

            handleOpenStatDimensionSettingDialog() {
                this.formDialogVisible = true;
            },
            handleSettingFinish() {
                this.settingOptions = [
                    {
                        text: this.inventoryStatType === 1 ? '按一级分类统计' : '按二级分类统计',
                        value: '',
                        isOpen: false,
                        groupName: '',
                    },
                ];
                this.getTableData();
            },
            transformArray(arr) {
                const res = [];

                // 遍历输入数组
                for (let i = 0; i < arr.length; i++) {
                    const item = arr[i];
                    const [parent, child] = item;

                    // 检查是否已存在具有相同 value 的父级项
                    const existingParent = res.find((p) => p.value === parent.value);

                    if (existingParent) {
                        // 如果已存在相同 value 的父级项，将子级项添加到其 children 数组中
                        existingParent.children.push(child);
                    } else {
                        // 如果不存在相同 value 的父级项，创建新的父级项对象并添加到结果数组中
                        const newParent = {
                            label: parent.label,
                            value: parent.value,
                            children: [child],
                        };
                        res.push(newParent);
                    }
                }

                return res;
            },
            handleFeeTypeChange(list) {
                const feeType1 = [];
                const feeType2 = [];
                const selectedList = this.transformArray(list);
                const originalList = this.feeTypeFilterOptions;

                selectedList.forEach((item) => {
                    const {
                        value, children,
                    } = item;
                    originalList.forEach((oriItem) => {
                        const {
                            value: oriValue, children: oriChildren,
                        } = oriItem;
                        if (value === oriValue) {
                            if (children.length === oriChildren.length) {
                                feeType1.push(value);
                            } else {
                                const selectedIds = children.map((child) => child.value);
                                feeType2.push(...selectedIds);
                            }
                        }
                    });
                });
                const uniqueFeeType1 = Array.from(new Set(feeType1));
                const uniqueFeeType2 = Array.from(new Set(feeType2));

                this.params.feeType1 = uniqueFeeType1.join();
                this.params.feeType2 = uniqueFeeType2.join();
                this.getTableData();
            },

            // 选择费用分类
            async fetchFeeTypeSelectOptions() {
                const {
                    begin: beginDate, end: endDate,
                } = this.params.dateFilter$;
                const {
                    queryClinicId: clinicId , pharmacyType,
                } = this;

                try {
                    const { data } = await StockEntryAPi.fee({
                        clinicId,
                        beginDate,
                        endDate,
                        pharmacyType,
                    });
                    this.feeTypeFilterOptions = data || [];
                } catch (err) {
                    console.error(err);
                }
            },
        },
    };
</script>
