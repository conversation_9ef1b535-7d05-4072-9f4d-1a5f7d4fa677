<template>
    <abc-layout class="common-padding-container" preset="page-table">
        <abc-layout-header>
            <stat-toolbar
                :enable-features="toolbarFeatures"
                :date-filter.sync="params.dateFilter$"
                :is-year-limit-date="isYearLimitDate"
                custom-clinic-employee
                :clinic-list="clinicList"
                :clinic-id-filter.sync="params.clinicId"
                :handle-export="handleExport"
                :export-task-type="exportTaskType"
                @change-date="handleDateChange"
                @change-clinic="handleClinicChange"
            >
                <filter-select
                    v-if="multiPharmacyCanUse && pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY && showWareSearch && !currentPharmacyNo"
                    v-model="params.pharmacyNos"
                    :custom-style="{
                        marginLeft: 0
                    }"
                    :width="140"
                    multiple
                    :placeholder="showWareHouseSetBasic ? '库房' : '药房'"
                    :options="pharmacyList"
                    @change="handlePharmacyList"
                >
                </filter-select>
            </stat-toolbar>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :loading="loading"
                :render-config="renderTableHeader"
                :summary="displaySummaryData"
                :summary-render-keys="summaryRenderKeys"
                :data-list="tableData"
            >
                <template
                    #pharmacyName="{
                        trData, cellRowSpan
                    }"
                >
                    <abc-table-cell align="center" :cell-row-span="cellRowSpan">
                        <span>{{ trData.pharmacyName }}</span>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="currentPageParams"
                :count="count"
                :class="{ 'show-total': true }"
                @current-change="changePageIndex"
            >
                <ul slot="tipsContent">
                    <li>
                        共 <span>{{ count }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import StockOutAPI from 'views/statistics/core/api/stock-out.js';
    import clinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import { mapGetters } from 'vuex';
    import DateParamsMixin from '../mixins/date-params-mixin';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import { PharmacyTypeEnum } from '@abc/constants';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import { isEqual } from 'utils/lodash';
    import {
        getSummaryRenderKeys, resolveHeader,
    } from 'utils/table';

    export default {
        name: 'StockDamaged',
        components: {
            FilterSelect,
            StatToolbar,
        },
        mixins: [clinicTypeJudger, PickerOptions, DateParamsMixin],
        props: {
            currentPharmacyNo: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                action: [],
                actionOptions: [],
                params: {
                    clinicId: '',
                    pageIndex: 0,
                    pageSize: 0,
                    pharmacyNos: [this.currentPharmacyNo],
                },
                tableHeader: [],
                tableData: [],
                count: 0,
                loading: false,
                calcStatTableInfo: {},
                pharmacyList: [],
                exportTaskType: 'damaged-out',
                isYearLimitDate: true,
                summaryData: {},
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'subClinics', 'multiPharmacyCanUse']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            PharmacyTypeEnum() {
                return PharmacyTypeEnum;
            },
            showWareHouseSetBasic() {
                return this.multiPharmacyCanUse || this.multiPharmacyCanUseWithDefault;
            },
            showWareSearch() {
                return this.viewDistributeConfig.Statistics.stockEntryStat.showWareSearch;
            },
            toolbarFeatures() {
                return [StatToolbar.Feature.DATE, StatToolbar.Feature.CLINIC, StatToolbar.Feature.EXPORT];
            },
            clinicList() {
                return this.subClinics.map((clinic) => {
                    return {
                        ...clinic,
                        shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                    };
                });
            },
            pharmacyType() {
                const { mode } = this.$route.query;

                if (mode === 'airPharmacy') {
                    return PharmacyTypeEnum.VIRTUAL_PHARMACY;
                }

                return PharmacyTypeEnum.LOCAL_PHARMACY;
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },
            renderTableHeader() {
                const list = resolveHeader(
                    this.tableHeader,
                    {},
                    true,
                    this.tableData,
                );
                list.forEach((item) => {
                    item.children.forEach((child) => {
                        child.autoSort = child.sortable;
                    });
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },
            displaySummaryData() {
                return this.tableData.length ? this.summaryData : null;
            },
            summaryRenderKeys() {
                return getSummaryRenderKeys(this.tableHeader);
            },
            currentPageParams() {
                return {
                    showTotalPage: false,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.pageSize,
                    count: this.count,
                };
            },
        },
        created() {
            if (this.isYearLimitDate) {
                this.formatDateToOneYear();
            }
            if (this.multiPharmacyCanUse) {
                this.getPharmacyList();
            }
            this.exportService = new ExportService();
        },
        beforeDestroy() {
            this.exportService?.destroy();
        },
        methods: {
            async handleMounted(data) {
                this.params.pageSize = data.paginationLimit || 10;
                await this.getTableData();
            },
            handlePharmacyList() {
                this.getTableData();
            },
            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                    this.params.pageSize = 0;
                }
                this.loading = true;
                const params = this.getTableParams();
                try {
                    const { data } = await StockOutAPI.getInventoryDamaged(params);
                    if (isEqual(params, this.getTableParams())) {
                        if (data) {
                            this.tableHeader = data?.header || [];
                            this.tableData = data?.data || [];
                            this.summaryData = data?.summary || {};
                            this.count = data?.total?.count || 0;
                        } else {
                            this.tableHeader = [];
                            this.tableData = [];
                            this.summaryData = {};
                            this.count = 0;
                        }
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            handleDateChange() {
                this.getPharmacyList();
                this.getTableData();
            },

            handleClinicChange() {
                if (this.multiPharmacyCanUse) {
                    this.getPharmacyList();
                }
                this.getTableData();
            },

            changePageIndex(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            getTableParams(isExport = false) {
                const {
                    clinicId,
                    dateFilter$: {
                        begin: beginDate,
                        end: endDate,
                    },
                    pharmacyNos,
                } = this.params;

                const { pharmacyType } = this;
                const _pharmacyNos = pharmacyNos ? pharmacyNos.filter(Boolean) : [];
                return {
                    clinicId: this.isChainAdmin ? clinicId : this.currentClinic?.clinicId,
                    beginDate,
                    endDate,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.pageSize,
                    isMultiPharmacy: this.isChainAdmin ? 0 : +this.multiPharmacyCanUse,
                    pharmacyType,
                    pharmacyNos: this.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY ? (isExport ? _pharmacyNos : _pharmacyNos.join(',')) : [],
                };
            },

            async getPharmacyList() {
                try {
                    const { data } = await StockOutAPI.getInventoryDamagedCondition(this.getTableParams());
                    if (data) {
                        this.pharmacyList = data.map((item) => {
                            return {
                                name: item.pharmacyName,
                                id: `${item.pharmacyNo}`,
                            };
                        });
                    }

                } catch (e) {
                    console.error(e);
                    this.pharmacyList = [];
                }
            },

            async handleExport() {
                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        ...this.getTableParams(true),
                        pageSize: undefined,
                        pageIndex: undefined,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },
        },
    };
</script>
