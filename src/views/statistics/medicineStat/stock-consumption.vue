<template>
    <div class="common-padding-container">
        <stat-toolbar
            :enable-features="toolbarFeatures"
            :date-filter.sync="params.dateFilter$"
            :is-year-limit-date="isYearLimitDate"
            custom-clinic-employee
            :clinic-list="clinicList"
            :clinic-id-filter.sync="params.clinicId"
            :handle-export="handleExport"
            :export-task-type="exportTaskType"
            @change-date="handleDateChange"
            @change-clinic="handleClinicChange"
        >
            <filter-select
                v-if="multiPharmacyCanUse && pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY && showWareSearch && !currentPharmacyNo"
                v-model="params.pharmacyNos"
                :custom-style="{
                    marginLeft: 0
                }"
                multiple
                :width="140"
                :placeholder="showWareHouseSetBasic ? '库房' : '药房'"
                :options="pharmacyList"
                @change="handlePharmacyList"
            >
            </filter-select>
        </stat-toolbar>
        <div>
            <abc-table-fixed2
                ref="tableFixed2Ref"
                :loading="loading"
                :header="tableHeader"
                :data="tableData"
                :empty-opt="{ label: '暂无数据' }"
                show-total
                :summary-method="handleSummaries"
                :max-height="calcStatTableInfo && calcStatTableInfo.maxHeight"
                :min-height="calcStatTableInfo && calcStatTableInfo.minHeight"
                style="margin-top: 16px;"
            >
                <template #tableFooter>
                    <abc-pagination
                        :pagination-params="params"
                        :count="count"
                        :class="{ 'show-total': true }"
                        @current-change="changePageIndex"
                    >
                        <ul slot="tipsContent">
                            <li>
                                共 <span>{{ count }}</span> 条数据
                            </li>
                        </ul>
                    </abc-pagination>
                </template>
            </abc-table-fixed2>
        </div>
    </div>
</template>

<script>
    import StockOutAPI from 'views/statistics/core/api/stock-out.js';
    import clinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import { mapGetters } from 'vuex';
    import DateParamsMixin from '../mixins/date-params-mixin';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import {
        calcStatTablePageSize, HeaderHeightEnum,
    } from 'utils/statistic.js';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import { PharmacyTypeEnum } from '@abc/constants';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import { isEqual } from 'utils/lodash';

    export default {
        name: 'StockConsumption',
        components: {
            FilterSelect,
            StatToolbar,
        },
        mixins: [clinicTypeJudger, PickerOptions, DateParamsMixin],
        props: {
            currentPharmacyNo: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                action: [],
                actionOptions: [],
                params: {
                    clinicId: '',
                    pageIndex: 0,
                    pageSize: 0,
                    pharmacyNos: [this.currentPharmacyNo],
                },
                tableHeader: [],
                tableData: [],
                count: 0,
                loading: false,
                calcStatTableInfo: {},
                pharmacyList: [],
                exportTaskType: 'consumption-out',
                isYearLimitDate: true,
                summaryData: {},
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'subClinics', 'multiPharmacyCanUse']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            PharmacyTypeEnum() {
                return PharmacyTypeEnum;
            },
            showWareHouseSetBasic() {
                return this.multiPharmacyCanUse || this.multiPharmacyCanUseWithDefault;
            },
            showWareSearch() {
                return this.viewDistributeConfig.Statistics.stockEntryStat.showWareSearch;
            },
            toolbarFeatures() {
                return [StatToolbar.Feature.DATE, StatToolbar.Feature.CLINIC, StatToolbar.Feature.EXPORT];
            },
            clinicList() {
                return this.subClinics.map((clinic) => {
                    return {
                        ...clinic,
                        shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                    };
                });
            },
            pharmacyType() {
                const { mode } = this.$route.query;

                if (mode === 'airPharmacy') {
                    return PharmacyTypeEnum.VIRTUAL_PHARMACY;
                }

                return PharmacyTypeEnum.LOCAL_PHARMACY;
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },
        },
        created() {
            if (this.isYearLimitDate) {
                this.formatDateToOneYear();
            }
            if (this.multiPharmacyCanUse) {
                this.getPharmacyList();
            }
            this.getTableData();
            this.exportService = new ExportService();
        },
        beforeDestroy() {
            this.exportService?.destroy();
        },
        methods: {
            handleSummaries(data, col) {
                return this.summaryData[col.prop];
            },
            handlePharmacyList() {
                this.getTableData();
            },
            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                const extendHeight = this.$refs?.tableFixed2Ref?.$refs?.mainHeader?.clientHeight || HeaderHeightEnum.ONE_ROW;
                this.calcStatTableInfo = calcStatTablePageSize(extendHeight, true);

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                    this.params.pageSize = 0;
                }
                this.loading = true;
                const params = this.getTableParams();
                try {
                    const { data } = await StockOutAPI.getInventoryConsumption(params);
                    if (isEqual(params, this.getTableParams())) {
                        if (data) {
                            this.tableHeader = data?.header || [];
                            this.tableData = data?.data || [];
                            this.summaryData = data?.summary || {};
                            this.count = data?.total?.count || 0;
                        } else {
                            this.tableHeader = [];
                            this.tableData = [];
                            this.summaryData = {};
                            this.count = 0;
                        }
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            handleDateChange() {
                this.getPharmacyList();
                this.getTableData();
            },

            handleClinicChange() {
                if (this.multiPharmacyCanUse) {
                    this.getPharmacyList();
                }
                this.getTableData();
            },

            changePageIndex(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            getTableParams(isExport = false) {
                const {
                    clinicId,
                    dateFilter$: {
                        begin: beginDate,
                        end: endDate,
                    },
                    pharmacyNos,
                } = this.params;

                const { pharmacyType } = this;
                const _pharmacyNos = pharmacyNos ? pharmacyNos.filter(Boolean) : [];
                return {
                    clinicId: this.isChainAdmin ? clinicId : this.currentClinic?.clinicId,
                    beginDate,
                    endDate,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.pageSize,
                    isMultiPharmacy: this.isChainAdmin ? 0 : +this.multiPharmacyCanUse,
                    pharmacyType,
                    pharmacyNos: this.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY ? (isExport ? _pharmacyNos : _pharmacyNos.join(',')) : [],
                };
            },

            async getPharmacyList() {
                try {
                    const { data } = await StockOutAPI.getInventoryConsumptionCondition(this.getTableParams());

                    if (data) {
                        this.pharmacyList = data.map((item) => {
                            return {
                                name: item.pharmacyName,
                                id: `${item.pharmacyNo}`,
                            };
                        });
                    }

                } catch (e) {
                    console.error(e);
                    this.pharmacyList = [];
                }
            },

            async handleExport() {
                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        ...this.getTableParams(true),
                        pageSize: undefined,
                        pageIndex: undefined,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },
        },
    };
</script>
