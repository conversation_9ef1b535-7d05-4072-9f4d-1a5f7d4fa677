<template>
    <abc-layout class="common-padding-container" preset="page-table">
        <abc-layout-header>
            <stat-toolbar
                :enable-features="toolbarFeatures"
                :clinic-id-filter.sync="params.clinicIdFilter"
                :date-filter.sync="params.dateFilter$"
                :is-year-limit-date="isYearLimitDate"
                :handle-export="handleExport"
                :export-task-type="exportTaskType"
                :setting-options="settingOptions"
                @change-date="handleDateChange"
                @change-clinic="handleClinicChange"
                @setting-click="handelSettingClick"
                @setting-item-click="handleOpenStatDimensionSettingDialog"
            >
                <filter-select
                    v-model="params.supplierId"
                    :width="120"
                    :options="supplierOptions"
                    placeholder="供应商"
                    @change="handleSupplierChange"
                >
                </filter-select>
                <template slot="right">
                    <table-comment class="stock-supplier-table-comment" :font-size="14">
                        <p>供应商统计：统计某时间段内各个供应商，药品的入库，退货，销售，库存余量情况，以此分析供应商质量</p>
                    </table-comment>
                </template>
            </stat-toolbar>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :loading="loading"
                :render-config="renderTableHeader"
                :data-list="tableData"
                :summary="displaySummaryData"
                :summary-render-keys="summaryRenderKeys"
                @handleClickTr="showDetail"
            >
                <template
                    #supplierName="{
                        trData,cellRowSpan
                    }"
                >
                    <abc-table-cell :cell-row-span="cellRowSpan">
                        <abc-text theme="primary">
                            {{ trData.supplierName }}
                        </abc-text>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="params"
                :count="count"
                :class="{ 'show-total': true }"
                @current-change="changePageIndex"
            >
                <ul slot="tipsContent">
                    <li>
                        共 <span>{{ count }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>

        <supplier-detail
            v-if="detailDialogVisible"
            :visible.sync="detailDialogVisible"
            :date-params="params.dateFilter$"
            :clinic-id="queryClinicId"
            :raw-item="rawItemData"
            :inventory-stat-distinguish="!!inventoryStatDistinguish"
        >
        </supplier-detail>
        <setting-stat-distinguish-dialog v-if="formDialogVisible" v-model="formDialogVisible" @refresh="getTableData"></setting-stat-distinguish-dialog>
    </abc-layout>
</template>

<script>
    import SupplierApi from 'views/statistics/core/api/supplier.js';
    import clinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import { mapGetters } from 'vuex';
    import DateParamsMixin from '../mixins/date-params-mixin';
    import SupplierDetail from './supplier-detail';
    import TableComment from 'views/statistics/common/table-comment/table-comment';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import { resolveHeaderV2 } from 'src/views/statistics/utils.js';
    import { PharmacyTypeEnum } from 'views/common/enum';
    import FilterSelect from 'views/layout/filter-select/index.vue';

    import {
        formatMoney, formatNumber,
    } from 'utils';
    import { getSummaryRenderKeys } from 'utils/table';
    import { StockinSupplierrStaticConfig } from 'views/statistics/medicineStat/constants';
    import SettingStatDistinguishDialog from 'views/statistics/common/setting-stat-distinguish-dialog/index.vue';


    export default {
        name: 'StockSupplier',
        components: {
            SettingStatDistinguishDialog,
            SupplierDetail,
            TableComment,
            StatToolbar,
            FilterSelect,
        },
        mixins: [clinicTypeJudger, PickerOptions, DateParamsMixin],
        data() {
            return {
                params: {
                    clinicIdFilter: '',
                    supplierId: '',
                    pageIndex: 0,
                    pageSize: 12,
                },
                tableData: [],
                count: 0,
                tableHeader: [],
                summaryData: {},
                chargeDateRange: '',
                customClinics: [],
                loading: false,
                detailDialogVisible: false,
                rawItemData: null,
                exportTaskType: 'supplier-info',
                isYearLimitDate: true,
                list: [],
                summary: {},
                formatMoney,
                formatNumber,
                listByFilter: [],
                supplierOptions: [],
                settingOptions: [
                    {
                        text: '区分药品类型统计',
                        value: '',
                        isOpen: false,
                        groupName: '',
                    },
                ],
                formDialogVisible: false,
            };
        },
        computed: {
            ...mapGetters(['currentClinic','inventoryStatDistinguish']),
            renderTableHeader() {
                const config = resolveHeaderV2({
                    header: this.tableHeader,
                    staticConfig: StockinSupplierrStaticConfig,
                    renderTypeList: this.renderTypeList,
                    descriptionMap: {
                        lotRejectRateText: 'lotRejectRatePopover',
                        marketingRateText: 'marketingRatePopover',
                    },
                    tableData: this.tableData,
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    ...config,
                };
            },
            displaySummaryData() {
                return this.tableData.length ? this.summaryData : null;
            },
            summaryRenderKeys() {
                return getSummaryRenderKeys(this.tableHeader);
            },
            pharmacyType() {
                const { mode } = this.$route.query;

                if (mode === 'airPharmacy') {
                    return PharmacyTypeEnum.VIRTUAL_PHARMACY;
                }
                return PharmacyTypeEnum.LOCAL_PHARMACY;
            },
            renderTypeList() {
                return {
                    'lotRejectRatePopover': () => {
                        return (
                            <div>
                                <p>用于评估供应商供货品质</p>
                                <p>批退率=(包含退货药品的入库批次总数 / 入库批次总数) * 100%</p>
                            </div>
                        );
                    },
                    'marketingRatePopover': () => {
                        return (
                            <div>
                                <p>用于评估供应商供货种类的销量情况</p>
                                <p>动销率=(销售品种数/库存总品种数) * 100%</p>
                            </div>
                        );
                    },
                };
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicIdFilter;
                }
                return this.currentClinic?.clinicId;
            },

            toolbarFeatures() {
                return [StatToolbar.Feature.DATE, StatToolbar.Feature.CLINIC, StatToolbar.Feature.EXPORT,StatToolbar.Feature.SETTING];
            },
        },
        created() {
            if (this.isYearLimitDate) {
                this.formatDateToOneYear();
            }
            this.exportService = new ExportService();
            this.getSupplierOptions();
        },
        beforeDestroy() {
            this.exportService && this.exportService.destroy();
        },
        methods: {
            handleSummaries(data, col) {
                return this.summaryData[col.prop];
            },

            handleClinicChange() {
                this.getTableData();
            },

            handleDateChange() {
                this.getTableData();
                this.getSupplierOptions();
            },

            handleSupplierChange() {
                this.getTableData();
            },

            changePageIndex(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            showDetail(item) {
                if (item) {
                    this.rawItemData = item;
                    this.detailDialogVisible = true;
                }
            },
            async handleMounted(data) {
                await this.$store.dispatch('fetchInventoryStatIsDistinguishClassify');
                this.params.pageSize = data.paginationLimit || 10;
                await this.getTableData();
            },
            async getTableData(resetPageParams = true) {
                await this.$nextTick();

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                    this.params.pageSize = 0;
                }
                this.loading = true;

                const {
                    begin, end,
                } = this.params.dateFilter$;
                const { pharmacyType } = this;

                const { supplierId = '' } = this.params;

                try {
                    const { data } = await SupplierApi.getSupplierInfo(begin, end, this.queryClinicId, pharmacyType, supplierId,this.inventoryStatDistinguish);
                    if (data?.data) {
                        this.tableData = data.data || [];
                        this.summaryData = data.summary || {};
                        this.tableHeader = data.header || [];
                        this.count = data.total.count || 0;
                    } else {
                        this.tableData = [];
                        this.tableHeader = [];
                        this.summaryData = {};
                        this.count = 0;
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            async getSupplierOptions() {
                try {
                    const {
                        begin: beginDate, end: endDate,
                    } = this.params.dateFilter$;
                    const { supplierId = '' } = this.params;
                    const { pharmacyType } = this;
                    const { data } = await SupplierApi.getSupplierCondition({
                        beginDate,
                        endDate,
                        supplierId,
                        pharmacyType,
                    });

                    if (data) {
                        this.supplierOptions = data?.supplier?.map((item) => {
                            return {
                                ...item,
                                name: item.suppliername,
                                id: item.supplierid,
                            };
                        });
                    } else {
                        this.supplierOptions = [];
                    }

                } catch (err) {
                    console.error(err);
                }

            },

            async handleExport() {
                const {
                    begin: beginDate, end: endDate,
                } = this.params.dateFilter$;
                const {
                    queryClinicId: clinicId, pharmacyType,
                } = this;
                const { supplierId = '' } = this.params;

                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        beginDate,
                        endDate,
                        clinicId,
                        supplierId,
                        pharmacyType,
                        isDistinguishClassify: this.inventoryStatDistinguish,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },
            handleOpenStatDimensionSettingDialog() {
                this.formDialogVisible = true;
            },
            handelSettingClick() {
                this.settingOptions[0].text = this.inventoryStatDistinguish ? '区分药品类型统计' : '不区分药品类型统计';

            },

        },
    };
</script>

<style lang="scss">
.stock-supplier-table-bottom-description {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 18px;
    line-height: 21px;

    .pagination-wrapper {
        margin-top: 0;
    }
}
</style>
