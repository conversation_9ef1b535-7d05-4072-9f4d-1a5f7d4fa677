<template>
    <abc-layout preset="page-table" class="common-padding-container">
        <abc-layout-header>
            <stat-toolbar
                :enable-features="toolbarFeatures"
                :date-filter.sync="params.dateFilter$"
                :clinic-id-filter.sync="params.clinicId"
                :clinic-list="clinicList"
                :dimension-filter.sync="dimension"
                :dimension-picker-options="dimensionOptions"
                :handle-export="onClickExports"
                @change-date="fetchSocialApproveStat"
                @change-clinic="fetchSocialApproveStat"
                @change-dimension="fetchSocialApproveStat"
            >
                <template v-if="isRegionSearch">
                    <abc-select
                        v-model="params.psnType"
                        style="margin-right: 2px;"
                        :width="124"
                        clearable
                        placeholder="人员类别"
                        @change="fetchSocialApproveStat"
                    >
                        <abc-option
                            v-for="item in psnTypeOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                    <abc-select
                        v-model="params.medType"
                        style="margin-right: 2px;"
                        :width="124"
                        clearable
                        placeholder="医疗类别"
                        @change="fetchSocialApproveStat"
                    >
                        <abc-option
                            v-for="item in medTypeOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </template>
                <abc-cascader
                    v-if="$abcSocialSecurity.config.isShandongJinan"
                    v-model="params.insuranceRegions"
                    :options="insuranceRegionsOptions"
                    :width="95"
                    placeholder="医保类型"
                    multiple
                    mutually-exclusive
                    clearable
                    @change="fetchSocialApproveStat"
                ></abc-cascader>
            </stat-toolbar>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table-fixed2
                ref="tableFixed2Ref"
                custom-class="large bordered"
                :loading="loading"
                :cell-merge="cellMerge"
                :header="showTableHeader"
                :data="tableData"
                :empty-opt="{ label: '暂无数据' }"
                :min-height="300"
            ></abc-table-fixed2>
        </abc-layout-content>
    </abc-layout>
</template>

<script>
    import qs from 'qs';
    import StatSocialApi from 'api/stat/social';
    import nationalSocialStatConfig from './config.js';
    import { exportFileByAxios } from 'utils/excel';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar.vue';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';

    export default {
        name: 'SocialStatViewApprove',
        components: { StatToolbar },
        mixins: [ClinicTypeJudger, DateParamsMixins],
        data() {
            return {
                params: {
                    clinicId: '',
                    psnType: '',//人员类别
                    medType: '',//医疗类别
                    insuranceRegions: [],//医保类型
                },
                loading: false,
                tableData: [],
                dimension: '0',//筛选条件
                dimensionOptions: [
                    {
                        label: '0', name: '人员类别',
                    },
                    {
                        label: '1', name: '参保区划',
                    },
                ],
                insuranceRegionsOptions: [
                    {
                        value: '1', label: '市医保',
                    },
                    {
                        value: '2', label: '省医保',
                    },
                    {
                        value: '3', label: '省内异地',
                    },
                    {
                        value: '4', label: '省外异地',
                    },
                ],
                medTypeOptions: [],
                psnTypeOptions: [],
            };
        },
        computed: {
            // 可选门店列表
            clinicList() {
                return this.subClinics
                    .filter((item) => item.chainAdmin !== 1)
                    .map((clinic) => {
                        return {
                            ...clinic,
                            shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                        };
                    });
            },
            // 当前门店名称
            clinicName() {
                if (this.isChainAdmin) {
                    // 当为连锁总部时，显示选中门店名称
                    const { clinicId } = this.params;
                    const target = this.clinicList.find((item) => item.id === clinicId);
                    return target ? target.name : '';
                }
                // 不是连锁总部时，显示当前门店名称
                return this.currentClinic?.clinicName;
            },
            // 是否按参保地区查询
            isRegionSearch() {
                return this.dimension === '1';
            },
            // 获取表头
            showTableHeader() {
                return nationalSocialStatConfig.createTableHeader(this.isRegionSearch);
            },
            templateList() {
                return nationalSocialStatConfig.createTemplate();
            },
            // 没有模板地区
            isExistTemplateRegion() {
                return this.$abcSocialSecurity?.$national?.config?.isChongqingGb ||
                    this.$abcSocialSecurity?.$national?.config?.isGuangdongShenzhen;
            },
            toolbarFeatures() {
                const toolbar = [StatToolbar.Feature.CLINIC, StatToolbar.Feature.DATE, StatToolbar.Feature.EXPORT];
                if (!this.$abcSocialSecurity.config.isShandongJinan) {
                    toolbar.push(StatToolbar.Feature.DIMENSION);
                }
                return toolbar;
            },
        },
        mounted() {
            // 初始化门店默认选中
            const defaultClinic = this.clinicList[0];
            if (this.isChainAdmin && defaultClinic) {
                this.params.clinicId = defaultClinic.id;
            }
        },
        methods: {
            handleMounted() {
                this.fetchNationalPsnMedOptional();
                this.fetchSocialApproveStat();
            },
            /**
             * @desc 获取查询参数
             * <AUTHOR>
             * @date 2021-12-20 20:02:02
             */
            async fetchNationalPsnMedOptional() {
                try {
                    const { data } = await StatSocialApi.fetchNationalPsnMedOptional();
                    const {
                        medTypeOptions, psnTypeOptions,
                    } = data || {};
                    this.medTypeOptions = medTypeOptions;
                    this.psnTypeOptions = psnTypeOptions;
                } catch (error) {
                    console.log('fetchNationalPsnMedOptional error', error);
                }
            },
            /**
             * 处理单元格合并
             * <AUTHOR>
             * @date 2021-02-22
             * @param {Object} row 行数据
             * @param {Object} col 行配置
             * @returns {Object|Boolean}
             */
            cellMerge(row, col) {
                if (col.prop === 'clinicName') {
                    return {
                        rowSpan: this.tableData.length,
                        content: row.clinicName,
                    };
                }
                if (col.prop === 'psnType' && row.isCellMerge) {
                    return {
                        rowSpan: row.rowSpan,
                        content: row.psnType,
                    };
                }
                if (col.prop === 'insuranceRegion' && row.isSummary) {
                    return {
                        colSpan: 3,
                        content: row.insuranceRegion,
                    };
                }
                if (col.prop === 'psnType' && row.isSummary) {
                    return {
                        colSpan: 2,
                        content: row.psnType,
                    };
                }
                return false;
            },
            /**
             * 获取查询参数
             * <AUTHOR>
             * @date 2021-02-22
             * @returns {Object}
             */
            getParams() {
                const {
                    clinicId,
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    psnType,
                    medType,
                } = this.params;
                const params = {
                    clinicId, // 总部筛选门店，可以为空
                    beginDate,
                    endDate,
                };
                if (this.isRegionSearch) {
                    params.psnTypes = psnType;
                    params.medType = medType;
                }
                if (this.params.insuranceRegions.length) {
                    params.insuranceRegions = this.params.insuranceRegions[0].map((item) => item.value).join(',');
                }
                return params;
            },
            /**
             * 拉取统计数据
             * <AUTHOR>
             * @date 2021-02-05
             */
            async fetchSocialApproveStat() {
                await this.$nextTick();
                this.loading = true;
                try {
                    const params = this.getParams();
                    if (this.isRegionSearch) {
                        const response = await nationalSocialStatConfig.fetchApproveStatByRegion(params);
                        this.tableData = response.data.settleItemList.map((item) => ({
                            clinicName: this.clinicName,
                            ...item,
                        }));
                    } else {
                        const response = await nationalSocialStatConfig.fetchApproveStatByPsnType(params);
                        this.tableData = this.isExistTemplateRegion ?
                            this.createTableData(response.data) :
                            this.createNotemplateTableData(response.data);
                    }
                } catch (error) {
                    console.log('fetchSocialApproveStat error', error);
                    this.tableData = [];
                } finally {
                    this.loading = false;
                }
            },
            /**
             * 创建表单数据
             * <AUTHOR>
             * @date 2021-02-22
             * @param {Object} sourceData 原始数据
             * @returns {Object}
             */
            createTableData(sourceData) {
                const {
                    approveItems = [],//其他维度数据
                    summary,//合计
                } = sourceData;
                const tableData = this.templateList
                    .map((item) => {
                        const {
                            psnType, clinicType, isSummary,
                        } = item;
                        let approveItem = null;
                        if (isSummary) {
                            approveItem = summary.find((one) => one.clinicType === psnType);
                        } else {
                            approveItem = approveItems.find((one) => one.psnType === psnType) && approveItems.find((one) => one.psnType === psnType)
                                .ordinaryOutpatient.find((two) => two.clinicType === clinicType);
                        }

                        return {
                            clinicName: this.clinicName,
                            ...approveItem,
                            ...item,
                        };
                    });
                return tableData;
            },

            /**
             * 创建表单数据
             * <AUTHOR>
             * @date 2021-02-22
             * @param {Object} sourceData 原始数据
             * @returns {Object}
             */
            createNotemplateTableData(sourceData) {
                const {
                    approveItems = [],//其他维度数据
                    summary,//合计
                } = sourceData;
                if (approveItems.length === 0) {
                    // 如果没有分项数据，直接返回
                    return [];
                }

                const approveItem = [];
                approveItems.forEach((item) => {
                    const len = item.ordinaryOutpatient.length;
                    const arr = item.ordinaryOutpatient.map((ele,index) => {
                        if (index === 0) {
                            return {
                                ...ele,
                                isCellMerge: true,
                                rowSpan: len,
                                psnType: item.psnType,
                                clinicName: this.clinicName,
                            };
                        }
                        return {
                            ...ele,
                            psnType: item.psnType,
                            clinicName: this.clinicName,
                        };
                    });
                    approveItem.push(...arr);
                });

                summary.forEach((item) => {
                    approveItem.push({
                        ...item,
                        clinicName: this.clinicName,
                        psnType: item.clinicType,
                        insuranceRegion: item.clinicType,
                        isSummary: true,
                    });
                });
                return approveItem;
            },
            /**
             * 当点击导出按钮
             * <AUTHOR>
             * @date 2021-02-05
             */
            onClickExports() {
                const params = this.getParams();
                if (this.isRegionSearch) {
                    const url = `${this.$abcSocialSecurity.config.exportPathByRegion}`;
                    return exportFileByAxios({
                        url,
                        params,
                        paramsSerializer(p) {
                            return qs.stringify(p);
                        },
                    });
                }
                const url = `${this.$abcSocialSecurity.config.exportPathByPsnType}`;
                return exportFileByAxios({
                    url,
                    params,
                    paramsSerializer(p) {
                        return qs.stringify(p);
                    },
                });

            },
        },
    };
</script>
