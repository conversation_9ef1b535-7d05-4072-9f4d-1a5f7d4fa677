import { mapGetters } from 'vuex';

export default {
    computed: {
        ...mapGetters([ 'clinics', 'currentClinic', 'subClinics', 'isChain', 'isChainAdmin', 'isSingleStore', 'isChainSubStore', 'isAdmin' ]),
        //currentClinic 存在ChainId且isChainAdmin != 1
        isChainStore() {
            return this.isChainSubStore;
        },
        chainId() {
            return this.currentClinic && this.currentClinic.chainId;
        },

        chainClinicId() {
            const { chainId, clinicId } = this;
            return chainId ? chainId : clinicId;
        },
        clinicId() {
            return this.currentClinic && this.currentClinic.clinicId;
        },
        isOnlyChain() {
            return this.isChainSubStore;
        },
        chainClinics() {
            if (this.currentClinic) {
                return this.clinics.filter((it) => {
                    return it && it.chainId === this.currentClinic.chainId && it.clinicId !== this.currentClinic.clinicId;
                });
            }
            return [];

        },
        chains() {
            if (this.currentClinic) {
                return this.clinics.filter((it) => {
                    return it && it.chainId === this.currentClinic.chainId;
                });
            }
            return [];

        },
        chainClinicsExcludeSelf() {
            if (this.currentClinic) {
                return this.clinics.filter((it) => {
                    return it && it.chainId === this.currentClinic.chainId && it.clinicId !== this.currentClinic.clinicId && it.chainAdmin !== 1;
                });
            }
            return [];

        },
        clinicName() {
            return this.currentClinic && this.currentClinic.clinicName;
        },
        editable() {
            return this.isChainAdmin || !this.isChain;
        },
    },
};
