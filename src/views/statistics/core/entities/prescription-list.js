import { calTotal } from 'views/statistics/common/util';

/**
 * @desc 处方分类列表，管理各种形态，用于 tableHeader
 * <AUTHOR>
 * @date 2020/03/09 22:56:05
 * @params
 * @return
 */
export default class PrescriptionList {
    constructor(list) {
        // 存储原始的数据结构，原始结构是一个树状结构，包含 children
        this.originalList = list.map((item) => {
            // const { children, ...rest } = item;
            return {
                value: item.value,
                name: item.name,
            };
        });
    }

    get() {
        return this.originalList;
    }

    /**
     * 展平
     * @returns {*}
     */
    forTableHeader() {
        function flatten(arr) {
            return arr.reduce((acc, item) => {
                if (item.children) {
                    return acc.concat([item, ...flatten(item.children)]);
                } else {
                    return acc.concat(item);
                }
            }, []);
        }

        const res = flatten(this.originalList);
        return res.map((item) => {
            const width = Math.max(item.name.length * 20 + 20, 86);
            return {
                label: item.name,
                prop: item.value,
                align: 'right',
                width: width,
                summary: (data, col) => {
                    return calTotal(
                        data.filter((item) => !item.isWriter),
                        col.prop
                    );
                },
                isChild: item.isChild,
                sortable: true,
                formatter: (item, col) => {
                    return (item && item[col.prop]) || '-';
                },
            };
        });
    }
}
