import fetch from 'utils/fetch';
import Qs from 'qs';
import store from '@/store';
import { getApp } from '@/core';
import { exportFileByAxios } from 'utils/excel';

async function get(url, params, options = {}) {
    const res = await fetch.get(url, {
        params,
        ...options,
    });
    return res.data;
}

export default {
    overview: {
        /**
         * @desc 【经营概况】经营概况 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=333
         * @param beginDate
         * @param endDate
         * @param clinicId
         */
        async summary(beginDate, endDate, clinicId) {
            const res = await fetch.get('/api/v2/sc/stat/revenue/overview/summary', {
                params: {
                    beginDate,
                    endDate,
                    clinicId,
                },
            });
            return res.data;
        },

        /**≠
         * @desc 【经营概况】实收金额趋势 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=335
         * @param {yyyy-MM-dd} beginDate
         * @param {yyyy-MM-dd} endDate
         * @param {'day' | 'month'} groupBy
         * @param {string} clinicId
         * @returns {array}
         */
        async dailyRevenue(beginDate, endDate, clinicId, groupBy) {
            const res = await fetch.get('/api/v2/sc/stat/revenue/overview/revenue/trend', {
                params: {
                    beginDate,
                    endDate,
                    clinicId,
                    groupBy,
                },
            });
            return res.data;
        },

        /**
         * @desc 【经营概况】收费人次趋势 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=336
         * @param {yyyy-MM-dd} beginDate
         * @param {yyyy-MM-dd} endDate
         * @param {string} clinicId
         * @param {'day' | 'month'} groupBy
         * @returns {array: [{"day": "2019-12-01", "value": 3}]}
         */
        async dailyPatient(beginDate, endDate, clinicId, groupBy) {
            const res = await fetch.get('/api/v2/sc/stat/revenue/overview/patient/trend', {
                params: {
                    beginDate,
                    endDate,
                    clinicId,
                    groupBy,
                },
            });
            return res.data;
        },

        /**
         * @desc 医生视角工作汇总
         * <AUTHOR>
         * @date 2022-01-13 12:03:09
         */
        async fetchDoctorJobSummary(params) {
            const {
                clinicId, beginDate,endDate, doctorId,
            } = params;
            const res = await fetch.get('/api/v2/sc/stat/revenue/overview/job-summary/summary', {
                params: {
                    clinicId, beginDate,endDate, doctorId,
                },
            });
            return res && res.data;
        },

        /**
         * @desc 医生视角工作汇总-收费查看
         * <AUTHOR>
         * @date 2022-01-13 12:03:09
         */
        summaryIncome: {
            //诊所
            async fetchClinicDoctorJobSummaryIncome(params) {
                const {
                    clinicId, beginDate,endDate, doctorId,
                    offset,
                    size,
                } = params;
                const res = await fetch.get('/api/v2/sc/stat/revenue/overview/job-summary/income/doctor', {
                    params: {
                        clinicId,
                        beginDate,
                        endDate,
                        doctorId,
                        offset,
                        size,
                    },
                });
                return res && res.data;
            },
            //医院
            async fetchHospitalDoctorJobSummaryIncome(params) {
                const {
                    clinicId, beginDate,endDate, doctorId,
                    offset,
                    size,
                } = params;
                const res = await fetch.get('/api/v2/sc/stat/revenue/overview/job-summary/income/hospital/doctor', {
                    params: {
                        clinicId,
                        beginDate,
                        endDate,
                        doctorId,
                        offset,
                        size,
                    },
                });
                return res && res.data;
            },
        },

        /**
         * @desc 收费小工具-收费明细
         * <AUTHOR>
         * @date 2022-03-22 15:31:42
         */
        async fetchCashierTransaction(params) {
            const res = await fetch.get('/api/v2/sc/stat/revenue/gadget/transaction-list', {
                params,
            });
            return res && res.data;
        },

        /**
         * @desc 收费小工具-收费看板汇总
         * <AUTHOR>
         * @date 2022-03-22 15:32:41
         */
        async fetchCashierOverview(params) {
            const {
                clinicId,
                beginDate,
                endDate,
                employeeId,
                employeeType,
            } = params;
            const res = await fetch.get('/api/v2/sc/stat/revenue/gadget/overview', {
                params: {
                    clinicId,
                    beginDate,
                    endDate,
                    employeeId,
                    employeeType,
                },
            });
            return res && res.data;
        },

        /**
         * @desc 收费小工具-对账信息
         * <AUTHOR>
         * @date 2022-03-22 15:32:41
         */
        async fetchCashierCheckAccount(params) {
            const {
                clinicId,
                beginDate,
                endDate,
                employeeId,
                employeeType,
            } = params;
            const res = await fetch.get('/api/v2/sc/stat/revenue/gadget/check-account', {
                params: {
                    clinicId,
                    beginDate,
                    endDate,
                    employeeId,
                    employeeType,
                },
            });
            return res && res.data && res.data.data;
        },

        /**
         * @desc 药房小工具-药房看板汇总
         * <AUTHOR>
         * @date 2022-03-22 15:32:41
         */
        async fetchPharmacyOverview(params) {
            const {
                clinicId,
                employeeId,
                beginDate,
                endDate,
            } = params;
            const res = await fetch.get('/api/v2/sc/stat/achievement/dispensing/gadget/summary', {
                params: {
                    clinicId,
                    employeeId,
                    beginDate,
                    endDate,
                },
            });
            return res && res.data;
        },
        /**
         * @desc 药房小工具-药房明细
         * <AUTHOR>
         * @date 2022-03-22 15:31:42
         */
        async fetchPharmacyTransaction(params) {
            const {
                clinicId,
                employeeId,
                beginDate,
                endDate,
                offset,
                size,
            } = params;
            const res = await fetch.get('/api/v2/sc/stat/achievement/dispensing/gadget/detail', {
                params: {
                    clinicId,
                    employeeId,
                    beginDate,
                    endDate,
                    offset,
                    size,
                },
            });
            return res && res.data;
        },

    },

    dailyReport: {
        /**
         * 【收费日报】收费日报 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=339
         * @param {yyyy-MM-dd} beginDate
         * @param {yyyy-MM-dd} endDate
         * @param {string} clinicId
         * @param payModes
         * @param feeTypes
         * @returns {Promise<any>}
         */
        async stat(beginDate, endDate, clinicId, payModes, feeType1, feeType2, includeReg, feeTypeIds) {
            const {
                enableGrossInStatistics,enableCostInStatistics,
            } = store.getters;
            const res = await fetch.get('/api/v2/sc/stat/operation/charge/report/list', {
                params: {
                    beginDate,
                    endDate,
                    clinicId,
                    payModes,
                    feeType1,
                    feeType2,
                    enableCost: enableCostInStatistics,
                    enableGross: enableGrossInStatistics,
                    includeReg,
                    feeTypeIds,
                },
            });
            return res.data;
        },

        statExport(beginDate, endDate, clinicId, payModes, feeType1, feeType2, includeReg, feeTypeIds) {
            const {
                enableGrossInStatistics,enableCostInStatistics,
            } = store.getters;
            const query = {
                beginDate,
                endDate,
                clinicId,
                payModes,
                feeType1,
                feeType2,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
                includeReg,
                feeTypeIds,
            };
            const url = '/api/v2/sc/stat/operation/charge/report/list/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },

        selectOptions({
            beginDate,
            endDate,
            clinicId,
            includeReg,
            needSort,
            includeCard,
        }) {
            return get('/api/v2/sc/stat/revenue/fee-types/selection', {
                beginDate,
                endDate,
                clinicId,
                includeReg,
                needSort,
                includeCard,
            });
        },
    },

    cashier: {
        /**
         * 【收费员统计】收费员统计 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=341
         * @param beginDate
         * @param endDate
         * @param clinicId
         * @param payModes
         * @param feeTypes
         * @returns {Promise<any>}
         */
        list(beginDate, endDate, clinicId, payModes, feeType1, feeType2, feeTypeIds) {
            const {
                enableCostInStatistics,enableGrossInStatistics,
            } = store.getters;
            return get('/api/v2/sc/stat/operation/charge/cashier/list', {
                beginDate,
                endDate,
                clinicId,
                payModes,
                feeType1,
                feeType2,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
                feeTypeIds,
            });
        },

        /**
         * 【收费员统计】收费员统计导出 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=350
         * @param beginDate
         * @param endDate
         * @param clinicId
         * @param payModes
         * @param feeTypes
         */
        exportList(beginDate, endDate, clinicId, payModes, feeType1, feeType2, feeTypeIds) {
            const {
                enableCostInStatistics,enableGrossInStatistics,
            } = store.getters;
            const query = {
                beginDate,
                endDate,
                clinicId,
                payModes,
                feeType1,
                feeType2,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
                feeTypeIds,
            };
            const url = '/api/v2/sc/stat/operation/charge/cashier/list/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },

        /**
         * 【收费员统计】收费员统计明细 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=345
         * @param sellerId
         * @param beginDate
         * @param endDate
         * @param clinicId
         * @param payModes
         * @param feeTypes
         * @returns {Promise<any>}
         */
        detail(sellerId, beginDate, endDate, clinicId, payModes, feeType1, feeType2) {
            return get('/api/v2/stat/es/operation/charge/seller/list', {
                sellerId,
                beginDate,
                endDate,
                clinicId,
                payModes,
                feeType1,
                feeType2,
            });
        },

        exportDetail(sellerId, beginDate, endDate, clinicId, payModes, feeTypes) {
            const query = {
                sellerId,
                beginDate,
                endDate,
                clinicId,
                payModes,
                feeTypes,
            };
            const url = '/api/v2/sc/stat/operation/charge/cashier/list/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },
    },

    transaction: {
        /**
         * [desc] 单据 - total
         */
        async listTotal(
            beginDate,
            endDate,
            clinicId,
            sellerId,
            doctorId,
            payModes,
            patientId,
            medicalRecordType1,
            medicalRecordType2,
            medicalRecordType3,
            action,
            sourceType,
            employeeTypeEnum,
        ) {
            const res = await fetch ({
                url: '/api/v2/sc/stat/revenue/charge/detail/transaction/total',
                method: 'post',
                data: {
                    beginDate,
                    endDate,
                    clinicId,
                    sellerId,
                    doctorId,
                    payModes,
                    patientId,
                    medicalRecordType1,
                    medicalRecordType2,
                    medicalRecordType3,
                    action,
                    sourceType,
                    employeeTypeEnum,
                },
                disallowDuplicate: true,
            });

            return res.data;
        },

        // 单据 - 收费单明细
        async queryChargeSheet(params) {
            const res = await fetch({
                url: '/api/v2/sc/stat/revenue/charge/detail/query-sheet',
                method: 'get',
                params,
            });

            return res && res.data;
        },
        // 收费单明细-修改支付方式 查询收费明细统计有更改支付方式的详情
        async queryChargePayMode(params) {
            const res = await fetch({
                url: '/api/v2/sc/stat/revenue/charge/detail/query/pay-mode',
                method: 'post',
                data: params,
            });

            return res && res.data;
        },
        /**
         * [desc] 分类 - total
         */
        async feeTypeTotal(
            beginDate,
            endDate,
            clinicId,
            sellerId,
            doctorId,
            payModes,
            feeType1,
            feeType2,
            patientId,
            medicalRecordType1,
            medicalRecordType2,
            medicalRecordType3,
            action,
            sourceType,
            employeeTypeEnum,
        ) {
            const res = await fetch ({
                url: '/api/v2/sc/stat/revenue/charge/detail/classify/total',
                method: 'post',
                data: {
                    beginDate,
                    endDate,
                    clinicId,
                    sellerId,
                    doctorId,
                    payModes,
                    feeType1,
                    feeType2,
                    patientId,
                    medicalRecordType1,
                    medicalRecordType2,
                    medicalRecordType3,
                    action,
                    sourceType,
                    employeeTypeEnum,
                },
                disallowDuplicate: true,
            });

            return res.data;
        },

        /**
         * @desc 收费明细 - 明细
         * <AUTHOR>
         * @date 2020/03/17 14:21:25
         * @params
         * @return
         */
        async detail(
            beginDate,
            endDate,
            clinicId,
            sellerId,
            doctorId,
            payModes,
            feeType1,
            feeType2,
            medicalRecordType1,
            medicalRecordType2,
            medicalRecordType3,
            patientId,
            offset,
            size,
            productId,
            keyword,
            action,
            sourceType,
            employeeTypeEnum,
        ) {
            const { enablePatientMobileInStatistics } = getApp().store.getters;
            return get(
                '/api/v2/sc/stat/revenue/charge/detail/items',
                {
                    beginDate,
                    endDate,
                    clinicId,
                    sellerId,
                    doctorId,
                    payModes,
                    feeType1,
                    feeType2,
                    medicalRecordType1,
                    medicalRecordType2,
                    medicalRecordType3,
                    patientId,
                    offset,
                    size,
                    productId,
                    keyword,
                    action,
                    sourceType,
                    employeeTypeEnum,
                    enablePatientMobile: enablePatientMobileInStatistics,
                },
                {
                    disallowDuplicate: true,
                },
            );
        },

        /**
         * [desc] 明细 total
         */
        async detailTotal(
            beginDate,
            endDate,
            clinicId,
            sellerId,
            doctorId,
            payModes,
            feeType1,
            feeType2,
            patientId,
            productId,
            keyword,
            medicalRecordType1,
            medicalRecordType2,
            medicalRecordType3,
            action,
            sourceType,
            employeeTypeEnum,
        ) {
            return get(
                '/api/v2/sc/stat/revenue/charge/detail/items/total',
                {
                    beginDate,
                    endDate,
                    clinicId,
                    sellerId,
                    doctorId,
                    payModes,
                    feeType1,
                    feeType2,
                    patientId,
                    productId,
                    keyword,
                    medicalRecordType1,
                    medicalRecordType2,
                    medicalRecordType3,
                    action,
                    sourceType,
                    employeeTypeEnum,
                },
                {
                    disallowDuplicate: true,
                },
            );
        },

        exportList(
            beginDate,
            endDate,
            clinicId,
            sellerId,
            doctorId,
            payModes,
            feeType1,
            feeType2,
            patientId,
            productId,
            keyword,
            medicalRecordType1,
            medicalRecordType2,
            medicalRecordType3,
            employeeTypeEnum,
        ) {
            const query = {
                beginDate,
                endDate,
                clinicId,
                sellerId,
                doctorId,
                payModes,
                feeType1,
                feeType2,
                patientId,
                productId,
                keyword,
                medicalRecordType1,
                medicalRecordType2,
                medicalRecordType3,
                employeeTypeEnum,
            };
            const url = '/api/v2/sc/stat/revenue/charge/detail/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },

        /**
         * @desc 收费方式
         * <AUTHOR>
         * @date 2022/10/27 14:52:33
         * @param
         * @return
         */
        async getPayModes(params) {
            const res = await fetch({
                url: '/api/v2/sc/stat/revenue/payModes/selection',
                method: 'get',
                params,
            });
            return res?.data;
        },
    },

    feeType: {
        /**
         * 【收费分类统计】收费分类统计 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=348
         * @param beginDate
         * @param endDate
         * @param clinicId
         * @returns {Promise<any>}
         */
        async stat(begin, end, clinicId) {
            const beginDate = `${begin} 00:00:00`;
            const endDate = `${end} 23:59:59`;
            const {
                enableCostInStatistics,enableGrossInStatistics,
            } = store.getters;
            return get('/api/v2/sc/stat/operation/fee/types/list', {
                beginDate,
                endDate,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
                clinicId,
            });
        },

        async export(begin, end, clinicId) {
            const {
                enableCostInStatistics,enableGrossInStatistics,
            } = store.getters;
            const beginDate = `${begin} 00:00:00`;
            const endDate = `${end} 23:59:59`;
            const query = {
                beginDate,
                endDate,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
                clinicId,
            };

            const url = '/api/v2/sc/stat/operation/fee/types/list/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },
    },

    /**
     * 收费项目
     */
    product: {
        async list(
            beginDate,
            endDate,
            clinicId,
            cashierId,
            employeeId,
            feeType1,
            feeType2,
            productId,
            offset,
            size,
            sort,
            order,
            keyword,
        ) {
            const {
                enableCostInStatistics,enableGrossInStatistics,
            } = store.getters;
            return get('/api/v2/sc/stat/operation/charge/product/list', {
                beginDate,
                endDate,
                clinicId,
                cashierId,
                employeeId,
                feeType1,
                feeType2,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
                productId,
                sort,
                order,
                offset,
                size,
                keyword,
            });
        },

        async exportList(beginDate, endDate, clinicId, cashierId, employeeId, feeType1, feeType2, productId, keyword) {
            const {
                enableCostInStatistics,enableGrossInStatistics,
            } = store.getters;
            const query = {
                beginDate,
                endDate,
                clinicId,
                cashierId,
                employeeId,
                feeType1,
                feeType2,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
                productId,
                keyword,
            };
            const url = '/api/v2/sc/stat/operation/charge/product/list/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },
    },
};
