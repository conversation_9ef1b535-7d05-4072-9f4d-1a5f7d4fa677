import fetch from 'utils/fetch';
import Qs from 'qs';
import store from '@/store';

export default {
    // 分类
    async classify(params) {
        const {
            enableCostInStatistics,
        } = store.getters;
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/classify',
            method: 'post',
            data: {
                ...params,
                enableCost: enableCostInStatistics,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            disallowDuplicate: true,
        });
        return res.data;
    },
    // 药品
    async goods(params) {
        const {
            enableCostInStatistics,
        } = store.getters;
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/summary',
            method: 'post',
            data: {
                ...params,
                enableCost: enableCostInStatistics,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            disallowDuplicate: true,
        });
        return res.data;
    },
    // 汇总
    async summary(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/stock/summary',
            method: 'get',
            params,
            disallowDuplicate: true,
        });
        return res?.data;
    },
    // 库存变更
    async stockChange(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/stock/summary/stock/change',
            method: 'get',
            params,
        });
        return res?.data?.data;
    },
    // 明细
    async record(params) {
        const {
            enableCostInStatistics,
        } = store.getters;
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/record',
            method: 'post',
            data: {
                ...params,
                enableCost: params.enableCost !== undefined ? params.enableCost : enableCostInStatistics,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            disallowDuplicate: true,
        });
        return res.data;
    },
    // 动作
    async action(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/available/action',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res.data;
    },
    // 药房
    async getPharmacyOptions(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/pharmacy',
            method: 'get',
            params,
        });
        return res?.data;
    },
    // 分类
    async fee(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/available/fee',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res.data;
    },
    /**
     * @desc 进销存，获取虚拟药房供应商接口
     * <AUTHOR>
     * @date 2022-04-26 14:10:14
     */
    async virtualSupplier() {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/record/supplier',
            method: 'get',
        });
        return res.data;
    },

    // 税率分布
    async getGoodssTaxDIs(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/goods/taxDis',
            method: 'get',
            params,
        });
        return res?.data;
    },

    // 税率分布
    async getGoodssTaxDetail(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/goods/taxDetail',
            method: 'get',
            params,
        });
        return res?.data;
    },
    // 药品列表
    async getGoodsId(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/stock/summary/select/goods/list',
            method: 'get',
            params,
        });
        return res?.data;
    },

    // 税率变更
    async getRateModifyData(params) {
        const res = await fetch({
            url: 'api/v2/sc/stat/goods/inventory/stock/summary/taxrate/modifyinfo',
            method: 'get',
            params,
        });
        return res?.data?.data;
    },

    // 获取检索条件-来源
    async getSceneTypeSelection(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/available/scene',
            method: 'get',
            params,
        });

        return res?.data;
    },
    //查询入库单修正变更记录
    async getGoodsAmendChangeLog(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/goods/inventory/in-order/amend/change-log',
            method: 'get',
            params,
        });
        return res?.data;
    },
};
