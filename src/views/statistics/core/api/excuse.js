import fetch from 'utils/fetch';
import Qs from 'qs';
import { exportFileByAxios } from 'utils/excel';

export default {
    async projects(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/projects',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            disallowDuplicate: true,
        });
        return res && res.data;
    },

    async person(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/person',
            method: 'post',
            data: params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            disallowDuplicate: true,
        });
        return res && res.data;
    },

    async detail(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/detail',
            method: 'post',
            data: params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
            disallowDuplicate: true,
        });
        return res && res.data;
    },

    async clinics(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/available/clinics',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res && res.data;
    },

    async types(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/available/types',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res && res.data;
    },

    async compose(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/available/compose',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res && res.data;
    },

    async employees(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/available/persons',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res && res.data;
    },
    async adviceFeeTypes() {
        const res = await fetch({
            url: '/api/v2/sc/stat/execute/bill/available/classify',
            method: 'get',
        });
        return res && res.data;
    },
    export(clinicId, beginDate, endDate, composeId, goodsId, goodsKeyword, personId, personKeyword) {
        const query = {
            clinicId,
            beginDate,
            endDate,
            composeId,
            goodsId,
            goodsKeyword,
            personId,
            personKeyword,
        };
        const url = '/api/v2/sc/stat/execute/bill/export';
        return exportFileByAxios({
            url,
            params: query,
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
    },


};
