import fetch from 'utils/fetch';
import Qs from 'qs';
import { getApp } from '@/core';
import { exportFileByAxios } from 'utils/excel';

async function get(url, params, options = {}) {
    const res = await fetch.get(url, {
        params,
        ...options,
    });
    return res.data;
}

export default {
    patient: {
        /**
         * @desc 建档患者 https://doc.abczs.cn/organization/repository/editor?id=14&mod=186&itf=1398
         * @param query: {beginDate, endDate, clinicId, patientId, projectId, offset, size}
         */
        async list(query) {
            return get('/api/v2/sc/stat/chronic/recovery/patient/records', query);
        },

        /**
         * @desc 建档患者导出 https://doc.abczs.cn/organization/repository/editor?id=14&mod=186&itf=1399
         * <AUTHOR>
         * @date 2021/01/20 15:25:49
         * @params query: {beginDate, endDate, clinicId, patientId, projectId}
         * @return
         */
        async exportList(query) {
            const url = '/api/v2/sc/stat/chronic/recovery/patient/records/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },
    },

    charge: {
        /**
         * 收费统计-汇总 https://doc.abczs.cn/organization/repository/editor?id=14&mod=186&itf=1403
         * @param query: {beginDate, endDate, clinicId, patientId, projectId, offset, size, order, sort}
         * @returns {Promise<any>}
         */
        async stat(query) {
            return get('/api/v2/sc/stat/chronic/recovery/transaction/summary', query);
        },

        /**
         * 收费统计-明细 https://doc.abczs.cn/organization/repository/editor?id=14&mod=186&itf=1403
         * @param query: {beginDate, endDate, clinicId, patientId, projectId, offset, size}
         * @returns {Promise<any>}
         */
        async detail(query) {
            const { enablePatientMobileInStatistics } = getApp().store.getters;
            return get('/api/v2/sc/stat/chronic/recovery/transaction/detail', {
                ...query,
                enablePatientMobile: enablePatientMobileInStatistics,
            });
        },

        /**
         * 收费统计-汇总导出 https://doc.abczs.cn/organization/repository/editor?id=14&mod=186&itf=1403
         * @param query: {beginDate, endDate, clinicId, patientId, projectId}
         * @returns {Promise<any>}
         */
        exports(query) {
            const url = '/api/v2/sc/stat/chronic/recovery/transaction/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },
    },

    effect: {
        /**
         * 疗效统计 https://doc.abczs.cn/organization/repository/editor?id=14&mod=186&itf=1400
         * @param query: {beginDate, endDate, clinicId, patientId, projectId, offset, size}
         * @returns {Promise<any>}
         */
        list(query) {
            return get('/api/v2/sc/stat/chronic/recovery/treat/effect', query);
        },

        /**
         * 疗效统计导出 https://doc.abczs.cn/repository/editor?id=14&mod=56&itf=350
         * @param query: {beginDate, endDate, clinicId, patientId, projectId}
         */
        exportList(query) {
            const url = '/api/v2/sc/stat/chronic/recovery/treat/effect/export';
            return exportFileByAxios({
                url,
                params: query,
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        },
    },

    /**
     * @desc 获取慢病康复项目列表，作为筛选项
     * <AUTHOR>
     * @date 2021/01/26 10:51:04
     * @params
     * @return
     */
    fetchChronicCareProjectOptions() {
        return get('/api/v2/sc/stat/chronic/recovery/templates');
    },
};
