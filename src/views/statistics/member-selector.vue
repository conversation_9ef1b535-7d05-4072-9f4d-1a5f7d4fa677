<template>
    <abc-autocomplete
        v-model.trim="nameCache"
        class="abc-autocomplete-search patient-selector"
        custom-class="patient-suggestion"
        inner-width="auto"
        placeholder="搜索会员"
        :width="240"
        :delay-time="0"
        :async-fetch="true"
        :fetch-suggestions="querySearchAsync"
        clearable
        @clear="clearPatient"
        @enterEvent="selectPatient"
    >
        <abc-search-icon slot="prepend"></abc-search-icon>

        <template slot="suggestion-header">
            <div class="suggestion-title">
                <div class="patient-info">
                    患者信息
                </div>
                <div class="mr-info">
                    上次就诊
                </div>
            </div>
        </template>

        <template slot="suggestions" slot-scope="props">
            <dt
                class="patient-suggestions suggestions-item"
                :class="{ selected: props.index == props.currentIndex }"
                :disabled="props.suggestion.disabled"
                @click="selectPatient(props.suggestion)"
                v-html="patientInfo(props.suggestion)"
            ></dt>
        </template>
    </abc-autocomplete>
</template>

<script type="text/ecmascript-6">
    import {
        formatAge, parseTime,
    } from 'utils/index';
    import PatientsAPI from 'api/patients';

    export default {
        name: 'MemberSelector',

        props: {
            value: String,
            dateParams: {
                type: Object,
                default: () => ({
                    beginDate: '',
                    endDate: '',
                }),
            },
            clinicId: {
                type: String,
                default: '',
            },
            typeId: {
                type: String,
                default: '',
            },
            name: {
                type: String,
                default: '',
            },
        },

        data() {
            return {
                nameCache: this.name,
            };
        },

        computed: {
            patientId: {
                get () {
                    return this.value;
                },
                set (val) {
                    this.$emit('input', val);
                },
            },
        },
        watch: {
            nameCache(val) {
                if (!val) {
                    this.clearPatient();
                }
                this.$emit('update:name', val);
            },

            name() {
                this.nameCache = this.name;
            },
        },

        mounted() {
            if (this.$route.path.indexOf('/outpatient/add') > -1) {
                $('.patient-form').find('input').eq(0).focus();
            }
        },
        methods: {
            /**
             * @desc 患者下拉列表中展示信息
             * <AUTHOR>
             * @date 2018/10/18 12:29:32
             */
            patientInfo(patient) {
                return `<div class="name ellipsis">
                           ${patient.isMember ? `<span class="vip">${patient.name}</span>` : patient.name}
                        </div>
                        <div class="sex">${patient.sex}</div>
                        <div class="age">${formatAge(patient.age)}</div>
                        <div class="mobile">${patient.mobile || ''}</div>
                        <div class="mr ellipsis">${this.formatMrInfo(patient)}</div>`;
            },

            /**
             * @desc 格式化患者就诊信息
             * <AUTHOR>
             * @date 2018/10/18 12:27:42
             */
            formatMrInfo({
                clinicName, ActiveDate, memberTypeId,
            }) {
                const timeStr = ActiveDate ? parseTime(ActiveDate, 'y-m-d', true) : '';

                let clinicNameStr = '';
                if (memberTypeId === 0) {
                    clinicNameStr = '';
                } else if (memberTypeId === 1) {
                    clinicNameStr = '总部';
                } else {
                    clinicNameStr = clinicName;
                }
                return `${timeStr} ${clinicNameStr || ''}`;
            },

            /**
             * @desc 输入时根据输入内容 异步查询患者数据
             * <AUTHOR>
             * @date 2018/10/18 12:36:51
             * @param queryString
             * @param callback
             */
            async querySearchAsync(queryString, callback) {
                queryString = queryString.trim();
                if (!queryString) {
                    callback([]);
                    return false;
                }
                const { data } = await PatientsAPI.fetchPatientsByName(queryString);
                if (!data.list) {
                    return false;
                }
                callback(data.list);
            },

            /**
             * @desc 选择患者
             * <AUTHOR>
             * @date 2018/10/18 12:36:10
             * @params patient
             */
            selectPatient(patient) {
                this.patientId = patient.id;
                this.nameCache = patient.name;
                this.$emit('change', this.id);
            },

            /**
             * @desc 清除 patient
             * <AUTHOR>
             * @date 2018/10/24 18:44:04
             */
            clearPatient() {
                this.patientId = '';
                this.nameCache = '';
                this.$emit('change', this.patientId);
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .content-wrapper.form-item-no-margin.patient-form {
        position: relative;
        display: flex;
        align-items: flex-end;
        margin-top: 32px;

        .is-old-patient {
            .abc-form-item-label .label-name {
                color: #05b387;
            }
        }

        .append-input {
            width: 32px;
        }

        .patient-age-year {
            margin-right: 0;

            .append-input {
                border-right: 0;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
        }

        .patient-age-month {
            margin-top: 22px;
            margin-right: 16px;

            .abc-input__inner {
                border-radius: 0;
            }

            .append-input {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
    }

    .patient-medical-record-wrapper {
        position: absolute;
        right: 0;
    }

    .patient-selector {
        .clear-btn {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 3 !important;
            display: block;
            width: 24px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            cursor: pointer;

            i {
                font-size: 12px;
                color: $P1;
            }

            &:hover i {
                color: $T3;
            }
        }
    }

    .patient-suggestion {
        top: 36px;
        background: white;

        .suggestion-title {
            display: flex;
            align-items: center;
            padding: 0 16px;

            .patient-info {
                width: 270px;
            }
        }

        .patient-suggestions.suggestions-item {
            z-index: 1009;
            display: flex;
            align-items: center;
            min-height: 36px;
            padding: 0 16px;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            user-select: none;
            outline: 0;

            &[disabled] {
                color: #687481;
                cursor: not-allowed;
            }

            .name {
                flex: none;
                width: 80px;

                .vip::after {
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    margin-left: 4px;
                    content: '';
                    background: url('~assets/images/vip.png') no-repeat;
                    background-size: 12px 12px;
                }
            }

            .mobile {
                width: 110px;
                padding-left: 10px;
            }

            .age,
            .sex {
                width: 40px;
                text-align: right;
            }

            .mr {
                flex: 1;
                color: $T2;
            }
        }
    }
</style>
