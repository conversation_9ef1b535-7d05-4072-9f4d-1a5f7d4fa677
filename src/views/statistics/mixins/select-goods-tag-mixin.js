import {
    mapActions, mapState,
} from 'vuex';

export default {
    data() {
        return {
            selectedTag: [],
        };
    },
    computed: {
        ...mapState('goods', ['originLabels']),
        goodsTagOptions() {
            return this.originLabels;
        },
    },
    created() {
        this.updateTagsByFetch();
    },
    methods: {
        ...mapActions('goods', ['updateTagsByFetch']),
        handleTagChange(val) {
            this.selectedTags = val;
            const selectedId = val.flat().reduce((pre, cur) => {
                if (!pre.includes(cur.id)) pre.push(cur.id);
                return pre;
            }, []);
            const tagIds = [];
            this.goodsTagOptions.forEach((item) => {
                if (item.children) {
                    item.children.forEach((tag) => {
                        if (selectedId.includes(tag.id)) {
                            tagIds.push(tag.id);
                        }
                    });
                }
            });
            this.params.tagIds = tagIds;
            this.getTableData();
        },
    },
};
