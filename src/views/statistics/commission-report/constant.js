import { getViewDistributeConfig } from '@/views-distribute/utils.js';
import i18n from '@/i18n';
const {
    commissionStat,
} = getViewDistributeConfig().Statistics;
// 提成类型
export const CommissionTypeEnum = Object.freeze({
    // 销售提成 / 医嘱开具提成
    SALE: 1,
    // 挂号提成
    REGISTRATION: 2,
    // 充值提成
    RECHARGE: 3,
    // 执行提成
    EXECUTE: 4,
    // 检查检验提成
    EXAM: 5,
    // 收费员提成
    COLLECTOR: 6,
    // 发药提成
    DISPENSE: 7,
    // 开卡提成
    CARD: 8,
    //费用项开单提成
    CHARGE: 9,
});

// 提成类型说明
export const CommissionTypeHintEnum = Object.freeze({
    // 销售提成
    1: commissionStat.isEnableHospitalCommissionRule ? '根据开具的医嘱项进行提成' : commissionStat.isEnableProfitRulesCommissionRule ? '根据销售的药品/物资/商品进行提成' : '根据开具的处方、销售的药品/物资/治疗理疗等项目进行提成',
    // 挂号提成
    2: `根据${i18n.t('registrationFeeName')}的收费情况进行提成`,
    // 充值提成
    3: '根据会员、卡项充值的金额进行提成',
    // 执行提成
    4: '根据执行的治疗理疗项目进行提成',
    // 检查检验提成
    5: '根据完成的检查检验项目进行提成',
    // 收费员提成
    6: '根据完成的收费单数量进行提成',
    // 发药提成
    7: '根据完成的检查检验项目进行提成',
    // 开卡提成
    8: '根据卡项的开卡金额进行提成',
    // 费用项提成
    9: '根据开具的费用项目进行提成',
});

export const SummaryStaticConfig = {
    'hasInnerBorder': true,
    customPropConfig: {
        colType: 'money',
        style: {
            'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },
    list: [
        {
            'label': '门店名称',
            'key': 'clinicName',
            'pinned': false,
            'position': 1,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },
        {
            'label': '人员名称',
            'key': 'employeeName',
            'pinned': false,
            'position': 2,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },
        {
            'label': '提成汇总',
            'key': 'totalAmt',
            'pinned': false,
            'position': 3,
            'sortable': false,
            'colType': 'money',
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        },
    ],
};
