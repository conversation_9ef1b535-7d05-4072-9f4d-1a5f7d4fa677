<template>
    <abc-layout class="common-padding-container" preset="page-table">
        <abc-layout-header>
            <abc-flex :gap="16" vertical="vertical">
                <stat-toolbar
                    :enable-features="toolbarFeatures"
                    :date-filter.sync="params.dateFilter$"
                    :clinic-id-filter.sync="params.clinicIdFilter"
                    :clinic-list="clinicList"
                    :service-id-filter.sync="params.servicePackFilter"
                    :sign-type-filter.sync="params.signTypeFilter"
                    :service-status-filter.sync="params.serviceStatusFilter"
                    :handle-export="exports"
                    :is-family-doctor="true"
                    date-label="签约时间"
                    @change-date="handleDateChange"
                    @change-service-pack="handleServicePackChange"
                    @change-sign-type="handleSignTypeChange"
                    @change-service-status="handleServiceStatusChange"
                    @change-clinic="handleClinicChange"
                >
                </stat-toolbar>
                <biz-data-statistics-card :list="tableBannerData">
                </biz-data-statistics-card>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :loading="loading"
                :render-config="tableHeader"
                :data-list="list"
                :empty-opt="{ label: '暂无数据' }"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="false"
                :pagination-params="params"
                :count="count"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li>
                        共 <span>{{ count }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import FamilyDoctorAPI from 'views/statistics/core/api/family-doctor.js';
    import { mapGetters } from 'vuex';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import BizDataStatisticsCard from '@/components-composite/biz-data-statistics-card/src/views/index.vue';

    export default {
        name: 'SignContact',

        components: {
            BizDataStatisticsCard,
            StatToolbar,
        },

        mixins: [ClinicTypeJudger, DateParamsMixins],

        data() {
            return {
                list: [],
                count: 0,
                loading: false,
                summaryData: {},
                params: {
                    clinicIdFilter: '',
                    servicePackFilter: '',
                    signTypeFilter: '',
                    serviceStatusFilter: '',
                    pageIndex: 0,
                    pageSize: 0,
                },
            };
        },

        computed: {
            ...mapGetters(['currentClinic', 'subClinics','enablePatientMobileInStatistics']),

            clinicList() {
                return this.subClinics
                    .map((clinic) => {
                        return {
                            ...clinic,
                            shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                        };
                    })
                    .filter((clinic) => clinic.name !== '总部');
            },

            tableBannerData() {
                const {
                    signPatientTimes, firstPatientTimes, renewPatientTimes, inServicePatient,
                } = this.summaryData;
                return [
                    {
                        text: '签约人次',
                        value: signPatientTimes || 0,
                    },
                    {
                        text: '首签人次',
                        value: firstPatientTimes || 0,
                    },
                    {
                        text: '续签人次',
                        value: renewPatientTimes || 0,
                    },
                    {
                        text: '当前服务人数',
                        value: inServicePatient || 0,
                    },
                ];
            },

            tableHeader() {
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list: [
                        {
                            label: '患者姓名',
                            key: 'patientName',
                            style: {
                                width: '100px',
                            },
                            customRender: (h, row) => {
                                return <abc-table-cell>{ row.patientName || '-' }</abc-table-cell> ;
                            },
                        },
                        {
                            label: '性别',
                            key: 'patientGender',
                            style: {
                                width: '50px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '年龄',
                            key: 'patientAge',
                            style: {
                                width: '50px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '手机号',
                            key: 'patientMobile',
                            style: {
                                width: '80px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '签约日期',
                            key: 'signTime',
                            style: {
                                width: '100px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '签约服务包',
                            key: 'serviceName',
                            style: {
                                width: '100px',
                            },
                        },
                        {
                            label: '签约类型',
                            key: 'isRenew',
                            style: {
                                width: '50px',
                                textAlign: 'center',
                            },
                            customRender: (h, row) => {
                                return <abc-table-cell>{ row.isRenew === 0 ? '首签' : '续签' }</abc-table-cell> ;
                            },
                        },
                        {
                            label: '签约门店',
                            key: 'signClinicName',
                            style: {
                                width: '150px',
                            },
                        },
                        {
                            label: '服务状态',
                            key: 'isInserviceName',
                            style: {
                                width: '80px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '签约医生',
                            key: 'doctorName',
                            style: {
                                width: '80px',
                            },
                        },
                    ],
                };
            },

            /**
             * @desc 工具栏筛选功能
             * <AUTHOR>
             * @date 2020/03/11 14:59:26
             * @params
             * @return
             */
            toolbarFeatures() {
                return [StatToolbar.Feature.DATE, StatToolbar.Feature.EXPORT, StatToolbar.Feature.CLINIC, StatToolbar.Feature.SERVICE_PACK, StatToolbar.Feature.SIGN_TYPE, StatToolbar.Feature.SERVICE_STATUS ];
            },
        },

        mounted() {
            this.getTableData();
            this.getSummaryData();
        },

        methods: {
            async handleMounted(data) {
                this.params.pageSize = (data.paginationLimit - 1) || 10;
                await this.getTableData();
            },
            handleClinicChange() {
                this.getTableData();
                this.getSummaryData();
            },

            async getTableData(resetPageParams = true) {
                await this.$nextTick();

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }

                const {
                    clinicIdFilter, dateFilter$: {
                        begin, end,
                    }, servicePackFilter, signTypeFilter, serviceStatusFilter, pageIndex, pageSize,
                } = this.params;

                const {
                    chainId,enablePatientMobileInStatistics,
                } = this;
                const offset = pageIndex * pageSize;

                try {
                    this.loading = true;

                    const res = await FamilyDoctorAPI.sign.signList(chainId, clinicIdFilter, begin, end, servicePackFilter, signTypeFilter, serviceStatusFilter, offset, pageSize,enablePatientMobileInStatistics);

                    if (begin === this.params.dateFilter$.begin && end === this.params.dateFilter$.end) {

                        const { data } = res;

                        if (data) {
                            this.list = data;
                        }
                    }
                } catch (err) {
                    console.error('getTableData', err);
                } finally {
                    this.loading = false;
                }
            },

            async getSummaryData() {
                const {
                    clinicIdFilter, dateFilter$: {
                        begin, end,
                    }, servicePackFilter, signTypeFilter, serviceStatusFilter, pageIndex, pageSize,
                } = this.params;

                const { chainId } = this;

                try {
                    const res = await FamilyDoctorAPI.sign.signOverview(chainId, clinicIdFilter, begin, end, servicePackFilter, signTypeFilter, serviceStatusFilter, pageIndex, pageSize);

                    if (begin === this.params.dateFilter$.begin && end === this.params.dateFilter$.end) {

                        const { data } = res;

                        if (data) {
                            this.summaryData = data;
                            this.count = data.totalNum;
                        }
                    }
                } catch (err) {
                    console.error('getSummaryData', err);
                }
            },

            handleDateChange() {
                this.getTableData();
                this.getSummaryData();
            },

            handleServicePackChange() {
                this.getTableData();
                this.getSummaryData();
            },

            handleSignTypeChange() {
                this.getTableData();
                this.getSummaryData();
            },

            handleServiceStatusChange() {
                this.getTableData();
                this.getSummaryData();
            },

            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            exports() {
                const {
                    clinicIdFilter, dateFilter$: {
                        begin, end,
                    }, servicePackFilter, signTypeFilter, serviceStatusFilter,
                } = this.params;
                const { chainId } = this;
                FamilyDoctorAPI.sign.signExport(chainId, clinicIdFilter, begin, end, servicePackFilter, signTypeFilter, serviceStatusFilter);
            },
        },
    };
</script>
