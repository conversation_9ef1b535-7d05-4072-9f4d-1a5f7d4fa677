<template>
    <div v-abc-loading="loading" class="goods-list-box" :style="boxStyle">
        <div
            v-if="rows.length > 0"
            class="goods-list-item-wrap"
            :class="[size, itemAlone ? 'alone' : '']"
        >
            <template v-for="item in rows">
                <slot>
                    <goods-select-dialog-list-item
                        :key="item.id"
                        :size="size"
                        :item="item"
                        :selected-goods-ids="selectedGoodsIds"
                        :item-alone="itemAlone"
                        :fee-type-name="feeTypeName"
                        :patient="patient"
                        :item-mode="itemMode"
                        :feature-support-filter-eye-glasses="featureSupportFilterEyeGlasses"
                        :disable-no-stock-goods="disableNoStockGoods"
                        @select-item="handleClickItem(item)"
                    ></goods-select-dialog-list-item>
                </slot>
            </template>
        </div>
        <div v-else style="position: relative; width: 100%; height: 300px;">
            <abc-content-empty top="100px"></abc-content-empty>
        </div>
    </div>
</template>

<script>
    import { GoodsSubTypeEnumStr } from '@abc/constants';
    import GoodsSelectDialogListItem
        from '@/components-composite/biz-goods-select-dialog/src/views/components/goods-select-dialog-list-item.vue';

    export default {
        components: {
            GoodsSelectDialogListItem,
        },
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            rows: {
                type: Array,
                default: () => [],
            },
            selectedGoodsIds: {
                type: Array,
                default: () => [],
            },
            size: {
                type: String,
                default: 'small',
            },
            itemAlone: Boolean, // item 独占一行
            itemMode: String, // item 内容的显示模式，中西药有自定义显示内容
            feeTypeName: String, // 费别
            patient: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            featureSupportFilterEyeGlasses: Boolean,
            disableNoStockGoods: Boolean,
        },
        data() {
            return {
                GoodsSubTypeEnumStr,
            };
        },
        computed: {
            boxStyle() {
                return {
                    padding: this.size === 'small' ? '8px 2px 8px 12px' : '16px 6px 16px 16px',
                    height: this.size === 'small' ?
                        'calc(100% - 40px)' : this.featureSupportFilterEyeGlasses ?
                            'calc(100% - 39px)' : '100%',
                };
            },
        },
        methods: {
            handleClickItem(item) {
                this.$emit('select-item', item);
            },
        },
    };
</script>
