<template>
    <div class="setting-layout-sidebar" :class="{ 'open-zoom': openZoom }">
        <!-- @slot 操作栏内容 -->
        <slot></slot>
        <div v-if="showPreviewIcon" class="setting-layout-sidebar-preview-icon">
            <img src="@/assets/images/icon_example.png" alt="" class="sidebar-preview-icon" />
        </div>
    </div>
</template>

<script>
    export default {
        name: 'BizSettingSidebar',
        props: {
            /**
             * 是否展示预览图标
             */
            showPreviewIcon: {
                type: Boolean,
                default: false,
            },

            /**
             * 当屏幕大小变化时侧边栏内容是否开启缩放
             */
            openZoom: {
                type: Boolean,
                default: true,
            },
        },
    };
</script>
