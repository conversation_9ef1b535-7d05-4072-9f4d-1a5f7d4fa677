import BizWeekSchedule from './src/views/index.vue';
import BizWeekScheduleCell from './src/views/cell.vue';

export default {
    title: 'Components/业务模块/周排班（排班系统）-BizWeekSchedule',
    component: BizWeekSchedule,
};

export const Base = {
    render: () => ({
        data() {
            return {
                size: 'large',
                list: [
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生0',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生1',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['11', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生2',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['1'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                        ],
                    },
                    {
                        label: '不指定医生3',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                ],
            };
        },

        components: {
            BizWeekSchedule,
            BizWeekScheduleCell,
        },

        template: `
              <BizWeekSchedule  :data="list" :content-max-height="300">
                  <template #headerPrepend>
                      <BizWeekScheduleCell excel>
                          <abc-select
                              v-model="size"
                              adaptive-width
                              size="large"
                          >
                              <abc-option value="default" label="default"></abc-option>
                              <abc-option value="large" label="large"></abc-option>
                          </abc-select>
                      </BizWeekScheduleCell>
                  </template>

                  <template #bodyPrepend="{ data }">
                      <BizWeekScheduleCell>
                          <div style="height: 100%; display: flex; align-items: center;">{{ data.label }}</div>
                      </BizWeekScheduleCell>
                  </template>

                  <template #default="{ data, config }">
                      <BizWeekScheduleCell  :background="data.scheduleShifts.length > 0" :date="config.date">
                          <div v-for="(o, key) in data.scheduleShifts" :key="key">{{ o }}</div>
                      </BizWeekScheduleCell>
                  </template>
              </BizWeekSchedule>
            `,
    }),

    name: 'Base',
};

export const Fill = {
    render: () => ({
        data() {
            return {
                size: 'large',
                list: [
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生0',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生1',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['11', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生2',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['1'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                        ],
                    },
                    {
                        label: '不指定医生3',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                ],
                prependWidth: 200,
            };
        },

        components: {
            BizWeekSchedule,
            BizWeekScheduleCell,
        },

        methods: {
            handleClick() {
                this.prependWidth = 300;
            },
        },

        template: `
              <div>
                  <AbcButton @click="handleClick">改变第一列宽度</AbcButton>
                  <br />
                  <br />
                  <BizWeekSchedule :prependWidth="prependWidth" :data="list" variant="fill" border radius="4px">
                      <template #bodyPrepend="{ data }">
                          <BizWeekScheduleCell>
                              <div style="height: 100%; display: flex; align-items: center;">{{ data.label }}</div>
                          </BizWeekScheduleCell>
                      </template>

                      <template #default="{ data, config }">
                          <BizWeekScheduleCell  :background="data.scheduleShifts.length > 0" :date="config.date">
                              <div v-for="(o, key) in data.scheduleShifts" :key="key">{{ o }}</div>
                          </BizWeekScheduleCell>
                      </template>
                  </BizWeekSchedule>
              </div>
            `,
    }),

    name: 'Fill',
};

export const Paginate = {
    render: () => ({
        data() {
            return {
                list: [
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生0',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生1',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['11', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生2',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['1'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                        ],
                    },
                    {
                        label: '不指定医生3',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                    {
                        label: '不指定医生',
                        schedules: [
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: [],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                            {
                                scheduleShifts: ['10', '12'],
                            },
                        ],
                    },
                ],
            };
        },

        components: {
            BizWeekSchedule,
            BizWeekScheduleCell,
        },

        methods: {
            handleNext() {
                alert('next');
            },
        },

        template: `
              <div style="height: 500px;">
                  <div style="border-bottom: 1px solid #ccc;">
                      <BizWeekSchedule :data="list" variant="fill" @next="handleNext" :prepend-width="300" :pagination="{
                      visible: true,
                      prevDisable: true,
                      nextDisable: false,
                  }" :enable-persistent-scrollbar="true">
                          <template #headerPrepend>
                              <abc-flex align="center" justify="center" style="height: 100%;">
                                  <abc-text theme="gray">2023-03-03</abc-text>
                              </abc-flex>
                          </template>

                          <template #bodyPrepend="{ data }">
                              <BizWeekScheduleCell>
                                  <div style="height: 100%; display: flex; align-items: center;">{{ data.label }}</div>
                              </BizWeekScheduleCell>
                          </template>

                          <template #default="{ data, config }">
                              <BizWeekScheduleCell  :background="data.scheduleShifts.length > 0" :date="config.date">
                                  <div v-for="(o, key) in data.scheduleShifts" :key="key">{{ o }}</div>
                              </BizWeekScheduleCell>
                          </template>
                      </BizWeekSchedule>
                  </div>
              </div>
            `,
    }),

    name: 'Fill',
};

