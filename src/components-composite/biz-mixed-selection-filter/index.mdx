import {Meta, Canvas, Controls, Story,} from '@storybook/blocks';
import * as Stories from './index.stories';

import img1 from "./src/assets/images/img.png";

<Meta of={Stories} />

# BizMixedSelectionFilter
混合筛选器，支持3种模式，radio模式是单选，checkbox-button模式、checkbox模式支持多选。

和普通radio、checkbox\checkbox-button的不同: radio、checkbox后面带数字，支持了checkbox-button-group模式。
## 基础用法

<Canvas>
    <Story of={Stories.base} />
</Canvas>

## 指南
### 何时使用
要求这种样式时，且可能互相切换时，使用这个组件可以保持用法不变

### 使用场景
<img src={img1} alt="库存商品列表预警筛选" />

## 属性详情
<Controls />
