<template>
    <abc-input-mobile
        v-bind="$attrs"
        v-model="currentValue"
        :country-code.sync="mobileCountryCode"
        :width="width"
        :size="size"
        :auto-width="autoWidth"
        :readonly="isReadonly"
        :clearable="enableEncrypt && !isEdit"
        :placeholder="placeholder"
        :show-prepend-icon="showPrependIcon"
        :icon-color="iconColor"
        :icon-left-padding="iconLeftPadding"
        :is-disabled-country-code="isDisabledCountryCode"
        :is-show-country-code="isShowCountryCode"
        :select-validate-event="selectValidateEvent"
        :input-validate-event="inputValidateEvent"
        :auto-focus="autoFocus"
        :disabled="disabled"
        @clear="handleClearMobile"
        @input-enter="handleEnter"
        @input-change="handleChange"
        @select-change="selectChange"
        @select-enter="selectEnter"
    ></abc-input-mobile>
</template>

<script>
    export default {
        name: 'InputMobileEncrypt',
        props: {
            value: {
                type: [String,Number],
                default: '',
            },
            countryCode: {
                type: String,
                default: '',
            },
            size: String,
            enableEncrypt: {
                type: Boolean,
                default: false,
            },
            width: Number,
            autoWidth: {
                type: Boolean,
                default: false,
            },
            placeholder: {
                type: String,
                default: '',
            },
            /**
             * 是否显示图标
             */
            showPrependIcon: {
                type: Boolean,
                default: false,
            },
            /**
             * 图标颜色
             */
            iconColor: String,
            /**
             * 图标左侧padding
             */
            iconLeftPadding: Number,
            /**
             * 是否禁用选区
             */
            isDisabledCountryCode: {
                type: Boolean,
                default: false,
            },
            /**
             * 是否禁用
             */
            disabled: Boolean,
            /**
             * 是否显示选区
             */
            isShowCountryCode: {
                type: Boolean,
                default: true,
            },
            /**
             * 下拉框是否通知 form-item 进行校验，默认为 false
             */
            selectValidateEvent: {
                type: Boolean,
                default: false,
            },
            /**
             * 输入框是否通知 form-item 进行校验，默认为 false
             */
            inputValidateEvent: {
                type: Boolean,
                default: true,
            },
            /**
             * 自动聚焦到input
             */
            autoFocus: {
                type: Boolean,
                default: false,
            },
            readonly: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                isEdit: false,
            };
        },
        computed: {
            currentValue: {
                get() {
                    const mobile = this.value;
                    if (this.isEdit || !this.enableEncrypt) {
                        return mobile;
                    }
                    return this.encryptMobile(mobile);
                },
                set(val) {
                    this.isEdit = true;
                    this.$emit('input', val);
                },
            },
            isReadonly() {
                return this.readonly || !!this.currentValue && this.enableEncrypt && !this.isEdit;
            },
            mobileCountryCode: {
                get() {
                    return this.countryCode;
                },
                set(val) {
                    this.$emit('update:countryCode',val);
                },
            },
        },
        methods: {
            encryptMobile(value) {
                if (!value) return '';
                let reg = '';
                let encryptMobile = '';

                if (value.length === 11) {
                    reg = /(\d{3})\d{4}(\d{4})/;
                    encryptMobile = `${value}`.replace(reg, '$1****$2');
                } else {
                    // 从第二位开始隐藏三位
                    encryptMobile = `${value[0]}***${value.substring(4)}`;
                }

                return encryptMobile;
            },
            handleClearMobile() {
                this.isEdit = true;
            },
            handleEnter(event) {
                this.$emit('enter', event);
            },
            handleChange(event) {
                this.$emit('change', event);
            },
            selectChange() {
                this.$emit('select-change');
            },
            selectEnter(event) {
                this.$emit('select-enter', event);
            },
        },
    };
</script>
