<template>
    <abc-flex :vertical="labelPosition === 'top'" :align="labelAlign" class="quick-options-panel-group-wrapper">
        <abc-text
            v-if="label"
            theme="black"
            bold
            size="normal"
            class="quick-options-panel-group-title"
            :class="labelPosition === 'top' ? 'label-top' : 'label-left'"
            :style="labelStyleObj"
        >
            {{ label }}
        </abc-text>

        <abc-flex vertical class="quick-options-panel-group-content" :style="{ width: vertical ? '100%' : 'auto' }">
            <template v-if="$slots.default">
                <slot></slot>
            </template>
            <biz-quick-options-panel-row
                v-else-if="list && list.length"
                :list="list"
                :vertical="vertical"
                :item-label="itemLabel"
                :item-size="itemSize"
                @select="(val) => $emit('select', val)"
            ></biz-quick-options-panel-row>
        </abc-flex>
    </abc-flex>
</template>

<script>
    import BizQuickOptionsPanelRow from './row.vue';

    export default {
        name: 'BizQuickOptionsPanelGroup',
        components: { BizQuickOptionsPanelRow },
        props: {
            /**
             * 分组标题，可选
             */
            label: {
                type: String,
                default: '',
            },
            labelColor: {
                type: String,
                default: '',
            },
            /**
             * 分组 label 的位置，可选值：top、left
             */
            labelPosition: {
                type: String,
                default: 'top',
                validator: (val) => ['top', 'left'].includes(val),
            },
            /**
             * 分组 label 的宽度
             */
            labelWidth: {
                type: [Number, String],
                default: '',
            },
            /**
             * label 和内容的对齐方式
             */
            labelAlign: {
                type: String,
                default: 'start',
                validator: (val) => ['start', 'center', 'end'].includes(val),
            },
            /**
             * label 的样式
             */
            labelStyle: {
                type: Object,
                default() {
                    return {};
                },
            },
            /**
             * 选择项列表
             */
            list: {
                type: Array,
                default() {
                    return [];
                },
            },
            /**
             * item 是否纵向排列
             */
            vertical: {
                type: [Boolean, Number],
                default: false,
            },
            /**
             * 如果 list 是一个对象数组，则表示对象指定的 key
             */
            itemLabel: {
                type: String,
                default: 'label',
            },
            /**
             * 是否展示分割线，控制该组
             */
            showSplitLine: Boolean,
            /**
             * item 的尺寸，支持 small - 26 medium - 32
             */
            itemSize: {
                type: String,
                default: 'small',
                validator: (value) => ['small', 'medium'].includes(value),
            },
        },
        computed: {
            labelStyleObj() {
                const obj = {};
                if (this.labelWidth) {
                    obj.width = typeof this.labelWidth === 'string' ? obj.width = this.labelWidth : obj.width = `${this.labelWidth}px`;
                }
                if (this.labelColor) {
                    obj.color = this.labelColor;
                }
                return {
                    ...this.labelStyle,
                    ...obj,
                };
            },
        },
    };
</script>

<style lang="scss">
.quick-options-panel-group-wrapper {
    .quick-options-panel-group-title {
        height: 26px;
        margin: 0 var(--abc-paddingLR-m);
        line-height: 26px;

        &.label-top {
            margin-bottom: 2px;
        }

        &.label-left {
            margin-right: 10px;
        }
    }

    .quick-options-panel-group-content {
        flex: 1;
    }
}
</style>
