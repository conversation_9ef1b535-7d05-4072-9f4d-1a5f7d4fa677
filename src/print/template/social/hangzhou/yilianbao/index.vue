<template>
    <div class="print-stat-wrapper">
        <div class="print-stat-content">
            <table class="print-yilianbao-wrapper">
                <caption class="table-title">
                    <p class="title">
                        杭州西湖益联保医疗机构医疗费用拨付表
                    </p>
                    <p class="organ-info">
                        所属期：{{ printData.monthLabel }}
                    </p>
                </caption>
                <thead>
                    <tr class="border-tr">
                        <th class="border-right-th" colspan="1">
                            编号
                        </th>
                        <th class="border-right-th" colspan="3">
                            医疗机构名称
                        </th>
                        <th class="border-right-th" colspan="1">
                            类别
                        </th>
                        <th class="border-right-th" colspan="2">
                            申请拨付款
                        </th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <td colspan="2">
                            制表：
                        </td>
                        <td colspan="2">
                            审批：
                        </td>
                        <td colspan="2">
                            打印时间：{{ _nowTime }}
                        </td>
                    </tr>
                </tfoot>
                <tbody class="table-body">
                    <tr v-for="(item, index) in approveItems" :key="index" class="border-tr">
                        <td
                            v-if="index === 0"
                            class="border-right-td"
                            colspan="1"
                            rowspan="3"
                        >
                            {{ cityHospitalCode }}
                        </td>
                        <td
                            v-if="index === 0"
                            class="border-right-td"
                            colspan="3"
                            rowspan="3"
                        >
                            {{ hospitalName }}
                        </td>
                        <td class="border-right-td" colspan="1">
                            {{ item.insuranceCategory }}
                        </td>
                        <td class="border-right-td" colspan="2">
                            {{ item.totalApproveFee | formatMoney }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'CadreCheckList',
        props: {
            printData: {
                type: Object,
                required: true,
            },
        },
        computed: {
            hospitalName() {
                return this.printData.hospitalName;
            },
            approveItems() {
                return this.printData.approveItems;
            },
            cityHospitalCode() {
                return this.printData.cityHospitalCode;
            },
        },
        created() {
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth() + 1;
            const day = now.getDate();
            this._nowTime = `${year}年${month}月${day}日`;
        },
    };
</script>

<style lang="scss">
    @import './index';
</style>
