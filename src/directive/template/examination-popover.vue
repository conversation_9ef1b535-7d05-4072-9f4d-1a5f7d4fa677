<template>
    <div
        id="medicine-hover-popover"
        @mouseenter="isInContent = true"
        @mouseleave="handleLeave"
    >
        <div class="medicine-info-wrapper">
            <div class="first-row">
                <div class="cadn">
                    {{ goodsName }}
                </div>

                <div v-if="medicalFeeGrade2Str" class="medical-fee-grade">
                    [{{ medicalFeeGrade2Str }}]
                </div>

                <div class="price" :class="{ 'no-data': unitPriceStr(productInfo) === '无价格' }">
                    <span style="margin-right: 2px; font-size: 12px;">{{ i18n.t('currencySymbol') }}</span>{{ unitPriceStr(productInfo) }}
                </div>
            </div>
            <div class="examination-items list-scroll">
                <div v-for="(item,index) in examinationChildrenList" :key="index" style="margin-top: 10px;">
                    <div class="first-row">
                        <div class="cadn exam-item-font">
                            {{ item.name }}
                        </div>
                        <div class="exam-item-font">
                            {{ filterExItemType(item.bizExtensions && item.bizExtensions.itemCategory) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        formatMoney, medicalFeeGrade2Str,
    } from '@/filters';
    import TreatmentConfig from '@/assets/configure/treatment-config.js';
    import i18n from '@/i18n';

    export default {
        props: {
            goods: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            destroyPopper: {
                type: Function,
            },

        },
        data() {
            return {
                i18n,
                isInContent: false,
            };
        },
        computed: {
            goodsName() {
                let name = '';
                if (this.productInfo) {
                    name = this.productInfo.name;
                }
                name = name || this.goods.name;
                return name || '';
            },
            productInfo() {
                return this.goods.productInfo || {};
            },

            examinationChildrenList() {
                return this.productInfo.children;
            },
            medicalFeeGrade2Str() {
                return medicalFeeGrade2Str(this.productInfo.medicalFeeGrade || '');
            },
        },
        created() {
        },

        methods: {

            unitPriceStr(productInfo) {
                if (this.config && !this.config.showPrice) return '';

                if (!productInfo) return '无价格';
                const {
                    packagePrice,
                    packageUnit,
                    piecePrice,
                    pieceUnit,
                    subType,
                    type,
                    id,
                } = productInfo;

                if (!id) return '无价格';

                if (type === 1 && subType === 2) {
                    return pieceUnit ? `${formatMoney(piecePrice, false)} / ${pieceUnit}` : '无价格';
                }
                return packageUnit ? `${formatMoney(packagePrice)} / ${packageUnit}` : '无价格';

            },
            filterExItemType (exItem) {
                const { itemCategory } = TreatmentConfig;
                const filterExItem = itemCategory.find((item) => {
                    return item.value === Number(exItem);
                }) || {};
                return filterExItem.name;
            },
            handleLeave() {
                if (this.config.trigger !== 'hover') return;
                this.isInContent = false;
                if (typeof this.destroyPopper === 'function') {
                    this.destroyPopper();
                }
                this.$destroy();
                $(this.$el).remove();
            },


        },
    };
</script>
<style rel="stylesheet/scss" scoped lang="scss">
    @import '../../styles/theme';
    @import '../../styles/mixin';

    .diagnosis-examination-popover-wrapper {
        position: relative;
        width: 100%;
    }

    .first-row {
        min-width: 260px;

        .cadn {
            flex: 1;
        }

        .exam-item-font {
            font-size: 10px;
            color: $T2;
        }
    }

    .examination-items {
        max-height: 217px;
        overflow-y: scroll;
    }

    .list-scroll {
        &::-webkit-scrollbar-track-piece {
            background: none;
        }

        &::-webkit-scrollbar {
            display: block;
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            max-height: 2px;
            background: transparent;
            border: transparent;
            border-radius: 20px;
        }
    }
</style>
