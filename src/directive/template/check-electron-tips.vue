<template>
    <div
        class="check-electron-tips"
        :style="getTipsStyles"
        @mouseenter="isInContent = true"
        @mouseleave="handleLeave"
    >
        <span> {{ config.disableTips }} </span>
        <abc-link v-if="!$abcSocialSecurity.isWindowsElectron" @click="clickInstallClient">
            安装客户端
        </abc-link>
        <div
            :class="[
                'check-electron-tips__arrow',
                { 'check-electron-tips-top__arrow': isTopTips },
                { 'check-electron-tips-bottom__arrow': !isTopTips },
            ]"
            :style="tipsArrowStyles"
        >
        </div>
    </div>
</template>
<script>
    import { openUpgradeGuideDialog } from 'src/views/layout/upgrade-guide-dialog/upgrade-guide-helper.js';
    export default {
        name: 'CheckElectronTips',
        props: {
            config: {
                type: Object,
                required: true,
                default: () => ({}),
            },
            destroyPopper: {
                type: Function,
                default: () => { },
            },
        },
        data() {
            return {
                isInContent: false,
                popoverWidth: 228,
                popoverHeight: 98,
            };
        },
        computed: {
            // 提示位置是否在顶部 默认是
            isTopTips() {
                // 相对鼠标元素位置定位
                const { bottom } = this.config.elLocationAttribute;
                if (bottom < 100) {
                    return false;
                }
                return true;
            },
            // 提示框向左移动距离
            translateLeft() {
                // 相对鼠标元素位置定位
                const {
                    right,
                    width,
                } = this.config.elLocationAttribute;
                const popverToOffsetRigthWidth = window.innerWidth - right - this.popoverWidth / 2 + width / 2; // 提示信息距离右边窗口的距离
                if (popverToOffsetRigthWidth < 0) {
                    return popverToOffsetRigthWidth - 10;
                }
                return 0;
            },
            // 提示箭头样式
            tipsArrowStyles() {
                return { transform: `translateX(${-(this.translateLeft)}px)` };
            },
            getTipsStyles() {
                // 相对鼠标元素位置定位
                const {
                    right,
                    bottom,
                    width,
                    height,
                } = this.config.elLocationAttribute;
                let transform = '';
                const translateX = right - width / 2 - this.popoverWidth / 2 + this.translateLeft;
                if (this.isTopTips) {
                    const translateY = this.$abcSocialSecurity.isWindowsElectron ? bottom - this.popoverHeight - 10 : bottom - height - this.popoverHeight;
                    transform = `translate(${translateX}px, ${translateY}px)`;
                } else {
                    transform = `translate(${translateX}px, ${bottom + 10}px)`;
                }
                return {
                    transform,
                };
            },
        },
        watch: {
            'config.disableTips': {
                immediate: true,
                async handler() {
                    await this.$nextTick();
                    this.popoverWidth = this.$el.offsetWidth;
                    this.popoverHeight = this.$el.offsetHeight + 10;
                },
            },
        },
        methods: {
            handleLeave() {
                if (this.config.trigger !== 'hover') return;
                this.isInContent = false;
                if (typeof this.destroyPopper === 'function') {
                    this.destroyPopper();
                }
                this.$destroy();
                // eslint-disable-next-line no-undef
                $(this.$el).remove();
            },
            clickInstallClient() {
                const params = {};
                const {
                    isPublicHealth = false, // 是否公卫
                    isExaminations = false, // 是否检查检验
                } = this.config;
                // 安装弹窗的提示信息，支持字符串，字符串数组，或者 boolean，传 true 时，使用默认的提示信息
                let {
                    installTips,
                } = this.config;
                const {
                    installTitle,
                } = this.config;
                if (installTips) {
                    if (typeof installTips !== 'boolean') {
                        if (typeof installTips === 'string') {
                            installTips = [installTips];
                        }
                        params.info = {
                            title: installTitle || '推荐使用ABC客户端',
                            list: installTips.map((item) => ({ desc: item })),
                        };
                    }
                } else if (!isPublicHealth && !isExaminations) {
                    params.info = {
                        title: installTitle || '推荐使用ABC客户端',
                        list: [
                            {
                                desc: '支持医保：结算、对账、清算一键搞定',
                            },
                            {
                                desc: '更快更稳：运行速度提升30%，更稳定',
                            },
                            {
                                desc: '更好找：常驻电脑桌面，直接点击使用',
                            },
                        ],
                    };
                }
                openUpgradeGuideDialog(params);
            },
        },
    };
</script>
<style scoped lang="scss">
    .check-electron-tips {
        --popover-popper-border-color: var(--abc-color-LY1);
        --popover-popper-fill-color: var(--abc-color-LY4);

        position: fixed;
        top: 0;
        left: 0;
        z-index: 1992;
        width: 228px;
        padding: var(--abc-paddingTB-ml) var(--abc-paddingLR-ml);
        line-height: 22px;
        text-align: justify;
        background: var(--popover-popper-fill-color);
        border: 1px solid var(--popover-popper-border-color);
        border-radius: var(--abc-border-radius-small);
        box-shadow: var(--abc-shadow-1);

        .abc-link {
            position: absolute;
            right: 10px;
        }

        .check-electron-tips__arrow {
            position: absolute;
            left: 50%;
            display: block;
            margin-left: -6px;
            border-color: rgba(0, 0, 0, 0);
            border-style: solid;
            border-width: 6px;
        }

        .check-electron-tips__arrow::after {
            position: absolute;
            margin-left: -5px;
            content: '';
            border-color: rgba(0, 0, 0, 0);
            border-style: solid;
            border-width: 5px;
        }

        .check-electron-tips-top__arrow {
            top: 100%;
            border-top-color: var(--popover-popper-border-color);
            border-top-width: 6px;
            border-bottom-width: 0;
        }

        .check-electron-tips-top__arrow::after {
            top: -6px;
            border-top-color: var(--popover-popper-fill-color);
            border-top-width: 5px;
            border-bottom-width: 0;
        }

        .check-electron-tips-bottom__arrow {
            top: -6px;
            border-top-width: 0;
            border-bottom-color: var(--popover-popper-border-color);
            border-bottom-width: 6px;
        }

        .check-electron-tips-bottom__arrow::after {
            top: 1px;
            border-top-width: 0;
            border-bottom-color: var(--popover-popper-fill-color);
            border-bottom-width: 5px;
        }
    }
</style>
