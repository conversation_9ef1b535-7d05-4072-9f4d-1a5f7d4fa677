import AbcAccess from '@/access/utils.js';
/**
 * @desc 避免渲染额外的标签，这里用render形式，这里就限定了slot进来的只有一个root节点
 * <AUTHOR>
 * @date 2022-08-12 08:39:27
 */

export default {
    name: 'AbcCheckAccess',
    props: {
        accessKey: {
            type: String,
            validator: (val) => Object.values(AbcAccess.accessMap).includes(val) || val === undefined,
        },
        permissionKeys: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    computed: {
        isVisible() {
            return (
                AbcAccess.getPurchasedByKey(this.accessKey) &&
                AbcAccess.getPermissionByKey(this.permissionKeys)
            );
        },
        hasAccess() {
            return AbcAccess.getAccessByKey(this.accessKey);
        },
    },
    methods: {
        invokerErrorHandler() {
            this.$slots.default?.forEach((vNode) => {
                const { listeners } = vNode.componentOptions || {};

                if (listeners) {
                    Object.keys(listeners).forEach((key) => {
                        listeners[key] = () => {
                            AbcAccess.check(this.accessKey);
                        };
                    });
                    vNode.componentOptions.listeners.click = (e) => {
                        e.stopPropagation();
                        e.stopImmediatePropagation();
                        AbcAccess.check(this.accessKey);
                    };
                }
            });
        },
    },
    render() {
        if (this.isVisible) {
            if (!this.hasAccess) {
                this.invokerErrorHandler();
            }
            return this.$slots.default;
        }
        return null;
    },
};
