export default class Dragger {
    static activeEvents = ['pointerdown', 'pointermove', 'pointerup', 'pointercancel'];
    static activeListeners = [];

    static instance = [];
    static DRAGGER_MOVING_CLASS = 'is-dragger-moving';
    static DRAGGER_SHADOW_CLASS = 'drag-grid-item-shadow';
    static DRAGGER_STRETCH_CLASS = 'drag-grid-item-stretch';
    static DRAGGER_ACTIVE_CLASS = 'drag-grid-item-active';
    static DRAGGER_DISABLE_DRAG_OVER_CLASS = 'drag-grid-item-disable-drag-over';
    static DRAGGER_STRETCH_SINGLE_LINE_CLASS = 'drag-grid-item-stretch-single-line';
    static DRAGGER_SHOW_DISABLE_TIPS_CLASS = 'drag-grid-item-show-disable-tips';
    static DRAGGER_OVER_CLASS = 'drag-grid-item-over';
    static DRAGGER_MODE_TIME = 'mode-time';
    static DRAGGER_MODE_FREE = 'mode-free';
    static DISABLE_DRAG_OVER = 'disable-drag-over';
    static DRAGGER_X = 'dragger-x';
    static DRAGGER_Y = 'dragger-y';
    static DRAGGER_STRETCH_ORIGIN_HEIGHT = 'dragger-stretch-origin-height';
    static DRAGGER_STRETCH_ORIGIN_START = 'dragger-stretch-origin-start';
    static DRAGGER_TYPE = {
        DARG: 'drag',
        STRETCH: 'stretch',
    };
    static isBindGlobalEvents = false;
    static getActive() {
        return Dragger.instance[Dragger.instance.length - 1];
    }
    _options = {
        // 包含x,y轴的容器
        container: document.body,
        // 内容容器 不包含x,y轴
        contentContainer: document.body,
        // 时间模式每一个单元格的大小 [width, height]
        grid: [199, 23],
        // y轴占用宽度
        containerLeft: 52,
        // 拉伸触发距离元素底部的位置
        scaleYTriggerHeight: 8,
        // 时间模式x的长度
        xAxis: 7,
        // 拖拽或者拉伸结束
        onDragEnd() {
        },
        onDragUpdate() {
        },
        onDragTimeModeShadowUpdate() {
        },
        onOverDragItem() {
        },
        onClickMask() {
        },
        onClickInactiveElement() {
        },
        onClickOutsideContentContainer() {
        },
        onContinueDragEnd() {
            return true;
        },
        isBlockDragEnd() {
            return true;
        },
        onContinueDragStart() {
            return true;
        },
        // 时间模式 | 自由模式
        // eslint-disable-next-line no-use-before-define
        mode: Dragger.DRAGGER_MODE_TIME,
        // 自由模式中包裹可拖拽元素的组
        dragGridItemGroup: null,
        // 越界滚动高度
        scrollOffset: 23,
        // 越界触发边距
        scrollTriggerHeight: 8,
        // 禁用拖拽
        disableDrag: false,
        // 底部空白
        bottomSpace: 0,
        // 鼠标移动小于该值视为点击
        clickThreshold: 5,
        // x轴禁用
        disableX: [0],
    };
    _container = {
        height: 0,
        width: 0,
        x: 0,
        y: 0,
        scrollHeight: 0,
    };
    _pointerRelative = {
        top: 0,
        left: 0,
    };
    _pointer = {
        left: 0,
        top: 0,
    };
    _pointerdownEvent = null;
    _pointerDownRelative = {
        left: 0,
        top: 0,
    };
    _pointerUpRelative = {
        left: 0,
        top: 0,
    };
    _activeEl = {
        left: 0,
        top: 0,
        height: 0,
        width: 0,
    };
    _draggerType = null;
    isIdle = true;

    // TODO 移除
    get _isIdle() {
        return this.isIdle;
    }

    set _isIdle(newValue) {
        this.isIdle = newValue;
    }

    _dragItemsRect = null;
    _dragGridItemGroupRect = null;
    _isReferenceElDragEnable = false;
    _isPointerDownMask = false;
    _isNormalDragEnd = false;
    _onClickMaskOK = true;
    _originData = null;
    _referenceData = null;

    $dragItems = null;
    $dragGridItemGroup = null;
    $originEl = null;
    $activeEl = null;
    $shadowEl = null;
    $referenceEl = null;
    $maskEl = null;
    $maskContentEl = null;

    constructor($dragItems, options) {
        this.initConfig($dragItems, options);
        Dragger.instance.push(this);
        if (!Dragger.isBindGlobalEvents) {
            this.bindGlobalEvents();
        }
    }

    updateOptions($dragItems, options) {
        this.initConfig($dragItems, options);
    }

    initConfig($dragItems, options) {
        this.$dragItems = $dragItems;
        this.$dragGridItemGroup = options.dragGridItemGroup;
        Object.assign(this._options, options);
        this._options.grid[0] = parseInt(String(this._options.grid[0]));
    }

    bindGlobalEvents = () => {
        Dragger.activeEvents.forEach((eventName, eventIndex) => {
            window.addEventListener(eventName, Dragger.activeListeners[eventIndex] = (...args) => {
                if (typeof Dragger.getActive()[eventName] === 'function') {
                    Dragger.getActive()[eventName](...args);
                }
            });
        });
        Dragger.isBindGlobalEvents = true;
    };

    unBindGlobalEvents = () => {
        Dragger.activeEvents.forEach((eventName, eventIndex) => {
            window.removeEventListener(eventName, Dragger.activeListeners[eventIndex]);
        });
        Dragger.isBindGlobalEvents = false;
    };

    initContainer = () => {
        const {
            height, x, y,
        } = this._options.container.getBoundingClientRect();
        this._container.width = this._options.grid[0] * this._options.xAxis;
        this._container.height = height;
        this._container.x = x;
        this._container.y = y;
        this._container.scrollHeight = this._options.container.scrollHeight;
    };

    checkDraggerDisable = () => {
        // 有遮罩元素需要继续检测事件
        return !this._isIdle;
    };

    positionElement = (oldEl, positionEl) => {
        positionEl.style.position = 'absolute';
        positionEl.style.left = `${oldEl.offsetLeft}px`;
        positionEl.style.top = `${oldEl.offsetTop - oldEl.parentElement.scrollTop}px`;
        positionEl.style.width = `${oldEl.offsetWidth}px`;
    };

    getElRect = ($els) => {
        if (!Array.isArray($els)) {
            return [];
        }
        return $els.map(($el) => {
            const rect = $el.getBoundingClientRect();
            const left = rect.left - this._options.contentContainer.getBoundingClientRect().left;
            const top = rect.top - this._options.contentContainer.getBoundingClientRect().top;
            const x1 = left;
            const x2 = left + rect.width;
            const y1 = top;
            const y2 = top + rect.height;
            return {
                height: rect.height,
                width: rect.width,
                left,
                top,
                x1,
                x2,
                y1,
                y2,
                $el,
            };
        });
    };

    getTimeModeCoord($el) {
        let coord = {};
        if (this._draggerType === Dragger.DRAGGER_TYPE.DARG) {
            // 生成坐标
            const {
                x, y,
            } = this.decodeTranslateString(($el || this.$shadowEl || this.$activeEl).style.transform);
            const relativeY = this._activeEl.top + y;
            const relativeX = this._activeEl.left + x;
            const y0 = relativeY / this._options.grid[1];
            const x0 = Math.ceil(relativeX) / this._options.grid[0];
            const y1 = this._activeEl.height / this._options.grid[1] + y0;
            coord = {
                x: parseInt(x0), y: [y0.toFixed(0), y1.toFixed(0)],
            };
        }

        if (this._draggerType === Dragger.DRAGGER_TYPE.STRETCH || !this._draggerType) {
            const x0 = Math.ceil(this._activeEl.left) / this._options.grid[0];
            const y0 = this._activeEl.top / this._options.grid[1];
            const y1 = y0 + this.$activeEl.getBoundingClientRect().height / this._options.grid[1];
            coord = {
                x: parseInt(x0), y: [y0.toFixed(0),y1.toFixed(0)],
            };
        }

        return coord;
    }

    initActiveElRect = () => {
        this._dragItemsRect = this.getElRect(this.$dragItems);
    };

    initActiveElGroupRect = () => {
        this._dragGridItemGroupRect = this.getElRect(this.$dragGridItemGroup);
    };
    pointerdown = (e) => {
        const currentSelected = this.$dragItems.find(($el) => {
            return $el.contains(e.target);
        });
        if (currentSelected?.getAttribute('disabled-click')) { // 停诊的后续号源不能点开
            this._options.showTip();
            return;
        }
        if (this.checkDraggerDisable() && !this.$maskEl && !this._isNormalDragEnd) {
            return;
        }

        if (this._isNormalDragEnd) {
            const isBoardEvent = e.composedPath().some((it) => this._options.container === it);
            if (isBoardEvent && typeof this._options.onClickMask === 'function' && this._options.onClickMask()) {
                this._isNormalDragEnd = false;
                this._onClickMaskOK = true;
            } else {
                this._onClickMaskOK = false;
                return;
            }
        }

        this.initContainer();

        if (this.$maskEl) {
            if (this.$referenceEl && this.$referenceEl.contains(e.target) && !this._options.disableDrag) {
                this._isReferenceElDragEnable = false;
            }
            if (e.target === this.$maskContentEl) {
                this._isPointerDownMask = true;
            }
        } else {
            if (Array.isArray(this.$dragItems)) {
                this.$activeEl = this.$dragItems.find(($el) => {
                    return $el.contains(e.target);
                });
            }
        }

        this._pointerdownEvent = e;
        this._pointerDownRelative = this.getPointerRelative(this._pointerdownEvent);
        this.initActiveEl(this.$activeEl);
        // 判断是否为拉伸
        const pointerTopOfActiveEl = this._pointerDownRelative.top - this._activeEl.top;
        const bottomOffset = this._activeEl.height - pointerTopOfActiveEl;

        this._draggerType = this._options.scaleYTriggerHeight > bottomOffset ? Dragger.DRAGGER_TYPE.STRETCH : Dragger.DRAGGER_TYPE.DARG;

        // 自由模式下需要计算可拖动元素的位置表
        if (this._options.mode === Dragger.DRAGGER_MODE_FREE) {
            this.initActiveElRect();
            this.initActiveElGroupRect();
        }
    };

    pointermove = (e) => {
        if (this.checkDraggerDisable() && !this._isReferenceElDragEnable) {
            return;
        }

        if (typeof this._options.onContinueDragStart === 'function' && !this._options.onContinueDragStart(
            this.$activeEl,
        )) {
            return;
        }

        if (typeof this._options.onDragUpdate === 'function') {
            this._options.onDragUpdate();
        }

        if (this.$activeEl?.getAttribute('disabled') === 'disabled') {
            return;
        }

        // 最大滚动高度
        const maxScrollHeight = this._options.contentContainer.getBoundingClientRect().height - this._container.height;
        // 当前滚动高度
        const { scrollTop } = this._options.container;

        // 时间模式
        if (this._options.mode === Dragger.DRAGGER_MODE_TIME) {
            // 已触发拖拽或者拉伸事件
            if (this.$activeEl) {
                // 移动和点击距离
                if (!this.$shadowEl && Math.abs(this.getPointerRelative(e).left - this._pointerDownRelative.left) < 1 && Math.abs(this.getPointerRelative(e).top - this._pointerDownRelative.top) < 1) {
                    return;
                }

                // 指针到容器的相对位置
                this._pointerRelative = this.getPointerRelative(e);
                this._pointer = this.getPointer(e);

                // 向下滚动
                if (this._pointer.top > this._container.height - this._options.scrollTriggerHeight && scrollTop < maxScrollHeight) {
                    this._options.container.scrollTop = Math.min(maxScrollHeight, scrollTop + this._options.scrollOffset);
                }

                // 向上滚动
                if (this._pointer.top < this._options.scrollTriggerHeight && scrollTop > 0) {
                    this._options.container.scrollTop = Math.max(0, scrollTop - this._options.scrollOffset);
                }

                // 拖拽
                if (this._draggerType === Dragger.DRAGGER_TYPE.DARG) {
                    // 创建影子元素
                    if (!this.$shadowEl) {
                        this.$shadowEl = this.$activeEl.cloneNode(true);
                        this._options.contentContainer.append(this.$shadowEl);
                        this.$shadowEl.classList.add(Dragger.DRAGGER_SHADOW_CLASS);
                    }
                    this.$activeEl.classList.add(Dragger.DRAGGER_MOVING_CLASS);

                    // 计算偏移量
                    let colMoveIndex = Math.floor(this._pointerRelative.left / this._options.grid[0]);
                    const colDownIndex = Math.floor(this._pointerDownRelative.left / this._options.grid[0]);
                    const moveY = this._pointerRelative.top - this._pointerDownRelative.top;
                    const maxMoveY = this._options.contentContainer.getBoundingClientRect().height - (this._activeEl.top + this._activeEl.height);
                    const minMoveY = -Math.floor(this._activeEl.top);
                    const maxColIndex = this._options.xAxis - 1;

                    // X轴左边越界
                    if (colMoveIndex < 0) {
                        colMoveIndex = 0;
                    }

                    // x轴右边越界
                    if (colMoveIndex > maxColIndex) {
                        colMoveIndex = maxColIndex;
                    }

                    const safeMoveY = Math.max(Math.min(moveY, maxMoveY), minMoveY);
                    const moveVectorX = (colMoveIndex - colDownIndex) * this._options.grid[0] - Math.ceil(this._activeEl.left) % this._options.grid[0];
                    const moveVectorY = Math.floor(safeMoveY / this._options.grid[1]) * this._options.grid[1];

                    // 元素位移
                    const moveVector = {
                        x: moveVectorX,
                        y: moveVectorY,
                    };

                    this.$shadowEl.style.transform = `translate3d(${moveVector.x}px, ${moveVector.y}px, 1px)`;
                    this.$shadowEl.style.width = `${this._options.grid[0]}px`;

                    this._options.onDragTimeModeShadowUpdate(this.$shadowEl, this.getTimeModeCoord(), Dragger.DRAGGER_TYPE.DARG);

                    // 影子元素禁用提示
                    if (this.$shadowEl) {
                        if (this._options.disableX.includes(this.getTimeModeCoord().x)) {
                            this.$shadowEl.classList.add(Dragger.DRAGGER_SHOW_DISABLE_TIPS_CLASS);
                        } else {
                            this.$shadowEl.classList.remove(Dragger.DRAGGER_SHOW_DISABLE_TIPS_CLASS);
                        }
                    }
                }

                // 拉伸
                if (this._draggerType === Dragger.DRAGGER_TYPE.STRETCH) {
                    this._pointerRelative = this.getPointerRelative(e);
                    let activeElHeight = this._pointerRelative.top - this._activeEl.top;
                    activeElHeight = Math.ceil(activeElHeight / this._options.grid[1]) * this._options.grid[1];
                    const maxActiveElHeight = this._options.contentContainer.getBoundingClientRect().height - this._activeEl.top;

                    this.$activeEl.style.height = `${Math.max(Math.min(activeElHeight, maxActiveElHeight), this._options.grid[1])}px`;
                    this.$activeEl.classList.add(Dragger.DRAGGER_STRETCH_CLASS);
                    this._options.onDragTimeModeShadowUpdate(this.$activeEl, this.getTimeModeCoord(), Dragger.DRAGGER_TYPE.STRETCH);

                    if (this._isReferenceElDragEnable) {
                        this.$shadowEl.style.height = this.$activeEl.style.height;
                        this.$shadowEl.classList.add(Dragger.DRAGGER_STRETCH_CLASS);
                    }

                    // 允许误差
                    if (Math.abs(this.$activeEl.getBoundingClientRect().height - this._options.grid[1]) < 1) {
                        this.$activeEl.classList.add(Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS);
                        if (this._isReferenceElDragEnable) {
                            this.$shadowEl.classList.add(Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS);
                        }
                    } else {
                        this.$activeEl.classList.remove(Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS);
                        if (this._isReferenceElDragEnable) {
                            this.$activeEl.classList.remove(Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS);
                        }
                    }

                    this._pointer = this.getPointer(e);

                    if (this._pointer.top > this._container.height - 5 && scrollTop < maxScrollHeight) {
                        this._options.container.scrollTop = Math.min(maxScrollHeight, scrollTop + this._options.grid[1]);
                    }
                }
            }
        }

        // 自由模式
        if (this._options.mode === Dragger.DRAGGER_MODE_FREE) {
            if (this.$activeEl) {
                // 给不可被放置的元素增加样式
                this.$dragItems.forEach(($dragItem) => {
                    // 除了被激活元素的其他元素
                    if (this.$activeEl !== $dragItem && $dragItem.getAttribute(Dragger.DISABLE_DRAG_OVER)) {
                        $dragItem.classList.add(Dragger.DRAGGER_DISABLE_DRAG_OVER_CLASS);
                    }
                });

                // 创建影子元素
                if (!this.$shadowEl) {
                    this.$shadowEl = this.$activeEl.cloneNode(true);
                    this._options.contentContainer.append(this.$shadowEl);
                    this.$shadowEl.classList.add(Dragger.DRAGGER_SHADOW_CLASS);
                    this.$shadowEl.style.height = `${this.$activeEl.getBoundingClientRect().height}px`;
                    this.$shadowEl.style.width = `${this.$activeEl.getBoundingClientRect().width}px`;
                    this.$shadowEl.style.position = 'absolute';
                    this.$shadowEl.style.left = `${this.$activeEl.getBoundingClientRect().left - this._options.contentContainer.getBoundingClientRect().left}px`;
                    this.$shadowEl.style.top = `${this.$activeEl.getBoundingClientRect().top - this._options.contentContainer.getBoundingClientRect().top}px`;
                    this.$shadowEl.style.zIndex = '1';
                }

                this.$activeEl.classList.add(Dragger.DRAGGER_MOVING_CLASS);

                // 指针到容器的相对位置
                this._pointerRelative = this.getPointerRelative(e);
                this._pointer = this.getPointer(e);

                // 向下滚动
                if (this._pointer.top > this._container.height - this._options.scrollTriggerHeight && scrollTop < maxScrollHeight) {
                    this._options.container.scrollTop = Math.min(maxScrollHeight, scrollTop + this._options.scrollOffset);
                }

                // 向上滚动
                if (this._pointer.top < this._options.scrollTriggerHeight && scrollTop > 0) {
                    this._options.container.scrollTop = Math.max(0, scrollTop - this._options.scrollOffset);
                }

                const maxMoveY = this._options.contentContainer.getBoundingClientRect().height - (this._activeEl.top + this._activeEl.height);
                const minMoveY = -this._activeEl.top;
                const maxMoveX = this._options.contentContainer.getBoundingClientRect().width - (this._activeEl.left + this._activeEl.width);
                const minMoveX = -this._activeEl.left;

                let moveY = this._pointerRelative.top - this._pointerDownRelative.top;
                let moveX = this._pointerRelative.left - this._pointerDownRelative.left;
                let isOverSingleDragItem = false;

                // 上边界
                if (moveY < minMoveY) {
                    moveY = minMoveY;
                }

                // 下边界
                if (moveY > maxMoveY) {
                    moveY = maxMoveY;
                }

                // 右边界
                if (moveX > maxMoveX) {
                    moveX = maxMoveX;
                }

                // 左边界
                if (moveX < minMoveX) {
                    moveX = minMoveX;
                }

                // 元素位移
                const moveVector = {
                    x: moveX,
                    y: moveY,
                };

                this.$shadowEl.style.transform = `translate3d(${moveVector.x}px, ${moveVector.y}px, 1px)`;

                // 禁用区域检查
                if (this.$shadowEl) {
                    const x = Math.floor(this.getPointerRelative(e).left / this._options.grid[0]);
                    if (this._options.disableX.includes(x)) {
                        this.$shadowEl.classList.add(Dragger.DRAGGER_SHOW_DISABLE_TIPS_CLASS);
                    } else {
                        this.$shadowEl.classList.remove(Dragger.DRAGGER_SHOW_DISABLE_TIPS_CLASS);
                    }
                }

                // 是否经过单个落点
                if (Array.isArray(this._dragItemsRect)) {
                    this._dragItemsRect.forEach(({
                        x1, x2, y1, y2, $el,
                    }) => {
                        const {
                            left: pointerLeft, top: pointerTop,
                        } = this._pointerRelative;
                        if (pointerLeft >= x1 && pointerLeft <= x2 && pointerTop >= y1 && pointerTop <= y2) {
                            isOverSingleDragItem = true;
                            $el.classList.add(Dragger.DRAGGER_OVER_CLASS);
                            // 禁止hover元素不触发
                            if (!$el.classList.contains(Dragger.DRAGGER_DISABLE_DRAG_OVER_CLASS) && this._options.onOverDragItem) {
                                this._options.onOverDragItem(this.$shadowEl, $el);
                            }
                        } else {
                            $el.classList.remove(Dragger.DRAGGER_OVER_CLASS);
                        }
                    });
                }

                // 是否经过空的组
                if (!isOverSingleDragItem && Array.isArray(this._dragGridItemGroupRect)) {
                    this._dragGridItemGroupRect.forEach(({
                        x1, x2, y1, y2, $el: $dragItemGroup,
                    }) => {
                        const {
                            left: pointerLeft, top: pointerTop,
                        } = this._pointerRelative;
                        if (pointerLeft >= x1 && pointerLeft <= x2 && pointerTop >= y1 && pointerTop <= y2) {
                            // 判断元素下都多少可拖拽元素
                            if (Array.isArray(this.$dragItems)) {
                                let $currentDragItem = null;
                                const containsDragItemCount = this.$dragItems.reduce((count, $dragItem) => {
                                    if ($dragItemGroup.contains($dragItem)) {
                                        $currentDragItem = $dragItem;
                                        return count + 1;
                                    }
                                    return count;
                                }, 0);

                                // 只有一个可拖拽元素
                                if (containsDragItemCount === 1) {
                                    $currentDragItem.classList.add(Dragger.DRAGGER_OVER_CLASS);
                                    // 禁止hover元素不触发
                                    if (!$currentDragItem.classList.contains(Dragger.DRAGGER_DISABLE_DRAG_OVER_CLASS) && this._options.onOverDragItem) {
                                        this._options.onOverDragItem(this.$shadowEl, $currentDragItem);
                                    }
                                }
                            }
                        }
                    });
                }
            }
        }
    };

    pointerup = (e, isCustomClick = false) => {
        if (this.checkDraggerDisable() && !this._isReferenceElDragEnable && !this.$maskEl && !this._isNormalDragEnd) {
            return;
        }

        if (this._isNormalDragEnd) {
            if (!window._vue.$el.contains(e.target)) {
                return;
            }
        }

        if (!this._onClickMaskOK) {
            return;
        }

        if (this._isPointerDownMask && e.target === this.$maskContentEl && !this._isReferenceElDragEnable) {
            if (typeof this._options.onClickMask === 'function') {
                this._options.onClickMask();
            }
            this._isPointerDownMask = false;
            return;
        }

        if (this.$maskEl && !this._isReferenceElDragEnable) {
            return;
        }

        if (!this.$activeEl) {
            if (this._options.mode === Dragger.DRAGGER_MODE_TIME) {
                if (this._options.contentContainer.contains(e.target)) {
                    if (typeof this._options.onClickInactiveElement === 'function') {
                        // 获取当前点击位置
                        this._pointerUpRelative = this.getPointerRelative(e);
                        const x = this._pointerUpRelative.left / this._options.grid[0];
                        const y0 = parseInt(this._pointerUpRelative.top / this._options.grid[1]);
                        const coord = {
                            x: parseInt(x),
                            y: [y0, y0],
                        };
                        this._options.onClickInactiveElement({
                            destroy: () => {
                                this.clear();
                            },
                            e,
                            coord,
                            relativePosition: this._pointerUpRelative,
                        });
                    }
                } else {
                    this._options.onClickOutsideContentContainer(e);
                }
                return;
            }
        }

        // 时间模式
        if (this._options.mode === Dragger.DRAGGER_MODE_TIME) {
            if (this.$shadowEl && this._options.disableX.includes(this.getTimeModeCoord().x)) {
                this.backToOrigin();
                this.clear();
                return;
            }
            // 坐标未发生变动
            if (this.$shadowEl && this.$activeEl) {
                const coordShadowEl = this.getTimeModeCoord(this.$shadowEl);
                const coordActiveEl = this.getTimeModeCoord(this.$activeEl);

                if (coordShadowEl.x === coordActiveEl.x && coordShadowEl.y[0] === coordActiveEl.y[0] && coordShadowEl.y[1] === coordActiveEl.y[1]) {
                    this.backToOrigin();
                    this.clear();
                    return;
                }
            }
            this.onDragEnd({
                destroy: () => {
                    if (this.$originEl) {
                        const draggerOriginHeight = this.$originEl.getAttribute(Dragger.DRAGGER_STRETCH_ORIGIN_HEIGHT);
                        const draggerOriginStart = this.$originEl.getAttribute(Dragger.DRAGGER_STRETCH_ORIGIN_START);
                        if (draggerOriginHeight) {
                            this.$originEl.style.height = draggerOriginHeight;
                        }
                        if (draggerOriginStart) {
                            const $dataTime = this.$originEl.querySelector('[data-time]');
                            if ($dataTime) {
                                $dataTime.innerHTML = draggerOriginStart;
                            }
                        }

                        // 允许误差
                        if (Math.abs(parseFloat(draggerOriginHeight) - this._options.grid[1]) < 1) {
                            this.$originEl.classList.add(Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS);
                        } else {
                            this.$originEl.classList.remove(Dragger.DRAGGER_STRETCH_SINGLE_LINE_CLASS);
                        }
                    }
                    this.clear();
                },
                coord: this.getTimeModeCoord(),
            });
        }

        // 自由模式
        if (this._options.mode === Dragger.DRAGGER_MODE_FREE) {
            let $overDragItem;
            this._pointerUpRelative = this.getPointerRelative(e);
            const isClickEvent = isCustomClick || Math.abs(this._pointerUpRelative.left - this._pointerDownRelative.left) < this._options.clickThreshold && Math.abs(this._pointerUpRelative.top - this._pointerDownRelative.top) < this._options.clickThreshold;
            // 销毁影子元素
            if (this.$shadowEl) {
                this.$shadowEl.parentElement?.removeChild(this.$shadowEl);
                this.$shadowEl = null;
            }

            // 获取被over的元素
            if (Array.isArray(this.$dragItems)) {
                // 不能是不可放置的位置
                $overDragItem = this.$dragItems.find(($dragItem) => !$dragItem.getAttribute(Dragger.DISABLE_DRAG_OVER) && $dragItem.classList.contains(Dragger.DRAGGER_OVER_CLASS));
                // 元素不在视口内
                if ($overDragItem && this._options.container) {
                    if ($overDragItem.getBoundingClientRect().top < this._options.container.getBoundingClientRect().top) {
                        $overDragItem.scrollIntoView({});
                    }
                }
            }

            // 有over元素则进行正常操作
            // 处理点击事件
            if ($overDragItem) {
                this.$shadowEl = this.$activeEl.cloneNode(true);
                $overDragItem.parentElement.appendChild(this.$shadowEl);
                this.positionElement($overDragItem, this.$shadowEl);
                this._referenceData = $overDragItem._data;
                this.onDragEnd({
                    destroy: () => {
                        this.clear();
                    },
                    coord: {
                        x: JSON.parse($overDragItem.getAttribute(Dragger.DRAGGER_X)),
                        y: JSON.parse($overDragItem.getAttribute(Dragger.DRAGGER_Y)),
                    },
                });
            } else if (this.$activeEl && isClickEvent) {
                this.$shadowEl = this.$activeEl.cloneNode(true);
                this.$activeEl.parentElement.appendChild(this.$shadowEl);
                this.positionElement(this.$activeEl, this.$shadowEl);
                this._referenceData = this.$activeEl._data;
                this.onDragEnd({
                    destroy: () => {
                        this.clear();
                        if (this.$shadowEl) {
                            this.$shadowEl.parentElement?.removeChild(this.$shadowEl);
                            this.$shadowEl = null;
                        }

                        this.$activeEl = null;
                    },
                    coord: {
                        x: JSON.parse(this.$activeEl.getAttribute(Dragger.DRAGGER_X)),
                        y: JSON.parse(this.$activeEl.getAttribute(Dragger.DRAGGER_Y)),
                    },
                });
            } else {
                this.backToOrigin();
                this.clear();
            }
        }
    };

    getPointerRelative(e) {
        return {
            left: e.pageX - this._container.x + this._options.container.scrollLeft - this._options.containerLeft,
            top: e.pageY - this._container.y + this._options.container.scrollTop,
        };
    }

    getPointer(e) {
        return {
            left: e.pageX - this._container.x - this._options.containerLeft,
            top: e.pageY - this._container.y,
        };
    }

    initActiveEl = (activeEl) => {
        if (activeEl) {
            const {
                left,
                top,
                height,
                width,
            } = activeEl.getBoundingClientRect();
            this._activeEl.left = left - this._options.contentContainer.getBoundingClientRect().left;
            this._activeEl.top = top - this._options.contentContainer.getBoundingClientRect().top;
            this._activeEl.height = height;
            this._activeEl.width = width;
        }
    };

    triggerOnDragEnd = ($activeEl) => {
        this.$activeEl = $activeEl;
        this.initActiveEl($activeEl);
        this.pointerup({}, true);
    };

    backToOrigin = () => {
        this._isIdle = true;
        if (this.$shadowEl) {
            this.$shadowEl.parentElement?.removeChild(this.$shadowEl);
            this.$shadowEl = null;
        }
        if (this.$activeEl) {
            // 取消时滚动到可视区域
            if (this.$activeEl.getBoundingClientRect().top < this._options.container.getBoundingClientRect().top) {
                this.$activeEl.scrollIntoView({});
            }
            this.$activeEl.classList.remove(Dragger.DRAGGER_MOVING_CLASS);
            this.$activeEl.classList.remove(Dragger.DRAGGER_STRETCH_CLASS);
            this.$activeEl = null;
        }
    };

    clear = () => {
        if (this.$originEl) {
            this.$originEl.classList.remove(Dragger.DRAGGER_MOVING_CLASS);
            this.$originEl.classList.remove(Dragger.DRAGGER_STRETCH_CLASS);
            this.$originEl = null;
        }

        if (this._pointerdownEvent) {
            this._pointerdownEvent = null;
        }

        if (this._draggerType) {
            this._draggerType = null;
        }
        // 自由模式要单独删除可拖动元素样式
        if (this._options.mode === Dragger.DRAGGER_MODE_FREE && Array.isArray(this.$dragItems)) {
            this.$dragItems.forEach(($el) => {
                $el.classList.remove(Dragger.DRAGGER_OVER_CLASS);
                $el.classList.remove(Dragger.DRAGGER_DISABLE_DRAG_OVER_CLASS);
                $el.classList.remove(Dragger.DRAGGER_ACTIVE_CLASS);
                $el.classList.remove(Dragger.DRAGGER_MOVING_CLASS);
            });
        }
        this._onClickMaskOK = true;
        this.$activeEl = null;
    };

    decodeTranslateString(translateString) {
        const [x = 0, y = 0, z = 0] = translateString.match(/-?[\d.]+px/g) || [];
        return {
            x: parseFloat(x),
            y: parseFloat(y),
            z: parseFloat(z),
        };
    }

    destroy = () => {
        this.clear();
        Dragger.instance.splice(Dragger.instance.indexOf(this), 1);
        if (!Dragger.instance.length) {
            this.unBindGlobalEvents();
        }
    };

    onDragEnd = (props) => {
        this._isIdle = false;
        const referenceEl = this.$shadowEl || this.$activeEl;
        referenceEl._data = this._referenceData ?? this.$activeEl._data;
        if (!props.isBlockDragEnd && !this._options.isBlockDragEnd({
            ...props,
            referenceEl,
            activeEl: this.$activeEl,
            originEl: this.$activeEl,
        })) {
            if (this._options.mode === Dragger.DRAGGER_MODE_FREE && Array.isArray(this.$dragItems)) {
                this.$dragItems.forEach(($el) => {
                    $el.classList.remove(Dragger.DRAGGER_OVER_CLASS);
                    $el.classList.remove(Dragger.DRAGGER_DISABLE_DRAG_OVER_CLASS);
                    $el.classList.remove(Dragger.DRAGGER_MOVING_CLASS);
                });
            }
            this._isNormalDragEnd = true;
            referenceEl.classList.add(Dragger.DRAGGER_ACTIVE_CLASS);
            this._destroyNormalDragEnd = () => {
                referenceEl.classList.remove(Dragger.DRAGGER_ACTIVE_CLASS);
                props.destroy();
                this._isIdle = true;
                this._isNormalDragEnd = false;
                // 移除事件
                window.removeEventListener('keyup', this._escListener, false);
            };
            this._escListener = (e) => {
                if (e.keyCode === 27 && typeof this._options.onClickMask === 'function') {
                    this._options.onClickMask();
                }
            };
            window.addEventListener('keyup', this._escListener, false);
            this._options.onDragEnd({
                ...props,
                referenceEl,
                activeEl: this.$activeEl,
                originEl: this.$activeEl,
                destroy: this._destroyNormalDragEnd,
            });
            return;
        }

        // 遮罩上更新完成
        if (this._isReferenceElDragEnable) {
            // 更新原始位置
            const {
                top: shadowElTop, left: shadowElLeft,
            } = this.$shadowEl.getBoundingClientRect();
            this.$activeEl.style.top = `${shadowElTop - this._container.y + this._options.container.scrollTop}px`;
            this.$activeEl.style.left = `${shadowElLeft - this._options.contentContainer.getBoundingClientRect().x}px`;
            this.$shadowEl.style.left = `${shadowElLeft}px`;
            this.$shadowEl.style.top = `${shadowElTop + this.$maskEl.scrollTop}px`;
            this.$shadowEl.style.transform = '';
            this._isReferenceElDragEnable = false;
        }

        // 未创建遮罩
        if (!this.$maskEl) {
            const ref = document.body;
            if (!this.$shadowEl) {
                this.$shadowEl = this.$activeEl.cloneNode(true);
                this.$activeEl.parentElement.appendChild(this.$shadowEl);
            }
            const referenceEl = this.$shadowEl.cloneNode(true);
            // 创建遮罩
            const mask = document.createElement('div');
            const maskClassName = 'drag-grid-mask';
            const maskContent = document.createElement('div');
            mask.setAttribute('class', maskClassName);
            mask.appendChild(maskContent);

            maskContent.setAttribute('style', 'height: 100%;');
            maskContent.appendChild(referenceEl);
            const {
                x, y, width, height,
            } = this.$shadowEl.getBoundingClientRect();
            referenceEl.style.position = 'absolute';
            referenceEl.style.left = `${x}px`;
            referenceEl.style.top = `${y}px`;
            referenceEl.style.width = `${width}px`;
            referenceEl.style.height = `${height}px`;
            referenceEl.style.transform = '';
            (referenceEl.querySelector('.drag-card') ?? referenceEl)?.classList?.add('active');
            ref.appendChild(mask);

            this.$maskEl = mask;
            this.$maskContentEl = maskContent;
            this.$referenceEl = referenceEl;
            this.$referenceEl.classList.add(Dragger.DRAGGER_ACTIVE_CLASS);

            // 存储数据
            this._originData = this.$activeEl._data;
            this.$originEl = this.$activeEl;
            this.$activeEl = this.$shadowEl;
            this.$activeEl.style.visibility = 'hidden';
            this.$shadowEl = this.$referenceEl;
            this._escListener = (e) => {
                if (e.keyCode === 27 && typeof this._options.onClickMask === 'function') {
                    this._options.onClickMask();
                }
            };
            window.addEventListener('keyup', this._escListener, false);
        }
        this.$activeEl._data = this._originData;
        this.$referenceEl._data = this._referenceData;
        this._destroyMask = () => {
            // 弹窗里面的元素
            this.$maskEl?.parentElement?.removeChild(this.$maskEl);
            this.$maskEl = null;
            this.$activeEl?.parentElement?.removeChild(this.$activeEl);
            this.$activeEl = null;
            this.$referenceEl?.classList?.remove(Dragger.DRAGGER_ACTIVE_CLASS);

            this.$referenceEl = null;
            this.$shadowEl = null;
            this._originData = null;
            this._isIdle = true;
            this._isReferenceElDragEnable = false;
            props.destroy();

            // 移除事件
            window.removeEventListener('keyup', this._escListener, false);
        };

        if (typeof this._options.onContinueDragEnd === 'function' && !this._options.onContinueDragEnd(props)) {
            this._destroyMask();
            return;
        }

        this._options.onDragEnd({
            ...props,
            destroy: this._destroyMask,
            referenceEl: this.$referenceEl,
            activeEl: this.$activeEl,
            originEl: this.$originEl,
            maskEl: this.$maskEl,
        });
    };
}
