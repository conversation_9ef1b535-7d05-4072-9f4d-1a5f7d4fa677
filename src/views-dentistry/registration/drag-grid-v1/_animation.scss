.right-to-left {
    animation: slideFromRight 0.15s ease-out;
}

@keyframes slideFromRight {
    0% {
        opacity: 0.5;
        transform: translateX(80px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.left-to-right {
    animation: slideFromLeft 0.15s ease-out;
}

@keyframes slideFromLeft {
    0% {
        opacity: 0.5;
        transform: translateX(-80px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}
