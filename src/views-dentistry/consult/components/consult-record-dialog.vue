<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        :title="title"
        size="large"
        :auto-focus="false"
        custom-class="consult-record-dialog"
        data-cy="consult-record-dialog"
    >
        <abc-form ref="form" v-abc-loading="loading" is-excel>
            <abc-descriptions
                :column="2"
                :label-width="88"
                grid
                size="large"
            >
                <abc-descriptions-item content-padding="0" label="咨询时间">
                    <abc-form-item required>
                        <abc-date-picker
                            v-model="postData.consultationDate"
                            :clearable="false"
                            placeholder="请选择"
                            data-cy="consult-record-dialog-咨询时间"
                            :picker-options="{
                                disabledDate(date) {
                                    return date > new Date()
                                }
                            }"
                        >
                        </abc-date-picker>
                    </abc-form-item>
                </abc-descriptions-item>
                <abc-descriptions-item content-padding="0" label="咨询师">
                    <abc-form-item required>
                        <abc-select
                            v-model="postData.consultantId"
                            placeholder="请选择"
                            adaptive-width
                            data-cy="consult-record-dialog-咨询师"
                        >
                            <abc-option
                                v-for="consultant in consultantList"
                                :key="consultant.employeeId"
                                :value="consultant.employeeId"
                                :label="consultant.employeeName"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </abc-descriptions-item>
                <abc-descriptions-item content-padding="0" label="咨询项目" :span="2">
                    <abc-form-item required>
                        <abc-select
                            v-model="postData.medicalPlanTypes"
                            multiple
                            size="large"
                            placeholder="选择方案类型"
                            adaptive-width
                            data-cy="consult-record-dialog-咨询项目"
                        >
                            <abc-option
                                v-for="typeItem in PROJECT_TYPE_LIST"
                                :key="typeItem.value"
                                :value="typeItem.value"
                                :label="typeItem.name"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </abc-descriptions-item>
                <abc-descriptions-item
                    content-padding="0"
                    label-vertical-align="center"
                    label="咨询内容"
                    :span="2"
                >
                    <abc-form-item required>
                        <abc-edit-div
                            v-model="postData.content"
                            size="huge"
                            :maxlength="200"
                            data-cy="consult-record-dialog-咨询内容"
                            :style="{
                                minHeight: '64px !important',
                                maxHeight: '180px !important',
                                overflowY: 'auto',
                            }"
                        ></abc-edit-div>
                    </abc-form-item>
                </abc-descriptions-item>
                <abc-descriptions-item
                    content-padding="0"
                    label-vertical-align="center"
                    label="患者备注"
                    :span="2"
                >
                    <abc-form-item>
                        <abc-edit-div
                            v-model="postData.patientRemark"
                            size="huge"
                            :maxlength="200"
                            data-cy="consult-record-dialog-患者备注"
                            :style="{
                                minHeight: '64px !important',
                                maxHeight: '180px !important',
                                overflowY: 'auto',
                            }"
                        ></abc-edit-div>
                    </abc-form-item>
                </abc-descriptions-item>
            </abc-descriptions>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="confirmLoading" @click="handleConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { PROJECT_TYPE_LIST } from '@/views-dentistry/consult/common/constant';
    import ConsultApi from 'api/consult';
    import Logger from 'utils/logger';
    import { ROLE_CONSULTANT_ID } from 'utils/constants';
    import { parseTime } from '@/utils';

    export default {
        name: 'ConsultRecordDialog',
        props: {
            value: Boolean,
            id: String,
            patientId: {
                type: String,
                required: true,
            },
        },
        data() {
            return {
                PROJECT_TYPE_LIST,
                postData: {
                    consultationDate: parseTime(new Date(), 'y-m-d', true),
                    consultantId: '',
                    medicalPlanTypes: [],
                    content: '',
                    patientRemark: '',
                },
                loading: false,
                confirmLoading: false,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
            ]),
            ...mapGetters('consult', ['consultantList']),

            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            title() {
                if (this.id) {
                    return '修改咨询记录';
                }
                return '新增咨询记录';
            },
            isConsultant() {
                return this.userInfo?.roleIds.includes(ROLE_CONSULTANT_ID);
            },
        },
        created() {
            if (this.id) {
                this.getRecordDetail();
            } else {
                this.postData.consultantId = this.isConsultant ? this.userInfo.id : '';
            }
        },
        methods: {
            async getRecordDetail() {
                try {
                    this.loading = true;
                    const { data } = await ConsultApi.getConsultRecord(this.id);
                    data.medicalPlanTypes = data.medicalPlanTypes?.map((x) => String(x));
                    this.postData = data;
                } catch (e) {
                    console.error(e);
                    Logger.error({
                        scene: '咨询记录-查询失败',
                        err: e,
                    });
                } finally {
                    this.loading = false;
                }
            },
            handleConfirm() {
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        try {
                            this.confirmLoading = true;
                            if (this.id) {
                                await ConsultApi.putConsultRecord({
                                    ...this.postData,
                                    patientId: this.patientId,
                                });
                                this.$emit('refresh', 'edit');
                            } else {
                                await ConsultApi.postConsultRecord({
                                    ...this.postData,
                                    patientId: this.patientId,
                                });
                                this.$emit('refresh', 'add');
                            }
                            this.showDialog = false;
                            this.$Toast.success('提交成功');
                        } catch (e) {
                            console.error(e);
                            Logger.error({
                                scene: '咨询记录-提交失败',
                                err: e,
                            });
                        } finally {
                            this.confirmLoading = false;
                        }
                    }
                });

            },
        },
    };
</script>

