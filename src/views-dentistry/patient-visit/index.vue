<template>
    <abc-container
        has-left-container
        :has-right-container="hasRightContainer"
        class="is-crm crm-module"
    >
        <!--右侧form区域-->
        <router-view></router-view>
    </abc-container>
</template>

<script>
    import ModulePermission from 'views/permission/module-permission';
    export default {
        name: 'CrmModule',
        mixins: [ModulePermission],
        computed: {
            hasRightContainer() {
                const { tab = 0 } = this.$route.query;
                if (this.$route.path.indexOf('patient-visit') > -1 && tab !== 3) {
                    return true;
                }
                return false;
            },
        },
    };
</script>
