import { errorCodes } from 'utils/fetch';
import { ModalFunc as AbcModal } from '@abc/ui-pc';

export function errorHandler(e) {
    const {
        code,
        detail,
        message,
    } = e;
    let callBack = {};
    if (code === 19116 || code === 12006) {
        callBack = {
            name: 'errorName',
        };
    } else if (code === 19125 || code === 12033) {
        callBack = {
            prop: 'errorName',
            name: 'errorEnName',
        };
    } else if (code === 12204) {
        callBack = {
            prop: 'errorItemCode',
            name: 'errorItemCode',
        };
    } else if (code === 12809) {
        detail.composite = detail.composite
            .filter((it) => it.type === 3)
            .map((item) => {
                return item.name;
            });

        AbcModal.alert({
            type: 'warn',
            title: '提示',
            content: `此项目已被添加到组合项目“${detail.composite.join(
                '，',
            )}”，需从中移除后才能变更此项目的类型`,
        });
    } else {
        if (errorCodes.includes(code)) {
            AbcModal.$alert({
                type: 'warn',
                title: '提示',
                content: message,
            });
        }
    }
    return callBack;
}
