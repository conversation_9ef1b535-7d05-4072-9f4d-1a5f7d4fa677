export default class AbcError extends Error {
    constructor( { code, message}, data ) {
        super( message );

        this.code = code;
        if (data) {
            this.data = data;
        }

    }

    toString() {
        return `
            code: ${this.code},
            message: ${this.message},
            data: ${this.data},
        `
    }

    static get Constants() {
        return {
            TIMEOUT: {
                code: 999999,
                message: 'timeout',
                status: 504,
            },
        }
    }
}
