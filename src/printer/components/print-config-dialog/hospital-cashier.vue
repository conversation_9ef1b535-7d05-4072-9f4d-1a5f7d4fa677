<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        content-styles="width: 820px; height: auto; max-height: 588px; padding: 24px;"
        title="打印规则"
        custom-class="print-config-hospital-cashier-dialog"
        append-to-body
    >
        <div class="dialog-content clearfix">
            <div v-if="noConnectionPrint" class="no-install-print">
                <div class="no-install-tip">
                    <i class="icon iconfont cis-icon-jinggao1"></i>
                    请连接打印机！
                </div>
            </div>

            <div class="print-setting-table">
                <abc-excel-table>
                    <div class="table-header">
                        <div class="th" style="width: 112px;">
                            单据类型
                        </div>
                        <div class="th" style="width: 126px;">
                            打印机
                        </div>
                        <div class="th" style="width: 78px;">
                            纸张尺寸
                        </div>
                        <div class="th" style="width: 78px;">
                            打印方向
                        </div>
                        <div class="th" style="width: 78px;">
                            预览模式
                        </div>
                        <div class="th" style="width: 135px;">
                            打印份数
                        </div>
                        <div class="th" style=" justify-content: center; width: 163px;">
                            收费完成同时打印
                        </div>
                    </div>

                    <div class="table-body">
                        <template v-for="(item, index) in printConfigWithKey">
                            <div :key="item.key" class="tr">
                                <!-- 单据类型 -->
                                <div class="td" style="width: 112px; padding: 0 10px;">
                                    <div class="label-container">
                                        {{ getLabel(item) }}
                                    </div>
                                </div>
                                <!-- 打印机 -->
                                <div class="td select-input-padding-right-20" style="width: 126px;">
                                    <abc-select
                                        :key="`printer-select-key-${printerListIsUpdate}`"
                                        v-model="item.deviceIndex"
                                        :width="126"
                                        :inner-width="360"
                                        :index="index"
                                        custom-class="print-preview-printer-select-wrapper"
                                        placeholder="选择打印机"
                                        :setting="isNewPrescriptionVersion && isABCClient()"
                                        setting-text="添加打印机"
                                        setting-icon="a-plus13px"
                                        @change="handleChangeDevice"
                                        @set="handleOpenAddPrinterDriverDialog"
                                    >
                                        <abc-option
                                            v-for="(it) in printerList"
                                            :key="it.deviceIndex"
                                            :value="it.deviceIndex"
                                            :label="it.name"
                                        >
                                            <img
                                                v-if="isTinyPrinter(it.deviceIndex)"
                                                class="print-preview-printer-img-icon"
                                                src="~assets/images/print/printer-tiny-icon.png"
                                                alt=""
                                            />
                                            <img
                                                v-else
                                                class="print-preview-printer-img-icon"
                                                src="~assets/images/print/printer-icon.png"
                                                alt=""
                                            />
                                            <span class="print-preview-printer-select-option-title">{{ it.name + (isTinyPrinter(it.deviceIndex) ? '（小票）' : '') }}</span>
                                            <span v-if="isNewPrescriptionVersion && it.offline" class="print-preview-printer-select-option-offline">脱机</span>
                                        </abc-option>
                                    </abc-select>
                                </div>
                                <!-- 纸张尺寸 -->
                                <div class="td select-input-padding-right-20" style="width: 78px;">
                                    <abc-select
                                        :key="`printer-select-key-${item.deviceIndex}-${printerListIsUpdate}`"
                                        v-model="item.pageSize"
                                        custom-class="print-page-list"
                                        placeholder="尺寸"
                                        :width="78"
                                        :inner-width="248"
                                        @change="handlePageSizeChange(item)"
                                    >
                                        <div v-for="(it, pageIndex) in getBusinessPageList(item)" :key="pageIndex">
                                            <li v-if="it.isElectronPage && !getBusinessPageList(item)[pageIndex - 1]?.isElectronPage" key="print-dialog-option-border" class="print-dialog-option-border"></li>
                                            <abc-option
                                                :value="it.paper.name"
                                                :label="it.paper.name"
                                            >
                                                {{ it.paper.name }}
                                                <span v-if="it.isRecommend" class="print-recommend-text">推荐</span>
                                            </abc-option>
                                        </div>
                                    </abc-select>
                                </div>
                                <!-- 打印方向 -->
                                <div
                                    class="td"
                                    style="width: 78px;"
                                >
                                    <abc-select
                                        v-if="heightLevelList(item)"
                                        v-model="item.pageHeightLevel"
                                        :width="77"
                                        placeholder="布局"
                                        class="spacing-right"
                                        :disabled="noConnectionPrint || disableHeightLevelOrOrient(item)"
                                    >
                                        <abc-option
                                            v-for="level in heightLevelList(item)"
                                            :key="level.key"
                                            :value="level.name"
                                            :label="level.name"
                                        ></abc-option>
                                    </abc-select>
                                    <abc-select
                                        v-else
                                        v-model="item.orient"
                                        placeholder="布局"
                                        :width="77"
                                        class="spacing-right"
                                        :disabled="noConnectionPrint || disableHeightLevelOrOrient(item)"
                                    >
                                        <abc-option
                                            v-for="orient in currentOrientations(item.pageSize)"
                                            :key="orient.value"
                                            :label="orient.label"
                                            :value="orient.value"
                                        ></abc-option>
                                    </abc-select>
                                </div>
                                <!-- 预览模式 -->
                                <div class="td" style="width: 78px;">
                                    <abc-select
                                        v-model="item.preview"
                                        :width="78"
                                        placeholder="预览"
                                    >
                                        <abc-option label="有预览" :value="1"></abc-option>
                                        <abc-option label="无预览" :value="0"></abc-option>
                                    </abc-select>
                                </div>
                                <!-- 打印份数 -->
                                <div class="td" style="width: 135px;">
                                    <print-count-input
                                        v-model="item.printCopies"
                                        :width="135"
                                    ></print-count-input>
                                </div>
                                <!-- 收费时同时打印 -->
                                <div class="td" style=" display: flex; align-items: center; justify-content: center; width: 163px;">
                                    <abc-switch
                                        :value="filterPrintOpts(item.label)"
                                        @change="(val) => changePrintOpts(item.label, val)"
                                    ></abc-switch>
                                </div>
                            </div>
                        </template>
                    </div>
                </abc-excel-table>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="saveLoading" :disabled="isSaveBtnDisabled" @click="ok">
                保存
            </abc-button>
            <abc-button :disabled="saveLoading" type="blank" @click="closeDialog">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import Clone from 'utils/clone';
    import ABCPrinterConfig from '../../config.js';
    import { FEE_SOCIAL } from '@/printer/constants';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import PrintManager from '@/printer/manager/print-manager.js';
    import Printer from 'views/print';
    import printConfigMixin from '@/printer/components/print-config-dialog/print-config-mixin';

    export default {
        name: 'PrintConfigHospitalCashierDialog',
        mixins: [printConfigMixin],
        props: {
            scene: {
                type: String,
                default: 'hospital-cashier',
            },
        },
        data() {
            return {
                printOpt: [], // 同时打印配置
            };
        },
        computed: {
            printConfigWithKey() {
                const sceneMap = {
                    'hospital-cashier': ['hospital-deposit-receipt', 'charge-list', 'fee-social', 'hospital-cashier'],
                };

                const currentScene = sceneMap[this.scene];
                if (!Array.isArray(currentScene)) {
                    console.error('当前场景值未定义');
                    return [];
                }

                return currentScene.map((key) => {
                    return this.allPrintConfig.find((it) => it.key === key);
                });
            },
        },
        created() {
            this.getPrintOpt();
        },
        methods: {
            closeDialog() {
                this.no();
            },
            getLabel(item) {
                if (item.key === 'treatment-execute') {
                    return getViewDistributeConfig().Print.treatmentExecuteLabel;
                }
                if (item.key === 'fee-bill') {
                    return '医疗纸质票据';
                }
                if (item.key === 'hospital-cashier') {
                    return '收费小票';
                }
                return item.label;

            },
            /**
             * @desc 获取纸张列表
             * @return
             */
            getBusinessPageList(item) {
                if (item.deviceIndex === -1) {
                    return [];
                }
                const { AbcTemplates } = window.AbcPackages;
                let key = this.formatCame(item.key);
                if (item.key === FEE_SOCIAL) {
                    key = this.formatCame(`medical-${FEE_SOCIAL}`);
                }
                // 增加纸张缓存机制
                if (!this._cache) {
                    this._cache = {};
                }
                const cacheKey = `${item.deviceName} ${JSON.stringify(AbcTemplates[key]?.pages ?? [])}`;
                if (this._cache[cacheKey] && !this.printerListIsUpdate) {
                    return this._cache[cacheKey];
                }
                const result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates[key]?.pages ?? []);
                this._cache[cacheKey] = result;
                return result;
            },
            async ok() {
                // 保存打印配置
                ABCPrinterConfig.setPrintConfig({
                    prescription: this.prescriptionConfig,
                    ticket: this.ticketConfig,
                    bill: this.billConfig,
                    inventory: this.inventoryConfig,
                    statistics: this.statisticsConfig,
                });

                // 保存同时打印配置
                const { cache } = Printer;
                cache.set({
                    hospitalCashier: this.printOpt,
                });

                this.visible = false;
            },
            printOptions() {
                return getViewDistributeConfig().Print.printOptions;
            },
            getPrintOpt() {
                const { cache } = Printer;
                const { hospitalCashier } = cache.get();
                this.printOpt = Clone(hospitalCashier);
            },
            filterPrintOpts(label = '') {
                if (!label) return false;
                if (label === '医疗收费票据' && this.printOpt.includes('打印收费票据')) {
                    return true;
                }
                return this.printOpt.includes(label);
            },
            changePrintOpts(label = '', val = false) {
                if (!label) return;
                if (val) {
                    this.printOpt.push(label);
                } else {
                    this.printOpt = this.printOpt.filter((opt) => opt !== label);
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';
@import "src/styles/mixin";

.print-config-hospital-cashier-dialog {
    .abc-dialog-body {
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .print-setting-table {
        .table-header {
            .th {
                &:not(:last-child) {
                    border-right: 1px solid $P6 !important;
                }
            }
        }

        .table-body {
            .tr {
                & + .tr {
                    border-top: 1px solid $P6 !important;
                }

                .td {
                    &:not(:last-child) {
                        border-right: 1px solid $P6 !important;
                    }

                    .medicine-tag-container {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0 10px;

                        &:hover {
                            .medicine-tag-setting {
                                display: flex;
                            }
                        }

                        .medicine-tag-setting {
                            position: absolute;
                            display: none;
                            align-items: center;
                            justify-content: center;
                            width: calc(100% - 20px);
                            height: 100%;
                            background-color: $S2;
                            opacity: 0.89;
                        }
                    }

                    .label-container {
                        display: flex;
                        align-items: center;
                        height: 40px;
                    }
                }

                .select-input-padding-right-20 {
                    .abc-input__inner {
                        padding-right: 20px !important;
                    }
                }
            }
        }
    }

    .dialog-content {
        .auto-print-config {
            margin-top: 24px;

            .select-container {
                display: flex;

                .title {
                    width: 100px;
                    margin-right: 24px;
                }
            }

            .select-pharmacy-container {
                margin-top: 16px;

                .title {
                    float: left;
                    width: 100px;
                    margin-right: 24px;
                }

                .pharmacy-checkbox {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 12px 18px;
                    float: right;
                    width: calc(100% - 124px);

                    .pharmacy-check-box {
                        width: 109px !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        overflow: hidden !important;

                        .abc-checkbox__label {
                            @include ellipsis;
                        }
                    }
                }
            }
        }
    }

    .dialog-footer {
        position: relative;

        .print-tips {
            position: absolute;
            left: 0;
            font-size: 12px;
            line-height: 16px;
            color: $T3;
        }
    }

    .no-install-print {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        margin: 0 auto 16px auto;
        font-size: 16px;
        color: $Y2;
        background-color: $Y4;
        border: 1px solid $Y5;
        border-radius: var(--abc-border-radius-small);

        i {
            color: $Y2;
        }

        span {
            color: $theme2;
            cursor: pointer;
        }

        ul {
            margin: 10px;
        }

        li {
            line-height: 24px;
            color: $T2;
        }
    }

    .installed-print {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        margin: 12px auto;
        font-size: 16px;
        color: $G2;
        background-color: $G4;
        border: 1px solid $G5;
        border-radius: var(--abc-border-radius-small);
    }

    .print-setting-item {
        display: flex;
        align-items: center;
        margin: 12px 0;

        .label {
            width: 100px;
        }

        .abc-select-wrapper,
        .abc-input-wrapper {
            margin-left: 12px;
        }

        .print-copies-unit {
            margin-left: 4px;
        }
    }

    .print-count-select {
        .count-icon {
            position: absolute;
            top: 50%;
            left: 18px;
            z-index: 2;
            margin-top: -7px;
        }
    }
}
</style>
