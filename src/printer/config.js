import Clone from 'utils/clone';
import LocalStorage from 'utils/localStorage-handler';
import { DuplexMode } from '@/printer/utils/electron';
import { isNull } from '@/utils';

const PRINT_CONFIG_KEY = '_print_config_v2_';
const PRINT_MODE_KEY = 'print_mode_key';

export const PrintPageRange = {
    All: 'all',
    Specify: 'specify',
    SpecifyRange: 'specify-range',
    SelectPage: 'select-page',
    MedicalDocumentContinue: 'medical-document-continue',
};

export const DefaultPrintRangeConfig = {
    range: PrintPageRange.All,
    start: null,
    end: null,
    specify: 1,
    selectPage: [],
};

export const PaperOrientation = {
    Portrait: 1,
    Landscape: 2,
};

export const PaperOrientationOptions = [
    {
        label: '竖向打印',
        value: PaperOrientation.Portrait,
        icon: 'a-n-shuxiang-line',
    },
    {
        label: '横向打印',
        value: PaperOrientation.Landscape,
        icon: 'n-hengxiang-line',
    },
];

export const AdvancePrintAdvanceConfig = {
    // 双面打印
    duplexMode: DuplexMode.Simplex,
    // 打印范围
    printRange: Clone(DefaultPrintRangeConfig),
    printRangeEnable: 0,
    // 打印纸张方向
    // 指的时放纸方向
    // 默认为正常放纸
    paperOrientation: PaperOrientation.Portrait,
    paperOrientationEnable: 0,
};

export const PrinterUseColor = {
    isColor: 0, // 0:黑白 1:彩色
};

const DEFAULT_PRINT_CONFIG = {
    label: '',
    key: '',
    deviceIndex: -1,
    deviceName: '',
    orient: 1,
    pageSize: '',
    driveName: '',
    pageSizeWidth: '',
    printCopies: 1,
    preview: 1,
    pageHeightLevel: null,
    pageSizeReduce: {
        bottom: 2,
        left: 2,
        right: 2,
        top: 2,
    },
};

/**
 * 打印 - 针对各模块 的打印机配置设置
 *
 老数据兼容
 客户端打印机配置
 _prescription_print_config_ 处方打印配置
 _medical_print_config_ 病历打印配置
 _infusion_execute_print_config_ 输注单
 _treatment_execute_print_config_ 治疗理疗单
 _examination_print_config_ 检查检验单
 _medical_certificate_print_config_ 病情证明书
 _chronic_care_print_config_ 慢病管理
 _a5_dispensing_print_config_ A5 发药单

 lodop 打印机配置
 _print_config_
 */

export const GLOBAL_PAGE_REDUCER = {
    bottom: 4,
    left: 4,
    right: 4,
    top: 4,
};

export const GLOBAL_PAGE_LARGE_REDUCER = {
    bottom: 15,
    left: 15,
    right: 15,
    top: 15,
};

export const GLOBAL_PAGE_SMALL_REDUCER = {
    bottom: 10,
    left: 10,
    right: 10,
    top: 10,
};

export const GLOBAL_SETTLEMENT_PAGE_REDUCER = {
    bottom: 4,
    left: 4,
    right: 4,
    top: 8,
};

export const NO_PAGE_REDUCER = {
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
};

export const GLOBAL_OFFSET = {
    left: 0,
    top: 0,
};

export default class ABCPrinterConfig {
    // 医疗文书
    static prescription = [
        {
            label: '病历',
            key: 'medical-record',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '处方',
            key: 'prescription',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '处方',
            key: 'prescriptionV2',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                bottom: 7,
                left: 11,
                right: 11,
                top: 7,
            },
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '治疗理疗单',
            key: 'treatment-execute',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '输液注射单',
            key: 'infusion-execute',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查检验单',
            key: 'examination',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查单',
            key: 'examine',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检验单',
            key: 'examinationInspect',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查检验申请单',
            key: 'examination-apply-sheet',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查申请单',
            key: 'examine-apply-sheet',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检验申请单',
            key: 'examination-inspect-apply-sheet',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检验报告',
            key: 'examination-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,

        },
        {
            label: '检验报告',
            key: 'examination-report-pdf',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                bottom: 0,
                left: 0,
                right: 0,
                top: 0,
            },
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检验报告',
            key: 'examination-cloud-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_SMALL_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检验报告',
            key: 'examination-cloud-intelligent-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_SMALL_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
        },
        {
            label: '检查报告',
            key: 'inspect-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查报告',
            key: 'eye-inspect-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查报告书',
            key: 'eye-inspect-report-custom',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查报告',
            key: 'hospital-inspect',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '诊断证明书',
            key: 'medical-certificate',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '慢病报告',
            key: 'chronic-care',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '健康报告',
            key: 'child-healthy-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '儿保测试题',
            key: 'child-healthy-tests',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '儿保测试题结果',
            key: 'child-healthy-tests-result',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '医疗文书',
            key: 'hospital-medical-document',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_LARGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                printRangeEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '中西成药处方',
            key: 'tianjin-western-prescription',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: NO_PAGE_REDUCER,
            offset: {
                top: -4,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '中药处方',
            key: 'tianjin-chinese-prescription',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: NO_PAGE_REDUCER,
            offset: {
                top: -4,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        // 医院护士医嘱单
        {
            label: '医嘱执行单',
            key: 'hospital-nurse-prescription',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_SMALL_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                printRangeEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '体温图',
            key: 'temperature-graph',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: NO_PAGE_REDUCER,
            offset: {
                top: -4,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '体温单',
            key: 'temperature-table',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            offset: {
                top: -4,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '血糖单',
            key: 'blood-sugar',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '收费清单',
            key: 'charge-list',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '批量收费清单',
            key: 'charge-list-batch',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        // 医院医生医嘱单
        {
            label: '医嘱单',
            key: 'hospital-doctor-medical-prescription',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_SMALL_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                printRangeEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        // 医院医生山东定制医嘱单
        {
            label: '医嘱单',
            key: 'hospital-advice-shandong',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                bottom: 10,
                left: 8,
                right: 8,
                top: 10,
            },
            offset: {
                top: 0,
                left: 0,
            },
            advance: Clone(AdvancePrintAdvanceConfig),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        // 医院护士领药单
        {
            label: '领药单',
            key: 'hospital-nurse-dispensing',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_SMALL_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                printRangeEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '体检个人报告',
            key: 'pe-individual-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_LARGE_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '团检报告',
            key: 'pe-group-report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                bottom: 10,
                left: 18,
                right: 12,
                top: 10,
            },
            offset: {
                top: 0,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '体检个人报告（单独打印）',
            key: 'pe-individual-report-single',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_LARGE_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                printRangeEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '导诊单',
            key: 'pe-guide-sheet',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_SMALL_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        // 住院处方
        {
            label: '处方',
            key: 'hospital-prescription',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            advance: Object.assign(Clone(AdvancePrintAdvanceConfig), {
                paperOrientationEnable: 1,
            }),
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '交班记录',
            key: 'charge-shift',
            deviceIndex: -1,
            deviceName: '',
            orient: 2,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '住院证',
            key: 'hospitalization-certificate',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_SMALL_REDUCER,
            offset: {
                top: 0,
                left: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '体检费用清单',
            key: 'pe-charge-fee-list',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '煎药工艺卡',
            key: 'decoction-craft-card',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '家庭医生签约协议',
            key: 'family-doctor-agreement',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                bottom: 0,
                left: 0,
                right: 0,
                top: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
    ];
    // 小票标签
    static ticket = [
        {
            label: '挂号单',
            key: 'registration',
            deviceIndex: -1,
            deviceName: '',
            driveName: '',
            orient: 1,
            pageSize: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '挂号标签',
            key: 'registration-tag',
            deviceIndex: -1,
            deviceName: '',
            driveName: '',
            orient: 1,
            pageSize: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '收费小票',
            key: 'cashier',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '新版药店收费小票',
            key: 'pharmacy-cashier-v2',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: Clone(GLOBAL_PAGE_REDUCER),
            printerUseColor: { isColor: 1 },
            dpi: null,
            isThermalPrinter: true, // 是否是热敏打印机 true:热敏小票打印机 false:针式小票打印机
        },
        // 住院收费小票
        {
            label: '住院收费小票',
            key: 'hospital-cashier',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 2,
                top: 2,
                bottom: 2,
                right: 2,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '发药小票',
            key: 'dispense',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '收据',
            key: 'receipt',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '退药小票',
            key: 'undispense',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '用药标签',
            key: 'medicine-tag',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printCountData: {
                // 口服
                oral: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 注射
                injections: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 外用
                external: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 输液
                infusion: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 雾化
                atomization: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 煎服
                decoction: {
                    printCopies: 1,
                    isPrint: 1,
                },
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        // 瓶贴
        {
            label: '用药标签',
            key: 'hospital-medicine-tag',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printCountData: {
                // 口服
                oral: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 注射
                injections: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 外用
                external: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 输液
                infusion: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 雾化
                atomization: {
                    printCopies: 1,
                    isPrint: 1,
                },
                // 煎服
                decoction: {
                    printCopies: 1,
                    isPrint: 1,
                },
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '患者标签',
            key: 'patient-tag',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '价签',
            key: 'price-tag',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '样本条码',
            key: 'examination-tag',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检验标签',
            key: 'examination-label',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        // 医院检验条码
        {
            label: '样本条码',
            key: 'hospital-examination-tag',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '收费告知书',
            key: 'charge-notification',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '检查标签',
            key: 'inspect-label',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '执行凭证',
            key: 'execute-certificate',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 2,
                top: 2,
                bottom: 2,
                right: 2,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '积分抵扣单',
            key: 'points-bill',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '药店收费小票',
            key: 'pharmacy-cashier',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
        },
    ];
    // 医疗票据
    static bill = [
        {
            label: '医疗收费票据',
            key: 'fee-bill',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '医疗收费清单',
            key: 'fee-list',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '医保结算单',
            key: 'fee-social',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 0,
                top: 0,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '押金收据',
            key: 'hospital-deposit-receipt',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_LARGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '医保结算单',
            key: 'fee-social-diwei-default', // 本地结算单
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 20,
                top: 12,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '医保结算单',
            key: 'fee-social-diwei-prov', // 省医保结算单
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: {
                left: 12,
                top: 12,
                bottom: 0,
                right: 0,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
    ];
    // 库存单据
    static inventory = [
        {
            label: '入库单',
            key: 'goods-in',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '出库单',
            key: 'goods-out',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '调拨单',
            key: 'goods-trans',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '盘点单',
            key: 'goods-check',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '采购单',
            key: 'goods-purchase',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '领用单',
            key: 'goods-apply',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '结算单',
            key: 'settlement-application',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_SETTLEMENT_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '结算明细',
            key: 'settlement-review',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_SETTLEMENT_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '付款明细',
            key: 'settlement-detail',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
    ];
    // 统计报表
    static statistics = [
        {
            label: '统计报表',
            key: 'report',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '影像图片',
            key: 'viewer',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: NO_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
        {
            label: '对账信息',
            key: 'pharmacy-reconciliation-info',
            deviceIndex: -1,
            deviceName: '',
            orient: 1,
            pageSize: '',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 1,
            pageHeightLevel: null,
            pageSizeReduce: GLOBAL_SETTLEMENT_PAGE_REDUCER,
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
    ];

    static orderCloud = [
        {
            label: '发货单',
            key: 'shipment',
            deviceIndex: -1,
            deviceName: '',
            orient: 2,
            pageSize: 'A5',
            driveName: '',
            pageSizeWidth: '',
            printCopies: 1,
            preview: 0,
            pageHeightLevel: null,
            pageSizeReduce: {
                bottom: 6,
                left: 8,
                right: 8,
                top: 6,
            },
            printerUseColor: Clone(PrinterUseColor),
            dpi: null,
        },
    ];

    static printLodopList = ['fee-bill', 'fee-list'];
    static printElectronList = ['medical-record'];

    // 打印配置迁移
    static migration() {
        const printConfigKeys = ['prescription', 'ticket', 'bill', 'inventory', 'statistics'];
        const printConfig = LocalStorage.get(PRINT_CONFIG_KEY, true);
        if (printConfig) {
            // 以前只有检查检验单的打印配置,现在将其拆分为了检查单和检验单,需要将以前的检查检验单的配置迁移到检查单和检验单
            // 旧的 检验检查单
            const oldExamination = printConfig.prescription.find((item) => item.key === 'examination');
            // 旧的 检验检查申请单单
            const oldExaminationApplySheet = printConfig.prescription.find((item) => item.key === 'examination-apply-sheet');
            // 新的 检查单
            const newExamine = printConfig.prescription.find((item) => item.key === 'examine');
            // 新的 检查申请单
            const newExamineApplySheet = printConfig.prescription.find((item) => item.key === 'examine-apply-sheet');
            // 新的 检验单
            const newExaminationInspect = printConfig.prescription.find((item) => item.key === 'examinationInspect');
            // 新的 检验申请单
            const newExaminationInspectApplySheet = printConfig.prescription.find((item) => item.key === 'examination-inspect-apply-sheet');

            // 处理瓶贴预览问题
            const hospitalMedicineTag = printConfig.ticket.find((item) => item.key === 'hospital-medicine-tag');

            if (hospitalMedicineTag && hospitalMedicineTag.preview === 0) {
                hospitalMedicineTag.preview = 1;
            }

            if (oldExamination && oldExamination.deviceIndex > -1) {
                if (!newExamine) {
                    const cacheExamination = Clone(oldExamination);
                    cacheExamination.label = '检查单';
                    cacheExamination.key = 'examine';
                    printConfig.prescription.push(cacheExamination);
                }
                if (!newExaminationInspect) {
                    const cacheExamination = Clone(oldExamination);
                    cacheExamination.label = '检验单';
                    cacheExamination.key = 'examinationInspect';
                    printConfig.prescription.push(cacheExamination);
                }
            }
            if (oldExaminationApplySheet && oldExaminationApplySheet.deviceIndex > -1) {
                if (!newExamineApplySheet) {
                    const cacheExamination = Clone(oldExaminationApplySheet);
                    cacheExamination.label = '检查申请单';
                    cacheExamination.key = 'examine-apply-sheet';
                    printConfig.prescription.push(cacheExamination);
                }
                if (!newExaminationInspectApplySheet) {
                    const cacheExamination = Clone(oldExaminationApplySheet);
                    cacheExamination.label = '检验申请单';
                    cacheExamination.key = 'examination-inspect-apply-sheet';
                    printConfig.prescription.push(cacheExamination);
                }
            }

            printConfigKeys.forEach((key) => {
                if (Array.isArray(printConfig[key])) {
                    ABCPrinterConfig[key].forEach((item) => {
                        const findItem = printConfig[key].find((config) => config.key === item.key);
                        if (!findItem) {
                            printConfig[key].push(item);
                        } else {
                            // 如果增加了新的配置项,需要将新的配置项的值赋值给本地的配置
                            // 每次新增就不要在这里去做迁移了
                            for (const key of Object.keys(item)) {
                                if (findItem[key] === undefined) {
                                    findItem[key] = item[key];
                                }
                            }
                        }
                    });
                } else {
                    // 如果本地没有整个配置
                    // 重新赋值
                    printConfig[key] = ABCPrinterConfig[key];
                }
            });

            LocalStorage.set(PRINT_CONFIG_KEY, printConfig);
        }
    }

    static initPrintConfig() {
        const printConfig = LocalStorage.get(PRINT_CONFIG_KEY, true);
        if (printConfig) {
            ABCPrinterConfig.prescription = printConfig.prescription;
            ABCPrinterConfig.ticket = printConfig.ticket;
            ABCPrinterConfig.bill = printConfig.bill;
            ABCPrinterConfig.inventory = printConfig.inventory;
            ABCPrinterConfig.statistics = printConfig.statistics;
        }
    }

    static setPrintConfig(data, key, subKey) {
        // key存在 只更新局部
        if (key) {
            // 更新局部key
            if (subKey) {
                ABCPrinterConfig[key].forEach((item) => {
                    if (item.key === subKey) {
                        Object.assign(item, data);
                    }
                });
            } else {
                ABCPrinterConfig[key] = data;
            }
        } else {
            const {
                prescription,
                ticket,
                bill,
                inventory,
                statistics,
            } = data;
            ABCPrinterConfig.prescription = prescription || ABCPrinterConfig.prescription;
            ABCPrinterConfig.ticket = ticket || ABCPrinterConfig.ticket;
            ABCPrinterConfig.bill = bill || ABCPrinterConfig.bill;
            ABCPrinterConfig.inventory = inventory || ABCPrinterConfig.inventory;
            ABCPrinterConfig.statistics = statistics || ABCPrinterConfig.statistics;
        }
        LocalStorage.set(PRINT_CONFIG_KEY, {
            prescription: ABCPrinterConfig.prescription,
            ticket: ABCPrinterConfig.ticket,
            bill: ABCPrinterConfig.bill,
            inventory: ABCPrinterConfig.inventory,
            statistics: ABCPrinterConfig.statistics,
        });
    }

    static getPrintConfigByKey(keyObj) {
        if (!keyObj) {
            console.error(
                'ABCPrintConfigKeyMap(src/printer/constants) 中未定义该key',
            );
            return DEFAULT_PRINT_CONFIG;
        }
        const printConfig = ABCPrinterConfig[keyObj.key].find(
            (item) => item.key === keyObj.subKey,
        );
        if (!printConfig) {
            console.error(`${keyObj}, ABCPrinterConfig 中未找到该配置`);
            return;
        }
        return printConfig;
    }

    static initPrintModeConfig() {
        const printModeConfig = LocalStorage.get(PRINT_MODE_KEY, true);
        if (printModeConfig) {
            ABCPrinterConfig.printLodopList = printModeConfig.printLodopList || [];
            ABCPrinterConfig.printElectronList = printModeConfig.printElectronList || [];
        } else {
            LocalStorage.set(PRINT_MODE_KEY, {
                printLodopList: ABCPrinterConfig.printLodopList,
                printElectronList: ABCPrinterConfig.printElectronList,
            });
        }
    }

    /**
     * 添加打印模式
     * @param {string} printConfigKey 单据key
     * @param {0 | 1} mode 0:Lodop打印 1:客户端打印
     */
    static addPrintModeConfig(printConfigKey, mode) {
        if (!printConfigKey || isNull(mode)) return;
        const addList = mode === 0 ? ABCPrinterConfig.printLodopList : ABCPrinterConfig.printElectronList;
        const removeList = mode === 0 ? ABCPrinterConfig.printElectronList : ABCPrinterConfig.printLodopList;
        const removeIdx = removeList.indexOf(printConfigKey);
        if (removeIdx > -1) {
            removeList.splice(removeIdx, 1);
        }
        const addIdx = addList.indexOf(printConfigKey);
        if (addIdx === -1) {
            addList.push(printConfigKey);
        }
        LocalStorage.set(PRINT_MODE_KEY, {
            printLodopList: ABCPrinterConfig.printLodopList,
            printElectronList: ABCPrinterConfig.printElectronList,
        });
    }

    /**
     * 删除打印模式
     * @param {string} printConfigKey 单据key
     */
    static removePrintModeConfig(printConfigKey) {
        const lodopIdx = ABCPrinterConfig.printLodopList.indexOf(printConfigKey);
        if (lodopIdx > -1) {
            ABCPrinterConfig.printLodopList.splice(lodopIdx, 1);
        }
        const electronIdx = ABCPrinterConfig.printElectronList.indexOf(printConfigKey);
        if (electronIdx > -1) {
            ABCPrinterConfig.printElectronList.splice(electronIdx, 1);
        }
        LocalStorage.set(PRINT_MODE_KEY, {
            printLodopList: ABCPrinterConfig.printLodopList,
            printElectronList: ABCPrinterConfig.printElectronList,
        });
    }
}

ABCPrinterConfig.migration();
ABCPrinterConfig.initPrintConfig();
ABCPrinterConfig.initPrintModeConfig();
