import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class PurchaseTableConfig extends BaseClinicTypeTable {
    constructor(clinic) {
        super(clinic);
        this.chainTableConfig = {
            'list': [{
                'label': '验收日期',
                'style': {
                    'flex': '1','width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'inspectTime',
                'sortable': false,
                'pinned': true,
            },{
                'label': '门店名称',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'clinicName',
                'pinned': true,
            },{
                'label': '商品编码',
                'key': 'shortId',
                'style': {
                    'flex': '','width': '100px','maxWidth': '120px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'pinned': true,
            },{
                'label': '商品名称',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'displayName',
                'pinned': true,
            },{
                'label': '规格',
                'key': 'displaySpec',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': true,
            },{
                'label': '厂家/产地',
                'key': 'manufacturerFull',
                'style': {
                    'flex': '1','width': '260px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '剂型',
                'key': 'dosageFormTypeName',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': false,
            },{
                'label': '到货数量',
                'key': 'receivePackageCount',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收数量',
                'key': 'inspectPackageCount',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '合格数量',
                'key': 'qualifiedPackageCount',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': true,
            },{
                'label': '进价',
                'key': 'packagePrice',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '验收结果',
                'key': 'inspectStatus',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '供应商',
                'key': 'supplierName',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收人',
                'key': 'inspector',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产日期',
                'key': 'productionDate',
                'style': {
                    'flex': 1,'width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有效日期',
                'key': 'expiryDate',
                'style': {
                    'flex': 1,'width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '到货日期',
                'key': 'receiveTime',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '批准文号',
                'key': 'medicineNmpn',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '上市许可持有人/器械注册人',
                'key': 'mha',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收单号',
                'key': 'orderNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '收货单号',
                'key': 'receiveOrderNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'show': true,
            }],
        };
        this.chainSubTableConfig = {
            'list': [{
                'label': '验收日期',
                'style': {
                    'flex': '1','width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'inspectTime',
                'sortable': false,
                'pinned': true,
            },{
                'label': '商品编码',
                'key': 'shortId',
                'style': {
                    'flex': '','width': '100px','maxWidth': '120px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'pinned': true,
            },{
                'label': '商品名称',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'displayName',
                'pinned': true,
            },{
                'label': '规格',
                'key': 'displaySpec',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': true,
            },{
                'label': '厂家/产地',
                'key': 'manufacturerFull',
                'style': {
                    'flex': '1','width': '260px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '剂型',
                'key': 'dosageFormTypeName',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': false,
            },{
                'label': '到货数量',
                'key': 'receivePackageCount',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收数量',
                'key': 'inspectPackageCount',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '合格数量',
                'key': 'qualifiedPackageCount',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': true,
            },{
                'label': '进价',
                'key': 'packagePrice',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '验收结果',
                'key': 'inspectStatus',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '供应商',
                'key': 'supplierName',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收人',
                'key': 'inspector',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产日期',
                'key': 'productionDate',
                'style': {
                    'flex': 1,'width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有效日期',
                'key': 'expiryDate',
                'style': {
                    'flex': 1,'width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '到货日期',
                'key': 'receiveTime',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '批准文号',
                'key': 'medicineNmpn',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '上市许可持有人/器械注册人',
                'key': 'mha',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收单号',
                'key': 'orderNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '收货单号',
                'key': 'receiveOrderNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'show': true,
            }],
        };
        this.singleTableConfig = {
            'list': [{
                'label': '验收日期',
                'style': {
                    'flex': '1','width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'inspectTime',
                'sortable': false,
                'pinned': true,
            },{
                'label': '商品编码',
                'key': 'shortId',
                'style': {
                    'flex': '','width': '100px','maxWidth': '120px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'pinned': true,
            },{
                'label': '商品名称',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'displayName',
                'pinned': true,
            },{
                'label': '规格',
                'key': 'displaySpec',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': true,
            },{
                'label': '厂家/产地',
                'key': 'manufacturerFull',
                'style': {
                    'flex': '1','width': '260px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '剂型',
                'key': 'dosageFormTypeName',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产批号',
                'key': 'batchNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': false,
            },{
                'label': '到货数量',
                'key': 'receivePackageCount',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收数量',
                'key': 'inspectPackageCount',
                'style': {
                    'flex': '1','width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '合格数量',
                'key': 'qualifiedPackageCount',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': true,
            },{
                'label': '进价',
                'key': 'packagePrice',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '验收结果',
                'key': 'inspectStatus',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '供应商',
                'key': 'supplierName',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收人',
                'key': 'inspector',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '生产日期',
                'key': 'productionDate',
                'style': {
                    'flex': 1,'width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有效日期',
                'key': 'expiryDate',
                'style': {
                    'flex': 1,'width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '到货日期',
                'key': 'receiveTime',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '批准文号',
                'key': 'medicineNmpn',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '上市许可持有人/器械注册人',
                'key': 'mha',
                'style': {
                    'flex': '1','width': '248px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '验收单号',
                'key': 'orderNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '收货单号',
                'key': 'receiveOrderNo',
                'style': {
                    'flex': '1','width': '148px','maxWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'show': true,
            }],
        };
    }
}
