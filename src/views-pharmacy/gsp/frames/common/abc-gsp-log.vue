<template>
    <div class="abc-gsp-logs">
        <abc-space>
            <abc-p v-if="content.createdTime">
                {{ formatDate(content.createdTime, 'YYYY-MM-DD HH:mm') }}
            </abc-p>
            <abc-p v-if="content.operatorName || content.action">
                {{ content.operatorName || '' }}{{ content.action || '' }}
            </abc-p>
            <abc-p v-if="content.comment" gray>
                {{ content.comment || '' }}
            </abc-p>
        </abc-space>
    </div>
</template>

<script>
    import { formatDate } from '@abc/utils-date';
    export default {
        name: 'AbcGspLog',
        props: {
            content: {
                type: Object,
                default: () => {
                    return {};
                },
            },
        },
        methods: {
            formatDate,
        },
    };
</script>
