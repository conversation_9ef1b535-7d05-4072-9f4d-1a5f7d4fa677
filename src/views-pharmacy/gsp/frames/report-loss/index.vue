<template>
    <abc-card :border="false" style="height: 100%;" shadow>
        <goods-out></goods-out>
    </abc-card>
</template>

<script>
    import GoodsOut from '@/views-pharmacy/inventory/frames/report-loss/index.vue';
    export default {
        name: 'PharmacyGspReportLoss',
        components: {
            GoodsOut,
        },
    };
</script>
<style lang="scss">
.abc-pharmacy-gsp-report-loss-container {
    .tabs-content {
        padding: var(--abc-layout-content-padding);

        .handle-bar {
            display: flex;
            width: 100%;
            height: auto;
            padding-top: 0;
            margin-bottom: 16px;

            .right-part {
                display: inline-block;
                margin-left: auto;
            }
        }
    }
}
</style>
