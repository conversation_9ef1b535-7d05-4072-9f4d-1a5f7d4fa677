<template>
    <div class="pharmacy__first-battalion__supplier__view-info">
        <abc-descriptions
            :column="10"
            :label-width="88"
            size="large"
            grid
            class="base-info"
            background
        >
            <abc-descriptions-item label="申请机构" :span="5">
                <div v-abc-title.ellipsis="gsp.applyOrganName"></div>
            </abc-descriptions-item>
            <abc-descriptions-item label="申请时间" :span="3">
                <div class="show-text-box">
                    <abc-text-tips :content="gsp.applyTime?.slice(0, 16)" :width="128"></abc-text-tips>
                </div>
            </abc-descriptions-item>
            <abc-descriptions-item label="申请人员" :span="2">
                <div class="show-text-box">
                    <abc-text-tips :content="gsp.applyUserName" :width="68"></abc-text-tips>
                </div>
            </abc-descriptions-item>
            <abc-descriptions-item label="备注" :span="10">
                <div class="show-text-box">
                    <abc-text-tips :content="gsp.remark" :width="710"></abc-text-tips>
                </div>
            </abc-descriptions-item>
        </abc-descriptions>
        <abc-form class="supplier-info">
            <template v-for="item in dataList">
                <h5
                    v-if="item.type === 'TITLE'"
                    :key="item.title"
                >
                    {{ item.title }}
                </h5>
                <div
                    v-else-if="item.type === 'CERT_LIST'"
                    :key="item.label"
                    :class="[`form-item-${item.type}`]"
                >
                    <cert-item
                        v-for="(cert, index) in item.value"
                        :key="index"
                        :item="cert"
                        :width="261"
                        disabled
                    ></cert-item>
                </div>
                <abc-form-item
                    v-else
                    :key="item.label"
                    :label="item.label"
                >
                    <abc-textarea
                        v-if="item.type === 'TEXTAREA'"
                        disabled
                        :width="item.width || 831"
                        :value="item.value"
                        :rows="item.rows || 3"
                        :placeholder="''"
                    ></abc-textarea>
                    <abc-input
                        v-else
                        disabled
                        :width="item.width || 261"
                        :value="item.value"
                        :title="item.value"
                    ></abc-input>
                </abc-form-item>
            </template>
        </abc-form>
    </div>
</template>

<script>
    import CertItem from './cert-item.vue';

    import * as tools from '@/views-pharmacy/common/tools';
    import { formatAddress } from '@/utils';
    import useBusinessScope from 'views/inventory/goods/archives/hook/useBusinessScope';
    import { CatalogueEnum } from '@/hooks/business/use-dictionary';

    export default {
        components: {
            CertItem,
        },
        props: {
            // 供应商详情
            supplierDetail: {
                type: Object,
                default: () => {},
            },
            // gsp审批详情
            gspDetail: {
                type: Object,
                default: null,
            },
            // 是否是修改审批单
            isApprovalForm: Boolean,
        },
        setup() {
            const {
                init,
                getBusinessScopeName,
            } = useBusinessScope(CatalogueEnum.BUSINESS_SCOPE_SUPPLIER);
            return {
                init,
                getBusinessScopeName,
            };
        },
        data() {
            return {
                infoConfig: [],
                dataList: [],
            };
        },
        computed: {
            // 基础信息
            gsp() {
                return this.supplierInfo?.gsp || {};
            },
            // 证照列表
            certificationInfos() {
                return (this.supplierInfo?.extendInfo?.certificationInfos || []).filter((one) => (one.pictureUrls || [])[0]?.url);
            },
            supplierSnapshotInfo() {
                if (this.gspDetail) {
                    const {
                        variables, businessId,
                    } = this.gspDetail;

                    return variables?.[businessId]?.snapshot ?? {};
                }
                return {};
            },
            modifySupplier() {
                if (this.gspDetail) {
                    const {
                        variables, businessId,
                    } = this.gspDetail;

                    return variables?.[businessId]?.modifyData ?? {};
                }
                return {};
            },
            supplierInfo() {
                if (this.isApprovalForm) {
                    return {
                        ...this.supplierSnapshotInfo,// 当时快照数据
                        ...this.modifySupplier,// 当时修改数据
                        gsp: {
                            ...this.supplierSnapshotInfo.gsp,
                            ...this.modifySupplier.gsp,
                        },
                    };
                }
                return this.supplierDetail;
            },
        },
        watch: {
            supplierInfo: {
                async handler() {
                    await this.init();

                    this.infoConfig = this.createInfoConfig();
                    this.dataList = tools.createInfoListByConfig(this.supplierInfo, this.infoConfig);
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            /**
             * 创建信息配置
             * <AUTHOR>
             * @date 2024-01-16
             * @returns {Array}
             */
            createInfoConfig() {
                const infoConfig = [
                    {
                        type: 'TITLE',
                        title: '供应商信息',
                    },
                    {
                        label: '供应商名称',
                        key: 'name',
                    },
                    {
                        label: '类型',
                        key: 'companyType',
                        format: (value) => tools.getSupplierTypeWording(value),
                    },
                    {
                        label: '法人代表',
                        key: 'legalRepresentative',
                    },
                    {
                        label: '社会信用统─代码',
                        key: 'unifiedSocialCreditIdentifier',
                    },
                    {
                        label: '经营范围',
                        key: 'businessScope',
                        format: (value) => this.getBusinessScopeName(value?.businessScopeList ?? []),
                    },
                    {
                        label: '联系人',
                        key: 'personInChargeOfEnterprise',
                    },
                    {
                        label: '联系电话',
                        key: 'mobile',
                    },
                    {
                        label: '联系地址',
                        key: 'registeredAddressDetail',
                        format: (value) => {
                            return value && formatAddress(value) || '';
                        },
                    },
                    {
                        label: '开户银行',
                        key: 'openingBank',
                    },
                    {
                        label: '银行账户',
                        key: 'bankAccount',
                    },
                    {
                        type: 'TITLE',
                        title: '销售员信息',
                    },
                    {
                        label: '姓名',
                        key: 'principal',
                    },
                    {
                        label: '电话',
                        key: 'principalTelephone',
                    },
                    {
                        label: '身份证号',
                        key: 'principalIdCard',
                    },
                    {
                        type: 'TITLE',
                        title: '资质证照',
                        show: this.certificationInfos.length !== 0,
                    },
                    {
                        type: 'CERT_LIST',
                        value: 'certificationInfos',
                        label: '资质证照列表',
                        format: () => this.certificationInfos,
                    },
                    {
                        type: 'TITLE',
                        title: '质量体系情况评价',
                    },
                    {
                        type: 'TEXTAREA',
                        value: 'evaluateContent',
                        label: '',
                        rows: 6,
                        format: (value, item, target) => target?.gsp?.evaluateContent || '',
                    },
                ];
                return infoConfig.filter((item) => item.show !== false);
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .pharmacy__first-battalion__supplier__view-info {
        .base-info {
            box-sizing: border-box;
            width: 832px;
            margin-left: 24px;
        }

        .supplier-info {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-start;
            margin-top: 24px;

            > h5 {
                width: 100%;
                margin-bottom: 12px;
                margin-left: 24px;
                font-size: 16px;
                line-height: 24px;
                color: $T1;
            }

            .abc-form-item {
                margin: 0 0 16px 24px;
            }

            .form-item-CERT_LIST {
                display: flex;
                flex-wrap: wrap;
                width: 856px;
                margin-bottom: 8px;

                .pharmacy__first-battalion__supplier__cert-item {
                    margin: 0 0 16px 24px;
                }
            }
        }

        .show-text-box {
            height: 20px;
        }
    }
</style>
