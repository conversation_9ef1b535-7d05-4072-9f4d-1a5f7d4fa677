import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class WorkerHealthTableConfig extends BaseClinicTypeTable {
    constructor(props) {
        super(props);
        this.chainTableConfig = {
            'list': [{
                'label': '门店名称',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'clinicName',
                'show': true,
            },{
                'label': '姓名',
                'key': 'employeeName',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '性别',
                'key': 'sex',
                'style': {
                    'flex': '','width': '48px','maxWidth': '48px','minWidth': '48px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '年龄',
                'key': 'age',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '',
            },{
                'label': '岗位',
                'key': 'work',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有无健康证',
                'key': 'healthCertificate',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检日期',
                'key': 'physicalExaminationDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检机构',
                'key': 'physicalExaminationInstitution',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检项目',
                'key': 'physicalExaminationProject',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检结果',
                'key': 'physicalExaminationResult',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '记录时间',
                'key': 'recordTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '记录人',
                'key': 'recorderInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': false,
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作',
                'key': 'handlers',
                'style': {
                    'flex': '','width': '120px','maxWidth': '120px','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            }],
        };
        this.chainSubTableConfig = {
            'list': [{
                'label': '姓名',
                'key': 'employeeName',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '性别',
                'key': 'sex',
                'style': {
                    'flex': '','width': '48px','maxWidth': '48px','minWidth': '48px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '年龄',
                'key': 'age',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '',
            },{
                'label': '岗位',
                'key': 'work',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有无健康证',
                'key': 'healthCertificate',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检日期',
                'key': 'physicalExaminationDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检机构',
                'key': 'physicalExaminationInstitution',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检项目',
                'key': 'physicalExaminationProject',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检结果',
                'key': 'physicalExaminationResult',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '记录时间',
                'key': 'recordTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '记录人',
                'key': 'recorderInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': false,
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作',
                'key': 'handlers',
                'style': {
                    'flex': '','width': '120px','maxWidth': '120px','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            }],
        };

        this.singleTableConfig = {
            'list': [{
                'label': '姓名',
                'key': 'employeeName',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '性别',
                'key': 'sex',
                'style': {
                    'flex': '','width': '48px','maxWidth': '48px','minWidth': '48px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '年龄',
                'key': 'age',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
                'testValue': '',
            },{
                'label': '岗位',
                'key': 'work',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '有无健康证',
                'key': 'healthCertificate',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检日期',
                'key': 'physicalExaminationDate',
                'style': {
                    'flex': '','width': '104px','maxWidth': '104px','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检机构',
                'key': 'physicalExaminationInstitution',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检项目',
                'key': 'physicalExaminationProject',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '体检结果',
                'key': 'physicalExaminationResult',
                'style': {
                    'flex': '','width': '80px','maxWidth': '80px','minWidth': '80px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '记录时间',
                'key': 'recordTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '记录人',
                'key': 'recorderInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'sortable': false,
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作',
                'key': 'handlers',
                'style': {
                    'flex': '','width': '120px','maxWidth': '120px','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            }],
        };
    }
}
