import BaseProTable from '@/views/layout/tables/base-pro-table.js';

export default class PriceConfirmAdjustmentTable extends BaseProTable {
    name = 'PriceConfirmAdjustmentTable';

    // 由产品.设计提供的静态配置, 开发只能修改key
    static staticConfig = {
        hasInnerBorder: true,
        list: [
            {
                key: 'shortId',
                label: '商品编码',
                style: {
                    width: '90px',
                    minWidth: '90px',
                    maxWidth: '120px',
                    flex: '1',
                    textAlign: 'left',
                },
                pinned: true,
                rowSpan: (trData) => {
                    if (trData.children?.length) {
                        return trData.children.length + 1;
                    }
                    return 1;
                },
            },
            {
                key: 'displayName',
                label: '商品名称',
                style: {
                    minWidth: '270px',
                    flex: '1',
                    textAlign: 'left',
                },
                pinned: true,
                rowSpan: (trData) => {
                    if (trData.children?.length) {
                        return trData.children.length + 1;
                    }
                    return 1;
                },
            },
            {
                key: 'lastPackageCostPrice',
                label: '最近进价',
                testValue: '09-0909:09',
                colType: 'money4',
                style: {
                    width: '90px',
                    minWidth: '90px',
                    maxWidth: '90px',
                    textAlign: 'center',
                },
                rowSpan: (trData) => {
                    if (trData.children?.length) {
                        return trData.children.length + 1;
                    }
                    return 1;
                },
            },
            {
                key: 'avgPackageCostPrice',
                label: '平均进价',
                testValue: '09-0909:09',
                colType: 'money4',
                style: {
                    width: '90px',
                    minWidth: '90px',
                    maxWidth: '90px',
                    textAlign: 'center',
                },
                rowSpan: (trData) => {
                    if (trData.children?.length) {
                        return trData.children.length + 1;
                    }
                    return 1;
                },
            },
            {
                key: 'memberTypeName',
                label: '会员等级',
                style: {
                    width: '90px',
                    minWidth: '90px',
                    maxWidth: '90px',
                    textAlign: 'center',
                },
            },
            {
                key: 'before',
                label: '调价前',
                testValue: '09-0909:09',
                style: {
                    width: '280px',
                    minWidth: '280px',
                    maxWidth: '280px',
                    textAlign: 'center',
                },
                children: [
                    {
                        key: 'beforePackagePrice',
                        label: '售价',
                        testValue: '10.0',
                        colType: 'money4',
                        style: {
                            width: '100px',
                            minWidth: '100px',
                            maxWidth: '100px',
                            textAlign: 'center',
                        },
                    },
                    {
                        key: 'beforePiecePrice',
                        label: '拆零售价',
                        testValue: '10.0',
                        colType: 'money4',
                        style: {
                            width: '90px',
                            minWidth: '90px',
                            maxWidth: '90px',
                            textAlign: 'center',
                        },
                    },
                    {
                        key: 'beforeProfitRat',
                        label: '毛利率',
                        testValue: '10.0',
                        style: {
                            width: '90px',
                            minWidth: '90px',
                            maxWidth: '90px',
                            textAlign: 'center',
                        },
                    },
                ],
            },
            {
                key: 'after',
                label: '调价后',
                testValue: '09-0909:09',
                style: {
                    width: '320px',
                    minWidth: '320px',
                    maxWidth: '320px',
                    textAlign: 'center',
                },
                children: [
                    {
                        key: 'afterPackagePrice',
                        label: '售价',
                        testValue: '10.0',
                        colType: 'money4',
                        style: {
                            width: '140px',
                            minWidth: '140px',
                            maxWidth: '140px',
                            textAlign: 'center',
                        },
                    },
                    {
                        key: 'afterPiecePrice',
                        label: '拆零售价',
                        testValue: '10.0',
                        colType: 'money4',
                        style: {
                            width: '90px',
                            minWidth: '90px',
                            maxWidth: '90px',
                            textAlign: 'center',
                        },
                    },
                    {
                        key: 'afterProfitRat',
                        label: '毛利率',
                        testValue: '10.0',
                        style: {
                            width: '90px',
                            minWidth: '90px',
                            maxWidth: '90px',
                            textAlign: 'center',
                        },
                    },
                ],
            },
        ],
    };
}
