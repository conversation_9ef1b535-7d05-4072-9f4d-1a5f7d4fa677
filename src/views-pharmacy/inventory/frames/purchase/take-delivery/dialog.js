import { FunctionalDialog } from '@/views/common/functional-dialog.js';
const template = () => import('./order-dialog.vue');

export default class TakeDeliveryOrderDialog extends FunctionalDialog {
    constructor(props) {
        super(props, template, 'take-delivery-order-dialog', 'showDialog');
    }

    generateDialog(extendProps = {}) {
        super.generateDialog(extendProps);
        this.instance.$on('input', (val) => {
            if (!val) {
                this.instance?.dom?.parentNode && this.instance.dom.parentNode.removeChild(this.instance.dom);
                this.destroyDialog();
            }
        });
    }
}
