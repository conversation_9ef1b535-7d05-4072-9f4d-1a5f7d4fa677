export default {
    sampleType: [
        {
            name: '血清',
            id: 1,
        },
        {
            name: '血浆',
            id: 2,
        },
        {
            name: '全血',
            id: 3,
        },
        {
            name: '末梢血',
            id: 4,
        },
        {
            name: '尿液',
            id: 5,
        },
        {
            name: '粪便',
            id: 6,
        },
        {
            name: '阴道分泌物',
            id: 7,
        },
        {
            name: '宫颈上皮细胞',
            id: 8,
        },
        {
            name: '宫颈抹片',
            id: 9,
        },
        {
            name: '痰液',
            id: 10,
        },
        {
            name: '咽喉拭子',
            id: 11,
        },
        {
            name: '精液',
            id: 12,
        },
        {
            name: '分泌物',
            id: 13,
        },
        {
            name: '鼻腔拭子',
            id: 14,
        },
        {
            name: '口腔拭子',
            id: 15,
        },
        {
            name: '呼吸气体',
            id: 16,
        },
        {
            name: '静脉血',
            id: 17,
        },
        {
            name: '脑脊液',
            id: 18,
        },
        {
            name: '宫颈脱落细胞',
            id: 19,
        },
        {
            name: '咽拭子',
            id: 20,
        },
        {
            name: '唾液',
            id: 21,
        },
        {
            name: '流产组织',
            id: 22,
        },
    ],
    unit: [
        {
            name: '次',
            id: 1,
        },
        {
            name: '项',
            id: 2,
        },
        {
            name: '针',
            id: 3,
        },
        {
            name: '根',
            id: 18,
        },
        {
            name: '条',
            id: 19,
        },
        {
            name: '颗',
            id: 4,
        },
        {
            name: '贴',
            id: 5,
        },
        {
            name: '副',
            id: 6,
        },
        {
            name: '袋',
            id: 7,
        },
        {
            name: '组',
            id: 8,
        },
        {
            name: '分钟',
            id: 9,
        },
        {
            name: '小时',
            id: 10,
        },
        {
            name: '穴位',
            id: 11,
        },
        {
            name: '部位',
            id: 12,
        },
        {
            name: '壮',
            id: 20,
        },
        {
            name: '牙',
            id: 13,
        },
        {
            name: '洞',
            id: 14,
        },
        {
            name: '根管',
            id: 15,
        },
        {
            name: '单颌',
            id: 16,
        },
        {
            name: '单侧',
            id: 17,
        },
        {
            name: 'g',
            id: 20,
        },
        {
            name: 'ml',
            id: 21,
        },
        {
            name: '付',
            id: 22,
        },
        {
            name: '料',
            id: 23,
        },
    ],
    sampleTube: [{
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '红色管',
    },
    {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '紫色管',
    }
    , {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '黄色管',
    },
    {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '蓝色管',
    },
    {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '绿色管',
    },
    {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '灰色管',
    },
    {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '黑色管',
    },
    {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '拭子',
    },
    {
        imgUrl: require('../images/tube/<EMAIL>'),
        name: '取样器',
    },
    {
        name: '大便杯',
    },
    {
        name: '小便杯',
    },
    {
        name: '玻片',
    },
    {
        name: '其他',
    },
    ],
    itemCategory: [{
        value: 1,
        name: '临床检验',
        label: '临床检验',
    },{
        value: 2,
        name: '生化检验',
        label: '生化检验',
    },{
        value: 3,
        name: '免疫检验',
        label: '免疫检验',
    },{
        value: 4,
        name: '微生物检验',
        label: '微生物检验',
    },{
        value: 5,
        name: 'PCR检验',
        label: 'PCR检验',
    },{
        value: 6,
        name: '其他分类',
        label: '其他分类',
    }],

    // 试管颜色
    testTubeColor: [{
        label: '紫色管',
        value: '紫色管',
    },{
        label: '红色管',
        value: '红色管',
    }, {
        label: '蓝色管',
        value: '蓝色管',
    },{
        label: '黄色管',
        value: '黄色管',
    },{
        label: '绿色管',
        value: '绿色管',
    },{
        label: '灰色管',
        value: '灰色管',
    },{
        label: '黑色管',
        value: '黑色管',
    },{
        label: '取样器',
        value: '取样器',
    },{
        label: '拭子',
        value: '拭子',
    }],

    // 添加剂
    additiveList: [{
        label: '无',
        value: '无',
    },
    {
        label: '促凝剂',
        value: '促凝剂',
    },{
        label: '促凝剂+分离胶',
        value: '促凝剂+分离胶',
    },{
        label: '肝素锂',
        value: '肝素锂',
    },{
        label: '肝素钠',
        value: '肝素钠',
    },{
        label: '肝素锂+分离',
        value: '肝素锂+分离',
    },{
        label: '3.2%枸橼酸钠',
        value: '3.2%枸橼酸钠',
    },{
        label: '3.8%枸橼酸钠',
        value: '3.8%枸橼酸钠',
    },{
        label: 'EDTA-K2',
        value: 'EDTA-K2',
    },{
        label: 'EDTA-K3',
        value: 'EDTA-K3',
    },{
        label: 'EDTA-Na2',
        value: 'EDTA-Na2',
    },{
        label: '氟化钠+草酸钙',
        value: '氟化钠+草酸钙',
    },{
        label: '氟化钠+K3-',
        value: '氟化钠+K3-',
    },{
        label: 'EDTAK2-EDTA+分',
        value: 'EDTAK2-EDTA+分',
    },{
        label: '离胶',
        value: '离胶',
    },{
        label: '3.2%柠檬酸钠',
        value: '3.2%柠檬酸钠',
    },{
        label: '3.8%柠檬酸钠',
        value: '3.8%柠檬酸钠',
    }],
};
