<template>
    <main class="abc-template-header">
        <slot>
            <header v-if="needSearch">
                <abc-search
                    v-model.trim="searchKeyword"
                    placeholder="搜索"
                    max-length="10"
                    @clear="searchKeyword = ''"
                >
                </abc-search>
            </header>

            <template v-if="curTabs.length">
                <div v-if="searchKeyword" class="search-result">
                    搜索到 <b>{{ totalCount }}</b> 条数据
                </div>

                <abc-tabs-v2
                    v-else
                    v-model="curTab"
                    size="middle"
                    :option="curTabs"
                    style="padding-left: 6px;"
                ></abc-tabs-v2>
            </template>
        </slot>
    </main>
</template>

<script>
    import { TemplateCategoryEnum } from '@/utils/constants';
    import AbcSearch from 'components/abc-search/index.vue';

    const baseTabs = [
        {
            label: '个人专用',
            value: TemplateCategoryEnum.PERSONAL,
        },
        {
            label: '科室公用',
            value: TemplateCategoryEnum.DEPARTMENT,
        },
        {
            label: '全院公用',
            value: TemplateCategoryEnum.PUBLIC_CLINIC,
        },
    ];

    export default {
        name: 'AbcTemplateHeader',
        components: { AbcSearch },
        inject: {
            theRoot: {
                default: {},
            },
        },
        props: {
            needSearch: {
                type: Boolean,
                default: true,
            },
            getTabs: {
                type: Function,
                default() {
                    return baseTabs;
                },
            },
        },
        data: () => ({
            curTabs: [],
        }),
        computed: {
            curTab: {
                get() {
                    return this.theRoot.curTab;
                },
                set(v) {
                    this.theRoot.curTab = v;
                },
            },
            searchKeyword: {
                get() {
                    return this.theRoot.searchKeyword;
                },
                set(v) {
                    this.theRoot.searchKeyword = v;
                },
            },
            totalCount() {
                return this.theRoot?.totalCount;
            },
        },
        watch: {
            curTab(v) {
                const tab = this.curTabs.find((t) => t.value === v) || {};
                this.$emit('change', tab);
            },
        },
        created() {
            this.handleTabInit();
        },
        methods: {
            handleTabInit() {
                this.curTabs = this.getTabs(baseTabs);
                this.curTab = this.curTabs[0]?.value;
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/mixin.scss';

.abc-template-header {
    width: 100%;

    >header {
        display: flex;
        align-items: center;
        height: 56px;
        padding: 0 12px;
        border-bottom: 1px solid var(--abc-color-P7);
    }

    .search-result {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 36px;
        padding-left: 12px;
        color: $T2;
        border-bottom: 1px solid var(--abc-color-P7);

        b {
            margin: 0 4px;
            font-weight: bold;
            color: $T1;
        }
    }

    .abc-tabs {
        height: 40px;
        padding-left: 12px;
        line-height: 40px;

        .abc-tabs-item {
            font-size: 14px;
        }
    }
}
</style>
