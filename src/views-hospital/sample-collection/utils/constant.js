export const CollectionStatusOptions = [
    {
        label: '待采集', value: 0,
    },
    {
        label: '已采集', value: 10,
    },
    {
        label: '已核收', value: 20,
    },
    {
        label: '拒收', value: 30,
    },
];

export const CollectionStatus = {
    wait: 0,
    finish: 10,
    verified: 20,
    refused: 30,
};

export const CollectionStatusToText = {
    [CollectionStatus.wait]: '待采集',
    [CollectionStatus.finish]: '待核收',
    [CollectionStatus.verified]: '已核收',
    [CollectionStatus.refused]: '拒收',
};

export const BusinessTypeOptions = [
    {
        label: '未知',value: 0,
    },
    {
        label: '门诊',value: 10,
    },
    {
        label: '收费',value: 20,
    },
    {
        label: '住院',value: 30,
    },
    {
        label: '检验',value: 40,
    },
    {
        label: '体检',value: 200,
    },
];

export const BusinessType = {
    unknown: 0,
    outpatient: 10,
    cashier: 20,
    inpatient: 30,
    examination: 40,
    openApi: 100,
    physicalExamination: 200,
    0: '未知',
    10: '门诊',
    20: '收费',
    30: '住院',
    40: '检验',
    100: '其他',
    200: '体检',
};

export const ExaminationBusinessTypeEnum = {
    0: '未知',
    10: '门诊',
    20: '收费',
    30: '住院',
    40: '检验',
    100: '其他',
    200: '体检',
};

export const BusinessTypeToFormLabel = {
    0: '未知',
    10: '门诊',
    20: '门诊',
    30: '住院',
    40: '门诊',
    50: '门诊',
    100: '其他',
    200: '体检',
};

export const EnumOperateWay = {
    input: 'input', // 输入框输入
    scan: 'scan', // 扫码枪扫码
    sheet: 'sheet', // 根据单据信息
};

export const ChargeSheetType = {
    NONE: 0, // 无类型，对应 Java 中的 NONE
    PERSONAL: 1, // 个检，对应 Java 中的 PERSONAL
    NORMAL_GROUP: 2, // 普通团检，对应 Java 中的 NORMAL_GROUP
    NORMAL_GROUP_PERSONAL_ADDITION: 3, // 普通团检-个人增项，对应 Java 中的 NORMAL_GROUP_PERSONAL_ADDITION
    PUBLIC_HEALTH_GROUP: 4, // 公卫团检，对应 Java 中的 PUBLIC_HEALTH_GROUP
    PUBLIC_HEALTH_GROUP_PERSONAL_ADDITION: 5, // 公卫团检-个人增项，对应 Java 中的 PUBLIC_HEALTH_GROUP_PERSONAL_ADDITION
    PERSONAL_PUBLIC_HEALTH: 6, // 公卫个检，对应 Java 中的 PERSONAL_PUBLIC_HEALTH
};
