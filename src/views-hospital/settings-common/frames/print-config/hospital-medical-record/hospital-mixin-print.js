import { mapGetters } from 'vuex';
import clone from 'utils/clone';
import { getLengthWithFullCharacter } from 'views/settings/print-config/utils';
import PrintAPI from 'api/print';
import { AbcPrintOrientation } from '@/printer/constants';

export default {
    computed: {
        ...mapGetters([
            'currentClinic',
            'clinicBasicConfig',
        ]),
    },
    watch: {
        postData: {
            handler(newPostData) {
                this.$nextTick(() => {
                    if (this._doctorMedicalPrescription) {
                        this.printInstance = null;
                    }
                    this.updatePreview(newPostData);
                });

            },
            deep: true,
            // todo lxl 待删除
            immediate: true,
        },
    },
    beforeDestroy() {
        this.destroyPrintInstanceStyle();
        this.$destroyed = true;
    },
    methods: {
        handleInitialPostData(postData, type = 'advice') {
            const cachePostData = clone(postData);
            // 判断抬头是否超过15个字符(需分区全角/半角),将超过部分拆分到副抬头
            const {
                fullCharacterLength, splitLength,
            } = getLengthWithFullCharacter(cachePostData.header.title, this.titleMaxLength);
            if (fullCharacterLength > this.titleMaxLength) {
                const cacheTitle = cachePostData.header.title;
                cachePostData.header.title = cacheTitle.slice(0, splitLength);
                cachePostData.header.subtitle = cacheTitle.slice(splitLength);
                this.postData = cachePostData;
                this.updateTitleByPrintHospitalMedicalDocumentsConfig(type);
            } else {
                this.postData = cachePostData;
            }

            this._cachePostData = clone(this.postData);
            if (!this.postData.header.title) {
                const cacheClinicName = this.currentClinic.clinicName;
                const {
                    fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                } = getLengthWithFullCharacter(cacheClinicName, this.titleMaxLength);
                if (fullClinicCharacterLength > this.titleMaxLength) {
                    this.postData.header.title = cacheClinicName.slice(0, splitClinicLength);
                    this.postData.header.subtitle = cacheClinicName.slice(splitClinicLength);
                } else {
                    this.postData.header.title = cacheClinicName;
                }
            }
            this.isShowSubtitle = !!this.postData.header.subtitle;
        },
        destroyPrintInstanceStyle() {
            this._destroyStyle?.();
            this._destroyStyle = null;
        },
        async updatePreview(newPostData) {
            await this.mountPrintInstance();
            // 更新预览界面
            await this.updateInstanceGlobalConfig(newPostData);
        },
        async mountPrintInstance() {
            if (this.printInstance) return;

            // 处理示例数据
            if (this.handleTransExampleData) {
                this.handleTransExampleData();
            }

            const pageInfo = this.previewPage || {
                size: 'A4',
                orientation: AbcPrintOrientation.portrait,
            };

            if (this.currentExampleData.clinicInfo?.logo) {
                const { logo } = this.clinicBasicConfig;
                this.currentExampleData.clinicInfo.logo = logo;
            }
            const printInstance = new window.AbcPackages.AbcPrint({
                template: this.getCurrentTemplate(),
                page: pageInfo,
                originData: this.currentExampleData,
                extra: {
                    isPreview: true,
                },
            });
            await printInstance.init();
            this.destroyPrintInstanceStyle();
            if (!this.$destroyed) {
                this._destroyStyle = printInstance.loadInstanceStyle();
            }
            this.$refs.previewMountPoint.innerHTML = '';
            this.$refs.previewMountPoint.appendChild(printInstance.instance.$el);
            this.printInstance = printInstance;
        },
        async updateInstanceGlobalConfig(newPostData) {
            await this.printInstance.updateInstanceGlobalConfig(this.instanceGlobalConfigHandler(newPostData));
            await this.printInstance.updateDatahandlerAndInstance();
        },
        async updateTitleByPrintHospitalMedicalDocumentsConfig(param = 'advice') {
            try {
                const {
                    title, subtitle,
                } = this.postData.header;
                await PrintAPI.updateMedicalDocumentsTitle('clinic', {
                    childKeys: [
                        `hisPrint.medicalDocuments.${param}.header.title`,
                    ],
                    value: title,
                });
                await PrintAPI.updateMedicalDocumentsTitle('clinic', {
                    childKeys: [
                        `hisPrint.medicalDocuments.${param}.header.subtitle`,
                    ],
                    value: subtitle,
                });
                this.$store.commit('SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_TITLE_WITH_PARAM', {
                    title, subtitle, param,
                });
            } catch (e) {
                console.error(e);
            }
        },
        async updateCustomTitleByPrintHospitalMedicalDocumentsConfig(headerName = 'header', type = 'advice', postData) {
            try {
                const {
                    title, subtitle,
                } = postData[headerName];
                await PrintAPI.updateMedicalDocumentsTitle('clinic', {
                    childKeys: [
                        `hisPrint.medicalDocuments.${type}.${headerName}.title`,
                    ],
                    value: title,
                });
                await PrintAPI.updateMedicalDocumentsTitle('clinic', {
                    childKeys: [
                        `hisPrint.medicalDocuments.${type}.${headerName}.subtitle`,
                    ],
                    value: subtitle,
                });
                this.$store.commit('SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_CUSTOM_TITLE_WITH_PARAM', {
                    headerName, title, subtitle, param: type,
                });
            } catch (e) {
                console.error(e);
            }
        },
        handleSetAllDocuments() {
            this.$refs.printForm.validate((val) => {
                if (val) {
                    let content = `是否确认将全部病历文书抬头名称统一修改为“${this.postData.header.title}”？`;
                    if (this.postData.header.subtitle) {
                        content = `是否确认将全部病历文书抬头名称统一修改为“${this.postData.header.title}”和“${this.postData.header.subtitle}”？`;
                    }
                    // 设置全部病例文书抬头
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content,
                        onConfirm: async () => {
                            await this.updateTitle(this.postData.header.title, this.postData.header.subtitle);
                            this.$Toast({
                                message: '设置成功',
                                type: 'success',
                            });
                        },
                    });
                }
            });
        },
        async updateTitle(title = '', subtitle = '') {
            try {
                await PrintAPI.updateMedicalDocumentsTitle('clinic', {
                    childKeys: [
                        'hisPrint.medicalDocuments.advice.header.title',
                        'hisPrint.medicalDocuments.advice.shandong.title',
                        'hisPrint.medicalDocuments.adviceExecution.header.title',
                        'hisPrint.medicalDocuments.dispenseAndUnDispenseOrder.header.title',
                        'hisPrint.medicalDocuments.prescription.header.title',
                        'hisPrint.medicalDocuments.infusionRecord.header.title',
                        'hisPrint.medicalDocuments.hospitalizationCertificate.header.title',
                    ],
                    value: title,
                });
                await PrintAPI.updateMedicalDocumentsTitle('clinic', {
                    childKeys: [
                        'hisPrint.medicalDocuments.advice.header.subtitle',
                        'hisPrint.medicalDocuments.advice.shandong.subtitle',
                        'hisPrint.medicalDocuments.adviceExecution.header.subtitle',
                        'hisPrint.medicalDocuments.dispenseAndUnDispenseOrder.header.subtitle',
                        'hisPrint.medicalDocuments.prescription.header.subtitle',
                        'hisPrint.medicalDocuments.infusionRecord.header.subtitle',
                        'hisPrint.medicalDocuments.hospitalizationCertificate.header.subtitle',
                    ],
                    value: subtitle,
                });
                this.$store.commit('SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_TITLE', {
                    title, subtitle,
                });
            } catch (e) {
                console.error(e);
            }
        },
        async updatePrintConfig(type = 'advice') {
            try {
                this.btnLoading = true;
                const { data } = await PrintAPI.updatePrintConfig('clinic', `hisPrint.medicalDocuments.${type}`, {
                    [type]: this.postData,
                });
                this.$store.commit('SET_PRINT_HOSPITAL_MEDICAL_DOCUMENTS_SUB_CONFIG', {
                    key: type,
                    data: data[type],
                });
                this.$Toast({
                    message: '保存成功',
                    type: 'success',
                });
                this.btnLoading = false;
            } catch (e) {
                console.error(e);
                this.btnLoading = false;
            }
        },
        handleResetPostData(ResetData = {}) {
            this.$confirm({
                type: 'warn',
                title: '提示',
                content: '是否确认将当前全部设置恢复为系统默认设置？',
                onConfirm: () => {
                    this.postData = clone(ResetData);

                    // 如果门店名称超过15(全角)/30(半角)个字,则进行拆分
                    const cacheClinicName = this.currentClinic.clinicName;
                    const {
                        fullClinicCharacterLength, splitClinicLength,
                    } = getLengthWithFullCharacter(cacheClinicName, this.titleMaxLength);
                    if (fullClinicCharacterLength > this.titleMaxLength) {
                        this.postData.header.title = cacheClinicName.slice(0, splitClinicLength);
                        this.postData.header.subtitle = cacheClinicName.slice(splitClinicLength);
                    } else {
                        this.postData.header.title = cacheClinicName;
                    }
                },
            });
        },
    },
};
