<template>
    <!--内容区域-->
    <abc-container
        has-left-container
        :left-container-max-width="320"
        :left-container-min-width="240"
        :is-support-center-scroll="false"
        class="abc-hospital-settings-container"
    >
        <!--左侧预约情况列表-->
        <abc-container-left>
            <div class="abc-hospital-settings-container-menu">
                <abc-menu
                    v-model="currentIndex"
                    badge-variant="dot"
                    @click="selectItem"
                >
                    <template v-for="navItem in menuList">
                        <template v-if="navItem.children && navItem.children.length">
                            <abc-sub-menu
                                :key="navItem.name"
                                :icon="navItem.icon"
                                :index="navItem.path"
                                :count="navItem.count || 0"
                                :value="navItem.name"
                            >
                                <abc-text v-if="navItem.tip" slot="appendInner" :theme="navItem.path === currentIndex ? 'white' : 'warning-light'">
                                    {{ navItem.tip }}
                                </abc-text>
                                <abc-menu-item
                                    v-for="navChildItem in navItem.children"
                                    :key="navChildItem.name"
                                    :icon="navChildItem.icon"
                                    :index="navChildItem.path"
                                    :count="navChildItem.count || 0"
                                >
                                    {{ navChildItem.name }}
                                </abc-menu-item>
                            </abc-sub-menu>
                        </template>
                        <abc-menu-item
                            v-else
                            :key="navItem.name"
                            :icon="(navItem.path === 'product-center' && navItem.path === currentIndex) ? navItem.iconActive : navItem.icon"
                            :index="navItem.path"
                            :count="navItem.count || 0"
                        >
                            <abc-text v-if="navItem.tip" slot="appendInner" :theme="navItem.path === currentIndex ? 'white' : 'warning-light'">
                                {{ navItem.tip }}
                            </abc-text>
                            {{ navItem.name }}
                        </abc-menu-item>
                    </template>
                </abc-menu>
            </div>
        </abc-container-left>
        <!--右侧form区域-->
        <abc-container-center class="content-container settings-container">
            <router-view></router-view>
        </abc-container-center>
    </abc-container>
</template>

<script type="text/ecmascript-6">
// 自定义组件
    import Layout from 'views/common/layout';
    import { mapGetters } from 'vuex';
    import * as core from '@/views-hospital/settings-common/core';
    import { createAbcPage } from '@/core/page/factory.js';
    import ModulePermission from 'views/permission/module-permission';
    import AbcAccess from '@/access/utils';
    import LogoDefault from '@/assets/images/edition/<EMAIL>';
    import LogoActive from '@/assets/images/edition/<EMAIL>';
    import { CHAIN_NOT_SUPPORT_PRICE_MAKE_UP_MODE } from '@/views-pharmacy/inventory/constant';
    import { MODULE_ID_MAP } from 'utils/constants';
    import { useCertsStore } from '@/views/settings/store/use-clinic-store';
    import {
        onMounted, onUnmounted,
    } from 'vue';

    import AbcUiThemeMixin from 'views/common/abc-ui-theme-mixin';

    export default {
        name: 'HospitalCommonSetting',
        mixins: [ModulePermission,createAbcPage(core),Layout,AbcUiThemeMixin],
        setup() {
            const certsStore = useCertsStore();
            const { initClinicCerts } = certsStore;

            onMounted(() => {
                initClinicCerts();
            });

            onUnmounted(() => {
                certsStore.$reset();
            });
        },
        data() {
            return {
                currentIndex: '',
            };
        },
        computed: {
            ...mapGetters(['clinicConfig']),
            ...mapGetters([
                'unUploadOrganCertCount',
                'currentClinic',
                'clinicConfig',
                'isChainAdmin',
                'isChainSubStore',
                'isSingleStore',
                'clinicBasic',
                'isClinicBasicInited',
                'currentClinic',
                'isEnableRegUpgrade',
                'goodsConfig',
                'showSubSetPrice',
                'isEnableOperationLog',
            ]),
            ...mapGetters('viewDistribute',[
                'featureContinueMR',
                'featureChronicRecovery',
                'featureFamilyDoctor',
                'featureChildHealth',
                'featureTreatOnline',
                'featureAirPharmacy',
                'viewDistributeConfig',
                'featureTherapy',
                'featureProcess',
            ]),
            NavLists() {
                const list = [];
                this.menuList.forEach((item) => {
                    if (item.children?.length) {
                        list.push(...item.children);
                    }
                });
                return this.menuList.concat(list);
            },
            isSupportCostPriceMakeUp() {
                const isOpenCostPriceMakeUp = !(this.goodsConfig.chainReview.chainExternalFlag & CHAIN_NOT_SUPPORT_PRICE_MAKE_UP_MODE);
                return isOpenCostPriceMakeUp && this.viewDistributeConfig.Inventory.isSupportCostPriceMakeUp;
            },
            priceTaxratName() {
                if (this.isChainAdmin || (this.isSingleStore && this.isSupportCostPriceMakeUp)) {
                    return '定价和税率';
                }
                return '税率';
            },
            menuList() {
                const list = [
                    {
                        name: '机构/人员设置',
                        id: '100',
                        icon: 's-profile-line',
                        isActive: 0,
                        up: false,
                        routeName: '',
                        visible: this.hasAdminConfigAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '基础设置',
                                id: '1001',
                                isActive: 0,
                                routeName: 'clinic',
                                visible: this.hasAdminConfigAdminModule,
                                path: 'clinic',
                                count: this.isChainAdmin ? 0 : this.unUploadOrganCertCount,
                            },
                            {
                                icon: '',
                                name: '人员管理',
                                id: '1002',
                                isActive: 0,
                                routeName: 'employeesModel',
                                visible: this.hasAdminConfigAdminModule,
                                path: 'employeesModel',
                            },
                        ],
                    },
                    {
                        name: '医嘱/费用设置',
                        id: '200',
                        icon: 's-currency-3-line',
                        isActive: 0,
                        up: false,
                        routeName: '',
                        visible: this.hasAdminFeeAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '医嘱设置',
                                id: '2001',
                                isActive: 0,
                                routeName: 'doctor-order',
                                visible: this.hasAdminFeeAdminModule,
                                path: 'doctor-order',
                            },
                            {
                                icon: '',
                                name: '费用设置',
                                id: '2002',
                                isActive: 0,
                                routeName: 'fee-common',
                                visible: this.hasAdminFeeAdminModule,
                                path: 'fee-common',
                            },
                            {
                                icon: '',
                                name: this.priceTaxratName,
                                id: '2005',
                                isActive: 0,
                                routeName: 'pricetaxrat',
                                visible: (this.isChainAdmin || this.isSingleStore) && this.hasAdminFeeAdminModule,
                                path: 'pricetaxrat',
                            },
                            {
                                icon: '',
                                name: '医嘱下达设置',
                                id: '2003',
                                isActive: 0,
                                routeName: 'open-set',
                                visible: (this.isChainSubStore || this.isSingleStore) && this.hasAdminFeeAdminModule,
                                path: 'open-set',
                            },
                            {
                                icon: '',
                                name: '用法关联设置',
                                id: '2004',
                                isActive: 0,
                                routeName: 'relation',
                                visible: this.hasAdminFeeAdminModule,
                                path: 'relation',
                            },
                        ],
                    },
                    {
                        name: '门诊设置',
                        id: '300',
                        icon: 's-outpatient-clinic-line',
                        isActive: 0,
                        up: false,
                        routeName: '',
                        visible: this.hasAdminOutpatientAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '基础设置',
                                id: '3001',
                                isActive: 0,
                                routeName: 'outpatient-setting',
                                visible: this.hasAdminOutpatientAdminModule && (this.isChainSubStore || this.isSingleStore),
                                path: 'outpatient-setting',
                            },
                            {
                                icon: '',
                                name: '预约设置',
                                id: '3002',
                                isActive: 0,
                                routeName: 'registered',
                                visible: this.hasAdminOutpatientAdminModule &&
                                    (this.isChainSubStore || this.isSingleStore || (this.viewDistributeConfig.Settings.reservation.reservationDentistry || this.isEnableRegUpgrade) &&
                                        this.isChainAdmin),
                                path: 'registered',
                            },
                            {
                                icon: '',
                                name: '叫号设置',
                                id: '3003',
                                isActive: 0,
                                routeName: 'callnumber',
                                visible: (this.isChainSubStore || this.isSingleStore) && this.hasAdminOutpatientAdminModule,
                                path: 'callnumber',
                            },
                            {
                                icon: '',
                                name: '儿保系统',
                                id: '4004',
                                isActive: 0,
                                routeName: 'child-health-settings',
                                visible: (this.isChainAdmin || this.isSingleStore) && this.hasAdminOutpatientAdminModule && this.featureChildHealth,
                                path: 'child-health',
                            },
                            {
                                icon: '',
                                name: '慢病管理',
                                id: '4005',
                                isActive: 0,
                                routeName: '@settings/chronic-care',
                                visible: (this.isChainAdmin || this.isSingleStore) && this.hasAdminOutpatientAdminModule && this.featureChronicRecovery,
                                path: 'chronic-care',
                            },
                            {
                                icon: '',
                                name: '执行站设置',
                                id: '4006',
                                isActive: 0,
                                routeName: 'treatment-settings',
                                visible: this.hasAdminOutpatientAdminModule && (this.isChainSubStore || this.isSingleStore),
                                path: 'treatment-settings',
                            },
                        ],
                    },
                    {
                        name: '排班设置',
                        id: '2000',
                        icon: 's-calendar-line',
                        isActive: 0,
                        up: false,
                        routeName: 'schedulesModel',
                        visible: this.hasScheduleAdminModule && !this.isChainAdmin,
                        path: 'schedulesModel',
                        children: [],
                    },
                    {
                        name: '住院设置',
                        id: '1500',
                        icon: 's-bed-line',
                        isActive: 0,
                        up: false,
                        routeName: '',
                        visible: this.hasAdminLiveAdminModule && !this.isChainAdmin,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '病区病房设置',
                                id: '15001',
                                isActive: 0,
                                routeName: '@inpatient-area',
                                visible: this.hasAdminLiveAdminModule,
                                path: 'inpatient-area',
                            },
                            {
                                icon: '',
                                name: '医嘱设置',
                                id: '15002',
                                isActive: 0,
                                routeName: '@doctor-advice-step',
                                visible: this.hasAdminLiveAdminModule,
                                path: 'doctor-advice-step',
                            },
                            {
                                icon: '',
                                name: '护士站设置',
                                id: '15007',
                                isActive: 0,
                                routeName: '@care-setting',
                                visible: this.hasAdminLiveAdminModule,
                                path: 'care-setting',
                            },
                            {
                                icon: '',
                                name: '会诊设置',
                                id: '15004',
                                isActive: 0,
                                routeName: '@consultation',
                                visible: this.hasAdminLiveAdminModule,
                                path: 'consultation',
                            },
                            {
                                icon: '',
                                name: '病案管理设置',
                                id: '15006',
                                isActive: 0,
                                routeName: '@medical-record-management-setting',
                                visible: this.hasAdminLiveAdminModule,
                                path: 'medical-record-management-setting',
                            },
                        ],
                    },
                    {
                        name: '体检系统介绍',
                        id: '18001',
                        icon: 's-onepersonlist-line',
                        isActive: 0,
                        up: false,
                        visible: !AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
                        routeName: '@physicalExaminationProductIntroduce',
                        path: 'physical-examination-product-introduce',
                    },
                    {
                        name: '体检系统',
                        id: '1800',
                        icon: 's-onepersonlist-line',
                        isActive: 0,
                        up: false,
                        path: '',
                        visible: this.hasPhysicalExaminationAdminModule && AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION),
                        children: [
                            {
                                icon: '',
                                name: '项目/套餐设置',
                                id: '18002',
                                isActive: 0,
                                visible: this.hasPhysicalExaminationAdminModule,
                                routeName: '@physicalExaminationProjectSet',
                                path: 'physical-examination-project-set',
                            },
                            {
                                icon: '',
                                name: '总评报告设置',
                                id: '18003',
                                isActive: 0,
                                visible: (this.isChainSubStore || this.isSingleStore) && this.hasPhysicalExaminationAdminModule,
                                routeName: '@physicalExaminationAssessmentSet',
                                path: 'physical-examination-assessment-set',
                            },
                            {
                                icon: '',
                                name: '体检流程设置',
                                id: '18004',
                                isActive: 0,
                                visible: (this.isChainSubStore || this.isSingleStore) && this.hasPhysicalExaminationAdminModule,
                                routeName: '@physicalExaminationSet',
                                path: 'physical-examination-set',
                            },
                            {
                                icon: '',
                                name: '体检系统介绍',
                                id: '18001',
                                isActive: 0,
                                visible: this.hasPhysicalExaminationAdminModule,
                                routeName: '@physicalExaminationProductIntroduce',
                                path: 'physical-examination-product-introduce',
                            },
                        ],
                    },
                    {
                        name: '文书设置',
                        id: '1900',
                        icon: 's-registration-line',
                        isActive: 0,
                        up: false,
                        routeName: '',
                        visible: this.hasPaperAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '住院文书设置',
                                id: '19001',
                                isActive: 0,
                                routeName: '@hospital-emr-setting',
                                visible: this.hasPaperAdminModule && !this.isChainAdmin,
                                path: 'hospital-emr-setting',
                            },
                            {
                                icon: '',
                                name: '门诊文书设置',
                                id: '19002',
                                isActive: 0,
                                routeName: '@outpatient-emr-setting',
                                visible: this.hasPaperAdminModule,
                                path: 'outpatient-emr-setting',
                            },
                        ],
                    },
                    {
                        name: '医技设置',
                        id: '500',
                        icon: 's-syringe-line',
                        isActive: 0,
                        up: false,
                        visible: this.hasInspectAndExamAdminModule,
                        routeName: '',
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '检验设置',
                                id: '5001',
                                isActive: 0,
                                routeName: 'examination-settings',
                                visible: this.hasInspectAndExamAdminModule,
                                path: 'examination-settings',
                            },
                            {
                                icon: '',
                                name: '检查设置',
                                id: '5002',
                                isActive: 0,
                                routeName: 'inspect-settings',
                                visible: (this.isChainSubStore || this.isSingleStore) && this.hasInspectAndExamAdminModule,
                                path: 'inspect-settings',
                            },
                            {
                                icon: '',
                                name: '手麻设置',
                                id: '5003',
                                isActive: 0,
                                routeName: 'surgery-settings',
                                visible: this.hasInspectAndExamAdminModule,
                                path: 'surgery-settings',
                            },
                        ],
                    },
                    {
                        name: '收费设置',
                        id: '600',
                        icon: 's-currency-2-line',
                        isActive: 0,
                        up: false,
                        visible: this.hasCashierAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '收费设置',
                                id: '6001',
                                isActive: 0,
                                routeName: 'chargeset',
                                visible: this.hasCashierAdminModule,
                                path: 'chargeset/basic',
                            },
                            {
                                icon: '',
                                name: '聚合支付',
                                id: '6002',
                                isActive: 0,
                                routeName: 'aggregate-payment',
                                visible: this.hasCashierAdminModule,
                                path: 'aggregate-payment',
                            },
                            {
                                icon: '',
                                name: '发票设置',
                                id: '6003',
                                isActive: 0,
                                routeName: '@chargeset/invoice',
                                visible: this.hasCashierAdminModule && !this.isChainAdmin,
                                path: 'chargeset/invoice',
                            },
                        ],
                    },
                    {
                        name: '打印设置',
                        id: '700',
                        icon: 's-print-line',
                        isActive: 0,
                        up: false,
                        visible: this.hasPrintAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '门诊打印',
                                id: '7001',
                                isActive: 0,
                                routeName: 'print',
                                visible: this.isChainSubStore || this.isSingleStore,
                                path: 'print',
                            },
                            {
                                icon: '',
                                name: '医技打印',
                                id: '7004',
                                isActive: 0,
                                routeName: 'medical-technology-print',
                                visible: this.isChainSubStore || this.isSingleStore,
                                path: 'medical-technology-print',
                            },
                            {
                                icon: '',
                                name: '住院打印',
                                id: '7002',
                                isActive: 0,
                                routeName: 'hospital-print',
                                visible: this.isChainSubStore || this.isSingleStore,
                                path: 'hospital-print',
                            },
                            {
                                icon: '',
                                name: '库存打印',
                                id: '7003',
                                isActive: 0,
                                routeName: 'inventory-print',
                                visible: this.hasPrintAdminModule,
                                path: 'inventory-print',
                            },
                        ],
                    },
                    {
                        name: '字段设置',
                        id: MODULE_ID_MAP.settingSub.fieldLayoutSetting,
                        icon: 's-layout-line',
                        isActive: 0,
                        up: false,
                        routeName: 'FieldLayout',
                        visible: this.hasAdminModule,
                        path: 'field-layout',
                        children: [],
                    },
                    {
                        name: '进销存设置',
                        id: '800',
                        icon: 's-inbox-outline',
                        isActive: 0,
                        up: false,
                        visible: this.hasInventoryAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '库存设置',
                                id: '8001',
                                isActive: 0,
                                routeName: 'ware-house-set',
                                visible: this.hasInventoryAdminModule,
                                path: 'ware-house-set',
                            },
                            {
                                icon: '',
                                name: `发药${this.featureProcess ? '/加工' : ''}/配送`,
                                id: '8002',
                                isActive: 0,
                                routeName: 'dispenseset',
                                visible: (this.isChainSubStore || this.isSingleStore) && this.hasInventoryAdminModule,
                                path: 'dispenseset',
                            },
                            {
                                icon: '',
                                name: '定价设置',
                                id: '8003',
                                isActive: 0,
                                routeName: 'sub-clinic-price-setting',
                                visible: (this.isChainSubStore && this.showSubSetPrice && this.isSupportCostPriceMakeUp) && this.hasInventoryAdminModule,
                                path: 'sub-clinic-price-setting',
                            },
                        ],
                    },
                    {
                        name: '运营管理',
                        id: '900',
                        icon: 's-user-line',
                        isActive: 0,
                        up: false,
                        visible: this.hasOperateAdminModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '患者设置',
                                id: '9001',
                                isActive: 0,
                                routeName: 'patient-tag-setting',
                                visible: (this.isChainAdmin || this.isSingleStore) && this.hasOperateAdminModule,
                                path: 'patient-tag-setting',
                            },
                            {
                                icon: '',
                                name: '企微管家',
                                id: '9002',
                                isActive: 0,
                                routeName: 'scrm-index',
                                visible: this.hasOperateAdminModule,
                                path: 'scrm-index',
                            },
                        ],
                    },
                    // 和配置关联更大
                    {
                        name: '合规监管',
                        id: '1100',
                        icon: 's-safety-line',
                        isActive: 0,
                        up: false,
                        visible: (this.isChainSubStore || this.isSingleStore) &&
                            this.hasRegularModule,
                        path: '',
                        children: [
                            {
                                icon: '',
                                name: '卫健监管',
                                id: '11001',
                                isActive: 0,
                                routeName: 'regulatory',
                                visible: (this.isChainSubStore || this.isSingleStore) &&
                                    this.hasRegularModule &&
                                    this.$abcRegulatory?.config?.isSupportRegulatory === true,
                                path: 'regulatory',
                            },
                            {
                                icon: '',
                                name: '抗菌用药管理',
                                id: '11002',
                                isActive: 0,
                                routeName: 'antimicrobial-management-hospital',
                                visible: (this.isChainSubStore || this.isSingleStore) && this.hasRegularModule,
                                path: 'antimicrobial-management-hospital',
                            },
                        ],
                    },
                    // 和版本关系更大
                    {
                        name: '家庭医生',
                        id: '1200',
                        icon: 's-doctor-line',
                        isActive: 0,
                        up: false,
                        visible: this.hasFamilyDoctorModule &&
                            this.featureFamilyDoctor &&
                            AbcAccess.getPurchasedByKey(AbcAccess.accessMap.FAMILY_DOCTOR),
                        routeName: 'family-doctor',
                        path: 'family-doctor-settings',
                        children: [],
                    },
                    {
                        name: '空中药房',
                        id: '1300',
                        icon: 's-store-line',
                        isActive: 0,
                        up: false,
                        visible: this.featureAirPharmacy && !this?.$abcSocialSecurity.config.isZhejiangHangzhou && this.hasAirPharmacyAdminModule,
                        routeName: 'airpharmacy',
                        path: 'airpharmacy',
                        children: [],
                    },
                    {
                        name: '区域检查检验中心',
                        routeName: 'InspectionCenter',
                        isActive: 0,
                        id: '2300',
                        up: false,
                        icon: 's-flask-4-line',
                        path: 'area-inspection-center',
                        visible: this.hasAdminModule && (this.isChainSubStore || this.isSingleStore),
                    },
                    {
                        name: '自助服务机',
                        id: '1400',
                        icon: 's-kiosk-line',
                        isActive: 0,
                        up: false,
                        visible: (this.isChainSubStore || this.isSingleStore) && this.hasServiceAdminModule,
                        routeName: 'selfservice',
                        path: 'selfservice',
                        children: [],
                    },
                    {
                        name: '操作日志',
                        routeName: 'OperationLogSettings',
                        icon: 's-document-line',
                        id: '2200',
                        isActive: 0,
                        path: 'operation-log',
                        showRedDot: false,
                        visible: this.hasAdminModule && this.isEnableOperationLog,
                    },
                    {
                        name: '产品中心',
                        id: '1600',
                        icon: 's-logo-a-color',
                        iconActive: 's-logo-a-fill',
                        isActive: 0,
                        up: false,
                        visible: this.hasProductAdminModule,
                        visibleOnExpired: true,
                        routeName: 'product-center',
                        path: 'product-center',
                        children: [],
                    },
                    // 用户日志比较特殊
                    {
                        name: '用户日志',
                        id: '1700',
                        icon: 'update_log',
                        isActive: 0,
                        up: false,
                        imgDefault: LogoDefault,
                        imgActive: LogoActive,
                        visible: this.hasAdminModule && this.clinicConfig.isDengbaoClinic,
                        routeName: 'userLog',
                        path: 'userLog',
                        children: [],
                    },

                ];
                if (this.$app.isExpired) {
                    return list.filter((item) => {
                        return item.visibleOnExpired;
                    }).map((item) => {
                        return {
                            ...item,
                            children: item.children?.filter((it) => {
                                return it.visibleOnExpired;
                            }) || [],
                        };
                    });
                }
                return list.map((item) => {
                    return {
                        ...item,
                        children: item.children?.filter((it) => {
                            return it.visible;
                        }) || [],
                    };
                }).filter((item) => {
                    if (!item.path) {
                        return item.visible && item.children.length > 0;
                    }
                    return item.visible;
                });
            },
        },
        watch: {
            '$route.path': {
                handler(val) {
                    if (val) {
                        this.currentIndex = this.NavLists.find((item) => {
                            return item.path && this.comparePath(item);
                        })?.path;
                    }
                },
                immediate: true,
            },
        },
        created() {
            this.$store.dispatch('fetchChainSubClinics');
        },
        methods: {
            comparePath(val) {
                const { path } = this.$route;
                return path.includes(`/common-settings/${val.path}`);
            },
            selectItem(index) {
                const name = this.NavLists.find((item) => item.path === index)?.routeName;
                if (name) {
                    this.redirectTo(name);
                }
            },
            redirectTo(name) {
                this.$router.push({
                    name,
                });
            },
        },
    };
</script>

<style lang="scss">
@import "./_index.scss";

.abc-hospital-settings-container-menu {
    height: 100%;
    padding: 10px 0 10px 10px;
    overflow-x: hidden;
    overflow-y: scroll;
    background-color: #f9fafc;

    @include scrollBar;
}
</style>
