<template>
    <div v-abc-loading:page="loading">
        <abc-container-center-top-head>
            <hospital-patient-info-container
                :patient-info="currentPatient"
                :patient-order-id="patientOrderId"
                @change-patient-info="changePatientInfo"
                @init-patient-hospital-info="handleInitPatientHospitalInfo"
            ></hospital-patient-info-container>
        </abc-container-center-top-head>

        <div class="main-content">
            <medical-document-main
                :key="patientOrderId"
                :patient-order-id="patientOrderId"
                :patient-id="currentQuickItem.patientId"
                :department-id="currentQuickItem.departmentId"
                :business-id="patientOrderId"
                :business-type="MedicalDocumentBusinessType.HOSPITAL"
                :enable-setup-blood-sugar="true"
                :enable-setup-temperature="true"
            ></medical-document-main>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import HospitalPatientInfoContainer from 'views/layout/patient/hospital-patient-info-container.vue';
    import MedicalDocumentMain from '@/views-hospital/nursing/components/medical-document-main.vue';
    import { MedicalDocumentBusinessType } from '@/views-hospital/nursing/common/constants.js';

    export default {
        name: 'HospitalNursingMain',
        components: {
            HospitalPatientInfoContainer,
            MedicalDocumentMain,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                loading: false,
                patientOrderId: '',
                MedicalDocumentBusinessType,
            };
        },
        computed: {
            currentQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },
            currentPatient() {
                return this.currentQuickItem.patient;
            },
        },
        watch: {
            // 如果路由有变化，会再次执行该方法
            '$route': function(newVal) {
                this.patientOrderId = newVal.params.id;
            },
        },

        created() {
            this.patientOrderId = this.$route.params.id;
        },
        methods: {
            changePatientInfo(data) {
                this.$abcPage.$store.updatePatientInfo(data);
            },

            handleInitPatientHospitalInfo(data) {
                this.$abcPage.$store.setCurPatientHospitalInfo(data);
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
