import BasePageStore from '@/core/page/store.js';

/**
* @desc 费用页面局部store，伴随page生命周期
*/
export default class HospitalInspectSettingPageStore extends BasePageStore {
    constructor() {
        const namespace = '@HospitalInspectSetting';
        const state = {
            clinicDeviceList: [],
        };
        super(namespace, state);
    }

    get clinicDeviceList() {
        return this.state.clinicDeviceList;
    }

    setClinicDeviceList(list) {
        this.state.clinicDeviceList = list;
    }
}
