<template>
    <stat-table-layout
        :table-key="tableCategory"
        :handle-table-mounted="handleTableMounted"
        :is-need-footer="false"
    >
        <template #header>
            <stat-toolbar
                :feature-configs="featureConfigs"
                :print-disabled="false"
                :filter-params="{
                    tableCategory,
                    ...filterParams
                }"
            ></stat-toolbar>
        </template>
        <template #content>
            <stat-chart-table
                :chart-list="chartCustomConfig"
                :loading="tablePresenter.loading ?? {}"
                @page-changed="handlePageChanged"
                @open-detail="handleOpenDetailDialog"
            >
            </stat-chart-table>
        </template>
    </stat-table-layout>
</template>

<script>
    import StatTableLayout from '@/views-hospital/statistics/components/stat-table-layout.vue';
    import StatToolbar from '@/views-hospital/statistics/components/stat-toolbar/index.vue';
    import StatChartTable from '@/views-hospital/statistics/components/stat-chart-table.vue';

    import {
        REPORT_TABLE_SCOPE,
        TABLE_CATEGORY_ENUM,
        tableInstanceConfigEnum,
    } from '@/views-hospital/statistics/pages/medical/components/disease-analysis/helper/constant';
    import { resolveToFilterParams } from '@/views-hospital/statistics/helper/utils';
    import { mapGetters } from 'vuex';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';
    import DiseaseDetailDialog from '@/views/statistics/patientStat/disease/disease-detail-dialog';

    export default {
        name: 'DiseaseAnalysisReport',
        components: {
            StatTableLayout,
            StatToolbar,
            StatChartTable,
        },
        props: {
            dateFilter: {
                type: Object,
                default: () => ({}),
            },
        },
        setup() {
            const {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            } = usePagination();
            return {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            };
        },
        data() {
            return {
                tableCategory: '',
                tablePresenter: null, //table实例
                filterParams: {}, //请求筛选参数
                tableCustomConfig: {}, //table配置 包含filter工具
                chartCustomConfig: [],
                isTablePrepared: false,
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            clinicId() {
                return this.currentClinic ? this.currentClinic.clinicId : '';
            },
            comDateFilter: {
                get() {
                    return this.dateFilter;
                },
                set(val) {
                    this.$emit('update:dateFilter', val);
                },
            },
            featureConfigs() {
                const filterTools = this.tableCustomConfig?.filterTools ?? [];
                const customConfig = filterTools.map((item) => ({
                    ...item,
                    changeMethod: this.handleFilterChange,
                }));
                return {
                    ...this.tableCustomConfig,
                    filterTools: customConfig,
                };
            },
        },
        created() {
            this.initTable(TABLE_CATEGORY_ENUM.DISEASE);
        },
        methods: {
            async handleTableMounted() {
                await this.loadTableData();
            },
            async handlePageChanged(data) {
                this.changePageIndex(data);
                await this.loadTableData();
                this.tablePresenter.updateFeatureConfig();
            },
            //初始化table
            async initTable(val) {
                //获取table实例
                this.tablePresenter = tableInstanceConfigEnum[val] && new tableInstanceConfigEnum[val](this, REPORT_TABLE_SCOPE[val]);
                if (!this.tablePresenter) return;
                await this.tablePresenter.init();
                this.tableCategory = val;
            },
            setTableCustomConfig(config, isInit = false) {
                this.tableCustomConfig = config;
                if (isInit) {
                    //根据filterTools处理成真正的请求参数
                    this.filterParams = resolveToFilterParams(this.tableCustomConfig?.filterTools ?? []);
                }
            },
            setChartCustomConfig(config) {
                this.chartCustomConfig = config.map((item) => {
                    if (item.type === 'statistic-card-group') {
                        item.cardItem = item.cardItem.map((one) => ({
                            ...one,
                            changeMethod: this.handleFilterChange,
                        }));
                        return item;
                    }
                    return {
                        ...item,
                        changeMethod: this.handleFilterChange,
                    };
                });
            },
            async loadTableData() {
                await this.tablePresenter.loadTableData();
                //获取table-renderConfig
                this.tablePresenter.createRenderConfig();
            },
            async handleFilterChange(filterValueKey, val, needReset = true) {
                if (filterValueKey === 'tableCategory') {
                    if (val === this.tableCategory) return;
                    await this.initTable(val);
                } else if (filterValueKey === 'category') {
                    this.filterParams[filterValueKey] = val;
                    await this.tablePresenter.fetchDoctorDiagnosisSort();
                    this.tablePresenter.updateFeatureConfig();
                } else if (filterValueKey === 'employeeId') {
                    this.filterParams[filterValueKey] = val;
                    await Promise.all([
                        this.tablePresenter.fetchDiagnoseDetailTotal(),
                        this.tablePresenter.loadTableData(),
                    ]);
                    this.tablePresenter.updateFeatureConfig();
                } else {
                    this.filterParams[filterValueKey] = val;
                    if (filterValueKey === 'dateFilter') {
                        this.comDateFilter = val;
                    }
                    if (needReset) {
                        this.tablePresenter.refreshFeatureConfig && this.tablePresenter.refreshFeatureConfig();
                        this.tablePresenter.loadTableData();
                    }
                }
            },
            handleOpenDetailDialog(type) {
                this.diseaseDetailDialog = new DiseaseDetailDialog({
                    value: true,
                    type,
                    beginDate: this.filterParams.dateFilter.begin,
                    endDate: this.filterParams.dateFilter.end,
                    clinicId: this.clinicId,
                    onClose: () => {
                        this.diseaseDetailDialog?.destroyDialog();
                    },
                });
                this.diseaseDetailDialog.generateDialog({ parent: this });
            },
        },
    };
</script>
