import { isEqual } from 'utils/lodash';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import {
    DIMENSION_ENUM, dimensionApiEnum, dimensionLabelEnum,
} from '@/views-hospital/statistics/pages/medical/components/follow-up-analysis/components/constant';
import { PersonalTableConfig } from '@/views-hospital/statistics/pages/medical/components/follow-up-analysis/components/personal';
import { DetailTableConfig } from '@/views-hospital/statistics/pages/medical/components/follow-up-analysis/components/detail';
import OperationStatAPI from 'views/statistics/core/api/operation-stat';
import StatApi from 'api/stat';
import qs from 'qs';
import { exportFileByAxios } from 'utils/excel';

// 门诊挂号
export default class ChronicCoreChargeAnalysisStatementReportTable extends StatBaseTable {
    constructor(...args) {
        super(...args);
        this.dimension = DIMENSION_ENUM.PERSONAL;

        this.employeeList = [];
    }

    createStaticConfig() {
        const mapping = {
            [DIMENSION_ENUM.PERSONAL]: PersonalTableConfig,
            [DIMENSION_ENUM.DETAIL]: DetailTableConfig,
        };
        return mapping[this.dimension];
    }

    async refreshFeatureConfig() {
        if (this.dimension === DIMENSION_ENUM.DETAIL) {
            await this.fetchEmployeesList();
        }
        super.refreshFeatureConfig();
    }
    async fetchEmployeesList() {
        try {
            const {
                beginDate, endDate, clinicId,
            } = this.createFetchParams();
            const { data } = await StatApi.fetchFollowupSelect({
                beginDate, endDate, clinicId,
            });
            this.employeeList = data.employeeEntityList.map((item) => ({
                value: item.id,
                label: item.name,
            }));
        } catch (e) {
            this.employeeList = [];
        }
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'achievement-registration',
            tableKey: `chronic-core-charge-analysis-statement-${this.dimension}`,
            isEnablePagination: this.dimension === DIMENSION_ENUM.DETAIL,
            filterTools: [
                {
                    label: '计划日期',
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: 'select',
                    valueKey: 'personnelId',
                    clearable: true,
                    placeholder: '随访人',
                    width: 120,
                    options: this.employeeList,
                    isHidden: this.dimension === DIMENSION_ENUM.PERSONAL,
                },
            ].filter((item) => !item.isHidden),
            dimensions: {
                options: [
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.PERSONAL],
                        value: DIMENSION_ENUM.PERSONAL,
                    },
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.DETAIL],
                        value: DIMENSION_ENUM.DETAIL,
                    },
                ],
                curDimension: this.dimension,
            },
        };
    }

    createFetchParams() {
        const {
            dateFilter: {
                begin, end,
            },
            sortConfig: {
                orderBy = '',
                orderType = '',
            } = {},
            personnelId,
        } = this.view.filterParams;
        const params = {
            beginDate: begin,
            endDate: end,
            clinicId: this.queryClinicId(),
            order: orderBy,
            sort: orderType,
        };
        if (this.dimension === DIMENSION_ENUM.DETAIL) {
            Object.assign(params, {
                offset: this.view.pageParams.offset,
                size: this.view.pageParams.limit,
                personnelId,
            });
        }
        return params;
    }

    async loadTableData() {
        const params = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await OperationStatAPI.followUp[dimensionApiEnum[this.dimension]](params);
            const afterParams = this.createFetchParams();
            if (isEqual(params, afterParams)) {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (e) {
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];
        const { renderTypeList } = ChronicCoreChargeAnalysisStatementReportTable;

        const staticConfig = this.createStaticConfig();
        this.tableRenderConfig = resolveRenderConfig(
            header,
            renderTypeList,
            staticConfig,
            this.renderTypeMap,
            this.headerRenderTypeMap,
            this.headerAppendRenderTypeMap,
        );
    }

    setDimension(val) {
        this.dimension = val;
        this.view.setTableCustomConfig(this.createFeatureConfigs());
    }

    async export() {
        const {
            dateFilter: {
                begin, end,
            },
            clinicId,
        } = this.view.filterParams;
        const params = {
            beginDate: `${begin} 00:00:00`, // 开始时间
            endDate: `${end} 23:59:59`, // 截止时间
            clinicId, // 门店id
        };
        const url = '/api/v2/sc/stat/medical/followup/export';
        return exportFileByAxios({
            url,
            params,
            paramsSerializer(p) {
                return qs.stringify(p);
            },
        });
    }
}
