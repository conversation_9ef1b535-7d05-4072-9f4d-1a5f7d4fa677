import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import ExportService from 'views/statistics/core/services/export/export-service';
import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import { isEqual } from 'utils/lodash';
import StockCheckAPI from 'views/statistics/core/api/stock-check';
import store from '@/store';

// 盘点统计
export default class StockCheckTable extends StatBaseTable {
    constructor(view) {
        super(view);
        this.multiPharmacyCanUse = store.getters.multiPharmacyCanUse;

        // key与renderType的映射关系
        this.renderTypeMap = {

        };
        // key与headerRenderType的映射关系
        this.headerRenderTypeMap = {

        };
        // key与headerAppendRenderType的映射关系
        this.headerAppendRenderTypeMap = {

        };

        this.descriptionMap = {
        };
    }
    static staticConfig = {
        'hasInnerBorder': true,
        'hasHeaderBorder': true,
        'list': [{
            'label': '门店',
            'key': 'clinicName',
            'pinned': false,
            'position': 1,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },{
            'label': '库房',
            'key': 'pharmacyName',
            'pinned': false,
            'position': 2,
            'sortable': false,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },{
            'label': '盈亏品种',
            'key': 'kindCount',
            'autoSort': true,
            'pinned': false,
            'position': 3,
            'sortable': true,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },{
            'label': '盈亏数量',
            'key': 'countText',
            'autoSort': true,
            'pinned': false,
            'position': 4,
            'sortable': true,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        },{
            'label': '盈亏进价金额（含税）',
            'key': 'checkCostText',
            'colType': 'money',
            'autoSort': true,
            'pinned': false,
            'position': 5,
            'sortable': true,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '180px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        },{
            'label': '盈亏进价金额（不含税）',
            'key': 'checkCostExcludeTaxText',
            'colType': 'money',
            'autoSort': true,
            'pinned': false,
            'position': 6,
            'sortable': true,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '180px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        },{
            'label': '盈亏销售金额（含税）',
            'key': 'checkAmountText',
            'colType': 'money',
            'autoSort': true,
            'pinned': false,
            'position': 7,
            'sortable': true,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '180px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        },{
            'label': '盈亏销售金额（不含税）',
            'key': 'checkAmountExcludeTaxText',
            'colType': 'money',
            'autoSort': true,
            'pinned': false,
            'position': 8,
            'sortable': true,
            'headerStyle': {
                'textAlign': 'center',
            },
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '180px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        }],
    };

    async init() {
        await this.initFeatureConfig();
    }

    async initFeatureConfig() {
        this.view.setTableCustomConfig(this.createFeatureConfigs(),true);
        this.initFilterOptions();
    }
    async initFilterOptions() {
        this.refreshFeatureConfig();
    }

    async refreshFeatureConfig() {
        this.view.setTableCustomConfig(this.createFeatureConfigs());
    }

    createRenderList() {
        return {};
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'check-list',
            tableKey: 'stock-check',
            isEnablePagination: false,
            filterTools: [
                {
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                    isYearLimitDate: true,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
            ].filter((item) => !item.isHidden),
        };
    }

    createFetchParams() {
        const { permission } = usePermission('isChainAdmin');
        const { isChainAdmin } = permission;
        const {
            begin, end,
        } = this.view.filterParams.dateFilter;
        return {
            beginDate: begin,
            endDate: end,
            clinicId: this.queryClinicId(),
            isMultiPharmacy: isChainAdmin ? 0 : +this.multiPharmacyCanUse,
        };
    }


    async loadTableData() {
        const beforeParams = this.createFetchParams();
        this.loading = true;
        try {
            const data = await StockCheckAPI.getStockCheck({
                ...beforeParams,
            });
            const afterParams = this.createFetchParams();
            if (isEqual(beforeParams, afterParams)) {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (e) {
            console.log(e);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];

        const renderTypeList = this.createRenderList();
        const { staticConfig } = StockCheckTable;
        this.tableRenderConfig = resolveRenderConfig(header,renderTypeList,staticConfig,
            this.renderTypeMap,this.headerRenderTypeMap,this.headerAppendRenderTypeMap,this.descriptionMap);
    }

    async export() {
        this.exportService = new ExportService();
        const params = this.createFetchParams();
        try {
            await this.exportService.startExport(this.view.tableCustomConfig?.exportTaskType, {
                ...params,
            });
        } catch (e) {
            console.error(e);
            return false;
        }
        return true;
    }
}
