import ProFilterConfig from '@/views-hospital/statistics/components/stat-toolbar/pro-filter-config';
import RevenueAPI from 'views/statistics/core/api/revenue';
import { RecommendService } from '@/service/recommend';
import SelectionAPI from '@/views-hospital/statistics/api/selection';
import useVisitSourceIdList from '@/views-hospital/statistics/hooks/useVisitSourceIdList';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import {
    GoodsSubTypeEnum, GoodsTypeEnum,
} from '@abc/constants/src';

export default class OutpatientChargeTypeTableProFilterConfig extends ProFilterConfig {
    constructor(view,calcTableKey,params,mode) {
        super(calcTableKey);
        this.view = view;
        this.params = params;
        this.calcTableKey = calcTableKey;
        this.mode = mode;
        this.employeeList = [];
        this.actionOptions = [];
        this.cashierList = [];
        this.payModeFilterOptions = [];
        this.billDepartmentList = [];
        this.visitSourceFilterOptions = [];
        this.referralDoctorList = [];

        this.sourceInputTypeIsCascader = false;

        this.initProFilterConfig();
    }
    initProFilterConfig() {
        this.getListSource();
    }
    async getOriginalProFilterConfig () {
        if (this.mode !== 'init') {
            await this.refreshFeatureConfig();
        }
        const {
            permission,
        } = usePermission('subClinics','isChainAdmin');
        const {
            subClinics,isChainAdmin,
        } = permission;
        return [
            {
                type: 'datePicker',
                valueKey: 'dateFilter',
                initData: this.view.comDateFilter,
            },
            {
                type: 'select',
                valueKey: 'clinicId',
                placeholder: '门店',
                options: subClinics,
                innerWidth: 200,
                isHidden: !isChainAdmin,
            },
            {
                type: 'patientSelector',
                valueKey: 'patientId',
                placeholder: '搜索患者',
            },
            {
                type: 'productSelector',
                valueKey: 'productId',
                placeholder: '搜索医嘱',
                productTypes: [
                    {
                        type: GoodsTypeEnum.MEDICINE,
                        subType: [
                            GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine,
                            GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM,
                        ],
                    }, // type = 1, 药品
                    { type: GoodsTypeEnum.EXAMINATION }, // type = 3, 检验检查
                    { type: GoodsTypeEnum.TREATMENT }, // type = 4, 治疗理疗
                    { type: GoodsTypeEnum.NURSE }, // type = 21, 护理费
                    { type: GoodsTypeEnum.LEAVE_HOSPITAL }, // type = 23, 出院转院
                    { type: GoodsTypeEnum.CONSULTATION }, // type = 25, 会诊
                    { type: GoodsTypeEnum.SURGERY }, // type = 29, 手术
                ],
            },
            {
                type: 'cascader',
                valueKey: 'feeTypeFilter',
                placeholder: '项目分类',
                options: this.feeTypeFilterOptions,
                initData: [],
            },
            {
                type: 'select',
                valueKey: 'action',
                placeholder: '类型',
                options: this.actionOptions,
                initData: [],
                multiLabelMode: 'text',
                multiple: true,
            },
            {
                type: 'payMode',
                valueKey: 'payModes',
                placeholder: '支付方式',
                options: this.payModeFilterOptions,
                initData: [],
            },
            {
                type: 'select',
                valueKey: 'sellerId',
                placeholder: '收费员',
                options: this.cashierList,
                withSearch: true,
            },
            {
                type: 'select',
                valueKey: 'doctorId',
                placeholder: '开单人',
                options: this.employeeList,
                valueWithLabel: true,
                withSearch: true,
            },
            {
                type: 'select',
                valueKey: 'sourceType',
                placeholder: '来源',
                options: this.sourceTypeOptions,
            },
            {
                type: 'select',
                valueKey: 'billDepartment',
                placeholder: '开单科室',
                options: this.billDepartmentList,
                withSearch: true,
            },
            {
                type: 'cascader',
                valueKey: 'visitSourceIdList',
                needOriValue: true,
                placeholder: '本次推荐',
                options: this.visitSourceFilterOptions,
                mutuallyExclusive: true,
                separation: '-',
                initData: [],
                width: 100,
                props: {
                    children: 'children',
                    label: 'name',
                    value: 'newId',
                },
            },
            {
                type: 'select',
                valueKey: 'revisitStatus',
                placeholder: '初复诊',
                options: [
                    {
                        label: '初诊',
                        value: 1,
                    },
                    {
                        label: '复诊',
                        value: 2,
                    },
                ],
            },
            {
                type: 'select',
                valueKey: 'referralDoctorId',
                placeholder: '转诊医生',
                options: this.referralDoctorList,
                withSearch: true,
            },
        ];
    }

    async refreshFeatureConfig() {
        const promises = [
            this.fetchEmployeeSelection(),
            this.fetchPersonOptions(),
            this.fetchRevenueSourceTypeSelection(),
            this.fetchCashierOptions(),
            this.fetchPayModeOptions(),
            this.fetchReferralDoctorList(),
            this.fetchClinicDepartments(),
            this.fetchFeeTypeOptions(),
        ];
        await Promise.all(promises);
    }

    async fetchEmployeeSelection() {
        const {
            beginDate, endDate,clinicId,employeeTypeEnum,
        } = this.params;
        try {
            const { data = [] } = await RevenueAPI.getEmployeeSelection({
                includeWriter: 0,
                employeeTypeEnum,
                beginDate,
                endDate,
                clinicId,
            });
            this.employeeList = data?.map((item) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            }) || [];
        } catch (e) {
            console.log(e);
            this.employeeList = [];
        }
    }

    async fetchPersonOptions() {
        const {
            beginDate, endDate,clinicId,
        } = this.params;
        try {
            const { data } = await RevenueAPI.getRevenueTypeSelection({
                beginDate,
                endDate,
                clinicId,
            });
            this.actionOptions = data?.actions?.map((item) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            }) || [];
        } catch (e) {
            this.actionOptions = [];
            console.log(e);
        }
    }

    async fetchRevenueSourceTypeSelection() {
        const {
            beginDate, endDate,clinicId,
        } = this.params;
        try {
            const { data } = await RevenueAPI.getRevenueSourceTypeSelection({
                beginDate,
                endDate,
                clinicId,
            });
            this.sourceTypeOptions = (data ?? []).map((item) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            });
            this.sourceInputTypeIsCascader = false;
        } catch (error) {
            this.sourceTypeOptions = [];
            console.error(error);
        }
    }

    async fetchCashierOptions() {
        const {
            beginDate, endDate,clinicId,
        } = this.params;
        try {
            const res = await RevenueAPI.getCashierSelection({
                beginDate,
                endDate,
                clinicId,
            });
            this.cashierList = (res?.data ?? []).map((item) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            });
        } catch (e) {
            console.log(e);
            this.cashierList = [];
        }
    }

    async fetchPayModeOptions() {
        const {
            beginDate, endDate,clinicId,employeeTypeEnum,
        } = this.params;
        try {
            const res = await RevenueAPI.getPayModeOptions({
                beginDate,
                endDate,
                clinicId,
                employeeTypeEnum,
            });

            this.payModeFilterOptions = res ?? [];
        } catch (e) {
            console.log(e);
            this.payModeFilterOptions = [];
        }
    }

    async fetchClinicDepartments() {
        const {
            beginDate, endDate,clinicId,
        } = this.params;
        try {
            const { data } = await SelectionAPI.department.getChargeDepartmentSelection({
                scope: 4,
                beginDate,
                endDate,
                clinicId,
            });
            this.billDepartmentList = (data ?? [])?.map((item) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            });
        } catch (e) {
            console.log(e);
            this.billDepartmentList = [];
        }
    }

    async getListSource() {
        if (!RecommendService.getInstance().originOptions.length) {
            await RecommendService.getInstance().structureOriginOptions();
        }
        this.visitSourceFilterOptions = RecommendService.getInstance().cascaderOptions;
        this.visitSourceFilterOptions.unshift({
            id: '00000000000000000000000000000000',
            name: '未指定',
        });
        const { processIds } = useVisitSourceIdList([]);
        this.visitSourceFilterOptions = processIds(this.visitSourceFilterOptions).map((item) => ({
            ...item,
            value: item.id,
        }));
    }

    async fetchReferralDoctorList() {
        const {
            beginDate, endDate,clinicId,
        } = this.params;
        try {
            const { data } = await SelectionAPI.employee.getChargeEmployeeSelection({
                scope: 6,
                beginDate,
                endDate,
                clinicId,
            });
            this.referralDoctorList = (data ?? [])?.map((item) => ({
                value: item.id,
                label: item.name,
            }));
        } catch (e) {
            console.log(e);
            this.referralDoctorList = [];
        }
    }
    async fetchFeeTypeOptions() {
        const {
            beginDate, endDate,clinicId,employeeTypeEnum,
        } = this.params;
        try {
            this.feeTypeFilterOptions = await RevenueAPI.getFeeTypeOptions({
                beginDate,
                endDate,
                clinicId,
                employeeTypeEnum,
            });
        } catch (e) {
            console.log(e);
            this.feeTypeFilterOptions = [];
        }
    }
}
