import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { isEqual } from 'utils/lodash';
import ExportService from 'views/statistics/core/services/export/export-service';
import OperationStatAPI from 'views/statistics/core/api/operation-stat';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import {
    ReportScopeEnum, ReportTypeLabelEnum,
} from '@/views-hospital/statistics/pages/revenue/components/charge-daily-report/helper/constant';
import {
    SummaryTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/charge-daily-report/components/collection-statement/summary';
import {
    OutpatientTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/charge-daily-report/components/collection-statement/outpatient';
import {
    PETableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/charge-daily-report/components/collection-statement/pe';
import {
    InpatientTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/charge-daily-report/components/collection-statement/inpatient';
import StatReportTable from '@/views-hospital/statistics/helper/stat-report-table';


export default class CollectionStatementReportTable extends StatReportTable {
    constructor(view, scope) {
        super(view);
        this.view = view;
        this.scope = scope;
        this.loading = false;
        this.employeeList = [];

        this.customStyle = {
            thStyle: {
                fontSize: '13.33px',
            },
            tdStyle: {
                fontSize: '13.33px',
            },
        };
    }

    createStaticConfig() {
        if (this.scope === ReportScopeEnum.OUTPATIENT_HOSPITAL || this.scope === ReportScopeEnum.OUTPATIENT_HOSPITAL_PE) {
            return SummaryTableConfig;
        }
        if (this.scope === ReportScopeEnum.OUTPATIENT) {
            return OutpatientTableConfig;
        }
        if (this.scope === ReportScopeEnum.HOSPITAL) {
            return InpatientTableConfig;
        }
        if (this.scope === ReportScopeEnum.PHYSICAL_EXAMINATION) {
            return PETableConfig;
        }
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin', 'subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'revenue-cost-recipt-summary',
            tableKey: `collection-statement-${this.scope}`,
            isEnablePrint: true,
            isNeedFooter: false,
            filterTools: [
                {
                    type: 'dateTimePicker',
                    valueKey: 'dateTimeFilter',
                    initData: this.view.filterParams.dateTimeFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: 'select',
                    valueKey: 'cashierIds',
                    clearable: true,
                    placeholder: '收费员',
                    width: 120,
                    options: this.employeeList,
                    initData: [],
                    multiple: true,
                    multiLabelMode: 'text',
                    withSearch: true,
                },
            ].filter((item) => !item.isHidden),
        };
    }

    createFetchParams() {
        const {
            begin, end,
        } = this.view.filterParams.dateTimeFilter;
        const { cashierIds } = this.view.filterParams;
        return {
            clinicId: this.queryClinicId(),
            beginDate: begin,
            endDate: end,
            scope: this.scope,
            cashierIds: cashierIds || [],
        };
    }

    async refreshFeatureConfig() {
        await this.fetchEmployeeSelection();
        this.view.setTableCustomConfig(this.createFeatureConfigs());
    }

    async loadTableData() {
        const params = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await OperationStatAPI.collectionStatement.report({
                ...params,
            });
            if (isEqual(params, this.createFetchParams())) {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (err) {
            this.loading = false;
            console.log(err);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];
        const normalTableHeader = this.handleOtherCol(header);
        const {
            renderTypeList,
        } = CollectionStatementReportTable;
        const staticConfig = this.createStaticConfig();
        this.tableRenderConfig = resolveRenderConfig(normalTableHeader,renderTypeList,staticConfig,
            this.renderTypeMap,this.headerRenderTypeMap,this.headerAppendRenderTypeMap);
    }

    createPrintHeader() {
        const header = this.handleOtherCol(this.table?.tableHeader ?? [],true);
        return this.handleReportTableStyle(header,this.customStyle);
    }

    async fetchEmployeeSelection() {
        try {
            const {
                beginDate, endDate, clinicId,
            } = this.createFetchParams();
            const { data } = await OperationStatAPI.collectionStatement.cashier({
                beginDate, endDate, clinicId,
            });
            this.employeeList = (data ?? []).map((item) => ({
                label: item.name,
                value: item.id,
            }));
        } catch (err) {
            console.log(err);
        }
    }


    createReportTitle() {
        const {
            permission,
        } = usePermission('currentClinic', 'isChainAdmin', 'subClinics');
        const {
            currentClinic, isChainAdmin, subClinics,
        } = permission;
        let clinicName = currentClinic.name;
        if (isChainAdmin) {
            clinicName = '';
        }
        if (this.view.filterParams.clinicId) {
            const clinicsList = subClinics ?? [];
            const clinic = clinicsList.find((_clinic) => _clinic.id === this.view.filterParams.clinicId);
            if (clinic) {
                clinicName = clinic.shortName || clinic.name;
            }
        }
        return `${clinicName}${ReportTypeLabelEnum[this.view.tableCategory]}收款汇总报表`;
    }
    createDateFilter$() {
        return this.view.filterParams.dateTimeFilter;
    }

    async export() {
        this.exportService = new ExportService();
        const params = this.createFetchParams();
        try {
            await this.exportService.startExport('revenue-cost-recipt-summary', {
                ...params,
            });
        } catch (e) {
            console.error(e);
            return false;
        }
        return true;
    }
}
