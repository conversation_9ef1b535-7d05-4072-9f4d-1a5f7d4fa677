<template>
    <app-cont-card sup-name="@StatisticsRevenue">
        <router-view :date-filter.sync="dateFilter"></router-view>
    </app-cont-card>
</template>

<script>
    import AppContCard from '@/views-hospital/statistics/components/app-cont-card.vue';
    import { formatDate } from '@tool/date';

    export default {
        name: 'RevenueIndex',
        components: { AppContCard },
        data() {
            const date = formatDate(new Date());
            return {
                dateFilter: {
                    begin: date,
                    end: date,
                },
            };
        },
    };
</script>
