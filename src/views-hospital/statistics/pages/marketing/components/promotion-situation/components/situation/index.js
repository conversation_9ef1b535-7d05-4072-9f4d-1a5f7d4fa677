import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { AbcDatePickerBar } from '@abc/ui-pc';
const { DatePickerBarOptions } = AbcDatePickerBar;
import { formatDate } from '@abc/utils-date';
import { dateRangeFormat } from 'views/statistics/common/util';
import { formatMoney } from '@/filters';
import PromotionApi from 'views/statistics/core/api/promotion';
import ConsumptionAmountModel from 'views/statistics/promotion/situation/adapter/consumption-amount';
import ConsumptionPeopleModel from 'views/statistics/promotion/situation/adapter/consumption-people';
import ChargeAmountModel from 'views/statistics/promotion/situation/adapter/charge-amount';
import ChargePeopleModel from 'views/statistics/promotion/situation/adapter/charge-people';
import GiftTotalAmountModel from 'views/statistics/promotion/situation/adapter/gift-total-amount';
import CouponTotalAmountModel from 'views/statistics/promotion/situation/adapter/coupon-total-amount';
import { getApp } from '@/core';

export default class MarketingPromotionSituationReportTable extends StatBaseTable {
    constructor(...args) {
        super(...args);

        this.memberData = {};
        this.consumptionAmountData = {};
        this.consumptionPeopleData = {};
        this.cardItemData = {};
        this.cardsSeriesData = [];
        this.cardsCategory = [];
        this.paysSeriesData = [];
        this.paysLegend = [];
        this.paysCategory = [];
        this.couponData = {};
        this.couponAnalysisCategory = [];
        this.couponAnalysisSeries = [];
        this.couponTotalAmountData = [];
        this.specialData = {};
        this.specialAmountData = [];
        this.specialPeopleData = [];
        this.discountData = {};
        this.chargeAmountData = [];
        this.chargePeopleData = [];
        this.pharmacyGiftData = {};
        this.pharmacyGiftTotalAmountData = [];
        this.pharmacyGiftCustomerPriceSeries = [];
        this.pharmacyGiftCustomerPriceLegend = [];
        this.pharmacyGiftCustomerPriceCategory = [];
        this.giftData = {};
        this.giftTotalAmountData = [];
        this.giftCustomerPriceSeries = [];
        this.giftCustomerPriceLegend = [];
        this.giftCustomerPriceCategory = [];

        this.loading = {
            memberChart1: false,
            memberChart2: false,
            cardChart1: false,
            cardChart2: false,
            couponChart1: false,
            couponChart2: false,
            specialChart1: false,
            specialChart2: false,
            discountChart1: false,
            discountChart2: false,
            pharmacyGiftChart1: false,
            pharmacyGiftChart2: false,
            giftChart1: false,
            giftChart2: false,
        };

        this.showCardOverview = getApp().store.state.viewDistribute.viewDistributeConfig.Statistics.promotionStat.showCardOverview;
        this.showCouponOverview = getApp().store.state.viewDistribute.viewDistributeConfig.Statistics.promotionStat.showCouponOverview;
        this.discountStatSectionTitle = getApp().store.state.viewDistribute.viewDistributeConfig.Statistics.promotionStat.discountStatSectionTitle;
        this.giftStatSectionTitle = getApp().store.state.viewDistribute.viewDistributeConfig.Statistics.promotionStat.giftStatSectionTitle;
        this.showSpecialStatSection = getApp().store.state.viewDistribute.viewDistributeConfig.Statistics.promotionStat.showSpecialStatSection;
        this.showPharmacyGiftSection = getApp().store.state.viewDistribute.viewDistributeConfig.Statistics.promotionStat.showPharmacyGiftSection;
        this.showDiscountStatSection = getApp().store.state.viewDistribute.viewDistributeConfig.Statistics.promotionStat.showDiscountStatSection;
    }
    refreshFeatureConfig() {
        this.view.setChartCustomConfig(this.createChartList());
        super.refreshFeatureConfig();
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        const date = formatDate(new Date());
        return {
            isEnableExport: false,
            filterTools: [
                {
                    type: 'datePickerBar',
                    valueKey: 'dateType',
                    dateFilterKey: 'dateFilter',
                    options: [
                        {
                            label: DatePickerBarOptions.DAY.label,
                            name: DatePickerBarOptions.DAY.name,
                            getValue() {
                                return [new Date(), new Date()];
                            },
                        },
                        DatePickerBarOptions.WEEK,
                        DatePickerBarOptions.MONTH,
                        DatePickerBarOptions.YEAR,
                    ],
                    pickerOptions: DatePickerBarOptions,
                    initData: DatePickerBarOptions.DAY.label,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: '',
                    valueKey: 'dateFilter',
                    initData: {
                        begin: date,
                        end: date,
                    },
                },
            ].filter((item) => !item.isHidden),
        };
    }
    createChartList() {
        const { permission } = usePermission('isChainAdmin','isChainSubStore');
        const {
            dateFilter: {
                begin, end,
            } = {},
        } = this.view.filterParams;
        const dateText = dateRangeFormat(begin, end);
        const chartList = [
            {
                flex: '0 0 100%',
                type: 'statistic-title',
                title: '会员',
            },
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 380,
                cardItem: [
                    {
                        chartCategory: 'member1',
                        topTitle: '新增会员人数',
                        topContent: dateText,
                        title: this.memberData.newMemberCount || 0,
                    },
                    {
                        chartCategory: 'member2',
                        topTitle: '会员消费金额',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.memberData.totalPrice) || '0.00',
                    },
                    {
                        chartCategory: 'member3',
                        topTitle: '充值金额',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.memberData.rechargePrice) || '0.00',
                        bottomContent: `本金${formatMoney(this.memberData.rechargePrincipalPrice)} | 赠金${formatMoney(this.memberData.rechargePresentPrice)}`,
                    },
                    {
                        chartCategory: 'member4',
                        topTitle: '余额消费',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.memberData.consumePrice) || '0.00',
                    },
                ],
            },
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 760,
                cardItem: [
                    {
                        type: 'pie-chart',
                        chartCategory: 'memberChart1',
                        label: '消费金额对比',
                        labelTips: '各类会员对应的收费单金额占比',
                        data: this.consumptionAmountData.data || [],
                        chartProps: {
                            width: '540px',
                            height: '240px',
                            legend: this.consumptionAmountData.legend || {},
                            center: [100, 125],
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                    {
                        type: 'pie-chart',
                        chartCategory: 'memberChart2',
                        label: '消费人次对比',
                        data: this.consumptionPeopleData.data || [],
                        chartProps: {
                            width: '540px',
                            height: '240px',
                            legend: this.consumptionPeopleData.legend || {},
                            center: [100, 125],
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                ],
            },
        ];
        if (this.showCardOverview) {
            chartList.push(...[
                {
                    flex: '0 0 100%',
                    type: 'statistic-title',
                    title: '卡项',
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 380,
                    cardItem: [
                        {
                            chartCategory: 'cardOverview1',
                            topTitle: '开卡人数',
                            topContent: dateText,
                            title: this.cardItemData.numberOfNewCard || 0,
                        },
                        {
                            chartCategory: 'cardOverview2',
                            topTitle: '开卡总金额',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.cardItemData.priceOfNewCard) || '0.00',
                        },
                        {
                            chartCategory: 'cardOverview3',
                            topTitle: '充值金额',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.cardItemData.rechargePrice) || '0.00',
                            bottomContent: `本金${formatMoney(this.cardItemData.rechargePrincipalPrice)} | 赠金${formatMoney(this.cardItemData.rechargePresentPrice)}`,
                        },
                        {
                            chartCategory: 'cardOverview4',
                            topTitle: '余额消费',
                            theme: 'warning',
                            topContent: dateText,
                            title: formatMoney(this.cardItemData.totalPrice) || '0.00',
                        },
                    ],
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 760,
                    cardItem: [
                        {
                            type: 'bar-chart',
                            chartCategory: 'cardChart1',
                            label: '开卡人数/购卡金额',
                            data: this.cardsSeriesData,
                            category: this.cardsCategory,
                            chartProps: {
                                width: '620px',
                                height: '250px',
                                barWidth: 16,
                                aAxisType: 'category',
                                yAxisType: 'value',
                                xAxisData: this.cardsCategory,
                                colors: ['#fd8351', '#546fc6'],
                                nameList: ['开卡人数', '购卡金额'],
                                legend: {
                                    data: ['开卡人数', '购卡金额'],
                                },
                                grid: {
                                    left: '1%',
                                    right: '0',
                                    top: '10%',
                                    bottom: '4%',
                                    containLabel: true,
                                },
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                        {
                            type: 'bar-chart',
                            chartCategory: 'cardChart2',
                            label: '余额支付',
                            data: this.paysSeriesData,
                            category: this.paysCategory,
                            chartProps: {
                                width: '545px',
                                height: '250px',
                                barWidth: 20,
                                aAxisType: 'category',
                                yAxisType: 'value',
                                xAxisData: this.paysCategory,
                                legend: {
                                    data: this.paysLegend,
                                },
                                grid: {
                                    left: '1%',
                                    right: '0',
                                    top: '10%',
                                    bottom: '4%',
                                    containLabel: true,
                                },
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                    ],
                },
            ]);
        }
        if (this.showCouponOverview || permission.isChainAdmin || permission.isChainSubStore) {
            chartList.push(...[
                {
                    flex: '0 0 100%',
                    type: 'statistic-title',
                    title: '优惠券',
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 380,
                    cardItem: [
                        {
                            chartCategory: 'coupon1',
                            topTitle: '领券/用券总数',
                            topContent: dateText,
                            title: `${this.couponData.gotCouponCount || 0}/${this.couponData.usedCouponCount || 0}` || 0,
                        },
                        {
                            chartCategory: 'coupon2',
                            topTitle: '用券总成交额',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.couponData.totalAmount) || '0.00',
                        },
                        {
                            chartCategory: 'coupon3',
                            topTitle: '折扣总金额',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.couponData.discountAmount) || '0.00',
                        },
                        {
                            chartCategory: 'coupon4',
                            topTitle: '费效比',
                            topContent: dateText,
                            title: `${parseFloat(((this.couponData.rate || 0) * 100)).toFixed(2)}%` || '0.00%',
                        },
                    ],
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 760,
                    cardItem: [
                        {
                            type: 'bar-chart',
                            chartCategory: 'couponChart1',
                            label: '使用分析',
                            data: this.couponAnalysisSeries,
                            category: this.couponAnalysisCategory,
                            chartProps: {
                                width: '620px',
                                height: '250px',
                                barWidth: 16,
                                aAxisType: 'category',
                                yAxisType: 'value',
                                xAxisData: this.couponAnalysisCategory,
                                colors: ['#5470c6', '#91cc75'],
                                nameList: ['领券总数', '用券总数'],
                                legend: {
                                    data: ['领券总数', '用券总数'],
                                },
                                grid: {
                                    left: '1%',
                                    right: '0',
                                    top: '10%',
                                    bottom: '4%',
                                    containLabel: true,
                                },
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                        {
                            type: 'pie-chart',
                            chartCategory: 'couponChart2',
                            label: '用券总成交额',
                            labelTips: '使用各个优惠券的收费单总金额占比',
                            data: this.couponTotalAmountData.data || [],
                            chartProps: {
                                width: '540px',
                                height: '240px',
                                legend: this.couponTotalAmountData.legend || {},
                                center: [100, 125],
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                    ],
                },
            ]);
        }
        if (this.showSpecialStatSection) {
            chartList.push(...[
                {
                    flex: '0 0 100%',
                    type: 'statistic-title',
                    title: '特价',
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 380,
                    cardItem: [
                        {
                            chartCategory: 'special1',
                            topTitle: '消费人数',
                            topContent: dateText,
                            title: this.specialData.patientOrderCount || 0,
                        },
                        {
                            chartCategory: 'special2',
                            topTitle: '消费金额',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.specialData.totalAmount) || '0.00',
                        },
                        {
                            chartCategory: 'special3',
                            topTitle: '折扣金额',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.specialData.discountAmount) || '0.00',
                        },
                        {
                            chartCategory: 'special4',
                            topTitle: '费效比',
                            topContent: dateText,
                            title: `${parseFloat(((this.specialData.rate || 0) * 100)).toFixed(2)}%` || '0.00%',
                        },
                    ],
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 760,
                    cardItem: [
                        {
                            type: 'pie-chart',
                            chartCategory: 'specialChart1',
                            label: '消费金额占比',
                            labelTips: '各类折扣活动对应的收费单金额占比',
                            data: this.specialAmountData.data || [],
                            chartProps: {
                                width: '540px',
                                height: '240px',
                                legend: this.specialAmountData.legend || {},
                                center: [100, 125],
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                        {
                            type: 'pie-chart',
                            chartCategory: 'specialChart2',
                            label: '消费人次',
                            labelTips: '使用折扣的收费人次',
                            data: this.specialPeopleData.data || [],
                            chartProps: {
                                width: '540px',
                                height: '240px',
                                legend: this.specialPeopleData.legend || {},
                                center: [100, 125],
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                    ],
                },
            ]);
        }
        chartList.push(...[
            {
                flex: '0 0 100%',
                type: 'statistic-title',
                title: this.discountStatSectionTitle,
            },
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 380,
                cardItem: [
                    {
                        chartCategory: 'discount1',
                        topTitle: '消费人数',
                        topContent: dateText,
                        title: this.discountData.patientOrderCount || 0,
                    },
                    {
                        chartCategory: 'discount2',
                        topTitle: '消费金额',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.discountData.totalAmount) || '0.00',
                    },
                    {
                        chartCategory: 'discount3',
                        topTitle: '折扣金额',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.discountData.discountAmount) || '0.00',
                    },
                    {
                        chartCategory: 'discount4',
                        topTitle: '费效比',
                        topContent: dateText,
                        title: `${parseFloat(((this.discountData.rate || 0) * 100)).toFixed(2)}%` || '0.00%',
                    },
                ],
            },
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 760,
                cardItem: [
                    {
                        type: 'pie-chart',
                        chartCategory: 'discountChart1',
                        label: '消费金额占比',
                        labelTips: '各类折扣活动对应的收费单金额占比',
                        data: this.chargeAmountData.data || [],
                        chartProps: {
                            width: '540px',
                            height: '240px',
                            legend: this.chargeAmountData.legend || {},
                            center: [100, 125],
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                    {
                        type: 'pie-chart',
                        chartCategory: 'discountChart2',
                        label: '消费人次',
                        labelTips: '使用折扣的收费人次',
                        data: this.chargePeopleData.data || [],
                        chartProps: {
                            width: '540px',
                            height: '240px',
                            legend: this.chargePeopleData.legend || {},
                            center: [100, 125],
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                ],
            },
        ]);
        if (this.showPharmacyGiftSection) {
            chartList.push(...[
                {
                    flex: '0 0 100%',
                    type: 'statistic-title',
                    title: '买赠',
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 380,
                    cardItem: [
                        {
                            chartCategory: 'pharmacyGift1',
                            topTitle: '消费人数',
                            topContent: dateText,
                            title: this.pharmacyGiftData.patientOrderCount || 0,
                        },
                        {
                            chartCategory: 'pharmacyGift2',
                            topTitle: '消费金额',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.pharmacyGiftData.totalAmount) || '0.00',
                        },
                        {
                            chartCategory: 'pharmacyGift3',
                            topTitle: '客单价',
                            topContent: dateText,
                            theme: 'warning',
                            title: formatMoney(this.pharmacyGiftData.avgAmount) || '0.00',
                        },
                        {
                            chartCategory: 'pharmacyGift4',
                            topTitle: '费效比',
                            topContent: dateText,
                            title: `${parseFloat(((this.pharmacyGiftData.rate || 0) * 100)).toFixed(2)}%` || '0.00%',
                        },
                    ],
                },
                {
                    flex: '0 0 100%',
                    type: 'statistic-card-group',
                    minWidth: 760,
                    cardItem: [
                        {
                            type: 'pie-chart',
                            chartCategory: 'pharmacyGiftChart1',
                            label: '收费总金额',
                            data: this.pharmacyGiftTotalAmountData.data || [],
                            chartProps: {
                                width: '540px',
                                height: '240px',
                                legend: this.pharmacyGiftTotalAmountData.legend || {},
                                center: [100, 125],
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                        {
                            type: 'bar-chart',
                            chartCategory: 'pharmacyGiftChart2',
                            label: '客单价',
                            data: this.pharmacyGiftCustomerPriceSeries,
                            category: this.pharmacyGiftCustomerPriceCategory,
                            chartProps: {
                                width: '545px',
                                height: '250px',
                                barWidth: 20,
                                aAxisType: 'category',
                                yAxisType: 'value',
                                xAxisData: this.pharmacyGiftCustomerPriceCategory,
                                legend: {
                                    data: this.pharmacyGiftCustomerPriceLegend,
                                },
                                grid: {
                                    left: '1%',
                                    right: '0',
                                    top: '10%',
                                    bottom: '4%',
                                    containLabel: true,
                                },
                            },
                            dateConfig: {
                                showDate: true,
                                dateText,
                            },
                        },
                    ],
                },
            ]);
        }
        chartList.push(...[
            {
                flex: '0 0 100%',
                type: 'statistic-title',
                title: this.giftStatSectionTitle,
            },
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 380,
                cardItem: [
                    {
                        chartCategory: 'giftStat1',
                        topTitle: '消费人数',
                        topContent: dateText,
                        title: this.giftData.patientOrderCount || 0,
                    },
                    {
                        chartCategory: 'giftStat2',
                        topTitle: '消费金额',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.giftData.totalAmount) || '0.00',
                    },
                    {
                        chartCategory: 'giftStat3',
                        topTitle: '客单价',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.giftData.avgAmount) || '0.00',
                    },
                    {
                        chartCategory: 'giftStat4',
                        topTitle: '优惠金额',
                        topContent: dateText,
                        theme: 'warning',
                        title: formatMoney(this.giftData.giftAmount) || '0.00',
                    },
                ],
            },
            {
                flex: '0 0 100%',
                type: 'statistic-card-group',
                minWidth: 760,
                cardItem: [
                    {
                        type: 'pie-chart',
                        chartCategory: 'giftChart1',
                        label: '收费总金额',
                        data: this.giftTotalAmountData.data || [],
                        chartProps: {
                            width: '540px',
                            height: '240px',
                            legend: this.giftTotalAmountData.legend || {},
                            center: [100, 125],
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                    {
                        type: 'bar-chart',
                        chartCategory: 'giftChart2',
                        label: '客单价',
                        data: this.giftCustomerPriceSeries,
                        category: this.giftCustomerPriceCategory,
                        chartProps: {
                            width: '545px',
                            height: '250px',
                            barWidth: 20,
                            aAxisType: 'category',
                            yAxisType: 'value',
                            xAxisData: this.giftCustomerPriceCategory,
                            legend: {
                                data: this.giftCustomerPriceLegend,
                            },
                            grid: {
                                left: '1%',
                                right: '0',
                                top: '10%',
                                bottom: '4%',
                                containLabel: true,
                            },
                        },
                        dateConfig: {
                            showDate: true,
                            dateText,
                        },
                    },
                ],
            },
        ]);
        return chartList;
    }

    createFetchParams() {
        const {
            dateFilter: {
                begin, end,
            } = {},
            clinicId,
        } = this.view.filterParams;
        return {
            clinicId,
            beginDate: begin,
            endDate: end,
        };
    }

    async loadTableData() {
        const { permission } = usePermission('isChainAdmin','isChainSubStore');
        try {
            const promiseList = [
                this.getMemberData(),
                this.getDisCountData(),
                this.getGiftData(),
            ];
            if (this.showCardOverview) {
                promiseList.push(this.getCardData());
            }
            if (this.showCouponOverview || !permission.isChainAdmin || permission.isChainSubStore) {
                promiseList.push(this.getCouponData());
            }
            if (this.showSpecialStatSection) {
                promiseList.push(this.getSpecialData());
            }
            if (this.showPharmacyGiftSection) {
                promiseList.push(this.getPharmacyGiftData());
            }
            await Promise.all(promiseList);
        } catch (err) {
            console.log(err);
        }
    }

    async getMemberData() {
        this.loading.memberChart1 = true;
        this.loading.memberChart2 = true;
        const params = this.createFetchParams();
        try {
            const { data } = await PromotionApi.situation.member(params);
            this.memberData = data;
            const consumptionAmountData = new ConsumptionAmountModel(this.memberData && this.memberData.amounts);
            this.consumptionAmountData = consumptionAmountData.data;
            const consumptionPeopleData = new ConsumptionPeopleModel(this.memberData && this.memberData.counts);
            this.consumptionPeopleData = consumptionPeopleData.data;
        } catch (error) {
            this.memberData = [];
            this.consumptionAmountData = {};
            this.consumptionPeopleData = {};
        } finally {
            this.loading.memberChart1 = false;
            this.loading.memberChart2 = false;
        }
    }
    async getCardData() {
        this.loading.cardChart1 = true;
        this.loading.cardChart2 = true;
        const params = this.createFetchParams();
        try {
            const { data } = await PromotionApi.situation.cardItem(params);
            this.cardItemData = data;

            const effectiveNewCards = this.cardItemData?.newCards?.filter((item) => item.numberOfNewCard || item.priceOfNewCard);
            this.cardsCategory = effectiveNewCards.map((item) => item.name);
            const cardsSeriesData = [effectiveNewCards.map((item) => item.numberOfNewCard)];
            cardsSeriesData.splice(1, 0, effectiveNewCards.map((item) => item.priceOfNewCard));
            this.cardsSeriesData = cardsSeriesData;

            const effectivePays = this.cardItemData && this.cardItemData.pays.filter((item) => item.totalPrice);
            this.paysSeriesData = effectivePays.map((item) => item.totalPrice);
            this.paysLegend = effectivePays.map((item) => item.name);
            this.paysCategory = effectivePays.map((item) => item.name);
        } catch (error) {
            this.cardItemData = {};
            this.cardsSeriesData = [];
            this.cardsCategory = [];
            this.paysSeriesData = [];
            this.paysLegend = [];
            this.paysCategory = [];
        } finally {
            this.loading.cardChart1 = false;
            this.loading.cardChart2 = false;
        }
    }
    async getCouponData() {
        this.loading.couponChart1 = true;
        this.loading.couponChart2 = true;
        const params = this.createFetchParams();
        try {
            const { data = {} } = await PromotionApi.situation.coupon(params);
            this.couponData = data;

            const effectiveGotCouponAnalyseList = data.gotCouponAnalyseList?.filter((item) => item.value);
            const effectiveUsedCouponAnalyseList = data.usedCouponAnalyseList?.filter((item) => item.value);
            const couponAnalysisCategory = effectiveGotCouponAnalyseList.map((item) => item.name);
            this.couponAnalysisCategory = couponAnalysisCategory && couponAnalysisCategory.concat(effectiveUsedCouponAnalyseList.map((item) => item.name));

            const couponAnalysisSeries = [effectiveGotCouponAnalyseList.map((item) => item.value)];
            couponAnalysisSeries.splice(1, 0, effectiveUsedCouponAnalyseList.map((item) => item.value));
            this.couponAnalysisSeries = couponAnalysisSeries;

            const couponTotalAmountData = new CouponTotalAmountModel(data.totalAmountList);
            this.couponTotalAmountData = couponTotalAmountData.data;
        } catch (error) {
            this.couponData = {};
            this.couponAnalysisCategory = [];
            this.couponAnalysisSeries = [];
            this.couponTotalAmountData = [];
        } finally {
            this.loading.couponChart1 = false;
            this.loading.couponChart2 = false;
        }
    }
    async getSpecialData() {
        this.loading.specialChart1 = true;
        this.loading.specialChart2 = true;
        const params = this.createFetchParams();
        try {
            const { data } = await PromotionApi.situation.pharmacySpecial(params);
            this.specialData = data;
            const chargeAmountData = new ChargeAmountModel(data.chargeList && data.chargeList);
            this.specialAmountData = chargeAmountData.data;
            const chargePeopleData = new ChargePeopleModel(data.chargeList && data.chargeList);
            this.specialPeopleData = chargePeopleData.data;
        } catch (error) {
            this.specialData = {};
            this.specialAmountData = [];
            this.specialPeopleData = [];
        } finally {
            this.loading.specialChart1 = false;
            this.loading.specialChart2 = false;
        }
    }
    async getDisCountData() {
        this.loading.discountChart1 = true;
        this.loading.discountChart2 = true;
        const params = this.createFetchParams();
        try {
            const { data = {} } = this.showDiscountStatSection ?
                await PromotionApi.situation.pharmacyDiscount(params) :
                await PromotionApi.situation.discount(params);
            this.discountData = data;
            const chargeAmountData = new ChargeAmountModel(data.chargeList && data.chargeList);
            this.chargeAmountData = chargeAmountData.data;
            const chargePeopleData = new ChargePeopleModel(data.chargeList && data.chargeList);
            this.chargePeopleData = chargePeopleData.data;
        } catch (error) {
            this.discountData = {};
            this.chargeAmountData = [];
            this.chargePeopleData = [];
        } finally {
            this.loading.discountChart1 = false;
            this.loading.discountChart2 = false;
        }
    }
    async getPharmacyGiftData() {
        this.loading.pharmacyGiftChart1 = true;
        this.loading.pharmacyGiftChart2 = true;
        const params = this.createFetchParams();
        try {
            const { data = {} } = await PromotionApi.situation.pharmacyGift(params);
            this.pharmacyGiftData = data;
            const giftTotalAmountData = new GiftTotalAmountModel(data.chargeList && data.chargeList.map((item) => {
                return {
                    id: item.id,
                    name: item.name,
                    amount: item.amount,
                };
            }));
            this.pharmacyGiftTotalAmountData = giftTotalAmountData.data;
            const effectiveGiftChargeList = data.chargeList?.filter((item) => item.avgAmount) || [];
            this.pharmacyGiftCustomerPriceSeries = effectiveGiftChargeList.map((item) => item.avgAmount);
            this.pharmacyGiftCustomerPriceLegend = effectiveGiftChargeList.map((item) => item.name);
            this.pharmacyGiftCustomerPriceCategory = effectiveGiftChargeList.map((item) => item.name);
        } catch (error) {
            this.pharmacyGiftData = {};
            this.pharmacyGiftTotalAmountData = [];
            this.pharmacyGiftCustomerPriceSeries = [];
            this.pharmacyGiftCustomerPriceLegend = [];
            this.pharmacyGiftCustomerPriceCategory = [];
        } finally {
            this.loading.pharmacyGiftChart1 = false;
            this.loading.pharmacyGiftChart2 = false;
        }
    }
    async getGiftData() {
        this.loading.giftChart1 = true;
        this.loading.giftChart2 = true;
        const params = this.createFetchParams();
        try {
            const { data = {} } = await PromotionApi.situation.gift(params);
            this.giftData = data;
            const giftTotalAmountData = new GiftTotalAmountModel(data.chargeList && data.chargeList.map((item) => {
                return {
                    id: item.id,
                    name: item.name,
                    amount: item.amount,
                };
            }));
            this.giftTotalAmountData = giftTotalAmountData.data;
            const effectiveGiftChargeList = data?.chargeList?.filter((item) => item.avgAmount) || [];
            this.giftCustomerPriceSeries = effectiveGiftChargeList.map((item) => item.avgAmount);
            this.giftCustomerPriceLegend = effectiveGiftChargeList.map((item) => item.name);
            this.giftCustomerPriceCategory = effectiveGiftChargeList.map((item) => item.name);
        } catch (error) {
            this.giftData = {};
            this.giftTotalAmountData = [];
            this.giftCustomerPriceSeries = [];
            this.giftCustomerPriceLegend = [];
            this.giftCustomerPriceCategory = [];
        } finally {
            this.loading.giftChart1 = false;
            this.loading.giftChart2 = false;
        }
    }
}
