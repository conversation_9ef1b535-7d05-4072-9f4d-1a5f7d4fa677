import { isEqual } from 'utils/lodash';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';

import ExportService from 'views/statistics/core/services/export/export-service';
import RecommendVisitApi from 'views/statistics/core/api/recommend-visit';
import { RecommendService } from '@/service/recommend';
import useVisitSourceIdList from '@/views-hospital/statistics/hooks/useVisitSourceIdList';

// 就诊推荐统计-推荐记录
export default class RecommendVisitRecordTable extends StatBaseTable {
    constructor(view) {
        super(view);

        this.visitSourceFilterOptions = [];

        // key与renderType的映射关系
        this.renderTypeMap = {

        };
        // key与headerRenderType的映射关系
        this.headerRenderTypeMap = {

        };
        // key与headerAppendRenderType的映射关系
        this.headerAppendRenderTypeMap = {

        };
    }

    static staticConfig = {
        'hasInnerBorder': true,
        'hasHeaderBorder': true,
        'list': [{
            'label': '门店',
            'key': 'sourceFromClinicName',
            'pinned': false,
            'position': 0,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '患者姓名',
            'key': 'patientName',
            'pinned': false,
            'position': 1,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '性别',
            'key': 'patientSex',
            'pinned': false,
            'position': 2,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '48px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '年龄',
            'key': 'patientAge',
            'pinned': false,
            'position': 3,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '56px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '手机号',
            'key': 'patientMobile',
            'pinned': false,
            'position': 4,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '首诊来源',
            'key': 'patientSourceType',
            'pinned': false,
            'position': 5,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '创建人',
            'key': 'createdByName',
            'pinned': false,
            'position': 8,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '创建时间',
            'key': 'created',
            'pinned': false,
            'position': 9,
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        }],
    };

    async init() {
        this.initFeatureConfig();
    }

    initFeatureConfig() {
        this.getListSource();
        this.view.setTableCustomConfig(this.createFeatureConfigs(),true);
        this.initFilterOptions();
    }
    async initFilterOptions() {
        this.refreshFeatureConfig();
    }

    async refreshFeatureConfig() {
        this.view.setTableCustomConfig(this.createFeatureConfigs());
    }

    createRenderList() {
        return {};
    }

    createDescriptionMap() {
        return {};
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const {
            subClinics,isChainAdmin,
        } = permission;
        return {
            isEnablePagination: true,
            exportTaskType: 'achievement-recommend',
            tableKey: 'recommend-visit-record',
            isMergedExport: true,
            filterTools: [
                {
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !isChainAdmin,
                },
                {
                    type: 'patientSelector',
                    valueKey: 'patientId',
                    placeholder: '搜索患者',
                    clinicId: '',
                    width: 120,
                },
                {
                    type: 'cascader',
                    valueKey: 'visitSourceIdList',
                    needOriValue: true,
                    initData: [],
                    props: {
                        children: 'children',
                        label: 'name',
                        value: 'newId',
                    },
                    placeholder: '来源',
                    width: 100,
                    options: this.visitSourceFilterOptions,
                    mutuallyExclusive: true,
                },
            ].filter((item) => !item.isHidden),
        };
    }

    createFetchParams() {
        const {
            begin, end,
        } = this.view.filterParams.dateFilter;
        const {
            oriCascadeValue,patientId,
        } = this.view.filterParams;
        const { visitSourceLevelIds } = useVisitSourceIdList(oriCascadeValue ?? []);
        const _visitSourceLevelIds = visitSourceLevelIds.value;
        const { permission } = usePermission('enablePatientMobile','enableCost','enableGross');
        return {
            beginDate: begin,
            endDate: end,
            offset: this.view.pageParams.offset,
            size: this.view.pageParams.limit,
            clinicId: this.queryClinicId(),
            patientId,
            visitSourceLevel1Ids: _visitSourceLevelIds.visitSourceLevel1Ids,
            visitSourceLevel2Ids: _visitSourceLevelIds.visitSourceLevel2Ids,
            visitSourceLevel3Ids: _visitSourceLevelIds.visitSourceLevel3Ids,
            ...permission,
        };
    }

    async loadTableData() {
        const beforeParams = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await RecommendVisitApi.record({
                ...beforeParams,
            });
            const afterParams = this.createFetchParams();
            if (isEqual(beforeParams, afterParams)) {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (e) {
            console.log(e);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    async getListSource() {
        if (!RecommendService.getInstance().originOptions.length) {
            await RecommendService.getInstance().structureOriginOptions();
        }
        this.visitSourceFilterOptions = RecommendService.getInstance().cascaderOptions;
        this.visitSourceFilterOptions.unshift({
            id: '00000000000000000000000000000000',
            name: '未指定',
        });
        const { processIds } = useVisitSourceIdList([]);
        this.visitSourceFilterOptions = processIds(this.visitSourceFilterOptions).map((item) => ({
            ...item,
            value: item.id,
        }));
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];

        const renderTypeList = this.createRenderList();
        const { staticConfig } = RecommendVisitRecordTable;
        const descriptionMap = this.createDescriptionMap();
        this.tableRenderConfig = resolveRenderConfig(header,renderTypeList,staticConfig,
            this.renderTypeMap,this.headerRenderTypeMap,this.headerAppendRenderTypeMap,descriptionMap);
    }

    async export(_,keys) {
        this.exportService = new ExportService();
        const params = this.createFetchParams();
        try {
            await this.exportService.startExport(this.view.tableCustomConfig?.exportTaskType, {
                ...params,
                keyWordList: keys,
            });
        } catch (e) {
            console.error(e);
            return false;
        }
        return true;
    }
}
