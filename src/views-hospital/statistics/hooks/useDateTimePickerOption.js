export default function useDateTimePickerOption() {
    const now = new Date();
    const nowDayOfWeek = now.getDay(); //今天本周的第几天
    const nowDay = now.getDate(); //当前日
    const nowMonth = now.getMonth(); //当前月
    const nowYear = now.getFullYear(); //当前年
    now.setDate(1);
    now.setMonth(now.getMonth() - 1);
    const lastYear = now.getFullYear();
    const lastMonth = now.getMonth();

    const _getMonthDays = (myYear, myMonth) => {
        const monthStartDate = new Date(myYear, myMonth, 1);
        const monthEndDate = new Date(myYear, myMonth + 1, 1);
        return (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24);
    };

    return {
        shortcuts: [{
            text: '今天',
            onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setHours(0,0);
                end.setHours(23,59);
                picker([start, end]);
            },
        }, {
            text: '昨天',
            onClick(picker) {
                const end = new Date(nowYear, nowMonth, nowDay - 1);
                const start = new Date(nowYear, nowMonth, nowDay - 1);
                end.setHours(23,59);
                picker([start, end]);
            },
        }, {
            text: '本周',
            onClick(picker) {
                let start = new Date(nowYear, nowMonth, nowDay - 6);
                if (nowDayOfWeek) {
                    start = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1);
                }
                const end = new Date();
                end.setHours(23,59);
                picker([start, end]);
            },
        }, {
            text: '本月',
            onClick(picker) {
                const start = new Date(nowYear, nowMonth, 1);
                const end = new Date();
                end.setHours(23,59);
                picker([start, end]);
            },
        }, {
            text: '上月',
            onClick(picker) {
                const start = new Date(lastYear, lastMonth, 1);
                const end = new Date(lastYear, lastMonth, _getMonthDays(lastYear, lastMonth));
                end.setHours(23,59);
                picker([start, end]);
            },
        }, {
            text: '今年',
            onClick(picker) {
                const start = new Date(nowYear, 0, 1);
                const end = new Date();
                end.setHours(23,59);
                picker([start, end]);
            },
        }],
        disabledDate(date) {
            return date > new Date();
        },
    };
}
