<template>
    <div class="inspect-history-item-wrapper">
        <template v-if="isInspect">
            <inspect-ris-detail
                v-if="historyDetailVis"
                :id="item.id"
                :is-show-copy-btn="isShowCopyBtn"
                :is-disable-copy-btn="isDisableCopyBtn"
                :is-two-column="isTwoColumn"
                :editable="editable"
                :sub-type="item.subType"
            ></inspect-ris-detail>
        </template>

        <template v-else>
            <examination-detail
                v-if="historyDetailVis"
                :id="item.id"
                :is-two-column="isTwoColumn"
            ></examination-detail>
        </template>
    </div>
</template>

<script>
    import ExaminationDetail from './examination-detail.vue';
    import InspectRisDetail from '@/views-hospital/inspect-diagnosis/components/inspect-assist/inspect-history/inspect-detail/index.vue';
    import {
        INSPECT_REPORT_TYPE,
    } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';

    export default {
        name: 'InspectHistoryItem',
        components: {
            ExaminationDetail,
            InspectRisDetail,
        },
        props: {
            item: {
                type: Object,
                default: () => ({}),
            },

            curSelectReportId: {
                type: String,
                default: '',
            },

            isNeedCopy: Boolean,

            curReportItem: {
                type: Object,
                default: () => ({}),
            },

            editable: Boolean,
        },
        data() {
            return {
                historyDetailVis: false,
                isTwoColumn: false,
            };
        },
        computed: {
            isInspect() {
                return this.item.type === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test;
            },

            // 是否显示复制按钮
            isShowCopyBtn() {
                if (!this.isNeedCopy) {
                    return false;
                }

                if (this.item.subType !== this.curReportItem.subType) {
                    return false;
                }

                if (this.item.subType === INSPECT_REPORT_TYPE.ris) {
                    return this.item.deviceType === this.curReportItem.deviceType;
                }

                return true;
            },

            // 是否禁用复制按钮
            isDisableCopyBtn() {
                return !this.editable;
            },
        },
        watch: {
            curSelectReportId(v) {
                if (this.item.id === v) {
                    this.handleToggle();
                }
            },
        },
        created() {
            this.handleWindowSize();
        },
        methods: {
            handleToggle() {
                if (!this.historyDetailVis) {
                    this.historyDetailVis = true;
                }
            },

            handleWindowSize() {
                if (this.$route.fullPath.includes('outpatient')) {
                    this.isTwoColumn = false;
                    return;
                }
                if (window.innerWidth > 1620) {
                    this.isTwoColumn = true;
                } else {
                    this.isTwoColumn = false;
                }
            },
        },
    };
</script>
