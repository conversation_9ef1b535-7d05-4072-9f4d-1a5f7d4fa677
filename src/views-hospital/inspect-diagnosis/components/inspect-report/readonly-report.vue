<template>
    <div v-abc-loading="loading" class="ris-readonly-report-wrapper">
        <!--头部-->
        <abc-flex vertical :gap="16">
            <!--人员信息-->
            <inspect-report-person
                ref="header"
                :patient="postData.patient"
                :show-checker-name="postData.checkerName"
                :show-tester-name="postData.testerName"
                report-is-finished
            >
                <template #append>
                    <single-print
                        v-if="allowExamReportPrint"
                        :disabled-print="!isFinishReport"
                        :button-style="{ height: '36px' }"
                        :arrow-wrapper-style="{ height: '36px' }"
                        @print="printHandler"
                        @openSettings="openPrintConfigSettingDialog"
                    ></single-print>
                </template>
            </inspect-report-person>

            <!--多报告tab-->
            <inspect-multiple-report-tab
                v-if="reportTabs.length > 1"
                :value="curReportIndex"
                :tabs="reportTabs"
                @change="handleChangeReport"
            ></inspect-multiple-report-tab>

            <!--检查报告头部信息-->
            <inspect-report-description
                type="header"
                :is-need-apply-sheet="isNeedApplySheet"
                :is-physical-examination="isPhysicalExamination"
                :post-data="postData"
                :device-model-desc="curReport.deviceModelDesc"
                :is-normal-report="isNormalReport"
            ></inspect-report-description>

            <!--检查影像-->
            <inspect-report-file
                v-if="isRenderImagePart"
                :id="postData.id"
                :image-list="curReport.imageFiles"
                :patient="postData.patient"
                :device-type="postData.deviceType"
            ></inspect-report-file>

            <!--超声所见 / 检查所见 / 影像表现 / 老报告影像描述-->
            <inspect-report-result
                v-if="isRenderVideoDescription"
                v-model="curReport.videoDescription"
                :title="videoDescriptionTitle"
                component-type="textarea"
            ></inspect-report-result>

            <!--活检部位-->
            <inspect-report-result
                v-if="isGastroscopyReport"
                v-model="curReport.inspectionSite"
                title="活检部位"
                component-type="input"
            ></inspect-report-result>

            <!--医生建议-->
            <inspect-report-result
                v-if="isGastroscopyReport"
                v-model="curReport.suggestion"
                title="医生建议"
                component-type="input"
            ></inspect-report-result>

            <!--临床检查表格-->
            <inspect-clinical-report-table
                v-if="isRenderClinicalTable"
                :items-value="itemsValue"
            >
                <template v-if="postData.attachments.length" #table-footer>
                    <examination-file-uploader
                        v-model="postData.attachments"
                        :business-type="BusinessTypeEnum.INSPECT_IMAGES"
                        business-desc="报告附件"
                        :patient-info="postData.patient"
                        upload-description="报告附件支持图片、PDF格式"
                        oss-filepath="inspect"
                        disable
                        class="report-content__attachments-add-uploader"
                    >
                    </examination-file-uploader>
                </template>
            </inspect-clinical-report-table>

            <!--一般检查表格-->
            <inspect-normal-report-table
                v-if="isRenderNormalTable"
                :items-value.sync="itemsValue"
            >
                <template v-if="postData.attachments.length" #table-footer>
                    <examination-file-uploader
                        v-model="postData.attachments"
                        :business-type="BusinessTypeEnum.INSPECT_IMAGES"
                        business-desc="报告附件"
                        :patient-info="postData.patient"
                        upload-description="报告附件支持图片、PDF格式"
                        oss-filepath="inspect"
                        disable
                        class="report-content__attachments-add-uploader"
                    >
                    </examination-file-uploader>
                </template>
            </inspect-normal-report-table>

            <!--诊断意见-->
            <diagnosis-advice
                :diagnosis-entry-items="curReport.diagnosisEntryItems"
                :diagnosis-flag="curReport.diagnosisFlag"
                :device-type="postData.deviceType"
            ></diagnosis-advice>

            <!--附件-->
            <inspect-report-attachment
                v-if="!isRenderClinicalTable && !isRenderNormalTable && postData.attachments.length"
                :attachments="postData.attachments"
            ></inspect-report-attachment>

            <!--备注|兼容老报告-->
            <examination-report-remark
                v-if="isNormalReport"
                v-model="postData.remark"
            ></examination-report-remark>

            <!--检查报告底部信息-->
            <inspect-report-description
                v-if="!isNormalReport"
                ref="footer"
                type="footer"
                :is-need-apply-sheet="isNeedApplySheet"
                :is-physical-examination="isPhysicalExamination"
                :post-data="postData"
                :examination-sheet-report="curReport"
            ></inspect-report-description>
        </abc-flex>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ExaminationAPI from 'api/examination';
    import {
        BusinessType,
    } from '@/views-hospital/sample-collection/utils/constant';
    import {
        EXAMINATION_STATUS, INSPECT_REPORT_TYPE,
    } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import { inspectDetailAdapter } from '@/views-hospital/inspect-diagnosis/utils/adapter.js';
    import { PEBusinessTypeEnum } from 'views/physical-examination/constants';
    import { INSPECT_TYPE } from '@/views-hospital/inspect-setting/utils/constant';
    import InspectReportPerson
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/inspect-report-person/index.vue';
    import {
        DiagnosisAdvice,
        InspectClinicalReportTable,
        InspectNormalReportTable,
        InspectReportAttachment,
        InspectReportDescription,
        InspectReportFile,
        InspectReportResult,
    } from '@/views-hospital/inspect-diagnosis/components/inspect-report/index';
    import ExaminationFileUploader from '@/views-hospital/inspect-diagnosis/components/inspect-report/examination-file-uploader.vue';
    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';
    import ExaminationReportRemark from 'views/examination/components/examination-report-remark/index.vue';
    import InspectMultipleReportTab
        from '@/views-hospital/inspect-diagnosis/components/inspect-report/multiple-report-tab/index.vue';

    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    const PrintInspectApiModule = () => import('@/printer/print-api/inspect');

    export default {
        name: 'ReadonlyReport',

        components: {
            InspectMultipleReportTab,
            ExaminationReportRemark,
            InspectReportAttachment,
            DiagnosisAdvice,
            InspectNormalReportTable,
            InspectClinicalReportTable,
            ExaminationFileUploader,
            InspectReportResult,
            InspectReportFile,
            InspectReportDescription,
            InspectReportPerson,
            SinglePrint: () => import('@/printer/components/print-button/single-print.vue'),
        },

        props: {
            id: {
                type: String,
                required: true,
            },
            allowExamReportPrint: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                postData: {
                    status: EXAMINATION_STATUS.WAIT,
                    name: '',
                    patient: {},
                    checkerName: '',
                    testerName: '',
                    checkTime: '',
                    reportTime: '',
                    testTime: '',
                    orderNo: '',
                    patientOrderNumber: '',
                    departmentName: '',
                    doctorName: '',
                    extendDiagnosisInfos: [],
                    chargeFormItemStatus: undefined,
                    deviceType: -1,
                    businessType: '',
                    examinationApplySheetView: {},
                    created: '',
                    itemsValue: [],
                    examinationSheetReport: {
                        diagnosisEntryItems: [],
                        videoDescription: '',
                        advice: '',
                        diagnosisFlag: undefined,
                        imageFiles: [],
                        recordDoctorName: '',
                        consultationDoctorName: '',
                        suggestion: '',
                        inspectionSite: '',
                        deviceModelDesc: '',
                    },
                    attachments: [],
                },
                loading: false,
                publicChargeTagVisible: false,
                curReportIndex: 0,
            };
        },

        computed: {
            BusinessTypeEnum() {
                return BusinessTypeEnum;
            },
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),

            isGastroscopyReport() {
                if (!this.postData) return false;

                return this.postData.deviceType === INSPECT_TYPE.GASTROSCOPE;
            },

            isNeedApplySheet() {
                return this.viewDistributeConfig.Inspect.isNeedApplySheet;
            },

            isFinishReport() {
                return this.postData.status === EXAMINATION_STATUS.CHECKED;
            },

            isPhysicalExamination() {
                return this.postData.businessType === BusinessType.physicalExamination;
            },

            applySheetItems() {
                const {
                    examinationApplySheetView: {
                        chiefComplaint,
                        presentHistory,
                        purpose,
                    },
                } = this.postData;

                return [
                    {
                        label: '主诉',
                        content: chiefComplaint || '',
                    },
                    {
                        label: '现病史',
                        content: presentHistory || '',
                    },
                    {
                        label: '检查目的',
                        content: purpose || '',
                    },
                ];
            },

            isNormalReport() {
                return this.postData.subType === INSPECT_REPORT_TYPE.normal || (
                    [INSPECT_TYPE.UN_KNOW, INSPECT_TYPE.OTHER].includes(this.postData.deviceType)
                );
            },

            isRenderImagePart() {
                return [
                    INSPECT_TYPE.CT,
                    INSPECT_TYPE.DR,
                    INSPECT_TYPE.MG,
                    INSPECT_TYPE.MR,
                    INSPECT_TYPE.MDB,
                    INSPECT_TYPE.ECG,
                    INSPECT_TYPE.GASTROSCOPE,
                    INSPECT_TYPE.CDU,
                    INSPECT_TYPE.ASO,
                    INSPECT_TYPE.C13_14,
                    INSPECT_TYPE.BODY_COMPOSITION,
                ].includes(this.postData.deviceType) || this.isNormalReport;
            },

            isRenderVideoDescription() {
                return [
                    INSPECT_TYPE.CDU,
                    INSPECT_TYPE.GASTROSCOPE,
                    INSPECT_TYPE.CT,
                    INSPECT_TYPE.DR,
                    INSPECT_TYPE.MG,
                    INSPECT_TYPE.MR,
                ].includes(this.postData.deviceType) || this.isNormalReport;
            },

            videoDescriptionTitle() {
                if (this.isNormalReport) {
                    return '影像描述';
                }
                switch (this.postData.deviceType) {
                    case INSPECT_TYPE.CDU:
                        return '超声所见';
                    case INSPECT_TYPE.GASTROSCOPE:
                        return '检查所见';
                    default:
                        return '影像表现';
                }
            },

            isRenderClinicalTable() {
                return [
                    INSPECT_TYPE.GYNECOLOGY,
                    INSPECT_TYPE.MOUTH,
                    INSPECT_TYPE.ENT,
                    INSPECT_TYPE.INTERNAL,
                    INSPECT_TYPE.SURGERY,
                    INSPECT_TYPE.EYE,
                ].includes(this.postData.deviceType);
            },

            itemsValue() {
                return (this.postData.itemsValue || []).filter((item) => !item.isDeleted);
            },

            isRenderNormalTable() {
                return [
                    INSPECT_TYPE.NORMAL,
                ].includes(this.postData.deviceType);
            },

            curReportList: {
                get() {
                    return [
                        this.postData.examinationSheetReport,
                        ...(this.postData.additionalExaminationSheetReports || []),
                    ];
                },
            },

            curReport() {
                return this.curReportList[this.curReportIndex] || {};
            },

            reportTabs() {
                const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];

                return this.curReportList.map((report, index) => {
                    const chineseNumber = chineseNumbers[index];
                    return {
                        label: `报告${chineseNumber}`,
                    };
                });
            },
        },

        watch: {
            id() {
                this.fetchDetail();
            },
        },

        created() {
            this.fetchDetail();
        },

        methods: {
            async fetchDetail() {
                this.loading = true;
                try {
                    const { data } = await ExaminationAPI.fetchDetailInterface(this.id);
                    this.postData = inspectDetailAdapter(data);

                    if (!this.postData.peSheetSimpleView) {
                        this.publicChargeTagVisible = false;
                    } else {
                        this.publicChargeTagVisible = this.postData.peSheetSimpleView.businessType === PEBusinessTypeEnum.PUBLIC_HEALTH;
                    }
                } catch (error) {
                    console.error('拉取报告详情失败：', error);
                }
                this.loading = false;
            },

            async printHandler() {
                const { InspectPrintAPI } = await PrintInspectApiModule();
                InspectPrintAPI.printReport(this.id);
            },

            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'hospital-doctor-examination' }).generateDialogAsync({ parent: this });
            },

            handleChangeReport(idx) {
                this.curReportIndex = idx;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common";

.ris-readonly-report-wrapper {
    .report-content__attachments-add-wrapper {
        display: flex;
        min-height: 60px;
        margin-top: 16px;
        border: 1px var(--abc-color-P6) solid;
        border-radius: var(--abc-border-radius-small);

        .report-content__attachments-add-title {
            flex-shrink: 0;
            align-content: stretch;
            width: 138px;
            padding-top: 20px;
            font-weight: bold;
            text-align: center;
            border-right: 1px var(--abc-color-P6) solid;
        }

        .report-content__attachments-add-uploader {
            flex: 1;
            overflow: hidden;
        }
    }
}
</style>
