<template>
    <abc-flex justify="space-between" style="width: 100%;">
        <h2>检查报告</h2>

        <abc-space>
            <abc-tips v-if="createWorkListVisible && createWorkListFail" theme="warning" size="small">
                上机失败
            </abc-tips>
            <template v-for="(btn,i) in optBtnItems">
                <template v-if="btn.isConnectDevice">
                    <template v-if="deviceList.length > 1">
                        <abc-dropdown
                            :key="btn.btnLabel"
                            size="small"
                            placement="bottom-start"
                            custom-class="inspect-header_device-dropdown"
                            @change="val => handleBtnOperate(btn.optType, val)"
                        >
                            <abc-button
                                slot="reference"
                                :variant="btn.variant"
                                :loading="btn.loading"
                                :disabled="btn.disabled"
                                :data-cy="btn.dataCy"
                            >
                                {{ btn.btnLabel }}
                            </abc-button>

                            <abc-dropdown-item
                                v-for="d in deviceList"
                                :key="d.deviceId"
                                :value="d.deviceId"
                            >
                                <abc-flex vertical>
                                    <abc-text>
                                        {{ d.deviceRoomName }}
                                    </abc-text>

                                    <abc-flex align="center" justify="space-between" :gap="8">
                                        <abc-flex style="flex: 1; overflow: hidden;" :gap="4" align="center">
                                            <abc-text
                                                theme="gray"
                                                size="mini"
                                                style="flex: 0 1 auto; max-width: 100%;"
                                                :title="d.manufacture"
                                            >
                                                {{ d.manufacture }}
                                            </abc-text>

                                            <abc-text style="flex-shrink: 0;" theme="gray" size="mini">
                                                {{ d.model }}
                                            </abc-text>
                                        </abc-flex>

                                        <abc-text theme="gray" size="mini" style="flex-shrink: 0;">
                                            #{{ d.deviceShortId }}
                                        </abc-text>
                                    </abc-flex>
                                </abc-flex>
                            </abc-dropdown-item>
                        </abc-dropdown>
                    </template>

                    <template v-else>
                        <abc-button
                            :key="btn.btnLabel"
                            :variant="btn.variant"
                            :loading="btn.loading"
                            :disabled="btn.disabled"
                            :data-cy="btn.dataCy"
                            @click="handleBtnOperate(btn.optType, deviceList[0]?.deviceId)"
                        >
                            {{ btn.btnLabel }}
                        </abc-button>
                    </template>
                </template>

                <inspect-print-popper
                    v-else-if="btn.isPrint"
                    :key="i"
                    :apply-sheet-id="postData.examinationApplySheetId"
                    :exam-sheet-id="postData.id"
                    :status="postData.status"
                    :inspect-sheet-no="postData.orderNo"
                    scene="hospital-report"
                ></inspect-print-popper>

                <template v-else>
                    <abc-button
                        :key="btn.btnLabel"
                        :variant="btn.variant"
                        :loading="btn.loading"
                        :disabled="btn.disabled"
                        :data-cy="btn.dataCy"
                        @click="handleBtnOperate(btn.optType)"
                    >
                        {{ btn.btnLabel }}
                    </abc-button>
                </template>
            </template>
        </abc-space>
    </abc-flex>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        COLLECTED_STATUS,
        EXAMINATION_STATUS,
        OPERATE_TYPE_ENUM,
    } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import InspectAPI from 'api/hospital/inspect';
    import clone from 'utils/clone';
    import { INSPECT_EVENT_KEY } from '@/views-hospital/inspect-diagnosis/utils/constant.js';
    import WaitingInspectManageService from '@/views-hospital/inspect-diagnosis/core/waiting-inspect-manage-service.js';
    import { AbcMedicalImagingViewerService } from '@/service/abcMedicalImagingViewer';
    import { INSPECT_DEVICE_TYPE } from 'utils/constants';
    import ExaminationAPI from 'api/examination';
    import {
        dcm4cheeType, INSPECT_TYPE,
    } from '@/views-hospital/inspect-setting/utils/constant';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import InspectPrintPopper from '@/views-hospital/inspect-diagnosis/common/inspect-print-popper.vue';
    import { formatDate } from '@tool/date';
    import AbcAccess from '@/access/utils';

    export default {
        name: 'InspectHeader',

        components: { InspectPrintPopper },

        inject: {
            $abcPage: {
                default: {},
            },
        },

        props: {
            isSendToCenterOrgan: Boolean,

            isPublicHealthyProject: Boolean,

            isReportUpdate: Boolean,

            handleSaveReport: {
                type: Function,
                default: () => {},
            },
        },

        data() {
            return {
                loading: false,
                saveDraftLoading: false,
                createWorkListLoading: false,
                createWorkListFail: false,
                collectLoading: false,
                isWorkListV2Finish: false,

                hadPublicHealthyProjectEmptyValidate: false,
            };
        },

        computed: {
            ...mapGetters(['userInfo', 'clinicBasicConfig','isEnablePacsUpgrade']),

            ...mapGetters('inspect', [
                'checkerUpdateReport',
                'needCheck',
            ]),

            isAdmin() {
                return this.userInfo?.isAdmin;
            },

            postData() {
                return this.$abcPage.$store.state.postData;
            },

            postDataCache: {
                get() {
                    return this.$abcPage.$store.state.postDataCache;
                },
                set(v) {
                    this.$abcPage.$store.state.postDataCache = v;
                },
            },

            editTag: {
                get() {
                    return this.$abcPage.$store.state.editTag;
                },
                set(v) {
                    this.$abcPage.$store.setEditTag(v);
                },
            },

            deviceList() {
                return this.$abcPage.$store.state.deviceList
                    .filter((item) => item.deviceType === this.postData.deviceType);
            },

            isOpenPhysicalExamination() {
                //开通体检
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION);
            },

            createWorkListVisible() {
                const { createWorkListBtnVisible } = getViewDistributeConfig().Inspect;

                return createWorkListBtnVisible && !this.isOpenPhysicalExamination && [
                    INSPECT_DEVICE_TYPE.DR,
                    INSPECT_DEVICE_TYPE.MR,
                    INSPECT_DEVICE_TYPE.CT,
                ].includes(this.postData.deviceType);
            },

            optBtnItems() {
                const isChecker = this.userInfo?.id === this.postDataCache.checkerId || this.isAdmin;
                const isInspector = this.userInfo?.id === this.postDataCache.testerId || this.isAdmin;
                const { sampleStatus } = this.postDataCache;

                const canCollectInspectType = [
                    INSPECT_TYPE.CT,
                    INSPECT_TYPE.DR,
                    INSPECT_TYPE.MR,
                    INSPECT_TYPE.CDU,
                    INSPECT_TYPE.MDB,
                ];

                // 待检查
                const btnVisibleOnWaitStatus = {
                    finishVisible: true,
                    modifyVisible: false,
                    saveVisible: false,
                    cancelVisible: false,
                    print: true,
                    finishCheckVisible: false,
                    cancelCheckVisible: false,
                    finishCollect: canCollectInspectType.includes(this.postData.deviceType),
                };

                // 待审核
                const btnVisibleOnWaitCheckStatus = {
                    finishVisible: false,
                    modifyVisible: !this.editTag,
                    saveVisible: this.editTag,
                    cancelVisible: this.editTag,
                    print: true,
                    finishCheckVisible: isChecker && !this.editTag,
                    cancelCheckVisible: false,
                    finishCollect: false,
                };

                // 已审核 - 开启审核
                const btnVisibleOnCheckedStatusWithOpenCheck = {
                    finishVisible: false,
                    modifyVisible: false,
                    saveVisible: false,
                    cancelVisible: false,
                    print: true,
                    finishCheckVisible: false,
                    cancelCheckVisible: isChecker,
                    finishCollect: false,
                };

                // 已审核 - 关闭审核
                const btnVisibleOnCheckedStatusWithCloseCheck = {
                    finishVisible: false,
                    modifyVisible: !this.editTag,
                    saveVisible: this.editTag,
                    cancelVisible: this.editTag,
                    print: true,
                    finishCheckVisible: false,
                    cancelCheckVisible: false,
                    finishCollect: false,
                };

                const inspectStatusMapToBtnVisible = {
                    [EXAMINATION_STATUS.WAIT]: btnVisibleOnWaitStatus,
                    [EXAMINATION_STATUS.WAIT_WRITE]: btnVisibleOnWaitStatus,
                    [EXAMINATION_STATUS.WAIT_CHECK]: btnVisibleOnWaitCheckStatus,
                    [EXAMINATION_STATUS.CHECKED]: this.needCheck ? btnVisibleOnCheckedStatusWithOpenCheck : (
                        btnVisibleOnCheckedStatusWithCloseCheck
                    ),
                };

                // 审核人是否可以修改报告
                const checkerCanModify = this.checkerUpdateReport === 1;

                const {
                    finishVisible = false,
                    modifyVisible = false,
                    saveVisible = false,
                    cancelVisible = false,
                    print = false,
                    cancelCheckVisible = false,
                    finishCheckVisible = false,
                    finishCollect = false,
                } = inspectStatusMapToBtnVisible[this.postData.status] || {};

                return [
                    {
                        btnLabel: sampleStatus === COLLECTED_STATUS.COLLECTED ? '已采集' : '完成采集',
                        variant: 'ghost',
                        optType: OPERATE_TYPE_ENUM.finishCollect,
                        visible: finishCollect && !this.isSendToCenterOrgan,
                        loading: this.collectLoading,
                        disabled: sampleStatus === COLLECTED_STATUS.COLLECTED,
                        dataCy: 'inspect-finish-collect-abc-button',
                    },
                    {
                        btnLabel: this.createWorkListFinish ? '已上机' : '上机检查',
                        visible: this.createWorkListVisible && !this.isSendToCenterOrgan,
                        optType: OPERATE_TYPE_ENUM.createWorkList,
                        variant: 'ghost',
                        loading: this.createWorkListLoading,
                        disabled: this.createWorkListFinish,
                        dataCy: 'inspect-create-work-list-abc-button',
                        isConnectDevice: true,
                    },
                    {
                        btnLabel: '保存',
                        visible: finishVisible && !this.isSendToCenterOrgan,
                        optType: OPERATE_TYPE_ENUM.saveDraft,
                        variant: 'ghost',
                        loading: this.saveDraftLoading,
                        dataCy: 'inspect-save-abc-button',
                    },
                    {
                        btnLabel: '完成检查',
                        visible: finishVisible && !this.isSendToCenterOrgan,
                        optType: OPERATE_TYPE_ENUM.finishInspect,
                        variant: 'fill',
                        loading: this.loading,
                        disabled: this.saveDraftLoading,
                        dataCy: 'inspect-finish-abc-button',
                    },
                    {
                        btnLabel: '修改',
                        visible: modifyVisible && !this.isSendToCenterOrgan,
                        optType: OPERATE_TYPE_ENUM.modifyReport,
                        variant: this.needCheck ? 'ghost' : 'fill',
                        disabled: !(isInspector || (isChecker && checkerCanModify)),
                        dataCy: 'inspect-modify-abc-button',
                    },
                    {
                        btnLabel: '保存',
                        visible: saveVisible,
                        optType: OPERATE_TYPE_ENUM.saveModifiedReport,
                        variant: 'fill',
                        loading: this.loading,
                        dataCy: 'inspect-save-abc-button',
                    },
                    {
                        btnLabel: '取消',
                        visible: cancelVisible,
                        optType: OPERATE_TYPE_ENUM.cancelModify,
                        variant: 'ghost',
                        dataCy: 'inspect-cancel-abc-button',
                    },
                    {
                        btnLabel: '完成审核',
                        visible: finishCheckVisible && !this.isSendToCenterOrgan,
                        optType: OPERATE_TYPE_ENUM.finishCheck,
                        variant: 'fill',
                        loading: this.loading,
                        dataCy: 'inspect-finish-check-abc-button',
                    },
                    {
                        btnLabel: '取消审核',
                        visible: cancelCheckVisible && !this.isSendToCenterOrgan,
                        optType: OPERATE_TYPE_ENUM.cancelCheck,
                        variant: 'fill',
                        loading: this.loading,
                        dataCy: 'inspect-cancel-check-abc-button',
                    },
                    {
                        btnLabel: '打印报告',
                        visible: print && !this.isPublicHealthyProject,
                        isPrint: true,
                    },
                ].filter((b) => b.visible);
            },

            createWorkListFinish() {
                return !!this.postData.examinationApplySheetView.dcm4cheeDetail.studyInstanceUid || this.isWorkListV2Finish;
            },
        },

        watch: {
            postData(val) {
                const { deviceType } = val;
                const { createWorkListBtnVisible } = getViewDistributeConfig().Inspect;
                if (dcm4cheeType.includes(deviceType) && this.isEnablePacsUpgrade && createWorkListBtnVisible) {
                    this.getDcm4cheeRelationExamV2(val);
                }
            },
        },

        mounted() {
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.PH_CONTINUE_FINISH, () => {
                this.hadPublicHealthyProjectEmptyValidate = true;
                this.handleBtnOperate(OPERATE_TYPE_ENUM.finishInspect);
            },this);
        },

        methods: {
            async handleBtnOperate(type, ...props) {
                const map = {
                    [OPERATE_TYPE_ENUM.createWorkList]: () => {
                        this.createWorkList(
                            this.postData,
                            this.postData.examinationApplySheetView.id,
                            ...props,
                        );
                    },

                    [OPERATE_TYPE_ENUM.saveDraft]: async () => {
                        this.saveDraftLoading = true;
                        try {
                            await this.handleSaveReport('','',false);
                            this.$Toast({
                                type: 'success',
                                message: '已保存',
                            });

                            await this.doSthAfterDoCheck();
                        } catch (e) {
                            console.error(e);
                        }
                        this.saveDraftLoading = false;
                    },

                    [OPERATE_TYPE_ENUM.finishInspect]: async () => {
                        if (this.isPublicHealthyProject && !this.hadPublicHealthyProjectEmptyValidate) {
                            this.$abcEventBus.$emit(INSPECT_EVENT_KEY.CALC_NOT_FINISH_DATA);
                            return;
                        }
                        this.loading = true;
                        try {
                            const updateStatus = this.needCheck ? EXAMINATION_STATUS.WAIT_CHECK : EXAMINATION_STATUS.CHECKED;
                            // 报告时间未修改默认当前时间
                            this.postData.reportTime = this.postData.reportTime ||
                                formatDate(new Date(), 'YYYY-MM-DD HH:mm');
                            if (!this.needCheck) {
                                this.postData.checkTime = this.postData.checkTime ||
                                    formatDate(new Date(), 'YYYY-MM-DD HH:mm');
                            }
                            await this.$nextTick();
                            await this.handleSaveReport(updateStatus);
                            this.$Toast({
                                type: 'success',
                                message: '已完成',
                            });
                            this.handleRefreshHistoryReport();
                            await this.doSthAfterDoCheck();
                        } catch (e) {
                            console.error(e);
                        }
                        this.loading = false;
                    },

                    [OPERATE_TYPE_ENUM.modifyReport]: () => {
                        this.editTag = true;
                    },

                    [OPERATE_TYPE_ENUM.saveModifiedReport]: async () => {
                        if (!this.isReportUpdate) {
                            this.editTag = false;
                        } else {
                            this.loading = true;
                            try {
                                await this.handleSaveReport();
                                this.$Toast({
                                    type: 'success',
                                    message: '已保存',
                                });
                                this.editTag = false;
                                await this.doSthAfterDoCheck();
                            } catch (e) {
                                console.error(e);
                            }
                            this.loading = false;
                        }
                    },

                    [OPERATE_TYPE_ENUM.cancelModify]: () => {
                        if (this.isReportUpdate) {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                content: '内容已被修改，是否要取消本次操作？',
                                onConfirm: () => {
                                    Object.assign(this.postData, clone(this.postDataCache));
                                    this.editTag = false;
                                },
                            });
                        } else {
                            this.editTag = false;
                        }
                    },

                    [OPERATE_TYPE_ENUM.finishCheck]: async () => {
                        this.loading = true;
                        try {
                            // 审核时间未修改默认当前时间
                            this.postData.checkTime = this.postData.checkTime || formatDate(new Date(), 'YYYY-MM-DD HH:mm');
                            await this.$nextTick();
                            await this.handleSaveReport(EXAMINATION_STATUS.CHECKED);
                            this.$Toast({
                                type: 'success',
                                message: '已完成',
                            });

                            this.handleRefreshHistoryReport();
                            await this.doSthAfterDoCheck();
                        } catch (e) {
                            console.error(e);
                        }
                        this.loading = false;
                    },

                    [OPERATE_TYPE_ENUM.cancelCheck]: async () => {
                        this.loading = true;
                        try {
                            await this.handleSaveReport(EXAMINATION_STATUS.WAIT_CHECK);
                            this.$Toast({
                                type: 'success',
                                message: '已取消',
                            });

                            this.handleRefreshHistoryReport();
                        } catch (e) {
                            console.error(e);
                        }
                        this.loading = false;
                    },

                    [OPERATE_TYPE_ENUM.finishCollect]: async () => {
                        this.loading = true;
                        try {
                            await this.handleSaveReport('',COLLECTED_STATUS.COLLECTED);
                            this.$Toast({
                                type: 'success',
                                message: '已完成',
                            });
                        } catch (e) {
                            console.error(e);
                        }
                        this.loading = false;
                    },
                };

                map[type].call(this);
            },

            // 在审核后，删除等待队列中的数据
            async doSthAfterDoCheck() {
                if (this.postData.deviceType !== INSPECT_DEVICE_TYPE['彩超']) return;

                await WaitingInspectManageService.getInstance().start();
                await WaitingInspectManageService.getInstance().deleteWaitingItem(this.$route.params.id);

                this.$abcEventBus.$emit('DELETE_WAITING_ITEM');
            },

            // 当整个报告已完成（无审核完成检查、有审核完成审核）刷新历史报告列表和总数
            handleRefreshHistoryReport() {
                this.$abcEventBus.$emit(INSPECT_EVENT_KEY.UPDATE_HISTORY_LIST);
            },

            async createWorkList(data, applySheetId, deviceId) {
                this.createWorkListLoading = true;
                try {
                    const instance = await AbcMedicalImagingViewerService.getInstance().start();
                    if (!instance.pacsConfigIsCorrect) {
                        this.createWorkListLoading = false;
                        return;
                    }
                    //如果升级了pacs2.0就直接创建worklist
                    if (this.isEnablePacsUpgrade) {
                        await InspectAPI.dcm4cheeRelationExamV2(applySheetId, { deviceId });

                        this.$Toast({
                            type: 'success',
                            message: '发送成功',
                        });
                        this.isWorkListV2Finish = true;
                        this.createWorkListLoading = false;
                        this.createWorkListFail = false;
                        return;
                    }

                    // 创建worklist操作
                    const { dcm4cheeView } = data.examinationApplySheetView;

                    const {
                        patient: dcmPatient, worklist,
                    } = dcm4cheeView;

                    if (!dcmPatient.PatientID) {
                        throw new Error('缺少患者档案号，请到患者管理完善档案资料');
                    }

                    const exist = await AbcMedicalImagingViewerService
                        .getInstance()
                        .getBusinessService()
                        .getInstance()
                        .checkPatientExist(dcmPatient.PatientID);

                    if (!exist) {
                        await AbcMedicalImagingViewerService
                            .getInstance()
                            .getBusinessService()
                            .getInstance()
                            .createPatient(dcmPatient);
                    }

                    const res = await AbcMedicalImagingViewerService
                        .getInstance()
                        .getBusinessService()
                        .getInstance()
                        .createWorklist(dcmPatient.PatientID, worklist[0]);

                    const {
                        StudyInstanceUID, ScheduledProcedureStepSequence,
                    } = res;

                    await InspectAPI.dcm4cheeRelationExam(applySheetId, {
                        studyInstanceUid: StudyInstanceUID,
                        scheduledProcedureStepId: ScheduledProcedureStepSequence[0].ScheduledProcedureStepID,
                    });

                    this.$Toast({
                        type: 'success',
                        message: '发送成功',
                    });

                    // 刷新创建workList状态
                    const { data: _data } = await ExaminationAPI.fetchDetailInterface(
                        this.$route.params.id,
                    );

                    this.postData.examinationApplySheetView.dcm4cheeDetail =
                        _data.examinationApplySheetView.dcm4cheeDetail || {};

                    this.createWorkListFail = false;
                } catch (error) {
                    this.createWorkListFail = true;
                    console.error('创建workList失败：', error);
                }

                this.createWorkListLoading = false;
            },

            async getDcm4cheeRelationExamV2(val) {
                try {
                    const { id } = val.examinationApplySheetView;
                    if (id) {
                        const { data } = await InspectAPI.getDcm4cheeRelationExamV2(id);
                        if (data.StudyInstanceUID) {
                            this.isWorkListV2Finish = true;
                        } else {
                            this.isWorkListV2Finish = false;
                        }
                    }
                } catch (e) {
                    this.isWorkListV2Finish = false;
                    console.log(e);
                }
            },
        },
    };
</script>

<style lang="scss">
.inspect-header_device-dropdown {
    width: 244px;
}
</style>
