<template>
    <div class="inspect-call-number-ordering">
        <div class="btn-list">
            <abc-button
                type="blank"
                class="handle"
                :width="64"
                style="min-width: 64px; margin-right: 15px;"
                @click="$emit('cancel')"
            >
                取消
            </abc-button>

            <abc-button
                type="ghost"
                class="icon-img"
                :width="32"
                :disabled="checkedIndex === -1"
                style="min-width: 32px; margin-left: 4px;"
                @click="onClickOrder(lockNum)"
            >
                <img src="~assets/images/<EMAIL>" alt="" />
            </abc-button>

            <abc-button
                type="ghost"
                class="icon-img"
                :width="32"
                :disabled="checkedIndex === -1"
                style="min-width: 32px; margin-left: 4px;"
                @click="onClickOrder(checkedIndex - 1)"
            >
                <img src="~assets/images/<EMAIL>" alt="" />
            </abc-button>

            <abc-button
                type="ghost"
                class="icon-img"
                :width="32"
                :disabled="checkedIndex === -1"
                style="min-width: 32px; margin-left: 4px;"
                @click="onClickOrder(checkedIndex + 1)"
            >
                <img src="~assets/images/<EMAIL>" alt="" />
            </abc-button>

            <abc-button
                type="ghost"
                class="icon-img"
                :width="32"
                :disabled="checkedIndex === -1"
                style="min-width: 32px; margin-left: 4px;"
                @click="onClickOrder(orderList.length - 1)"
            >
                <img src="~assets/images/<EMAIL>" alt="" />
            </abc-button>

            <abc-button
                class="handle"
                :width="64"
                :disabled="!isUpdate"
                :loading="saveLoading"
                style="min-width: 64px; margin-left: 15px;"
                @click="onClickSaveOrder"
            >
                确定
            </abc-button>
        </div>

        <div class="list-box">
            <div class="patient-box">
                <div
                    v-for="(item, index) in orderList"
                    :key="item.id"
                    @click="onChangeIndex(item, index)"
                    @mouseenter="onMouseenter(item, index)"
                    @mouseleave="onMouseleave(item, index)"
                >
                    <abc-tooltip
                        placement="right"
                        :disabled="item.isCanBeAdjust === 1"
                        content="锁屏患者，不可调整顺序"
                    >
                        <order-sign
                            :value="!!item.isCanBeAdjust && item.isAdjusted === 1 && selectedTagId === item.id"
                            :tag="item.tag"
                            @change="(newTag) => (item.tag = newTag)"
                            @mouseenter="onMouseenter(item, index)"
                            @mouseleave="onMouseleave(item, index)"
                        >
                            <div
                                :class="{
                                    item: true,
                                    await: item.status === 1,
                                    disabled: item.isCanBeAdjust === 0,
                                    active: index === checkedIndex,
                                }"
                            >
                                <span v-if="item.isCanBeAdjust === 0" class="radio-track"></span>

                                <abc-radio v-else :value="checkedIndex" :label="index">
                                    <span></span>
                                </abc-radio>

                                <span class="num">{{ item.showOrderNo }}</span>

                                <span class="zaw">
                                    <span v-if="getTargetRemark(item)" class="tag">
                                        {{ getTargetRemark(item) }}
                                    </span>
                                </span>

                                <span class="name">{{ item.patientName }}</span>

                                <span class="status" :style="item.showStyles">{{ item.showStatus }}</span>
                            </div>
                        </order-sign>
                    </abc-tooltip>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import OrderSign from 'views/layout/call-number/order-sign.vue';
    import clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    import { getTargetRemark } from 'assets/configure/call.js';
    import InspectAPI from 'api/hospital/inspect';

    export default {
        name: 'InspectCallNumberOrder',
        components: {
            OrderSign,
        },
        props: {
            selected: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                checkedIndex: -1,
                orderList: [],
                selectedTagId: '',
                timer: null,
                saveLoading: false,
            };
        },
        computed: {
            // 前几位是锁住的
            lockNum() {
                return this.orderList.filter((item) => item.isCanBeAdjust === 0).length;
            },
            // 是否更新过
            isUpdate() {
                return this.selected && !isEqual(clone(this.selected.waitingPatients), this.orderList);
            },
        },
        created() {
            if (this.selected) {
                this.orderList = clone(this.selected.waitingPatients);
            }
        },
        methods: {
            getTargetRemark,
            /**
             * desc [选中需要调序元素]
             */
            onChangeIndex(item, index) {
                if (item.isCanBeAdjust === 0) return;

                if (index >= this.lockNum && this.checkedIndex !== index) {
                    this.checkedIndex = index;
                }

                this.selectedTagId = this.selectedTagId === item.id ? '' : item.id;
            },
            /**
             * desc [插入到指定位置]
             */
            onClickOrder(index) {
                // 待诊患者排在过号前面
                if (this.orderList.length - 1 >= index && index >= this.lockNum && this.checkedIndex !== index) {
                    const checkedItem = this.orderList[this.checkedIndex];
                    if (checkedItem.subStatus !== 2) {
                        // 非过号
                        index = this.handleFindValidIndex(this.checkedIndex, index, (item) => item.subStatus !== 2);
                    } else {
                        // 已过号
                        index = this.handleFindValidIndex(this.checkedIndex, index, () => true);
                    }
                    if (this.checkedIndex !== index) {
                        const item = this.orderList.splice(this.checkedIndex, 1)[0];
                        this.orderList.splice(index, 0, item);
                        this.checkedIndex = index;
                        this.orderList[this.checkedIndex].isAdjusted = 1;
                    }
                }
            },
            /**
             * desc [循环找到可以调序的index]
             */
            handleFindValidIndex(startIndex, endIndex, validator) {
                if (startIndex < endIndex) {
                    for (let i = startIndex; i <= endIndex; i++) {
                        const item = this.orderList[i + 1];
                        if (item && !validator(item)) {
                            return i;
                        }
                    }
                    return endIndex;
                }
                for (let i = startIndex; i >= endIndex; i--) {
                    const item = this.orderList[i - 1];
                    if (item && !validator(item)) {
                        return i;
                    }
                }
                return endIndex;
            },
            /**
             * desc [点击保存]
             */
            async onClickSaveOrder() {
                if (this.saveLoading) return;
                this.saveLoading = true;
                try {
                    await InspectAPI.reorderCallLine({
                        roomId: this.selected.roomId,
                        patients: this.orderList.map((item) => ({
                            id: item.id,
                            name: item.name,
                            isAdjusted: item.isAdjusted,
                            tag: item.tag,
                        })),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '调序成功',
                    });
                    this.$emit('success');
                } catch (e) {
                    console.error('onClickSaveOrder error', e);
                }
                this.saveLoading = false;
            },

            /**
             * desc [当移动到]
             */
            onMouseenter(item) {
                if (this.timer) {
                    clearTimeout(this.timer);
                    this.timer = null;
                }
                this.selectedTagId = item.id;
            },
            /**
             * desc [当移动出]
             */
            onMouseleave() {
                this.timer = setTimeout(() => {
                    this.selectedTagId = '';
                }, 50);
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.inspect-call-number-ordering {
    box-sizing: border-box;
    height: 478px;

    .btn-list {
        @include flex(row, center, center);

        .icon-img img {
            width: 14px;
            height: 14px;
            animation: none;
        }

        .iconfont {
            font-size: 13px;
        }
    }

    .list-box {
        height: 402px;
        margin-top: 16px;
        overflow: hidden;
        background-color: #ffffff;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);

        @include flex(column, flex-start, stretch);

        .patient-box {
            flex: 1;
            overflow-x: hidden;
            overflow-y: auto;

            > div {
                &:not(:last-child) {
                    border-bottom: 1px solid var(--abc-color-P7);
                }
            }

            .item {
                width: 100%;
                height: 40px;
                padding: 0 12px;
                cursor: pointer;

                @include flex(row, center, center);

                &:hover {
                    background-color: var(--abc-color-cp-grey4);
                }

                .radio-track {
                    display: inline-block;
                    width: 24px;
                    height: 2px;
                }

                .num {
                    display: inline-block;
                    width: 70px;

                    @include ellipsis(1);
                }

                .zaw {
                    display: inline-block;
                    width: 50px;
                }

                .tag {
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    font-size: 14px;
                    line-height: 18px;
                    color: $Y2;
                    text-align: center;
                    border: 1px solid $Y2;
                    border-radius: 20px;
                }

                .name {
                    flex: 1;

                    @include ellipsis(1);
                }

                .status {
                    display: inline-block;
                    width: 44px;
                    text-align: right;

                    @include ellipsis(1);
                }
            }

            .active {
                box-shadow:
                    0 0 5px 0 rgba(0, 0, 0, 0.2),
                    0 1px 0 0 rgba(206, 208, 218, 1),
                    0 -1px 0 0 rgba(206, 208, 218, 1);
            }

            .await {
                span {
                    color: $G1;
                }
            }

            .disabled {
                background-color: var(--abc-color-cp-grey2);
            }
        }
    }
}
</style>
