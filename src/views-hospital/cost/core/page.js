import {
    BasePage, getApp,
} from '@/core/index.js';
import PageStore from './store.js';
import AbcSocket from 'views/common/single-socket';
import * as business from 'MfFeEngine/business';

export default class CostPage extends BasePage {
    name = '@HospitalCost';

    constructor() {
        super({
            store: new PageStore(),
        });
        this.instanceKey = getApp().store.state.hospitalGlobal.currentWardAreaId;
    }

    async init() {
        await super.init();
        const { socket } = AbcSocket.getSocket();
        this.QlService = new business.NurseQlService(socket);
        await this.QlService.start(
            getApp().store.state.hospitalGlobal.currentWardAreaId,
        );
    }

    created(vm) {
        super.created(vm);
    }

    destroyed() {
        this.QlService?.stop();
        this.QlService = null;
        super.destroyed();
    }
}
