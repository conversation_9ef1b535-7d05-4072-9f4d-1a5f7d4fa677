<template>
    <div class="discharge-select_box" :style="classContent">
        <div class="discharge-select_box-button" @click="doneReason">
            <abc-form-item required>
                <abc-input
                    v-model="reason"
                    :placeholder="placeholder"
                    readonly
                    :width="322"
                ></abc-input>
            </abc-form-item>
        </div>
        <discharge-hospital-dialog
            v-if="isShowDischargeHospitalDialog"
            v-model="isShowDischargeHospitalDialog"
            :advice.sync="adviceCurrent"
            :cur-start-time="curStartTime"
            :is-allow-open-advice-supplement="isAllowOpenAdviceSupplement"
            :disabled-select="true"
            @discharge="discharge"
        ></discharge-hospital-dialog>
        <transfer-hospital-dialog
            v-if="isShowTransferHospitalDialog"
            v-model="isShowTransferHospitalDialog"
            :cur-start-time="curStartTime"
            :is-allow-open-advice-supplement="isAllowOpenAdviceSupplement"
            :advice.sync="adviceCurrent"
            @discharge="discharge"
        ></transfer-hospital-dialog>
    </div>
</template>

<script>
    import DischargeHospitalDialog from '../discharge-hospital-dialog';
    import TransferHospitalDialog from '../transfer-hospital-dialog';
    import {
        AdviceTagEnum,
        DischargeTypeEnum,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import { mapGetters } from 'vuex';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import { isBeforeToday } from '@/views-hospital/medical-prescription/utils/format-advice';


    export default {
        name: 'DischargeSelect',
        components: {
            DischargeHospitalDialog,
            TransferHospitalDialog,
        },
        props: {
            width: {
                type: Number,
                default: 322,
            },
            advice: {
                type: Object,
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            placeholder: {
                type: String,
                default: '请选择出院原因',
            },
            isTransfer: Boolean,
            startTime: {
                type: String,
                default: '',
            },
            isAllowOpenAdviceSupplement: {
                type: Boolean,
                default: false,
            },
            groupItem: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                isShowDischargeHospitalDialog: false,
                isShowTransferHospitalDialog: false,
                diagnosisTableData: [],
                outpatientOrderId: '',
                dischargeType: '',
                dischargeHospitalReason: '',
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            adviceCurrent: {
                get() {
                    return this.advice;
                },
                set(v) {
                    this.$emit('update:advice', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            classContent() {
                return `width:${this.width}px`;
            },
            reason() {
                if (!this.dischargeType) return ;
                // 转院医嘱
                if (this.dischargeType === 'transfer') {
                    return `转院 ${this.dischargeHospitalReason ? ` / 转院原因:${this.dischargeHospitalReason}` : ''}`;
                }
                let dischargeDesc = '';
                if (this.dischargeType === DischargeTypeEnum.DEATH && this.adviceCurrent?.deathTime) {
                    dischargeDesc = `死亡时间:${this.adviceCurrent?.deathTime}`;
                } else {
                    if (this.adviceCurrent.dischargeHospitalTime) {
                        dischargeDesc = `出院时间：${this.adviceCurrent.dischargeHospitalTime || ''} / ${this.dischargeHospitalReason || ''}`;
                    }
                }
                return dischargeDesc ? `${this.dischargeType} / ${dischargeDesc}` : '';
            },
        },
        async created() {
            await this.getDiagnosisList();
        },
        mounted() {
            this.$nextTick(() => {
                this.doneReason();
            });
        },
        methods: {
            async getDiagnosisList() {
                try {
                    const {
                        patientOrderId, outpatientOrderId,
                    } = this;
                    const list = await MedicalPrescriptionAPI.getDiagnosisList({
                        patientOrderId,
                        outpatientOrderId,
                    });
                    if (list?.length) {
                        this.diagnosisTableData = list;
                    }
                } catch (err) {
                    console.log(err);
                }
            },
            doneReason() {
                if (this.isTransfer) {
                    this.isShowTransferHospitalDialog = true;
                    return;
                }
                this.isShowDischargeHospitalDialog = true;
            },
            async discharge(typeName, params, goodsList) {
                this.dischargeType = typeName;
                this.dischargeHospitalReason = params.dischargeHospitalReason || '';
                // 开始时间等于出院时间
                this.adviceCurrent.dischargeHospitalTime = params.dischargeHospitalTime;
                if (typeName !== DischargeTypeEnum.TOMORROW) {
                    this.curStartTime = this.adviceCurrent.dischargeHospitalTime;
                }
                // 出院时间在过去，为补开医嘱，增加补开标签
                const hasSupplement = this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT);
                if (isBeforeToday(this.adviceCurrent.dischargeHospitalTime) && !hasSupplement) {
                    this.groupItem.tagTypes.push(AdviceTagEnum.SUPPLEMENT);
                }

                if (typeName === 'transfer') {
                    this.adviceCurrent.stopLongAdviceTime = params.stopLongAdviceTime;
                    this.adviceCurrent.dischargeHospitalReason = params.dischargeHospitalReason;
                    this.isShowTransferHospitalDialog = false;
                    return;
                }
                this.isShowDischargeHospitalDialog = false;
                // 如果不是原本的typeName
                if (this.adviceCurrent.name !== typeName) {
                    const item = goodsList?.find((i) => {
                        return i.name === typeName;
                    });
                    const advices = MedicalPrescriptionService.getAdviceItem(item, this.adviceCurrent?.keyId);
                    this.adviceCurrent = Object.assign(this.adviceCurrent, advices.adviceRule);
                }
                this.adviceCurrent = Object.assign(this.adviceCurrent, params);
                if (typeName === DischargeTypeEnum.DEATH) {
                    this.adviceCurrent.stopLongAdviceTime = '';
                    this.adviceCurrent.dischargeHospitalReason = null;
                } else {
                    this.adviceCurrent.deathTime = '';
                }
                this.$emit('discharge');
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.discharge-select_box {
    height: 40px;

    &-button {
        box-sizing: border-box;
        width: 322px;
        height: 40px;
    }
}
</style>
