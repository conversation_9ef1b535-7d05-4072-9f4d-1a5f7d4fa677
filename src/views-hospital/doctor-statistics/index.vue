<template>
    <abc-container>
        <abc-container-center class="content-container">
            <abc-tabs
                v-model="currentTab"
                :option="tabOptions"
                style=" padding-left: 32px; background: #ffffff;"
                :border-style="{ borderBottom: 'none' }"
                size="huge"
                @change="changeTab"
            ></abc-tabs>
            <router-view ref="content"></router-view>
        </abc-container-center>
    </abc-container>
</template>

<script type="text/ecmascript-6">
    import * as core from '@/views-hospital/doctor-statistics/core/index.js';
    import { createAbcPage } from '@/core/page/factory.js';
    import { HospitalDoctorStatisticsRouterNameKeys } from '@/views-hospital/doctor-statistics/core/routes.js';
    import { mapState } from 'vuex';
    export default {
        name: 'HospitalDoctorStatisticsIndex',
        mixins: [
            createAbcPage(core),
        ],
        data() {
            return {
                pageLoading: false,
                currentTab: HospitalDoctorStatisticsRouterNameKeys.departmentIncome,
            };
        },
        computed: {
            ...mapState('hospitalGlobal', ['currentDepartmentId']),
            tabOptions() {
                const _arr = [
                    {
                        label: '科室住院收入',
                        value: HospitalDoctorStatisticsRouterNameKeys.departmentIncome,
                    },
                    {
                        label: '科室业绩报表',
                        value: HospitalDoctorStatisticsRouterNameKeys.departmentPerformance,
                    },
                    {
                        label: '住院开单业绩',
                        value: HospitalDoctorStatisticsRouterNameKeys.hospitalRevenue,
                    },
                ];
                return _arr;
            },
        },
        watch: {
            '$route': {
                handler() {
                    const { name } = this.$route;
                    if (this.currentTab !== name) {
                        this.currentTab = name;
                    }
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            changeTab(index, tab) {
                this.currentTab = tab.value;
                if (this.currentTab === HospitalDoctorStatisticsRouterNameKeys.departmentPerformance) {
                    this.$router.push({
                        name: tab.value,
                        query: {
                            currentDepartmentId: this.currentDepartmentId,
                        },
                    });
                } else {
                    this.$router.push({
                        name: tab.value,
                    });
                }
            },
        },
    };
</script>



