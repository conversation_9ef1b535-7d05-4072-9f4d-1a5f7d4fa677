<template>
    <div class="quick-list-wrapper">
        <div class="quick-search-wrapper">
            <div class="search-patient-wrapper">
                <abc-icon v-if="scrollParamsKey" icon="cross_small" @click.native="scrollParamsKey = ''"></abc-icon>
                <abc-input
                    v-model.trim="scrollParamsKey"
                    type="text"
                    placeholder="患者/床号/住院号"
                >
                    <template #prepend>
                        <abc-search-icon></abc-search-icon>
                    </template>
                </abc-input>
            </div>
        </div>

        <div class="quick-content-wrapper">
            <div class="quick-list-tabs">
                <div v-if="scrollParamsKey && totalCount > -1" class="search-tips">
                    <div>
                        搜索到<span style=" margin: 0 4px; font-size: 12px; color: #000000;">{{ totalCount }}</span>条信息
                    </div>
                </div>
                <template v-else>
                    <abc-checkbox
                        v-model="isCheckAll"
                        :indeterminate="isIndeterminate"
                        @change="onCheckAll"
                    >
                        全选
                    </abc-checkbox>
                </template>
            </div>

            <div
                v-abc-loading.middle.gray.noCover="loading"
                class="quick-list-small"
                style="height: calc(100% - 36px);"
            >
                <ul>
                    <quick-list-item
                        v-for="item in quickList"
                        :key="item.id"
                        :quick-item="item"
                        :is-active="currentQuickItem && currentQuickItem.id === item.id"
                        @select="handleSelect(item)"
                    >
                        <template #patient-pre>
                            <div class="checkbox-wrapper" @click.stop="">
                                <abc-checkbox v-model="item.checked"></abc-checkbox>
                            </div>
                        </template>
                    </quick-list-item>
                </ul>

                <p
                    v-if="!quickList.length && !loading"
                    class="no-patient"
                >
                    暂无患者
                </p>
                <div v-if="quickList.length > 0" class="no-more">
                    没有更多了
                </div>
            </div>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapState } from 'vuex';
    import { debounce } from 'utils/lodash.js';
    import { HospitalQLSceneType } from 'utils/constants-hospital';
    import clone from 'utils/clone';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription/index.js';
    import { MedicalAdviceStatusEnum } from '@/views-hospital/medical-prescription/utils/constants.js';
    import QuickListItem from 'src/views-hospital/components/quick-list/quick-list-item.vue';

    export default {
        name: 'QuickList',
        components: { QuickListItem },
        mixins: [],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                params: {
                    sceneType: HospitalQLSceneType.NURSE_MODULE_LIST,
                    limit: 500,
                    offset: 0,
                },
                scrollParamsKey: '',
                loading: false,
                totalCount: -1,
                isCheckAll: false,
                isIndeterminate: false,
                patientOrderIds: [],
            };
        },
        computed: {
            ...mapState('hospitalGlobal', ['currentWardAreaId']),
            quickList() {
                return this.$abcPage.$store.quickList;
            },

            currentQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },
        },
        watch: {
            scrollParamsKey(val) {
                if (val) {
                    this._debounceSearch();
                } else {
                    this.clearKey();
                }
            },
            quickList: {
                handler(val) {
                    this.isCheckAll = false;
                    if (val.length > 0) {
                        const isCheckAll = val.every((x) => x.checked);
                        this.isCheckAll = isCheckAll;
                        if (!isCheckAll) {
                            this.isIndeterminate = val.some((x) => x.checked);
                        }
                    }
                },
                deep: true,
            },
        },
        async created() {
            this.loading = true;
            const { autoCheck } = this.$route.query;
            if (autoCheck) {
                await this.getAutoCheckIds();
            }
            this.initQuickList();

            const qlServiceCallback = {
                onUpdate: () => {
                    this.filterByKeyword();
                },
            };
            this.$abcPage.QlService.addNurseQlCallback(qlServiceCallback);
            this.$on('hook:beforeDestroy', () => {
                this.$abcPage.QlService.removeNurseQlCallback(qlServiceCallback);
            });

            // 注册防抖search函数
            this._debounceSearch = debounce(async () => {
                this.filterByKeyword();
            }, 250, true);
        },
        methods: {
            /**
             * @desc 根据条件初始化quicklist
             * <AUTHOR>
             * @date 2019/11/20 15:52:32
             */
            async initQuickList() {
                this.loading = true;
                const data = await this.$abcPage.QlService.initData({
                    ...this.params,
                    wardId: this.currentWardAreaId,
                });
                const newData = this.transNewData(clone(data));
                this.$abcPage.$store.setQuickList(newData);
                this.loading = false;
            },

            /**
             * @desc keyword过滤
             * <AUTHOR>
             * @date 2023/02/22 14:51:29
             */
            async filterByKeyword() {
                this.loading = true;
                const data = await this.$abcPage.QlService.filterByKeyword(this.scrollParamsKey);
                this.totalCount = data.length;
                const newData = this.transNewData(clone(data));
                this.$abcPage.$store.setQuickList(newData);
                this.loading = false;
            },

            transNewData(newData) {
                if (this.patientOrderIds.length) {
                    newData.forEach((item) => {
                        if (this.patientOrderIds.includes(item.id)) {
                            item.checked = true;
                        }
                    });
                }
                return newData;
            },

            /** ------------------------------------------------------------------------------------------
             * quicklist 点击后的处理函数
             */
            handleSelect(selectedItem) {
                this.$set(selectedItem, 'checked', !selectedItem.checked);
            },

            clearKey() {
                this.scrollParamsKey = '';
                this.loading = true;
                this.filterByKeyword();
            },

            onCheckAll(val) {
                this.quickList.forEach((x) => {
                    this.$set(x, 'checked', val);
                });
            },

            /**
             * @desc 获取待办patientOrderIds
             * <AUTHOR>
             * @date 2023/02/26 11:20:10
             * @param {string} wardId
             * @return
             */
            async getAutoCheckIds() {
                try {
                    const { data } = await MedicalPrescriptionAPI.getWardAreaStatusCounts(this.currentWardAreaId);
                    const { contentTab } = this.$route.query;
                    if (contentTab === MedicalAdviceStatusEnum.CHECKED) {
                        this.patientOrderIds = data.adviceStatusCounts[0]?.patientOrderIds;
                    } else if (contentTab === MedicalAdviceStatusEnum.EXECUTED) {
                        this.patientOrderIds = data.adviceExecuteStatusCounts[0]?.patientOrderIds;
                    } else if (contentTab === MedicalAdviceStatusEnum.UNDONE) {
                        this.patientOrderIds = data?.adviceStatusCounts?.find((item) =>
                            item.status === MedicalAdviceStatusEnum.STOPPED_UNDONE)?.patientOrderIds || [];
                    }
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .nurse-prescription-ql-item-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 44px;
        padding: 7px 8px 9px;
        cursor: pointer;
        background-color: #f9fafc;
        border-radius: var(--abc-border-radius-small);

        .ql-item-info {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .checkbox-wrapper {
            width: 14px;
            margin-right: 8px;
        }

        .info-left {
            display: flex;
            align-items: center;
            width: 100px;

            @include ellipsis;
        }

        .info-center {
            position: relative;
            display: flex;
            align-items: center;
            width: 0;

            >div + div {
                min-width: 30px;
                margin-left: 8px;
            }
        }

        .info-right {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: flex-end;

            >span {
                margin-left: 8px;
            }
        }

        .gray-text {
            color: $T2;
        }

        &:not(.is-active):hover {
            background-color: #eff3f6;
        }

        &.is-active {
            background-color: #40acff;

            .img-wrapper {
                background: url("~assets/images/icon/ql-selected-avatar.png") no-repeat center;
                background-size: 12px 12px;

                &.vip {
                    background: url("~assets/images/icon/ql-selected-v-avatar.png") no-repeat center;
                    background-size: 12px 12px;
                }
            }

            &::after {
                display: none;
            }
        }
    }
</style>
