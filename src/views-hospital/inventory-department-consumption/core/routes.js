import Index from '../index.vue';
import { MODULE_ID_MAP } from 'utils/constants';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const Main = () => import('../frames/main.vue');

// 外部跳转都应该用该字段
export const HospitalInventoryDepartmentConsumptionRouterNameKeys = {
    index: '@HospitalInventoryDepartmentConsumption',
    main: '@HospitalInventoryDepartmentConsumptionMain',
};

export default {
    path: 'inventory-department-consumption',
    name: HospitalInventoryDepartmentConsumptionRouterNameKeys.index,
    component: Index,
    meta: {
        name: '科室消耗',
        needAuth: true,
        pageAsyncClass: PageAsync,
        moduleId: MODULE_ID_MAP.hospitalInventoryDepartmentOut,
        visible(store) {
            return store.getters.currentPharmacyModulePermission.showDepartmentOut;
        },
    },
    children: [
        {
            path: '',
            component: Main,
            name: HospitalInventoryDepartmentConsumptionRouterNameKeys.main,
            meta: {
                name: '科室消耗',
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalInventoryDepartmentOut,
                visible(store) {
                    return store.getters.currentPharmacyModulePermission.showDepartmentOut;
                },
            },
        },
    ],
};
