import { goodsFullName } from '@/filters';
import Model from '@/views-pharmacy/inventory/frames/goods/model.js';
import { debounce } from 'utils/lodash.js';
import GoodsAPI from 'api/goods/index.js';
import { isEqual } from '@abc/utils';

export default class Presenter {
    constructor(view, observable) {
        this.view = view;
        this.model = new Model();
        this.checkedList = [];
        this.fetchOptions = [];
        if (observable) {
            this.model = observable(new Model());
        }
        this._fetchData = debounce(this.fetchData, 200, true);
    }

    setState(key, val) {
        if (typeof key === 'string') {
            this.model.setState(key, val);
        } else {
            // 或者为对象，支持批量更新;
            Object.keys(key).forEach((k) => {
                this.model.setState(k, key[k]);
            });
        }
    }
    getState(key) {
        return this.model[key];
    }
    syncCheckedList(checkedList) {
        this.checkedList = checkedList || [];
    }
    syncFetchOptionsList(options) {
        this.fetchOptions = options || [];
    }
    syncDataListChecked() {
        this.model.panelData.rows?.forEach((item) => {
            item.checked = !!this.checkedList.find((it) => it.id === item.id || it.goodsId === item.id);
        });
    }
    setParams(key, val, isInit = true) {
        if (typeof key === 'string') {
            this.model.setParams(key, val);
        } else {
            // 或者为对象，支持批量更新;
            Object.keys(key).forEach((k) => {
                this.model.setParams(k, key[k]);
            });
        }
        if (isInit) {
            this.initOffset();
        }
        this._fetchData();
    }

    // 药品类型cascader
    handleChangeGoodsType(typeIdList, customTypeIdList) {
        console.log('handleChangeGoodsType', typeIdList, customTypeIdList);
        this.model.setParams('typeId', typeIdList);
        this.model.setParams('customTypeId', customTypeIdList);
        this.initOffset();
        this.fetchData();
    }
    handleChangeWarningType(key) {
        const keys = this.model.warningKeys.slice();
        if (keys.includes(key)) {
            keys.splice(keys.indexOf(key), 1);
        } else {
            keys.push(key);
        }

        this.model.setState('warningKeys', keys);
        this.initOffset();
        this.fetchData();
    }

    handleChangeSort(orderBy, orderType) {
        this.model.setParams('orderBy', orderBy);
        this.model.setParams('orderType', orderType);
        this.fetchData();
    }
    handleChangeSocialStatus(params) {
        this.model.setState('fetchParams', params);
        this.initOffset();
        this.fetchData();
    }
    handleUpdateList(options) {
        const {
            type,
            keepOrder,
            showToast = true,
        } = options;
        if (type === 1 || type === 2) {
            // 只有修改药品资料才需要保持顺序
            keepOrder && type === 1 ? this.updateData(options) : this.fetchData();
            this.view.closeDialogHandle();
            if (showToast) {
                this.view.$Toast({
                    message: type === 1 ? '保存成功' : '删除成功',
                    type: 'success',
                });
            }
        }
        if (type === 3) {
            this.fetchData(false);
        }
        if (type === 0) {
            this.view.closeDialogHandle();
        }

    }

    searchGoods(val = '') {
        this.model.setParams('keyword', val.trim());
        this.model.setParams('goodsId', '');


        this.initOffset();
        this.fetchData();
    }
    selectGoods(goods) {
        if (!goods) return;

        this.model.setState('searchKey', goodsFullName(goods));

        this.model.setParams('goodsId', goods.id);
        this.model.setParams('keyword', '');

        this.initOffset();
        this.fetchData();
    }

    clearSearch() {
        this.model.setState('searchKey', '');

        this.model.setParams('keyword', '');
        this.model.setParams('goodsId', '');

        this.initOffset();
        this.fetchData();
    }
    initOffset() {
        this.model.setParams('offset', 0);
    }
    createParams() {
        const params = {
            ...this.model.fetchParams,
            unGspWarn: this.model.warningKeys.includes('unGsp') ? 1 : undefined,
            profitWarn: this.model.warningKeys.includes('profit') ? 1 : undefined,
            stockWarn: this.model.warningKeys.includes('stock') ? 1 : undefined,
            expiredWarn: this.model.warningKeys.includes('expired') ? 1 : undefined,
            costPriceWarn: this.model.warningKeys.includes('costPrice') ? 1 : undefined,
            unPriceWarn: this.model.warningKeys.includes('unPrice') ? 1 : undefined,
            unsalableWarn: this.model.warningKeys.includes('unsalable') ? 1 : undefined,
        };
        delete params.isSpu;
        delete params.spuGoodsCondition;
        return params;
    }

    initChecked(data) {
        data?.rows?.forEach((item) => {
            item.checked = !!this.checkedList.find((it) => it.id === item.id || it.goodsId === item.id);
            if (this.view.isUseV3Api) {
                item.dispStockGoodsCount = item.stockPackageCount ?
                    `${item.stockPackageCount}${item.packageUnit}` :
                    `${item.stockPieceCount || 0}${item.pieceUnit || item.packageUnit}`;
            }
        });
        return data;
    }

    async fetchData() {
        const {
            showLoadingFunc,
            hideLoadingFunc,
            showErrorFunc,
        } = this.view;
        try {
            showLoadingFunc && showLoadingFunc();
            const params = this.createParams();
            const res = await GoodsAPI.goodsList(params);
            const afterParams = this.createParams();

            if (isEqual(params, afterParams) && res?.data) {
                this.model.setState('panelData', this.initChecked(res.data));
            }
        } catch (e) {
            console.error(e);
            showErrorFunc && showErrorFunc();
        } finally {
            hideLoadingFunc && hideLoadingFunc();
        }
    }
    async updateData(options) {
        // 查询当前goods最新列表数据
        const {
            currentGoods,
            currentGoodsIndex,
        } = options;
        const {
            showLoadingFunc,
            hideLoadingFunc,
            showErrorFunc,
        } = this.view;
        try {
            showLoadingFunc && showLoadingFunc();
            const beforeParams = this.createParams();
            beforeParams.goodsId = currentGoods.goodsId;
            const { data } = await GoodsAPI.goodsList(beforeParams);

            const item = data?.rows?.[0];

            // 查到数据就更新（只有修改才会调取，按道理这里99%都能查询到），没有就走老逻辑请求列表数据。
            if (item) {
                const rows = [...this.model.panelData.rows];
                rows.splice(currentGoodsIndex, 1, item);

                this.model.setState('panelData', {
                    ...this.model.panelData,
                    rows,
                });
            } else {
                await this.fetchData();
            }
        } catch (e) {
            showErrorFunc && showErrorFunc();
        } finally {
            hideLoadingFunc && hideLoadingFunc();
        }
    }
    destroy() {
        this.view = null;
        this.model = null;
    }
}
